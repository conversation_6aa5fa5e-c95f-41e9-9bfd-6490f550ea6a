package com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.MilestoneMeta;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.ZeroStateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.config.MilestoneProgressConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.Attributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneTrackResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;

import lombok.RequiredArgsConstructor;

/**
 * 
 * Service for providing Milestone ( Stage ) status.
 * 
 */


@Service
@RequiredArgsConstructor
public class ProgressTrackingService {

	private static final String EVENTS = "events";

	private static final String END_STAGE = "endStage";

	private static final String BEGIN_STAGE = "beginStage";
	
	private static final String BPMN_MODEL_SERVICE_TASK = "serviceTask";

	private final ProcessDetailsRepository processDetailsRepo;

	private final MilestoneProgressConfig milestoneProgressConfig;

	private Map<String, Map<String,ZeroStateConfig>> milestonMetaWorkflowTemplateConfigMap;
	
	private Map<String, ZeroStateConfig> milestonMetaWorkflowConfigMap;

	private DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;

	private final ActivityDetailsRepository activityDetailsRepo;

	private final ActivityProgressDetailsRepository activityProgressDetailsRepo;

	@PostConstruct
	public void afterPropertiesSet() {
		try {
			if(null != milestoneProgressConfig) {
				milestonMetaWorkflowTemplateConfigMap = milestoneProgressConfig.getZeroStateConfig().stream()
						.collect(Collectors.toMap(ZeroStateConfig::getWorkflow,
								milestoneMetaConfig -> CollectionUtils.isNotEmpty(milestoneMetaConfig.getTemplates())?
										milestoneMetaConfig.getTemplates().stream()
										.collect(Collectors.toMap(ZeroStateConfig::getTemplateId, Function.identity()))
										: Collections.emptyMap()));
				
				milestonMetaWorkflowConfigMap = milestoneProgressConfig.getZeroStateConfig().stream()
						.collect(Collectors.toMap(ZeroStateConfig::getWorkflow, Function.identity()));
			}
		} catch(Exception ex) {
			WorkflowLogger.logError(ex, "Error initialising zero state config.");
		}
	}

	/**
	 * Get all milestones for workflow belonging to recordId provided and apply status filter.
	 * @param recordId :: EngagamentId 
	 * @param statusFilter :: sub filter by status.
	 * @return WorkflowMilestoneTrackResponse
	 */
	public WorkflowMilestoneTrackResponse getProgressDetails(String recordId,
			Set<WorkflowMilestoneStatus> statusFilter) {
		WorkflowMilestoneTrackResponse response = new WorkflowMilestoneTrackResponse();
		response.setMilestones(new ArrayList<>());
		List<ProcessDetails> processDetails = processDetailsRepo.findProcessDetailsByRecordId(recordId);
		processDetails.stream()
			.forEach(processDetail -> 
			response.getMilestones().addAll(processMilestoneData(statusFilter, processDetail)));
		return response;
	}

	private List<WorkflowMilestoneResponse> processMilestoneData(Set<WorkflowMilestoneStatus> statusFilter,
			ProcessDetails processDetail) {

		TemplateDetails templateDetails = processDetail.getDefinitionDetails().getTemplateDetails();
		String templateId = templateDetails.getId();
		String workflowName = templateDetails.getTemplateName();
		
		ZeroStateConfig milestoneMetaConfig = (null != milestonMetaWorkflowTemplateConfigMap.get(workflowName) &&
				null != milestonMetaWorkflowTemplateConfigMap.get(workflowName).get(templateId)) 
		? milestonMetaWorkflowTemplateConfigMap.get(workflowName).get(templateId)
				: (null != milestonMetaWorkflowConfigMap.get(workflowName) ?
						milestonMetaWorkflowConfigMap.get(workflowName) : null);
		
		if (null == milestoneMetaConfig) {
			WorkflowLogger.logInfo("No Milestone Meta found for enagementId :: {}",
					processDetail.getRecordId());				
			return Collections.emptyList();
		}
		/**
		 * Fetch all milestones definition and create a Map.
		 */
		List<ActivityDetail> activityDetails = activityDetailsRepo.findByTemplateIdAndType(templateId,
				TaskType.MILESTONE);
		Map<String, ActivityDetail> activityDetailMap = activityDetails.stream()
				.collect(Collectors.toMap(ActivityDetail::getActivityId, Function.identity()));
		
		/**
		 * Fetch all runtime milestones and create a Map.
		 * Removed ServiceTask as loops on the same are causing duplicate keys.
		 */
		List<ActivityProgressDetails> activityProgressDetails = activityProgressDetailsRepo
				.findByProcessIdAndType(processDetail.getProcessId(), TaskType.MILESTONE);
		Map<String, ActivityProgressDetails> activityProgressDetailMap = activityProgressDetails.stream()
				.filter(activityProgressDetail -> 
					!BPMN_MODEL_SERVICE_TASK.equalsIgnoreCase(activityProgressDetail.getActivityDefinitionDetail().getActivityType()))
				.collect(Collectors.toMap(
						activityProgressDetail -> activityProgressDetail.getActivityDefinitionDetail().getActivityId(),
						Function.identity(), (existingActivityProgressDetail, newActivityProgressDetail)-> newActivityProgressDetail));
		
		return milestoneMetaConfig.getMilestoneMeta().stream()
				.map(milestoneMeta -> getMilestoneResponse(statusFilter, activityDetailMap, activityProgressDetailMap,
						milestoneMeta, processDetail))
				.filter(milestoneResponse -> null != milestoneResponse).sorted((obj1, obj2) -> {
					return sortBasedOnCreatedDateDesc(obj1, obj2);
				}).collect(Collectors.toList());
	}

	private int sortBasedOnCreatedDateDesc(WorkflowMilestoneResponse obj1, WorkflowMilestoneResponse obj2) {
		LocalDateTime obj1StartTime = null;
		LocalDateTime obj2StartTime = null;
		if (obj1.getCreatedDate() != null) {
			obj1StartTime = LocalDateTime.parse(obj1.getCreatedDate(), formatter.withZone(ZoneOffset.UTC));
		}
		if (obj2.getCreatedDate() != null) {
			obj2StartTime = LocalDateTime.parse(obj2.getCreatedDate(), formatter.withZone(ZoneOffset.UTC));
		}

		if (obj1StartTime != null && obj2StartTime != null) {
			return obj2StartTime.compareTo(obj1StartTime);
		} else if (obj1StartTime != null && obj2StartTime == null) {
			return -1;
		} else if (obj1StartTime == null && obj2StartTime != null) {
			return 1;
		}
		return 0;
	}

	private WorkflowMilestoneResponse getMilestoneResponse(Set<WorkflowMilestoneStatus> statusFilter,
			Map<String, ActivityDetail> activityDetailMap,
			Map<String, ActivityProgressDetails> activityProgressDetailMap, MilestoneMeta milestoneMeta,
			ProcessDetails processDetail) {
		final ActivityProgressDetails beginActivityProgressDetails = activityProgressDetailMap
				.get(milestoneMeta.getBeginElement());

		final ActivityProgressDetails endActivityProgressDetails = activityProgressDetailMap
				.get(milestoneMeta.getEndElement());

		final WorkflowMilestoneStatus status = getStatus(beginActivityProgressDetails, endActivityProgressDetails);
		if (CollectionUtils.isNotEmpty(statusFilter) && !statusFilter.contains(status)) {
			return null;
		}

		final List<Attributes> attributes = getAttributes(activityDetailMap, milestoneMeta);

		ZoneOffset systemOffset = ZoneOffset.systemDefault().getRules().getOffset(Instant.now());

		final String milestoneName = getMilestone(attributes);
		return WorkflowMilestoneResponse.builder().milestoneId(milestoneName).milestoneName(milestoneName)
				.workflowId(processDetail.getProcessId())
				.workflowName(processDetail.getDefinitionDetails().getTemplateDetails().getTemplateName())
				.attributes(attributes)
				.createdDate(null != beginActivityProgressDetails
						? beginActivityProgressDetails.getStartTime().toLocalDateTime().atOffset(systemOffset)
								.atZoneSameInstant(ZoneOffset.UTC)
								.format(formatter)
						: (null != endActivityProgressDetails
								? endActivityProgressDetails.getStartTime().toLocalDateTime().atOffset(systemOffset)
										.atZoneSameInstant(ZoneOffset.UTC)
										.format(formatter)
								: null))
				.completedDate(null != endActivityProgressDetails
						? endActivityProgressDetails.getStartTime().toLocalDateTime().atOffset(systemOffset)
								.atZoneSameInstant(ZoneOffset.UTC)
								.format(formatter)
						: null)
				.updatedDate(null != endActivityProgressDetails
						? endActivityProgressDetails.getStartTime().toLocalDateTime().atOffset(systemOffset)
								.atZoneSameInstant(ZoneOffset.UTC)
								.format(formatter)
						: (null != beginActivityProgressDetails
								? beginActivityProgressDetails.getStartTime().toLocalDateTime().atOffset(systemOffset)
										.atZoneSameInstant(ZoneOffset.UTC)
										.format(formatter)
								: null))
				.status(status).build();
	}

	private List<Attributes> getAttributes(Map<String, ActivityDetail> activityDetailMap, MilestoneMeta milestoneMeta) {
		final ActivityDetail beginActivityDefinitionDetail = activityDetailMap.get(milestoneMeta.getBeginElement());
		final ActivityDetail endActivityDefinitionDetail = activityDetailMap.get(milestoneMeta.getEndElement());

		final List<Attributes> attributes = new ArrayList<>();
		if (beginActivityDefinitionDetail != null) {
			attributes.addAll(attributes(beginActivityDefinitionDetail));
		}
		if (endActivityDefinitionDetail != null) {
			attributes.addAll(attributes(endActivityDefinitionDetail));
		}
		return attributes;
	}

	private WorkflowMilestoneStatus getStatus(ActivityProgressDetails beginActivityProgressDetails,
			ActivityProgressDetails endActivityProgressDetails) {
		return null != endActivityProgressDetails ? WorkflowMilestoneStatus.COMPLETED
				: (null != beginActivityProgressDetails ? WorkflowMilestoneStatus.IN_PROGRESS
						: WorkflowMilestoneStatus.NOT_STARTED);
	}

	/**
	 * All extension properties from both beginStage and engStage markers are added here.
	 * @param activityDefinitionDetail
	 * @return
	 * example,
	 *  "attributes": [{"name": "beginStage","value": "Client Unassigned"},
	 *  {"name": "activityName","value": "Begin Stage - Client Unassigned"},
	 *  {"name": "endStage","value": "Client Unassigned"},
	 *  {"name": "traversalId","value": "1"},
	 *  {"name": "activityName","value": "End Stage + Milestone - Client Unassigned"},
	 *  {"name": "milestoneName","value": "Client Unassigned"},
	 *  {"name": "stageDisplayName","value": "Client Unassigned"},
     *  {"name": "milestoneVisibility","value": "EXPERT_AND_CUSTOMER"},
     *  {"name": "milestoneDisplayName","value": "Client Unassigned"}]
	 */
	private List<Attributes> attributes(ActivityDetail activityDefinitionDetail) {
		WorkflowActivityAttributes beginAttributes = WorkflowTaskUtil
				.getActivityAttributes(activityDefinitionDetail.getAttributes());
		if (null != beginAttributes.getModelAttributes()) {
			return beginAttributes.getModelAttributes().entrySet().stream()
					//Skip Events.
					.filter(entry -> !EVENTS.equals(entry.getKey()))
					.map(entry -> Attributes.builder().name(entry.getKey()).value(entry.getValue()).build())
					.collect(Collectors.toList());
		}
		return Collections.emptyList();
	}

	private String getMilestone(List<Attributes> attributes) {
		if (CollectionUtils.isNotEmpty(attributes)) {
			Optional<Attributes> attribute = attributes.stream()
					.filter(attr -> attr.getName().equals(BEGIN_STAGE) || attr.getName().equals(END_STAGE)).findAny();
			return attribute.orElseThrow(() -> new WorkflowGeneralException(WorkflowError.PROGRESS_TRACKING_MILESTONE_NOT_FOUND))
					.getValue();
		}
		throw new WorkflowGeneralException(WorkflowError.PROGRESS_TRACKING_MILESTONE_NOT_FOUND);
	}

}

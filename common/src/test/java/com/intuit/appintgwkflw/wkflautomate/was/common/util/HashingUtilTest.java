package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HashingUtilTest {

  @Test
  public void testGenerateHashForString() {
    String firstInputString = "Planet";
    String secondInputString = "lepatn";
    String thirdInputString = "Planet";
    String firstStringHash = HashingUtil.generateHashForString(firstInputString);
    String secondStringHash = HashingUtil.generateHashForString(secondInputString);
    String thirdStringHash = HashingUtil.generateHashForString(thirdInputString);
    Assert.assertEquals(firstStringHash, thirdStringHash);
    Assert.assertNotEquals(firstStringHash, secondStringHash);
  }
}

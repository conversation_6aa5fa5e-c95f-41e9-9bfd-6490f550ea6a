[IPF - was-event-in - PRD - Invoice JVM Memory Utilization]
alert_metric_name = sum((ts(iks.jvm.memory.used.bytes and namespace=appintgwkflw-wkflautomate-waseventin-usw2-prd and pod=invoice*)), pod)/sum((ts(iks.jvm.memory.committed.bytes and namespace=appintgwkflw-wkflautomate-waseventin-usw2-prd and pod=invoice*)), pod) * 100
severity = SEVERE
threshold = 80
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =
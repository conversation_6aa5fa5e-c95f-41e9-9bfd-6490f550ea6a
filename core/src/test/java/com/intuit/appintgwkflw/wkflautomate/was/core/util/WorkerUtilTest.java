package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class WorkerUtilTest {

  @Mock private DefinitionDetailsRepository definitionRepository;
  @Mock private ProcessDetailsRepository processDetailsRepository;

  @InjectMocks WorkerUtil workerUtil;


  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testDisabledDefinition() {

    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setInternalStatus(InternalStatus.MARKED_FOR_DISABLE);

    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessStatus(ProcessStatus.ACTIVE);
    Optional<ProcessDetails> processDetailsOpt = Optional.of(processDetails);
    Mockito.when(processDetailsRepository.findById(Mockito.any())).thenReturn(processDetailsOpt);

    workerUtil.validate(
        WorkerActionRequest.builder()
            .processDefinitionId("definitionId")
            .processInstanceId("processId")
            .build());
  }

  @Test(expected = WorkflowNonRetriableException.class)
  public void testEnabledDefinitionProcessError() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    Optional<DefinitionDetails> value = Optional.of(definitionDetails);

    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessStatus(ProcessStatus.ERROR);
    Optional<ProcessDetails> processDetailsOpt = Optional.of(processDetails);
    Mockito.when(processDetailsRepository.findById(Mockito.any())).thenReturn(processDetailsOpt);

    workerUtil.validate(
        WorkerActionRequest.builder()
            .processDefinitionId("definitionId")
            .processInstanceId("processId")
            .build());
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testEnabledDefinitionProcessNotFound() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    Optional<DefinitionDetails> value = Optional.of(definitionDetails);

    Mockito.when(processDetailsRepository.findById(Mockito.any()))
        .thenReturn(Optional.ofNullable(null));

    workerUtil.validate(
        WorkerActionRequest.builder()
            .processDefinitionId("definitionId")
            .processInstanceId("processId")
            .build());
  }

  @Test
  public void testEnabledDefinitionEmptyProcessStatus() {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setOwnerId(123l);
    Optional<DefinitionDetails> value = Optional.of(definitionDetails);

    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setOwnerId(123l);
    processDetails.setDefinitionDetails(definitionDetails);
    Optional<ProcessDetails> processDetailsOpt = Optional.of(processDetails);

    Mockito.when(processDetailsRepository.findById(Mockito.any())).thenReturn(processDetailsOpt);

    WorkerActionRequest workerActionRequest =
        workerUtil.validate(
            WorkerActionRequest.builder()
                .processDefinitionId("definitionId")
                .processInstanceId("processId")
                .build());
    Assert.assertNotNull(workerActionRequest);
    Assert.assertEquals(123l, workerActionRequest.getOwnerId().longValue());
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testEnabledDefinitionEmptyDefinition() {
    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setOwnerId(123l);
    processDetails.setDefinitionDetails(null);
    Optional<ProcessDetails> processDetailsOpt = Optional.of(processDetails);

    Mockito.when(processDetailsRepository.findById(Mockito.any())).thenReturn(processDetailsOpt);

    WorkerActionRequest workerActionRequest =
        workerUtil.validate(
            WorkerActionRequest.builder()
                .processDefinitionId("definitionId")
                .processInstanceId("processId")
                .build());
    Assert.assertNotNull(workerActionRequest);
    Assert.assertEquals(123l, workerActionRequest.getOwnerId().longValue());
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testDefinitionNotFound() {
    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessStatus(ProcessStatus.ACTIVE);
    Optional<ProcessDetails> processDetailsOpt = Optional.of(processDetails);
    Mockito.when(processDetailsRepository.findById(Mockito.any())).thenReturn(processDetailsOpt);

    workerUtil.validate(WorkerActionRequest.builder().processInstanceId("processId").build());
  }
}

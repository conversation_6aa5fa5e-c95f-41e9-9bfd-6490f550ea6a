kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  labels:
    env: qal-usw2-eks
  name: was-event-in-rollout
spec:
  strategy:
    canary:
      canaryMetadata:
        annotations:
          role: canary
        labels:
          role: canary
      stableMetadata:
        annotations:
          role: stable
        labels:
          role: stable
  # Set replicas to null when enabling HPA, sample: 'replicas: null'
  replicas: null
  template:
    metadata:
      labels:
        env: qal-usw2-eks
        splunk-index: qal
      annotations:
        iam.amazonaws.com/role: k8s-appintgwkflw-wkflautomate-waseventin-usw2-qal
    spec:
      containers:
      - name: app
        env:
        - name: APP_ENV
          value: qal
        - name: APP_CONFIG_NAME
          value: waseventin
        - name: RUNTIME_ID
          value: was-event-in-runtime-enrichment
        - name: MESSAGE_MESH_SRC_TOPICS
          value: workflow-data-processor-qal,was-event-in-replay-qal
        - name: EVENT_BUS_IDPS_POLICY_ID
          value: p-uxhan6qw86aq
        - name: MESSAGE_MESH_SRC_CONSUMER_GROUP
          value: was-event-in-processor-group-qal-enrichment
        - name: MESSAGE_MESH_PIPELINE_FILENAME
          value: sample-pipeline.yml
        - name: SPRING_CONFIG_IDPS_POLICY_ID
          value: p-vkvgmu5wnx0r
        - name: HOSTED_CONFIG_BRANCH
          value: qal
        ports:
        - containerPort: 6300
          name: jacoco-agent
        resources:
          limits:
            cpu: 3000m
            memory: 5Gi
          requests:
            cpu: 1500m
            memory: 4Gi

      initContainers:
      - name: segment-app-init
        env:
        - name: APP_ENV
          value: qal
        - name: SEGMENT_CLUSTER_ROLE_ARN
          value: arn:aws:iam::914122326290:role/shared.sbg-qbo-ppd-usw2-k8s
        - name: SEGMENT_IDPS_APPLIANCE
          value: MSaaSBU02-PRODUCTION-RJPH0E.pd.idps.a.intuit.com
        - name: SEGMENT_IDPS_POLICY_ID
          value: p-zb0qgnak4k1g
        - name: AWS_SDK_LOAD_CONFIG
          value: "1"
        - name: AWS_ROLE_SESSION_NAME
          value: kiam-kiam
package com.intuit.appintgwkflw.wkflautomate.was.core.cache.exceptionHandlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.client.RedisConnectionException;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class CacheExceptionHandler {

    @AfterThrowing(pointcut = "execution(* com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations.RedissonClientOperationImpl.*(..))",
            throwing = "ex")
    public void handleCacheException(Exception ex) {
        if(ex instanceof RedisConnectionException) {
            throw new CacheRetriableException(WorkflowError.REDIS_CONNECTION_ERROR, ex);
        }
        throw new CacheNonRetriableException(WorkflowError.REDIS_CACHE_ERROR, ex);
    }
}

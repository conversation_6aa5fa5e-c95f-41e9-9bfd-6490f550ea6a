Contribution Guidelines
=======================
Great to have you here. Whether it's improving documentation, adding a new component, or suggesting an issue that will help us improve, all contributions are welcome!


## Contribution Expectations

#### Adding Functionality or Reporting Bugs
* You can look through the existing features/bugs in the project and contribute.
* You can report bugs as Jira issues. Give as much detail as possible. (Logs, stacktraces, versions, etc.)

#### Code Quality Expectations
- Tests: All new Java methods should have correlated JUnit tests
- Coverage: Ensure that code coverage does not fall below 80%
- Documentation: Code should be well-documented. What code is doing should be self-explanatory based on coding conventions. Why code is doing something should be explained:
	* Java code should have JavaDoc
	* `pom.xml` should have comments
	* Unit tests should have comments and failure messages
	* Integration tests should have comments and failure messages
- Code Style: We try to follow [Google's Coding Standards](https://google.github.io/styleguide/javaguide.html). It's easiest to format based on existing code you see. We don't enforce this; it's just a guideline


## Contribution Process
**All contributions should be done through a fork**

### Before PR
1. Create a fork of the codebase and checkout the code.
2. Create a feature branch that represents your changes, for e.g. 'perf-changes'.
3. Write proper unit tests for the change.
4. Do a basic sanity test for your change by verifying the expected results on Postman.
5. If your change affects more than one component in the platform, run the automation tests to verify there is no regression.
6. Make your code changes and push to your branch.


### During PR

1. Limit the maximum number of files in each PR to 10. Break your PR into multiple logical parts if the change is bigger.
2. Create a `Draft PR` and get it reviewed internally within your team. We require at-least one internal review before looking at your PR.
3. Give the Jira Id in the PR title.
4. In the PR description, provide the following details:  
   i. Details of the change  
   ii. Details of testing done  
   iii. Screenshots of Postman or Overwatch tests
5. Select the appropriate priority for your PR.
6. Link your PR to the original issue.
7. Ensure that `codecov` results are above 90% coverage (with 100% diff coverage).
8. Publish the PR once it's ready for review.

### After PR

1. Drop a message in Slack: `#oneintuit-was-onboarding` and tag `@workflow-platform-oncall`.
2. We will take a look at the PR according to the given SLA.

Note: Turnaround time for making changes related to review comments will not be considered as part of SLA.

package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum Status {
  ENABLED("enabled"),
  DISABLED("disabled");

  private String status;

  Status(String status) {
    this.status = status;
  }

  public static Status lookupStatus(String type) {
    for (Status status : Status.values()) {
      if (status.getStatus().equals(type)) {
        return status;
      }
    }
    throw new UnsupportedOperationException("The code " + type + " is not supported!");
  }
}

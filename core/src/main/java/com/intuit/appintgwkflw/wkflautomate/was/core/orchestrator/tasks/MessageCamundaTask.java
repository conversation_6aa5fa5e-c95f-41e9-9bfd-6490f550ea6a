package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateMessage;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.AllArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@AllArgsConstructor
public class MessageCamundaTask implements Task {

  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;
  private TriggerDetailsRepository triggerDetailsRepository;
  private String request;
  private String response;

  @Override
  public State execute(State state) {
    String processInstanceId = state.getValue(request);
    String definitionId = state.getValue(DEFINITION_ID_KEY);
    String triggerName = state.getValue(AsyncTaskConstants.CAMUNDA_MESSAGE_NAME);
    WorkflowVerfiy.verifyNull(processInstanceId, WorkflowError.INVALID_INPUT);
    WorkflowVerfiy.verifyNull(
        StringUtils.isEmpty(definitionId), WorkflowError.INVALID_DEFINITION_DETAILS);
    WorkflowVerfiy.verifyNull(
        StringUtils.isEmpty(triggerName), WorkflowError.INVALID_TRIGGER_DETAILS);

    Optional<List<TriggerDetails>> triggerDetailsOpt =
        triggerDetailsRepository.findTriggerDetailsByDefinitionId(definitionId);

    WorkflowVerfiy.verifyNull(
        !triggerDetailsOpt.isPresent() || CollectionUtils.isEmpty(triggerDetailsOpt.get()),
        WorkflowError.INVALID_TRIGGER_DETAILS);

    TriggerDetails triggerDetails =
        triggerDetailsOpt
            .get()
            .stream()
            .filter(trigger -> trigger.getTriggerName().toLowerCase().contains(triggerName))
            .findFirst()
            .orElse(null);

    WorkflowVerfiy.verifyNull(
        Objects.isNull(triggerDetails) || StringUtils.isEmpty(triggerDetails.getTriggerName()),
        WorkflowError.INVALID_TRIGGER_DETAILS);

    CorrelateMessage correlateMessage =
        CorrelateMessage.builder()
            .messageName(triggerDetails.getTriggerName())
            .processInstanceId(processInstanceId)
            .processVariables(Collections.emptyMap())
            .build();

    state.addValue(response, bpmnEngineRunTimeServiceRest.correlateMessage(correlateMessage));
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getName())
                .message(
                    "Invoked message=%s to camunda with definitionId=%s",
                    triggerDetails.getTriggerName(), processInstanceId)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DISABLE_DEFINITION));
    return state;
  }
}

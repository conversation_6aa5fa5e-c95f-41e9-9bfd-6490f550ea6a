package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 */
public class UCSHttpClientTest {

  @Mock WASHttpClient wasHttpClient;
  @InjectMocks private UCSHttpClient client;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  @SuppressWarnings("rawtypes")
  public void testHttpResponse() {
    UcsVerifyAccessRequest ucsVerifyAccessRequest = UcsVerifyAccessRequest.builder().build();

    WASHttpResponse response = Mockito.mock(WASHttpResponse.class);
    Mockito.when(response.isSuccess2xx()).thenReturn(true);

    WASHttpRequest<UcsVerifyAccessRequest, ResponseEntity<Boolean>> wasHttpRequest =
        WASHttpRequest.<UcsVerifyAccessRequest, ResponseEntity<Boolean>>builder()
            .url("url")
            .request(ucsVerifyAccessRequest)
            .httpMethod(HttpMethod.POST)
            .build();

    Mockito.when(wasHttpClient.httpResponse(wasHttpRequest)).thenReturn(response);

    WASHttpResponse<ResponseEntity<Boolean>> resp = client.httpResponse(wasHttpRequest);
    Assert.assertTrue(resp.isSuccess2xx() == true);
  }
}

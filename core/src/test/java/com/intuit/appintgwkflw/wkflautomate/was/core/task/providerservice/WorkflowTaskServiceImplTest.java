package com.intuit.appintgwkflw.wkflautomate.was.core.task.providerservice;

import static com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums.INTUIT_TID;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthorizationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaServiceManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.ExternalTaskRestHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Permission;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.TaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.WasAuthorizeRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task.WorkflowTaskRequestDetails;
import com.intuit.v4.Authorization;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.workflows.tasks.Task;
import com.intuit.v4.workflows.tasks.TaskAttribute;
import com.intuit.v4.workflows.tasks.TaskTypeEnum;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskServiceImplTest {

  private static final String WORKFLOW_ID = "workflowId";
  private static final String ACTIVITY_ID = "activityId";
  private static final String ACTIVITY_INSTANCE_ID = "activityInstanceId";
  private static final String RESOURCE_REALM = "123456";
  private static final String REQUESTING_REALM = "999999";


  @InjectMocks
  private WorkflowTaskServiceImpl workflowTaskService;

  @Mock
  private ActivityDetailsRepository activityDetailsRepository;

  @Mock
  private ActivityProgressDetailsRepository activityProgressDetailsRepository;

  @Mock
  private ProcessDetailsRepository processDetailsRepository;

  @Mock
  private WASContextHandler contextHandler;

  @Mock
  private AuthorizationConfig authorizationConfig;

  @Mock
  private CamundaServiceManager camundaServiceManager;

  @Mock
  private ExternalTaskRestHandler externalTaskRestHandler;

  @Mock
  private CamundaHistoryServiceRest camundaHistoryServiceRest;
  private Authorization authorization = new Authorization().realm("123456");

  @Mock
  private AccessVerifier accessVerifier;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
    WASContext.setAuthContext(authorization);
    WASContext.setOfferingId("offeringId");
    Mockito.when(
            camundaServiceManager.getExternalTaskVariables(
                Mockito.anyString(), Mockito.anyString()))
        .thenReturn(Collections.emptyMap());
  }

  @After
  public void after(){
    WASContext.clear();
  }

  @Test
  public void test_AllInputsPresent() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");
    input.put(ACTIVITY_INSTANCE_ID, "aIId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setId("aIId");

    Mockito.when(
            activityProgressDetailsRepository.findById(Mockito.eq("aIId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertEquals("aIId", task.getActivityInstanceId());
    Assert.assertEquals("Completed", task.getStatus());
    Assert.assertEquals("txnId1", task.getExternalRefId());

    Assert.assertNotNull(task.getCreatedDate());
    Assert.assertNotNull(task.getUpdatedDate());
    Assert.assertNotNull(task.getCompletedDate());

    Map<String, String> assertionMap = new HashMap<>();
    assertionMap.put(
        "handlerDetails", "{\"taskHandler\":\"was\",\"actionName\":\"workflowCustomTaskHandler\"}");
    assertionMap.put("email1", "<EMAIL>");
    assertionMap.put("notificationName", "customWorkflow");
    assertionMap.put(
        "notificationData",
        "{\"To\":\"<EMAIL>\",\"Message\":\"TestMail1\",\"Subject\":\"ReviewInvoice002\",\"FromName\":\"TestMail\",\"ReplyToEmail\":\"<EMAIL>\"}");
    validateAttributes(task, assertionMap);

    Assert.assertEquals(16, task.getAttributes().size());
  }

  @Test
  public void test_WorkflowId_and_ActivityId() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);
    ActivityDetail activityDetail = prepareActivityDetail2();
    activityDetail.setActivityId("aId");
    Mockito.when(
            activityDetailsRepository.findByProcessIdAndActivityId(
                Mockito.eq("wId"), Mockito.eq("aId"), Mockito.anyLong()))
        .thenReturn(Collections.singletonList(activityDetail));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertNull(task.getActivityInstanceId());
    Assert.assertNull(task.getCompletedDate());
    Assert.assertNull(task.getUpdatedDate());
    Assert.assertNull(task.getCreatedDate());
    Assert.assertNull(task.getExternalRefId());
    Assert.assertNull(task.getStatus());

    Map<String, String> assertionMap = new HashMap<>();
    assertionMap.put(
        "handlerDetails", "{\"taskHandler\":\"was\",\"actionName\":\"workflowCustomTaskHandler\"}");
    assertionMap.put("notificationName", "customWorkflow");
    assertionMap.put("notificationData", "${notificationDataEmail}");
    validateAttributes(task, assertionMap);
  }

  @Test
  public void test_WorkflowId_and_ActivityId_EmptyInstanceId() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);
    ActivityDetail activityDetail = prepareActivityDetail2();
    activityDetail.setActivityId("aId");
    Mockito.when(
            activityDetailsRepository.findByProcessIdAndActivityId(
                Mockito.eq("wId"), Mockito.eq("aId"), Mockito.anyLong()))
        .thenReturn(Collections.singletonList(activityDetail));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertNull(task.getActivityInstanceId());
    Assert.assertNull(task.getCompletedDate());
    Assert.assertNull(task.getUpdatedDate());
    Assert.assertNull(task.getCreatedDate());
    Assert.assertNull(task.getExternalRefId());
    Assert.assertNull(task.getStatus());

    Map<String, String> assertionMap = new HashMap<>();
    assertionMap.put(
        "handlerDetails", "{\"taskHandler\":\"was\",\"actionName\":\"workflowCustomTaskHandler\"}");
    assertionMap.put("notificationName", "customWorkflow");
    assertionMap.put("notificationData", "${notificationDataEmail}");
    validateAttributes(task, assertionMap);
  }

  @Test
  public void test_WorkflowId_and_ActivityId_With_NullAttributes() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);

    ActivityDetail activityDetail = prepareActivityDetail2();
    activityDetail.setAttributes(null);
    activityDetail.setActivityId("aId");

    Mockito.when(
            activityDetailsRepository.findByProcessIdAndActivityId(
                Mockito.eq("wId"), Mockito.eq("aId"), Mockito.anyLong()))
        .thenReturn(Collections.singletonList(activityDetail));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertNull(task.getActivityInstanceId());
    Assert.assertNull(task.getCompletedDate());
    Assert.assertNull(task.getUpdatedDate());
    Assert.assertNull(task.getCreatedDate());
    Assert.assertNull(task.getExternalRefId());
    Assert.assertNull(task.getStatus());

    Assert.assertEquals(0, task.getAttributes().size());
  }

  @Test
  public void test_WorkflowId() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);
    Mockito.when(activityDetailsRepository.findByProcessId(Mockito.eq("wId"), Mockito.anyLong()))
        .thenReturn(List.of(prepareActivityDetail1(), prepareActivityDetail2()));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(2, taskList.size());

    Task task1 = taskList.get(1);
    Assert.assertEquals("serviceTask", task1.getActivityType());
    Assert.assertEquals("act2", task1.getActivityId());
    Assert.assertEquals("wId", task1.getWorkflowId());
    Assert.assertEquals("Activity 2", task1.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task1.getType());

    Assert.assertNull(task1.getActivityInstanceId());
    Assert.assertNull(task1.getCompletedDate());
    Assert.assertNull(task1.getUpdatedDate());
    Assert.assertNull(task1.getCreatedDate());
    Assert.assertNull(task1.getExternalRefId());
    Assert.assertNull(task1.getStatus());

    Task task2 = taskList.get(0);
    Assert.assertEquals("serviceTask", task2.getActivityType());
    Assert.assertEquals("act1", task2.getActivityId());
    Assert.assertEquals("wId", task2.getWorkflowId());
    Assert.assertEquals("Activity 1", task2.getName());
    Assert.assertEquals(TaskTypeEnum.NOTIFICATION_TASK, task2.getType());

    Assert.assertNull(task2.getActivityInstanceId());
    Assert.assertNull(task2.getCompletedDate());
    Assert.assertNull(task2.getUpdatedDate());
    Assert.assertNull(task2.getCreatedDate());
    Assert.assertNull(task2.getExternalRefId());
    Assert.assertNull(task2.getStatus());
  }

  @Test
  public void testAttributes() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");
    input.put(ACTIVITY_INSTANCE_ID, "aIId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.setAttributes(
        "{\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\",\"key3\":5,\"key4\":null,\"key5\":true,\"dueDate\":\"2022-05-15\",\"testMap\":{\"testKey\":\"testValue\"},\"testMapLocal\":{\"testKeyLocal\":\"testValueLocal\"},\"engagementRequest\":{\"path\":\"/v1/engagement/1649250742345/deep\",\"method\":\"GET\",\"resultOperation\":\"[{\\\"outputVariableKey\\\":\\\"customerAuthId\\\",\\\"jsonPath\\\":\\\"customerAuthId\\\",\\\"singleValue\\\":true},{\\\"outputVariableKey\\\":\\\"firmId\\\",\\\"jsonPath\\\":\\\"firmId\\\",\\\"singleValue\\\":true}]\"}}}");
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setId("aIId");

    Mockito.when(
            activityProgressDetailsRepository.findById(Mockito.eq("aIId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertEquals("aIId", task.getActivityInstanceId());
    Assert.assertEquals("Completed", task.getStatus());
    Assert.assertEquals("txnId1", task.getExternalRefId());

    Assert.assertNotNull(task.getCreatedDate());
    Assert.assertNotNull(task.getUpdatedDate());
    Assert.assertNotNull(task.getCompletedDate());

    Map<String, String> assertionMap = new HashMap<>();
    assertionMap.put("key1", "value1");
    assertionMap.put("key2", "value2");
    assertionMap.put(
        "engagementRequest",
        "{\"path\":\"/v1/engagement/1649250742345/deep\",\"method\":\"GET\",\"resultOperation\":\"[{\\\"outputVariableKey\\\":\\\"customerAuthId\\\",\\\"jsonPath\\\":\\\"customerAuthId\\\",\\\"singleValue\\\":true},{\\\"outputVariableKey\\\":\\\"firmId\\\",\\\"jsonPath\\\":\\\"firmId\\\",\\\"singleValue\\\":true}]\"}");
    assertionMap.put("testMapLocal", "{\"testKeyLocal\":\"testValueLocal\"}");
    assertionMap.put("key5", "true");
    assertionMap.put("key3", "5");
    assertionMap.put("key4", null);
    validateAttributes(task, assertionMap);
  }

  @Test
  public void testAttributes_CamundaOverride() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");
    input.put(ACTIVITY_INSTANCE_ID, "aIId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);

    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.setAttributes(
        "{\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\",\"key3\":5,\"key4\":null,\"key5\":true,\"dueDate\":\"2022-05-15\",\"testMap\":{\"testKey\":\"testValue\"},\"testMapLocal\":{\"testKeyLocal\":\"testValueLocal\"},\"engagementRequest\":{\"path\":\"/v1/engagement/1649250742345/deep\",\"method\":\"GET\",\"resultOperation\":\"[{\\\"outputVariableKey\\\":\\\"customerAuthId\\\",\\\"jsonPath\\\":\\\"customerAuthId\\\",\\\"singleValue\\\":true},{\\\"outputVariableKey\\\":\\\"firmId\\\",\\\"jsonPath\\\":\\\"firmId\\\",\\\"singleValue\\\":true}]\"}}}");
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setId("aIId");

    Mockito.when(
            activityProgressDetailsRepository.findById(Mockito.eq("aIId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, Object> map = new HashMap<>();
    map.put("test1", "testVal");
    map.put("key1", "value3");
    Mockito.when(
            camundaServiceManager.getExternalTaskVariables(
                Mockito.anyString(), Mockito.anyString()))
        .thenReturn(map);

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals("serviceTask", task.getActivityType());
    Assert.assertEquals("aId", task.getActivityId());
    Assert.assertEquals("wId", task.getWorkflowId());
    Assert.assertEquals("Activity 2", task.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task.getType());

    Assert.assertEquals("aIId", task.getActivityInstanceId());
    Assert.assertEquals("Completed", task.getStatus());
    Assert.assertEquals("txnId1", task.getExternalRefId());

    Assert.assertNotNull(task.getCreatedDate());
    Assert.assertNotNull(task.getUpdatedDate());
    Assert.assertNotNull(task.getCompletedDate());

    Map<String, String> assertionMap = new HashMap<>();

    assertionMap.put("test1", "testVal");
    assertionMap.put("key1", "value3");
    assertionMap.put("key3", "5");
    assertionMap.put("key4", null);
    assertionMap.put("key5", "true");
    validateAttributes(task, assertionMap);
    Assert.assertEquals(19, task.getAttributes().size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_1() {
    Map<String, String> input = new HashMap<>();
    input.put(ACTIVITY_INSTANCE_ID, "aIId");
    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_2() {
    Map<String, String> input = new HashMap<>();
    input.put(ACTIVITY_ID, "aId");
    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_3() {
    Map<String, String> input = new HashMap<>();
    input.put(ACTIVITY_INSTANCE_ID, "aIId");
    input.put(ACTIVITY_ID, "aId");
    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_4() {
    Map<String, String> input = new HashMap<>();
    input.put(ACTIVITY_INSTANCE_ID, "aIId");
    input.put(WORKFLOW_ID, "wId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenThrow(WorkflowGeneralException.class);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_6() {
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    input.put(ACTIVITY_ID, "aIId");
    input.put(WORKFLOW_ID, "   ");
    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_InvalidI_Input_5() {
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  private ActivityDetail prepareActivityDetail1() {
    ActivityDetail activityDetail = ActivityDetail.builder()
        .id(1L)
        .type(TaskType.NOTIFICATION_TASK)
        .activityType("serviceTask")
        .activityId("act1")
        .activityName("Activity 1")
        .parentId(0)
        .attributes("{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"notificationData\":\"${notificationDataMobile}\",\"notificationMetaData\":\"${notificationMetaDataMobile}\"}}")
        .build();
    return activityDetail;
  }

  private ActivityDetail prepareActivityDetail2() {
    ActivityDetail activityDetail = ActivityDetail.builder()
        .id(1L)
        .type(TaskType.SYSTEM_TASK)
        .activityType("serviceTask")
        .activityId("act2")
        .activityName("Activity 2")
        .parentId(0)
        .attributes("{\"modelAttributes\":{\"type\":\"SYSTEM_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Email\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"customWorkflow\",\"notificationDataType\":\"customWorkflow\"},\"runtimeAttributes\":{\"notificationData\":\"${notificationDataEmail}\",\"notificationMetaData\":\"${notificationMetaDataEmail}\"}}")
        .build();
    return activityDetail;
  }

  private ActivityDetail prepareActivityDetailForMileStone() {
    ActivityDetail activityDetail = ActivityDetail.builder()
            .id(1L)
            .type(TaskType.MILESTONE)
            .activityType("serviceTask")
            .activityId("act2")
            .activityName("Activity 2")
            .parentId(0)
            .attributes("{\"modelAttributes\":{\"type\":\"SYSTEM_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Email\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"customWorkflow\",\"notificationDataType\":\"customWorkflow\"},\"runtimeAttributes\":{\"notificationData\":\"${notificationDataEmail}\",\"notificationMetaData\":\"${notificationMetaDataEmail}\"}}")
            .build();
    return activityDetail;
  }

  private ActivityProgressDetails prepareActivityProgressDetails() {
    TemplateDetails templateDetail = TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetail = DefinitionDetails.builder().templateDetails(templateDetail).build();
    
    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id("id1")
        .activityDefinitionDetail(prepareActivityDetail2())
        .name("Batch Notification Task")
        .txnDetails(TransactionDetails.builder().txnId("txnId1").build())
        .status("Completed")
        .startTime(Timestamp.from(Instant.now()))
        .endTime(Timestamp.from(Instant.now()))
        .updatedTime(Timestamp.from(Instant.now()))
        .processDetails(ProcessDetails.builder().processId("wId")
            .definitionDetails(definitionDetail).build())
        .attributes("{\"runtimeAttributes\":{\"email1\":\"<EMAIL>\",\"email2\":\"<EMAIL>\",\"notificationData\":\"{\\\"To\\\":\\\"<EMAIL>\\\",\\\"Message\\\":\\\"TestMail1\\\",\\\"Subject\\\":\\\"ReviewInvoice002\\\",\\\"FromName\\\":\\\"TestMail\\\",\\\"ReplyToEmail\\\":\\\"<EMAIL>\\\"}\",\"notificationMetaData\":\"{\\\"authId\\\":\\\"-1\\\"}\",\"notificationDataEmail\":\"{\\\"To\\\":\\\"<EMAIL>\\\",\\\"Message\\\":\\\"TestMail1\\\",\\\"Subject\\\":\\\"ReviewInvoice002\\\",\\\"FromName\\\":\\\"TestMail\\\",\\\"ReplyToEmail\\\":\\\"<EMAIL>\\\"}\",\"notificationTaskList1\":\"[{\\\"notificationName\\\":\\\"customWorkflow\\\",\\\"serviceName\\\":\\\"Workflow\\\",\\\"notificationDataType\\\":\\\"customWorkflow\\\",\\\"idempotencyKey\\\":\\\"abcdef\\\",\\\"notificationMetaData\\\":{\\\"authId\\\":\\\"-1\\\"},\\\"notificationData\\\":{\\\"To\\\":\\\"<EMAIL>\\\",\\\"Message\\\":\\\"Testmail2\\\",\\\"Subject\\\":\\\"ReviewInvoice001\\\",\\\"FromName\\\":\\\"TestMail\\\",\\\"ReplyToEmail\\\":\\\"<EMAIL>\\\"}},{\\\"notificationName\\\":\\\"customWorkflow\\\",\\\"serviceName\\\":\\\"Workflow\\\",\\\"notificationDataType\\\":\\\"customWorkflow\\\",\\\"idempotencyKey\\\":\\\"abcdef\\\",\\\"notificationMetaData\\\":{\\\"authId\\\":\\\"-1\\\"},\\\"notificationData\\\":{\\\"To\\\":\\\"<EMAIL>\\\",\\\"Message\\\":\\\"Testmail2\\\",\\\"Subject\\\":\\\"ReviewInvoice001\\\",\\\"FromName\\\":\\\"TestMail\\\",\\\"ReplyToEmail\\\":\\\"<EMAIL>\\\"}}]\",\"notificationDataMobile\":\"{\\\"subject\\\":\\\"GotoQuickBookstoviewit.\\\",\\\"action\\\":\\\"qb001://open/invoice/?id=14470&companyid=193514740074579\\\",\\\"title\\\":\\\"AnInvoiceneedsyourattention\\\"}\",\"notificationMetaDataEmail\":\"{\\\"authId\\\":\\\"-1\\\"}\",\"notificationMetaDataMobile\":\"{\\\"authId\\\":123146808309444}\"}}")
        .build();
    return activityProgressDetails;
  }

  private void validateAttributes(Task task, Map<String, String> assertionMap) {
    task.getAttributes().stream().filter(taskAttribute -> assertionMap.containsKey(taskAttribute.getName()))
        .forEach(taskAttribute -> {
          Assert.assertEquals(assertionMap.get(taskAttribute.getName()), taskAttribute.getValue());
          assertionMap.remove(taskAttribute.getName());
        });

    Assert.assertEquals(0, assertionMap.size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_WorkflowId_and_ActivityId_UnsupportedException() {
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(REQUESTING_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(RESOURCE_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testAttributes_UnsupportedException() {
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");
    input.put(ACTIVITY_INSTANCE_ID, "aIId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(REQUESTING_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(RESOURCE_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test
  public void test_WorkflowId_Same_Realm() {
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(RESOURCE_REALM);

    Mockito.when(activityDetailsRepository.findByProcessId(Mockito.eq("wId"), Mockito.anyLong()))
        .thenReturn(List.of(prepareActivityDetail1(), prepareActivityDetail2()));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(2, taskList.size());

    Task task1 = taskList.get(1);
    Assert.assertEquals("serviceTask", task1.getActivityType());
    Assert.assertEquals("act2", task1.getActivityId());
    Assert.assertEquals("wId", task1.getWorkflowId());
    Assert.assertEquals("Activity 2", task1.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task1.getType());

    Assert.assertNull(task1.getActivityInstanceId());
    Assert.assertNull(task1.getCompletedDate());
    Assert.assertNull(task1.getUpdatedDate());
    Assert.assertNull(task1.getCreatedDate());
    Assert.assertNull(task1.getExternalRefId());
    Assert.assertNull(task1.getStatus());

    Task task2 = taskList.get(0);
    Assert.assertEquals("serviceTask", task2.getActivityType());
    Assert.assertEquals("act1", task2.getActivityId());
    Assert.assertEquals("wId", task2.getWorkflowId());
    Assert.assertEquals("Activity 1", task2.getName());
    Assert.assertEquals(TaskTypeEnum.NOTIFICATION_TASK, task2.getType());

    Assert.assertNull(task2.getActivityInstanceId());
    Assert.assertNull(task2.getCompletedDate());
    Assert.assertNull(task2.getUpdatedDate());
    Assert.assertNull(task2.getCreatedDate());
    Assert.assertNull(task2.getExternalRefId());
    Assert.assertNull(task2.getStatus());
  }

  @Test
  public void test_WorkflowId_Whitelist() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(RESOURCE_REALM);

    Mockito.when(activityDetailsRepository.findByProcessId(Mockito.eq("wId"), Mockito.anyLong()))
        .thenReturn(List.of(prepareActivityDetail1(), prepareActivityDetail2()));

    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(2, taskList.size());

    Task task1 = taskList.get(1);
    Assert.assertEquals("serviceTask", task1.getActivityType());
    Assert.assertEquals("act2", task1.getActivityId());
    Assert.assertEquals("wId", task1.getWorkflowId());
    Assert.assertEquals("Activity 2", task1.getName());
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, task1.getType());

    Assert.assertNull(task1.getActivityInstanceId());
    Assert.assertNull(task1.getCompletedDate());
    Assert.assertNull(task1.getUpdatedDate());
    Assert.assertNull(task1.getCreatedDate());
    Assert.assertNull(task1.getExternalRefId());
    Assert.assertNull(task1.getStatus());

    Task task2 = taskList.get(0);
    Assert.assertEquals("serviceTask", task2.getActivityType());
    Assert.assertEquals("act1", task2.getActivityId());
    Assert.assertEquals("wId", task2.getWorkflowId());
    Assert.assertEquals("Activity 1", task2.getName());
    Assert.assertEquals(TaskTypeEnum.NOTIFICATION_TASK, task2.getType());

    Assert.assertNull(task2.getActivityInstanceId());
    Assert.assertNull(task2.getCompletedDate());
    Assert.assertNull(task2.getUpdatedDate());
    Assert.assertNull(task2.getCreatedDate());
    Assert.assertNull(task2.getExternalRefId());
    Assert.assertNull(task2.getStatus());
  }

  @Test
  public void updateWorkflow_withProcessId() {
    Task task = prepareTaskObject("in-progress");
    task.setAttributes(prepareUniqueAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"SYSTEM_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  @Test
  public void updateWorkflow_withRecordIdAndWorkflowName() {
    Task task = prepareTaskObject("in-progress");
    task.setWorkflowId(null);
    task.setWorkflowName("wName");
    task.setRecordId("rid");
    task.setAttributes(prepareUniqueAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pid")
            .recordId("rid")
            .ownerId(Long.valueOf(RESOURCE_REALM))
            .definitionDetails(DefinitionDetails.builder().definitionName("wName").build())
            .build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(any(), anyString()))
        .thenReturn(Optional.of(processDetails));
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  // Where all 3 values are present, we will give preference to workflow id
  @Test
  public void updateWorkflow_withProcessIDorRecordIdAndWorkflowName() {
    Task task = prepareTaskObject("in-progress");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    task.setAttributes(prepareUniqueAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_withRecordIdOnly() {
    Task task = prepareTaskObject("in-progress");
    task.setWorkflowId(null);
    task.setRecordId("rid");
    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_withWorkflowNameOnly() {
    Task task = prepareTaskObject("in-progress");
    task.setWorkflowId(null);
    task.setWorkflowName("wName");
    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  // No Workflow found
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_invalid_Process_Id() {
    Task task = prepareTaskObject("in-progress");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.empty());

    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_invalid_RecordIdAndWorkflowName() {
    Task task = prepareTaskObject("in-progress");
    task.setWorkflowId(null);
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName("rid", "wName"))
        .thenReturn(Optional.empty());

    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  // Both ExternalRefId and ActivityInstanceId are null
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_missingActivityInstanceId() {
    Task task = prepareTaskObject("in-progress");
    task.setActivityInstanceId(null);
    task.setExternalRefId(null);
    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  // Both ExternalRefId and ActivityInstanceId are set
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_bothIdSet() {
    Task task = prepareTaskObject("in-progress");
    task.setExternalRefId("eid");
    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_InvalidErrorDetails() {
    Task task = prepareTaskObject("failed");
    task.setExternalRefId("eid");
    task.setError(null);
    workflowTaskService.updateWorkflowTasks(TaskRequest.builder().task(task).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void crossRealmDenyUpdate() {
    Task task = prepareTaskObject("in-progress");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(RESOURCE_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.FALSE);
    
    
 // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));


    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void crossRealmDenyGet() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.FALSE);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.getWorkflowTasks(
        TaskRequest.builder().query(input).headers(requestHeaders).build());
  }

  @Test
  public void crossRealmSuccessUpdate() {
    Task task = prepareTaskObject("in-progress");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(RESOURCE_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    Mockito.when(accessVerifier.verifyAccess(Mockito.any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  /** Attribute Validation Use Case */
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_withProcessIdException() {
    Task task = prepareTaskObject("in-progress");
    task.setAttributes(prepareInvalidAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"TEST_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  /** Attribute Validation Use Case */
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_withRecordIdAndWorkflowNameException() {
    Task task = prepareTaskObject("in-progress");
    task.setWorkflowId(null);
    task.setWorkflowName("wName");
    task.setRecordId("rid");
    task.setAttributes(prepareInvalidAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pid")
            .recordId("rid")
            .ownerId(Long.valueOf(REQUESTING_REALM))
            .definitionDetails(DefinitionDetails.builder().definitionName("wName").build())
            .build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(
            processDetailsRepository
                .findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(any(), anyString()))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"TEST_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  /** Attribute Validation Use Case */
  // Where all 3 values are present, we will give preference to workflow id
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflow_withProcessIDorRecordIdAndWorkflowNameException() {
    Task task = prepareTaskObject("in-progress");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    task.setAttributes(prepareInvalidAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"TEST_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  /** Attribute Validation Use Case */
  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflowAlreadyCompleted() {
    Task task = prepareTaskObject("success");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    task.setAttributes(prepareInvalidAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"TEST_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"}}");
    activityProgressDetails.setId("externalTaskId");
    activityProgressDetails.setStatus("success");

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  /** Attribute Validation Use Case */
  @Test(expected = WorkflowGeneralException.class)
  public void updateInvalidActivityInstanceId() {
    Task task = prepareTaskObject("success");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    task.setAttributes(prepareInvalidAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = null;

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.ofNullable(activityProgressDetails));

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }

  private Task prepareTaskObject(String status) {
    Task task = new Task();
    task.setWorkflowId("pid");
    task.setActivityInstanceId("externalTaskId");
    task.setStatus(status);
    TaskAttribute taskAttribute = new TaskAttribute();
    taskAttribute.setName("local");
    taskAttribute.setValue("testing1");
    List<TaskAttribute> attributes = new ArrayList<>();
    attributes.add(taskAttribute);
    task.setAttributes(attributes);
    return task;
  }

  /**
   * Prepare List of Task Mutation attributes
   * @return
   */
  private List<TaskAttribute> prepareUniqueAttributes() {
    List<TaskAttribute> taskAttributes = new ArrayList<>();
    TaskAttribute taskAttributeA = new TaskAttribute().name("key1").value("value1");
    TaskAttribute taskAttributeB = new TaskAttribute().name("key2").value("value2");
    TaskAttribute taskAttributeC = new TaskAttribute().name("key3").value("value3");
    TaskAttribute taskAttributeD = new TaskAttribute().name("key4").value("value4");
    TaskAttribute taskAttributeE = new TaskAttribute().name("key5").value("value5");
    taskAttributes.add(taskAttributeA);
    taskAttributes.add(taskAttributeB);
    taskAttributes.add(taskAttributeC);
    taskAttributes.add(taskAttributeD);
    taskAttributes.add(taskAttributeE);
    return taskAttributes;
  }

  /**
   * Prepare List of Task Mutation attributes with some Extension Elements keys
   * @return
   */
  private List<TaskAttribute> prepareInvalidAttributes() {
    List<TaskAttribute> taskAttributes = new ArrayList<>();
    TaskAttribute taskAttributeA = new TaskAttribute().name("taskDetails").value("value1");
    TaskAttribute taskAttributeB = new TaskAttribute().name("key2").value("value2");
    TaskAttribute taskAttributeC = new TaskAttribute().name("handlerDetails").value("value3");
    TaskAttribute taskAttributeD = new TaskAttribute().name("key4").value("value4");
    TaskAttribute taskAttributeE = new TaskAttribute().name("key5").value("value5");
    taskAttributes.add(taskAttributeA);
    taskAttributes.add(taskAttributeB);
    taskAttributes.add(taskAttributeC);
    taskAttributes.add(taskAttributeD);
    taskAttributes.add(taskAttributeE);
    return taskAttributes;
  }
  
  @Test
  public void updateWorkflow_humanTask() {
    Task task = prepareTaskObject("in-progress");
    task.setAttributes(prepareUniqueAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"HUMAN_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"},"
        + "\"runtimeAttributes\":{\"ownerId\":\"1111\"}}");
    activityProgressDetails.setId("externalTaskId");
    activityProgressDetails.setActivityDefinitionDetail(ActivityDetail.builder()
    		.type(TaskType.HUMAN_TASK).attributes("{\"modelAttributes\":{\"domain\":\"QB_LIVE\"}}").build());

    Mockito.when(
            activityProgressDetailsRepository.findById(
                Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }
  
  @Test(expected = WorkflowGeneralException.class)
  public void getActivityProgressDetails_NoActivityInstanceId() {
	  String activityInstanceId = null;
    ReflectionTestUtils.invokeMethod(workflowTaskService, "getActivityProgressDetails",
    		activityInstanceId, true);
  }
  
  @Test
  public void test_HumanTask_WorkflowId_and_ActivityId_With_ActivityInstanceId() {
    Map<String, String> input = new HashMap<>();
    input.put(WORKFLOW_ID, "wId");
    input.put(ACTIVITY_ID, "aId");
    input.put(ACTIVITY_INSTANCE_ID, "aIId");

    Mockito.when(processDetailsRepository.findOwnerId("wId"))
        .thenReturn(Optional.of(Long.valueOf(RESOURCE_REALM)));
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(accessVerifier.verifyHumanTaskAccess(any(WasAuthorizeRequest.class)))
        .thenReturn(Boolean.TRUE);

 // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"HUMAN_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\"},"
        + "\"runtimeAttributes\":{\"ownerId\":\"1111\"}}");
    activityProgressDetails.setId("aIId");
    ActivityDetail activityDetail = ActivityDetail.builder()
    		.type(TaskType.HUMAN_TASK).attributes("{\"modelAttributes\":{\"domain\":\"QB_LIVE\"}}").build();
    activityProgressDetails.setActivityDefinitionDetail(activityDetail);
    
    Mockito.when(activityProgressDetailsRepository.findById(Mockito.eq("aIId")))
    	.thenReturn(Optional.of(activityProgressDetails));
    
    Map<String, String> requestHeaders = new HashMap<>();
    List<Task> taskList =
        workflowTaskService.getWorkflowTasks(
            TaskRequest.builder().query(input).headers(requestHeaders).build());

    Assert.assertEquals(1, taskList.size());

    Task task = taskList.get(0);
    Assert.assertEquals(TaskTypeEnum.HUMAN_TASK, task.getType());
  }
  
  @Test
  public void  test_isAuthzCheckRequired() {
	  WasAuthorizeRequest wasAuthorizeRequest = 
			  WasAuthorizeRequest.builder().workflowOwnerId(1l).RequestOwnerId(1l)
			  .permission(Permission.TASK_READ).build();
	  boolean result = (boolean)ReflectionTestUtils.invokeMethod(workflowTaskService, "isAuthzCheckRequired", wasAuthorizeRequest);
	  Assert.assertFalse(result);
  }
  
  @Test
  public void  test_isAuthzCheckRequired_diferentWorkflowOwnerId() {
	  WasAuthorizeRequest wasAuthorizeRequest = 
			  WasAuthorizeRequest.builder().workflowOwnerId(1l).RequestOwnerId(2l)
			  .permission(Permission.TASK_READ).build();
	  boolean result = (boolean)ReflectionTestUtils.invokeMethod(workflowTaskService, "isAuthzCheckRequired", wasAuthorizeRequest);
	  Assert.assertTrue(result);
  }
  
  @Test
  public void  test_getTaskOwner() {
	  ActivityProgressDetails activityProgressDtl = ActivityProgressDetails.builder()
			  .attributes(ObjectConverter.toJson(TaskAttributes.builder()
					  .runtimeAttributes(Map.of(WorkflowConstants.OWNER_ID, "1")).build())).build();
	  String ownerId = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getTaskOwner", activityProgressDtl);
	  Assert.assertEquals("1", ownerId);
  }
  
  @Test
  public void  test_getTaskOwner_activityProgressDetailsNull() {
	  ActivityProgressDetails activityProgressDtl = null;
	  String ownerId = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getTaskOwner", activityProgressDtl);
	  Assert.assertNull(ownerId);
  }
  
  @Test
  public void  test_getTaskOwner_runtimeAttrNull() {
	  ActivityProgressDetails activityProgressDtl = ActivityProgressDetails.builder()
			  .attributes(ObjectConverter.toJson(TaskAttributes.builder().build())).build();
	  String ownerId = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getTaskOwner", activityProgressDtl);
	  Assert.assertNull(ownerId);
  }
  
  @Test
  public void  test_getDomain() {
	  ActivityProgressDetails activityProgressDtl = ActivityProgressDetails.builder()
			  .activityDefinitionDetail(ActivityDetail.builder()
			  .attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
					  .modelAttributes(Map.of(ActivityConstants.ACTIVITY_DOMAIN, "QB_LIVE")).build())).build()).build();
	  String domain = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getDomain", activityProgressDtl);
	  Assert.assertEquals("QB_LIVE", domain);
  }
  
  @Test
  public void  test_getDomain_activityProgressDetailsNull() {
	  ActivityProgressDetails activityProgressDtl = ActivityProgressDetails.builder()
			  .activityDefinitionDetail(ActivityDetail.builder().build()).build();
	  String domain = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getDomain", activityProgressDtl);
	  Assert.assertNull(domain);
  }
  
  @Test
  public void  test_getDomain_modelAttrNull() {
	  ActivityProgressDetails activityProgressDtl = ActivityProgressDetails.builder()
			  .activityDefinitionDetail(ActivityDetail.builder().build()).build();
	  String domain = (String)ReflectionTestUtils.invokeMethod(workflowTaskService, "getDomain", activityProgressDtl);
	  Assert.assertNull(domain);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void updateWorkflowTaskTypeMilestone() {
    Task task = prepareTaskObject("in-progress");
    task.setRecordId("rid");
    task.setWorkflowName("wName");
    task.setAttributes(prepareUniqueAttributes());
    ProcessDetails processDetails =
        ProcessDetails.builder().processId("pid").ownerId(Long.valueOf(REQUESTING_REALM)).build();
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);
    Mockito.when(processDetailsRepository.getProcessDetailsWithoutDefinitionData("pid"))
        .thenReturn(Optional.of(processDetails));

    // Attribute Validation
    ActivityProgressDetails activityProgressDetails = prepareActivityProgressDetails();
    activityProgressDetails.setActivityDefinitionDetail(prepareActivityDetailForMileStone());
    activityProgressDetails.getActivityDefinitionDetail().setActivityId("aId");
    activityProgressDetails.setAttributes(
        "{\"modelAttributes\":{\"type\":\"NOTIFICATION_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Mobilenotification\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"InvoiceApproval\",\"notificationDataType\":\"InvoiceApproval\"},\"runtimeAttributes\":{\"key1\":\"value1\",\"key2\":\"value2\"}}");
    activityProgressDetails.setId("externalTaskId");

    Mockito.when(activityProgressDetailsRepository.findById(Mockito.eq("externalTaskId")))
        .thenReturn(Optional.of(activityProgressDetails));

    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REQUESTING_REALM);

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "uuid");
    headers.put(EventHeaderConstants.INTUIT_TID, "uuid");
    headers.put(EventHeaderConstants.OFFERING_ID, "offeringId");
    headers.put(EventHeaderConstants.OWNER_ID, REQUESTING_REALM);

    Map<String, String> requestHeaders = new HashMap<>();
    workflowTaskService.updateWorkflowTasks(
        TaskRequest.builder().task(task).headers(requestHeaders).build());
  }
  
  @Test
  public void test_getWkflwTask_page0(){
    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.OWNER_ID))).thenReturn("9130352403806696");
    
    TemplateDetails templateDetail = TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetail = DefinitionDetails.builder().templateDetails(templateDetail).build();
   
    ProcessDetails processDetail = ProcessDetails.builder().processId("pid")
        .definitionDetails(definitionDetail)
        .ownerId(9130352403806697l).build();
    
    ActivityProgressDetails humanTask1 = prepareHumanTaskActivityProgressDetails("Human Task 1", "txn1", "9130352403806696");
    humanTask1.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask2 = prepareHumanTaskActivityProgressDetails("Human Task 2", "txn2", "9130352403806696");
    humanTask2.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask3 = prepareHumanTaskActivityProgressDetails("Human Task 3", "txn3", "9130352403806696");
    humanTask3.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask4 = prepareHumanTaskActivityProgressDetails("Human Task 4", "txn4", "9130354821213726");
    humanTask4.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask5 = prepareHumanTaskActivityProgressDetails("Human Task 5", "txn5", "9130354821213726");
    humanTask5.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask6 = prepareHumanTaskActivityProgressDetails("Human Task 6", "txn6", "9130354821213726");
    humanTask6.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask7 = prepareHumanTaskActivityProgressDetails("Human Task 7", "txn7", "9130352403806696");
    humanTask7.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask8 = prepareHumanTaskActivityProgressDetails("Human Task 8", "txn8", "9130352403806696");
    humanTask8.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask9 = prepareHumanTaskActivityProgressDetails("Human Task 9", "txn9", "9130354821213726");
    humanTask9.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask10 = prepareHumanTaskActivityProgressDetails("Human Task 10", "txn10", "9130354821213726");
    humanTask10.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask11 = prepareHumanTaskActivityProgressDetails("Human Task 11", "txn11", "9130352403806696");
    humanTask11.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask12 = prepareHumanTaskActivityProgressDetails("Human Task 12", "txn12", "9130352403806696");
    humanTask12.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask13 = prepareHumanTaskActivityProgressDetails("Human Task 13", "txn13", "9130352403806696");
    humanTask13.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask14 = prepareHumanTaskActivityProgressDetails("Human Task 14", "txn14", "9130354821213726");
    humanTask14.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask15 = prepareHumanTaskActivityProgressDetails("Human Task 15", "txn15", "9130354821213726");
    humanTask15.setProcessDetails(processDetail);
    
    List<ActivityProgressDetails> resultSet1 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask12, humanTask1, humanTask13, 
            humanTask2, humanTask14});
    
    List<ActivityProgressDetails> resultSet2 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask15, humanTask3, humanTask4, 
            humanTask5, humanTask6});
    
    List<ActivityProgressDetails> resultSet3 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask7, humanTask8, humanTask9, 
            humanTask10, humanTask11});
    
    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsAndTypeIn(Mockito.anyList(),
        Mockito.anyList(),
        Mockito.any(Pageable.class))).thenReturn(resultSet1)
    .thenReturn(resultSet2).thenReturn(resultSet3);

    List<String> processDetails = new ArrayList<>();
    processDetails.add(processDetail.getProcessId());
    
    Mockito.when(processDetailsRepository.findByRecordId(Mockito.anyString()))
        .thenReturn(processDetails);
    
    Map<String, Boolean> authMap1 = Map.of("Human Task 12", Boolean.TRUE, "Human Task 1", Boolean.TRUE, "Human Task 13", Boolean.TRUE,
        "Human Task 2", Boolean.TRUE, "Human Task 14", Boolean.FALSE);
    
    Map<String, Boolean> authMap2 = Map.of("Human Task 15", Boolean.FALSE, "Human Task 3", Boolean.TRUE, "Human Task 4", Boolean.FALSE,
        "Human Task 5", Boolean.FALSE, "Human Task 6", Boolean.FALSE);
    
    Map<String, Boolean> authMap3 = Map.of("Human Task 7", Boolean.TRUE, "Human Task 8", Boolean.TRUE, "Human Task 9", Boolean.FALSE,
        "Human Task 10", Boolean.FALSE, "Human Task 11", Boolean.TRUE);
    
    Mockito.when(accessVerifier.verifyBatchHumanTaskAccess(Mockito.anyList()))
      .thenReturn(authMap1)
      .thenReturn(authMap2)
      .thenReturn(authMap3);
    
    WorkflowTaskRequestDetails request1 = 
        WorkflowTaskRequestDetails.builder().offset(0)
          .limit(5).recordId("abc")
          .taskTypes(Arrays.asList(new TaskType[] {TaskType.HUMAN_TASK}))
          .build();
    
    List<Task> tasks1 = workflowTaskService.getWkflTasks(request1);
    Assert.assertEquals("Tasks Return", 5, tasks1.size());
    Assert.assertEquals("Incorrect Offset of first Task", Integer.valueOf(0), tasks1.get(0).getOffset());
    Assert.assertEquals("Incorrect Offset of last Task", Integer.valueOf(6), tasks1.get(4).getOffset());
    
    WASContext.clear();
  }
  
  
  @Test
  public void test_getWkflwTask_page1(){
    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.OWNER_ID))).thenReturn("9130352403806696");
    
    TemplateDetails templateDetail = TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetail = DefinitionDetails.builder().templateDetails(templateDetail).build();
   
    ProcessDetails processDetail = ProcessDetails.builder().processId("pid")
        .definitionDetails(definitionDetail).ownerId(9130352403806697l).build();
    
    ActivityProgressDetails humanTask1 = prepareHumanTaskActivityProgressDetails("Human Task 1", "txn1", "9130352403806696");
    humanTask1.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask2 = prepareHumanTaskActivityProgressDetails("Human Task 2", "txn2", "9130352403806696");
    humanTask2.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask3 = prepareHumanTaskActivityProgressDetails("Human Task 3", "txn3", "9130352403806696");
    humanTask3.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask4 = prepareHumanTaskActivityProgressDetails("Human Task 4", "txn4", "9130354821213726");
    humanTask4.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask5 = prepareHumanTaskActivityProgressDetails("Human Task 5", "txn5", "9130354821213726");
    humanTask5.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask6 = prepareHumanTaskActivityProgressDetails("Human Task 6", "txn6", "9130354821213726");
    humanTask6.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask7 = prepareHumanTaskActivityProgressDetails("Human Task 7", "txn7", "9130352403806696");
    humanTask7.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask8 = prepareHumanTaskActivityProgressDetails("Human Task 8", "txn8", "9130352403806696");
    humanTask8.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask9 = prepareHumanTaskActivityProgressDetails("Human Task 9", "txn9", "9130354821213726");
    humanTask9.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask10 = prepareHumanTaskActivityProgressDetails("Human Task 10", "txn10", "9130354821213726");
    humanTask10.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask11 = prepareHumanTaskActivityProgressDetails("Human Task 11", "txn11", "9130352403806696");
    humanTask11.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask12 = prepareHumanTaskActivityProgressDetails("Human Task 12", "txn12", "9130352403806696");
    humanTask12.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask13 = prepareHumanTaskActivityProgressDetails("Human Task 13", "txn13", "9130352403806696");
    humanTask13.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask14 = prepareHumanTaskActivityProgressDetails("Human Task 14", "txn14", "9130354821213726");
    humanTask14.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask15 = prepareHumanTaskActivityProgressDetails("Human Task 15", "txn15", "9130354821213726");
    humanTask15.setProcessDetails(processDetail);
    
    List<ActivityProgressDetails> resultSet2 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask15, humanTask3, humanTask4, 
            humanTask5, humanTask6});
    
    List<ActivityProgressDetails> resultSet3 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask7, humanTask8, humanTask9, 
            humanTask10, humanTask11});
    
    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsAndTypeIn(Mockito.anyList(), 
        Mockito.anyList(),
        Mockito.any(Pageable.class))).thenReturn(resultSet2)
    .thenReturn(resultSet3).thenReturn(null);

    List<String> processDetails = new ArrayList<>();
    processDetails.add(processDetail.getProcessId());
    
    Mockito.when(processDetailsRepository.findByRecordId(Mockito.anyString()))
        .thenReturn(processDetails);
    
    Map<String, Boolean> authMap2 = Map.of("Human Task 15", Boolean.FALSE, "Human Task 3", Boolean.TRUE, "Human Task 4", Boolean.FALSE,
        "Human Task 5", Boolean.FALSE, "Human Task 6", Boolean.FALSE);
    
    Map<String, Boolean> authMap3 = Map.of("Human Task 7", Boolean.TRUE, "Human Task 8", Boolean.TRUE, "Human Task 9", Boolean.FALSE,
        "Human Task 10", Boolean.FALSE, "Human Task 11", Boolean.TRUE);
    
    Mockito.when(accessVerifier.verifyBatchHumanTaskAccess(Mockito.anyList()))
      .thenReturn(authMap2)
      .thenReturn(authMap3);
    
    WorkflowTaskRequestDetails request2 = 
        WorkflowTaskRequestDetails.builder().offset(7)
          .limit(5).recordId("abc")
          .taskTypes(Arrays.asList(new TaskType[] {TaskType.HUMAN_TASK}))
          .build();
    
    List<Task> tasks2 = workflowTaskService.getWkflTasks(request2);
    Assert.assertEquals("Tasks Return", 3, tasks2.size());
    Assert.assertEquals("Incorrect Offset of first Task", Integer.valueOf(10), tasks2.get(0).getOffset());
    Assert.assertEquals("Incorrect Offset of last Task", Integer.valueOf(14), tasks2.get(2).getOffset());
    
    WASContext.clear();
  }
  
  
  
  @Test
  public void test_getWkflwTask_ProcessOwner(){
    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.OWNER_ID))).thenReturn("9130352403806697");
    
    TemplateDetails templateDetail = TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetail = DefinitionDetails.builder().templateDetails(templateDetail).build();
   
    ProcessDetails processDetail = ProcessDetails.builder().processId("pid")
        .definitionDetails(definitionDetail)
        .ownerId(9130352403806697l).build();
    
    ActivityProgressDetails humanTask1 = prepareHumanTaskActivityProgressDetails("Human Task 1", "txn1", "9130352403806696");
    humanTask1.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask2 = prepareHumanTaskActivityProgressDetails("Human Task 2", "txn2", "9130352403806696");
    humanTask2.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask3 = prepareHumanTaskActivityProgressDetails("Human Task 3", "txn3", "9130352403806696");
    humanTask3.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask4 = prepareHumanTaskActivityProgressDetails("Human Task 4", "txn4", "9130352403806696");
    humanTask4.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask5 = prepareHumanTaskActivityProgressDetails("Human Task 5", "txn5", "9130352403806696");
    humanTask5.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask6 = prepareHumanTaskActivityProgressDetails("Human Task 6", "txn6", "9130352403806696");
    humanTask6.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask7 = prepareHumanTaskActivityProgressDetails("Human Task 7", "txn7", "9130352403806696");
    humanTask7.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask8 = prepareHumanTaskActivityProgressDetails("Human Task 8", "txn8", "9130352403806696");
    humanTask8.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask9 = prepareHumanTaskActivityProgressDetails("Human Task 9", "txn9", "9130352403806696");
    humanTask9.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask10 = prepareHumanTaskActivityProgressDetails("Human Task 10", "txn10", "9130352403806696");
    humanTask10.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask11 = prepareHumanTaskActivityProgressDetails("Human Task 11", "txn11", "9130352403806696");
    humanTask11.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask12 = prepareHumanTaskActivityProgressDetails("Human Task 12", "txn12", "9130352403806696");
    humanTask12.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask13 = prepareHumanTaskActivityProgressDetails("Human Task 13", "txn13", "9130352403806696");
    humanTask13.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask14 = prepareHumanTaskActivityProgressDetails("Human Task 14", "txn14", "9130352403806696");
    humanTask14.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask15 = prepareHumanTaskActivityProgressDetails("Human Task 15", "txn15", "9130352403806696");
    humanTask15.setProcessDetails(processDetail);
    
    List<ActivityProgressDetails> resultSet2 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask15, humanTask3, humanTask4, 
            humanTask5, humanTask6});
    
    List<ActivityProgressDetails> resultSet3 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask7, humanTask8, humanTask9, 
            humanTask10, humanTask11});
    
    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsAndTypeIn(Mockito.anyList(), 
        Mockito.anyList(),
        Mockito.any(Pageable.class))).thenReturn(resultSet2)
    .thenReturn(resultSet3).thenReturn(null);

    List<String> processDetails = new ArrayList<>();
    processDetails.add(processDetail.getProcessId());
    
    Mockito.when(processDetailsRepository.findByRecordId(Mockito.anyString()))
        .thenReturn(processDetails);
    
    Mockito.when(accessVerifier.verifyBatchHumanTaskAccess(Mockito.anyList()))
      .thenReturn(Map.of());
    
    WorkflowTaskRequestDetails request2 = 
        WorkflowTaskRequestDetails.builder().offset(0)
          .limit(5).recordId("abc").taskTypes(Arrays.asList(new TaskType[] {TaskType.HUMAN_TASK}))
          .build();
    
    List<Task> tasks2 = workflowTaskService.getWkflTasks(request2);
    Assert.assertEquals("Tasks Return", 5, tasks2.size());
    Assert.assertEquals("Incorrect Offset of last Task", Integer.valueOf(4), tasks2.get(4).getOffset());
    
    WASContext.clear();
  }

  @Test
  public void test_getWkflwTask_workflowName(){
    Mockito.when(contextHandler.get(Mockito.eq(WASContextEnums.OWNER_ID))).thenReturn("9130352403806696");
    
    TemplateDetails templateDetail = TemplateDetails.builder().templateName("templateName").build();
    DefinitionDetails definitionDetail = DefinitionDetails.builder().templateDetails(templateDetail).build();
    
    ProcessDetails processDetail = ProcessDetails.builder().processId("pid")
        .ownerId(9130352403806697l).definitionDetails(definitionDetail).build();
    
    ActivityProgressDetails humanTask1 = prepareHumanTaskActivityProgressDetails("Human Task 1", "txn1", "9130352403806696");
    humanTask1.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask2 = prepareHumanTaskActivityProgressDetails("Human Task 2", "txn2", "9130352403806696");
    humanTask2.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask3 = prepareHumanTaskActivityProgressDetails("Human Task 3", "txn3", "9130352403806696");
    humanTask3.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask4 = prepareHumanTaskActivityProgressDetails("Human Task 4", "txn4", "9130354821213726");
    humanTask4.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask5 = prepareHumanTaskActivityProgressDetails("Human Task 5", "txn5", "9130354821213726");
    humanTask5.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask6 = prepareHumanTaskActivityProgressDetails("Human Task 6", "txn6", "9130354821213726");
    humanTask6.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask7 = prepareHumanTaskActivityProgressDetails("Human Task 7", "txn7", "9130352403806696");
    humanTask7.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask8 = prepareHumanTaskActivityProgressDetails("Human Task 8", "txn8", "9130352403806696");
    humanTask8.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask9 = prepareHumanTaskActivityProgressDetails("Human Task 9", "txn9", "9130354821213726");
    humanTask9.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask10 = prepareHumanTaskActivityProgressDetails("Human Task 10", "txn10", "9130354821213726");
    humanTask10.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask11 = prepareHumanTaskActivityProgressDetails("Human Task 11", "txn11", "9130352403806696");
    humanTask11.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask12 = prepareHumanTaskActivityProgressDetails("Human Task 12", "txn12", "9130352403806696");
    humanTask12.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask13 = prepareHumanTaskActivityProgressDetails("Human Task 13", "txn13", "9130352403806696");
    humanTask13.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask14 = prepareHumanTaskActivityProgressDetails("Human Task 14", "txn14", "9130354821213726");
    humanTask14.setProcessDetails(processDetail);
    ActivityProgressDetails humanTask15 = prepareHumanTaskActivityProgressDetails("Human Task 15", "txn15", "9130354821213726");
    humanTask15.setProcessDetails(processDetail);
    
    List<ActivityProgressDetails> resultSet1 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask12, humanTask1, humanTask13, 
            humanTask2, humanTask14});
    
    List<ActivityProgressDetails> resultSet2 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask15, humanTask3, humanTask4, 
            humanTask5, humanTask6});
    
    List<ActivityProgressDetails> resultSet3 = 
        Arrays.asList(new ActivityProgressDetails[] {humanTask7, humanTask8, humanTask9, 
            humanTask10, humanTask11});
    
    Mockito.when(activityProgressDetailsRepository.findByProcessDetailsAndTypeIn(Mockito.anyList(), 
        Mockito.anyList(),
        Mockito.any(Pageable.class))).thenReturn(resultSet1)
    .thenReturn(resultSet2).thenReturn(resultSet3);

    List<String> processDetails = new ArrayList<>();
    processDetails.add(processDetail.getProcessId());
    
    Mockito.when(processDetailsRepository.findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(Mockito.anyString(), Mockito.anyList()))
        .thenReturn(processDetails);
    
    Map<String, Boolean> authMap1 = Map.of("Human Task 12", Boolean.TRUE, "Human Task 1", Boolean.TRUE, "Human Task 13", Boolean.TRUE,
        "Human Task 2", Boolean.TRUE, "Human Task 14", Boolean.FALSE);
    
    Map<String, Boolean> authMap2 = Map.of("Human Task 15", Boolean.FALSE, "Human Task 3", Boolean.TRUE, "Human Task 4", Boolean.FALSE,
        "Human Task 5", Boolean.FALSE, "Human Task 6", Boolean.FALSE);
    
    Map<String, Boolean> authMap3 = Map.of("Human Task 7", Boolean.TRUE, "Human Task 8", Boolean.TRUE, "Human Task 9", Boolean.FALSE,
        "Human Task 10", Boolean.FALSE, "Human Task 11", Boolean.TRUE);
    
    Mockito.when(accessVerifier.verifyBatchHumanTaskAccess(Mockito.anyList()))
      .thenReturn(authMap1)
      .thenReturn(authMap2)
      .thenReturn(authMap3);
    
    WorkflowTaskRequestDetails request1 = 
        WorkflowTaskRequestDetails.builder().offset(0)
          .limit(5).recordId("abc").taskTypes(Arrays.asList(new TaskType[] {TaskType.HUMAN_TASK}))
          .workflowNames(Arrays.asList(new String[] {"wkfl1", "wkfl2"})).build();
    
    List<Task> tasks1 = workflowTaskService.getWkflTasks(request1);
    Assert.assertEquals("Tasks Return", 5, tasks1.size());
    Assert.assertEquals("Incorrect Offset of last Task", Integer.valueOf(6), tasks1.get(4).getOffset());
    
    WASContext.clear();
  }
  
  
  @Test(expected=WorkflowGeneralException.class)
  public void test_getWkflwTask_workflowName_exception(){
    
    Mockito.when(processDetailsRepository.findByRecordIdAndDefinitionDetails_TemplateDetails_TemplateName(Mockito.anyString(), Mockito.anyList()))
    .thenReturn(Collections.emptyList());
    
    WorkflowTaskRequestDetails request1 = 
        WorkflowTaskRequestDetails.builder().offset(0)
          .limit(5).recordId("abc").taskTypes(Arrays.asList(new TaskType[] {TaskType.HUMAN_TASK}))
          .workflowNames(Arrays.asList(new String[] {"wkfl1", "wkfl2"})).build();
   
    workflowTaskService.getWkflTasks(request1);
    
  }
  
  private ActivityProgressDetails prepareHumanTaskActivityProgressDetails(String taskName,
      String txnId, String ownerId) {
    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .id(taskName)
        .activityDefinitionDetail(prepareActivityDetailHumanTask())
        .name(taskName)
        .txnDetails(TransactionDetails.builder().txnId(txnId).build())
        .status("created")
        .startTime(Timestamp.from(Instant.now()))
        .endTime(Timestamp.from(Instant.now()))
        .updatedTime(Timestamp.from(Instant.now()))
        .processDetails(ProcessDetails.builder().processId("wId").build())
        .attributes("{\n"
            + "    \"runtimeAttributes\": {\n"
            + "        \"ownerId\": \""+ownerId+"\",\n"
            + "        \"taskName\": \"Task-abcd-12377-3353\",\n"
            + "        \"journalNo\": \"j#149\",\n"
            + "        \"assigneeId\": \"Assignee149\",\n"
            + "        \"customerId\": \"User149\"\n"
            + "    }\n"
            + "}")
        .build();
    return activityProgressDetails;
  }
  
  
  private ActivityDetail prepareActivityDetailHumanTask() {
    ActivityDetail activityDetail = ActivityDetail.builder()
        .id(1L)
        .type(TaskType.HUMAN_TASK)
        .activityType("humanTask")
        .activityId("act")
        .activityName("Activity 2")
        .parentId(0)
        .attributes("{\"modelAttributes\":{\"type\":\"HUMAN_TASK\",\"serviceName\":\"Workflow\",\"taskDetails\":\"{\\\"fatal\\\":true}\",\"activityName\":\"Email\",\"handlerDetails\":\"{\\\"taskHandler\\\":\\\"was\\\",\\\"actionName\\\":\\\"workflowCustomTaskHandler\\\"}\",\"notificationName\":\"customWorkflow\",\"notificationDataType\":\"customWorkflow\"},\"runtimeAttributes\":{\"notificationData\":\"${notificationDataEmail}\",\"notificationMetaData\":\"${notificationMetaDataEmail}\"}}")
        .build();
    return activityDetail;
  }
  
}
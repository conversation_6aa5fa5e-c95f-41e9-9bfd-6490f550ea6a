package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowTemplateConstant;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DaysOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import java.text.MessageFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Contains the whole Logic to handle Migrations or Rollback
 */
@Component
@AllArgsConstructor
public class MigrationServiceHelper {

  private final DefinitionServiceImpl definitionService;
  private final MultiStepDefinitionTransformer multiStepDefinitionTransformer;
  private final MultiStepConfig multiStepConfig;

  @Metric(name = MetricName.WAS_MIGRATE_DEFINITION, type = Type.APPLICATION_METRIC)
  public Definition migrateDefinition(DefinitionDetails definitionDetails,
      Authorization authorization, String updatedTemplateId) {
    WorkflowLogger.logInfo("step=DefinitionMigration Migrating definition=%s oldTemplateId=%s newTemplateId=%s",
        definitionDetails.getDefinitionId(), definitionDetails.getTemplateDetails().getId(), updatedTemplateId);
    Definition definition = buildDefinitionEntity(definitionDetails,
        updatedTemplateId);
    Definition updatedDefinition = definitionService.updateDefinition(definition, authorization);
    WorkflowLogger.logInfo("step=DefinitionMigration Migration complete definition=%s newTemplateId=%s",
        updatedDefinition.getId(), updatedDefinition.getTemplate().getId());
    return updatedDefinition;
  }

  /**
   * @param definitionDetails
   * @param templateId        Update the template details to the new template Id which will migrate
   *                          the current definition to new Definition
   */
  private Definition buildDefinitionEntity(DefinitionDetails definitionDetails, String templateId) {

    Definition definition = new Definition();
    definition.setId(GlobalId.builder().setRealmId(Long.toString(definitionDetails.getOwnerId()))
        .setLocalId(definitionDetails.getDefinitionId()).build());
    Definition readDefinition = readDefinition(definition);

    //QBOES-14393 Appconnect is not able to trigger definition if conditionalExpression contains "ON 0"
    transformConditionalExpressionForMigration(readDefinition);

    String templateCategory = definitionDetails.getTemplateDetails().getTemplateCategory();
    String templateName = definitionDetails.getTemplateDetails().getTemplateName();

//  In Case of pre-canned there's no need to filter on actions.
    if (TemplateCategory.CUSTOM.name().equals(templateCategory)){
      filterActions(definitionDetails, readDefinition);
    }

    //checking if the latest template for the definition's template is of multi condition type
    if(Objects.nonNull(multiStepConfig.getWorkflowTemplates()) &&
        Objects.nonNull(multiStepConfig.getWorkflowTemplates().get(templateName))) {
      multiStepDefinitionTransformer.transformPayload(readDefinition, definitionDetails,
          templateId);
    }

    Template template = new Template();
    if (Objects.nonNull(readDefinition.getTemplate()) && Objects.nonNull(readDefinition.getTemplate().getName())) {
      template.setName(readDefinition.getTemplate().getName());
    }
    template.setId(GlobalId.builder().setRealmId(Long.toString(definitionDetails.getOwnerId()))
        .setLocalId(templateId).build());
    readDefinition.setTemplate(template);

    WorkflowLogger.logInfo("step=DefinitionMigration Build definition entity complete definitionId=%s updatedTemplateId=%s templateCategory=%s",
        readDefinition.getId(), readDefinition.getTemplate().getId(), templateCategory);

    return readDefinition;
  }


  /**
   *
   * @param definitionDetails
   * @param readDefinition
   * Filter Other actions and only send the needed actions in case of custom workflows.
   * Also, In Custom Workflows trigger is passed as null, which breaks the update defn. Adding the check here.
   * Remove trigger while updating defn when trigger is null or empty.
   */
  private void filterActions(DefinitionDetails definitionDetails, Definition readDefinition) {

    String actionName = CustomWorkflowType.templateNameActionKeyMapping()
        .get(definitionDetails.getTemplateDetails().getTemplateName());
    readDefinition.getWorkflowSteps().forEach(workflowStep -> {
      if (Objects.isNull(workflowStep.getTrigger()) || workflowStep.getTrigger().isEmpty()) {
        workflowStep.remove(WorkflowTemplateConstant.TRIGGER.getName());
      }
      List<ActionMapper> actions = workflowStep.getActions().stream()
          .filter(action -> actionName.equals(action.getActionKey()))
          .collect(Collectors.toList());
      workflowStep.setActions(actions);
    });
  }

  private Definition readDefinition(Definition definition) {
    return definitionService.getDefinitionReadOne(definition.getId().getLocalId(),
        definition.getId(), false);
  }

  /**
   *  (QBOES-14393) At the time of read definition, BPMN gets transformed if the conditional expression has a condition with '0' in it
   *  e.g. 'txnCreateDays':'AF 0' gets transformed to 'txnCreateDays':'ON 0'
   *  Due to this transformation, AppConnect is unable to trigger the definition
   *  This method transforms "ON 0" to "AF 0" if its present
   */
  private void transformConditionalExpressionForMigration(Definition readDefinition){
    readDefinition.getWorkflowSteps().stream().forEach(workflowStep -> {
      workflowStep.getWorkflowStepCondition().getRuleLines().stream().forEach(ruleLine -> {
        ruleLine.getRules().stream().forEach(rule -> {
          String[] conditionalExpression = rule.getConditionalExpression().split(WorkflowConstants.SPACE);
          if(conditionalExpression[WorkflowConstants.OPERATOR_INDEX].equalsIgnoreCase(DaysOperator.ON.getSymbol())
              && conditionalExpression[WorkflowConstants.VALUE_INDEX].equalsIgnoreCase(WorkflowConstants.ZERO)){
            rule.setConditionalExpression(MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, DaysOperator.AFTER.getSymbol(), conditionalExpression[WorkflowConstants.VALUE_INDEX]));
          }
        });
      });
    });
  }

  /**
   * This method gets the latest single definition template id
   *
   * @param definitionDetails
   * @param templateDetailsRepository
   * @param templateName
   * @return templateId : template id of latest single template
   */
  public static String getSingleTemplateId(DefinitionDetails definitionDetails, TemplateDetailsRepository templateDetailsRepository, String templateName){

    TemplateDetails templateDetails = templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(templateName).get();
    WorkflowVerfiy.verify((Objects.isNull(templateDetails) ||
                    !templateDetails.getDefinitionType().equals(DefinitionType.SINGLE)),
            WorkflowError.DEFINITION_MIGRATION_FAILED);
    //Migrate API supports rollback to a lower template version as well
    //This check validates that we are migrating only to a higher version
    WorkflowVerfiy.verify((templateDetails.getVersion() <= definitionDetails.getTemplateDetails().getVersion())
            , WorkflowError.DEFINITION_MIGRATION_FAILED);

    return templateDetails.getId();
  }


}

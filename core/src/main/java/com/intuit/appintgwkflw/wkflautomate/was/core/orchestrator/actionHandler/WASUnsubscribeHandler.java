package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeleteAuthDetailsTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UnsubscribeAppConnectTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for Unsubscribing to AppConnect and then Deleting the auth details in
 * WAS.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WASUnsubscribeHandler extends WorkflowTaskHandler {

  private AppConnectService appConnectService;
  private AuthDetailsService authDetailsService;
  private ProcessDetailsRepository processDetailsRepository;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private AuthDetailsRepository authDetailsRepository;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_UNSUBSCRIBE;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    final State state = new State();

    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    state.addValue(AsyncTaskConstants.IS_OFFLINE_KEY, true);
    final RxExecutionChain chain = new RxExecutionChain(state);
    chain
        .next(new UnsubscribeAppConnectTask(appConnectService, authDetailsService))
        .next(
            new DeleteAuthDetailsTasks(
                authDetailsRepository, definitionDetailsRepository, processDetailsRepository))
        .execute();
    logInfo(
        "Unsubscription of AppConnect workflow and Auth Details completed successfully for ownerId=%s",
        realmId);

    return ImmutableMap.of(
        new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString(),
        Boolean.TRUE);
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.APPCONNECT_UNSUBSCRIBE_WORKFLOW, Type.APP_CONNECT_METRIC, exception);
  }

  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_UNSUBSCRIBE));
  }
}

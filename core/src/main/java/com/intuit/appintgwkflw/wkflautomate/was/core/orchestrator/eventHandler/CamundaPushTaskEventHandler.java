
package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_ACTION_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_TASK_HANDLER_DETAILS;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UNSUPPORTED_HANDLER_NAME;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType.EXTERNALTASK;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors.WorkerExecutorHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.camunda.bpm.engine.rest.dto.VariableValueDto;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.springframework.stereotype.Component;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.WorkflowTaskHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.taskcompletionhandler.TaskCompletionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskCompleted;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.ExtendExternalTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest.WorkerActionRequestBuilder;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR> mmishra8
 * This class is an implementation to handle external task published by comunda.
 * It will publish a message on respctive downstream topic i.e in case slb it will publish on assigned topic
 */
@Component
@AllArgsConstructor
public class CamundaPushTaskEventHandler implements WorkflowEventHandler<ExternalTaskEvent> {

	private final CamundaRunTimeServiceRest camundaRest;
	private final ExternalTaskConfiguration externalTaskConfiguration;
	private CustomWorkflowConfig customWorkflowConfig;
	private ProcessDetailsRepoService processDetailsRepoService;

	@Override
	public ExternalTaskEvent transform(String event) {
		ExternalTaskEvent externalTaskEvent = ObjectConverter.fromJson(event, ExternalTaskEvent.class);
		WorkflowVerfiy.verify(externalTaskEvent == null, WorkflowError.INCORRECT_EVENT_PAYLOAD,
				"Unable to parse Or missing mandatory fields in event. step=transform payload=%s", event);

		return externalTaskEvent;
	}

	/**
	 * this method translate and validate the kafka event
	 * @param event
	 * @param headers
	 * @return externalTaskEvent
	 */

	@Override
	public ExternalTaskEvent transformAndValidate(String event, Map<String, String> headers) {
		try {
			WorkflowVerfiy.verify(!headers.containsKey(EventHeaderConstants.ENTITY_ID),
					WorkflowError.MISSING_EVENT_HEADERS,
					"Unable to parse or missing Mandatory field entity_id in event header. step=transformAndValidate headers=%s", headers);
			return transform(event);
		} catch (Exception e) {
			EventingLoggerUtil.logError("Error in transforming the event step=transformAndValidate exception=true taskId=%s error=%s",
					this.getClass().getSimpleName(), headers.get(EventHeaderConstants.ENTITY_ID),
					ExceptionUtils.getStackTrace(e));
			throw e;
		}
	}

	/**
	 * Executing the events
	 * @param externalTaskEvent
	 * @param headers
	 */

	@Override
	public void execute(ExternalTaskEvent externalTaskEvent, Map<String, String> headers) {

		try {
			EventingLoggerUtil.logInfo("started the execution of events step=execute processInstanceId=%s taskId=%s",
					this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),
					headers.get(EventHeaderConstants.ENTITY_ID));

			Map<String, Worker> workerMap = Optional.ofNullable(externalTaskConfiguration)
					.map(workers -> workers.getWorkers()).filter(MapUtils::isNotEmpty).orElse(Collections.emptyMap());

			Map<String, List<Worker>> workerTopicMap = workerMap.entrySet().stream().collect(Collectors.groupingBy(
					e -> e.getValue().getTopicName(), Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
			WorkflowVerfiy.verify(
					MapUtils.isEmpty(workerTopicMap)
							|| CollectionUtils.isEmpty(workerTopicMap.get(externalTaskEvent.getTopicName())),
					WorkflowError.MISSING_WORKER_ID, "Missing details for given topic name and workerId");

			Pair<TaskHandlerName, WorkerActionRequest> workerActionRequestPair = fetchRequest(externalTaskEvent);
			TaskHandlerName handlerName = workerActionRequestPair.getLeft();
			Map<String, Object> response = WorkflowTaskHandlers.getHandler(handlerName)
					.execute(workerActionRequestPair.getRight());

			if (response.containsKey(WorkFlowVariables.PENDING_EXTEND_LOCK.getName())) {
				EventingLoggerUtil.logInfo(
						"Calling Extend lock for processInstanceId=%s taskId=%s step=extendLock",
						this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),

						headers.get(EventHeaderConstants.ENTITY_ID));
				ExtendExternalTask extendRequest = ExtendExternalTask.builder().workerId(externalTaskEvent.getWorkerId())
						.newDuration(workerTopicMap.get(externalTaskEvent.getTopicName()).stream().findFirst()
						.map(lock -> lock.getExtendedLockDuration()).orElse(1L))
						.build();
				camundaRest.extendLock(extendRequest, externalTaskEvent.getId());

			} else {
				EventingLoggerUtil.logInfo(
						"Calling complete tasks for processInstanceId=%s; taskId=%s step=complete",
						this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),
						headers.get(EventHeaderConstants.ENTITY_ID));
				/**
				 * for making complete call the entity id should be  in the format of ->  entityId:workerId
				 *
				 */
				String customEntityId = Optional.ofNullable(headers.get(EventHeaderConstants.ENTITY_ID))
						.map( entityId -> entityId.concat(WorkflowConstants.COLON).concat(externalTaskEvent.getWorkerId()))
						.orElse(headers.get(EventHeaderConstants.ENTITY_ID));
				headers.put(EventHeaderConstants.ENTITY_ID, customEntityId);
				/**
				 * for completing the task camunda need the variables should be the type of
				 * VariableValueDto not in json format for i.e
				 * response which return by the handler {"decisionResult": "true" } , we can't pass directly
				 * else we get exception , the type should be
				 * { "decisionResult": {
				 *       	"value": true,
				 *        	"type": "Boolean"
				 * 		}
				 * }
				 */
				Map<String, VariableValueDto> variables = VariableValueDto.fromMap(new VariableMapImpl(response));
				TaskCompletionHandlers.getHandler(EXTERNALTASK)
						.completeTask(ExternalTaskCompleted.builder()
								.variables(ObjectConverter.convertObject(variables, new TypeReference<Map<String, Object>>() {}))
								.localVariables(Collections.emptyMap())
								.errorDetails(externalTaskEvent.getErrorDetails())
								.errorMessage(externalTaskEvent.getErrorMessage())
								.status(ExternalTaskStatus.SUCCESS.getStatus())
								.build(), headers);
			}
			EventingLoggerUtil.logInfo(
					"Event processed successfully processInstanceId=%s; taskId=%s",
					this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),
					headers.get(EventHeaderConstants.ENTITY_ID));
		} catch (WorkflowEventException e) {
			// in case of event exception we are going to reset the lock
			EventingLoggerUtil.logError("Error in publishing/handling event processInstanceId=%s taskId=%s error=%s",
					this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),
					externalTaskEvent.getId(), ExceptionUtils.getStackTrace(e));
			camundaRest.extendLock(ExtendExternalTask.builder().workerId(externalTaskEvent.getWorkerId())
					.newDuration(1L)
					.build(),externalTaskEvent.getId());
		} catch (Exception ex){
			EventingLoggerUtil.logError("Error in processing the event processInstanceId=%s taskId=%s error=%s",
					this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(),
					externalTaskEvent.getId(), ExceptionUtils.getStackTrace(ex));
		}
	}


	/**
	 * Extracting handler details from the task
	 * @param inputVariablesMap
	 * @param externalTaskEvent
	 * @return
	 */
	public HandlerDetails extractHandlerDetails(Map<String, String> inputVariablesMap,
			ExternalTaskEvent externalTaskEvent) {
		EventingLoggerUtil.logInfo(
				"Extracting handler details from event for processInstanceId=%s; taskId=%s step=extractHandlerDetails",
				this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(), externalTaskEvent.getId());
		/** get handler details for the task */
		HandlerDetails handlerDetails = getHandlerDetails(externalTaskEvent, inputVariablesMap);
		WorkflowVerfiy.verify(isEmpty(handlerDetails.getTaskHandler()), INVALID_TASK_HANDLER_DETAILS);
		WorkflowVerfiy.verify(isEmpty(handlerDetails.getActionName()), INVALID_ACTION_DETAILS);
		return handlerDetails;
	}

	/**
	 *  Get handlers details from external task event
	 * @param externalTaskEvent
	 * @param inputVariablesMap
	 * @return
	 */

	private HandlerDetails getHandlerDetails(ExternalTaskEvent externalTaskEvent,
			Map<String, String> inputVariablesMap) {
		return WorkerExecutorHelper.fetchHandlerDetails(externalTaskEvent, inputVariablesMap, customWorkflowConfig,
				processDetailsRepoService);
	}

	protected Pair<TaskHandlerName, WorkerActionRequest> fetchRequest(ExternalTaskEvent externalTaskEvent) {

		EventingLoggerUtil.logInfo(
				"Preparing request for submitting the task details from event for processInstanceId=%s; taskId=%s step=fetchRequest",
				this.getClass().getSimpleName(), externalTaskEvent.getProcessInstanceId(), externalTaskEvent.getId());

		Map<String, String> inputVariablesMap = WorkerExecutorHelper.createInputVariablesMap(externalTaskEvent.getVariables());
		HandlerDetails handlerDetails = extractHandlerDetails(inputVariablesMap, externalTaskEvent);
		TaskHandlerName handlerName = TaskHandlerName.getActionFromName(WorkerExecutorHelper.prepareHandlerName(handlerDetails));
		WorkerActionRequestBuilder workerActionRequestBuilder = WorkerActionRequest.builder();
		/** verify if handler name exists or not */
		WorkflowVerfiy.verify(!WorkflowTaskHandlers.contains(handlerName), UNSUPPORTED_HANDLER_NAME);

		workerActionRequestBuilder.handlerId(handlerDetails.getHandlerId());
		/**
		 * Add handlerScope in the workerActionRequest (expected in case of test else
		 * null)
		 */
		workerActionRequestBuilder.handlerScope(handlerDetails.getHandlerScope());

		/** prepare WorkerAction Request */
		WorkerActionRequest workerActionRequest = workerActionRequestBuilder
				.activityId(externalTaskEvent.getActivityId())
				.processDefinitionId(externalTaskEvent.getProcessDefinitionId())
				.processInstanceId(externalTaskEvent.getProcessInstanceId())
				.inputVariables(inputVariablesMap)
				.taskId(externalTaskEvent.getId())
				.workerId(externalTaskEvent.getWorkerId())
				.variableMap(new VariableMapImpl(externalTaskEvent.getVariableMap()))
				.extensionProperties(externalTaskEvent.getExtensionProperties())
				.handlerDetails(handlerDetails)
				.definitionKey(externalTaskEvent.getProcessDefinitionKey())
				.retries(externalTaskEvent.getRetries())
				.businessKey(externalTaskEvent.getBusinessKey())
				.build();

		return Pair.of(handlerName, workerActionRequest);
	}

	@Override
	public EventEntityType getName() {
		return EventEntityType.EXTERNAL_TASK_PUSH;
	}

	@Override
	public void handleFailure(String event, Map<String, String> headers, Exception e) {
		TaskCompletionHandlers.getHandler(EXTERNALTASK).invokeFailure(null, headers);
	}

}

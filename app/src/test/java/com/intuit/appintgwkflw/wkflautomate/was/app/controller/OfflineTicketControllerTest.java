package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import static org.mockito.Mockito.doReturn;
import com.intuit.appintgwkflw.wkflautomate.was.core.ticket.OfflineTicketService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@Import(OfflineTicketController.class)
public class OfflineTicketControllerTest {

  @Autowired
  private OfflineTicketController controller;

  @MockBean
  private OfflineTicketService offlineTicketService;

  @Test
  public void testSuccess() {
    final WorkflowGenericResponse expectedResponse =
        WorkflowGenericResponse.builder().status(ResponseStatus.SUCCESS).build();
    doReturn(expectedResponse).when(offlineTicketService).updateOfflineTicket();

    final WorkflowGenericResponse getProcessDetailsResponse = controller.updateOfflineTicket();
    Assert.assertEquals(expectedResponse, getProcessDetailsResponse);
  }
}
package com.intuit.appintgwkflw.wkflautomate.was.localisation.config;


public class LocalisationConfig {
  /*
  ex:
      folder structure is src/main/resources/templates/sla/CustomWorkflowTemplate/nls/_master/{{fileName}}
      fileName : workflowContent.properties
      Full Path: src/main/resources/templates/sla/CustomWorkflowTemplate/nls/_master/workflowContent.properties
      basePath :
   */
  private String basePath; // "templates.sla."
  private String folderName; // CustomWorkflowTemplate
  private String fileName; // workflowContent name of the .properties file

  public String getBasePath() {
    return this.basePath;
  }

  public void setBasePath(final String basePath) {
    this.basePath = basePath;
  }

  public String getFolderName() {
    return this.folderName;
  }

  public void setFolderName(final String folderName) {
    this.folderName = folderName;
  }

  public String getFileName() {
    return this.fileName;
  }

  public void setFileName(final String fileName) {
    this.fileName = fileName;
  }

  public LocalisationConfig(final String basePath, final String folderName, final String fileName) {
    this.basePath = basePath;
    this.folderName = folderName;
    this.fileName = fileName;
  }

  public LocalisationConfig() {}
}

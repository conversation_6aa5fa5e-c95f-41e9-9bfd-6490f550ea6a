apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: was-event-in-rollout
spec:
  template:
    spec:
      initContainers:
      - name: ipf-config-loader
        image: docker.intuit.com/fdp/enrichment/config-loader:latest
        args:
          - -config
        env:
          - name: "CONFIG_LOADER_ENABLED"
            value: "true"
          - name: APP_ID
            value: "Intuit.appintgwkflw.wkflautomate.waseventin"
          - name: APP_SECRET
            value: "{secret}idps:/messageMesh/appSecret"
          - name: CONFIG_FILE_NAMES
            value: "core-config.yml, pipeline-definition.yaml"
          - name: CONFIG_OUTPUT_PATH
            value: "/app/config/"
          - name: CONFIG_SOURCE
            value: "-config"
          - name: IDPS_ENDPOINT
            value: "vkm-e2e.ps.idps.a.intuit.com"
          - name: MAX_RETRY_COUNT
            value: "3"
          - name: REGISTRY_API_KEY
            value: "{secret}idps:/ipf/regSvcApiKey"
          - name: REGISTRY_ENDPOINT
            value: "https://financialdataschema-aws-e2e.api.intuit.com/"
          - name: RETRY_BACKOFF_MS
            value: "500"
          - name: RUNTIME_ID
            value: "was-event-in-runtime"
          - name: IDPS_FORCE_GENERIC_POLICIES
            value: "true"
        volumeMounts:
          - mountPath: /app/config
            name: config
      volumes:
        - name: config
          emptyDir: {}

#!/bin/bash
export WORKDIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
echo "Project folder: $WORKDIR"
cd $WORKDIR
mkdir -p database;

if [ $# -eq 0 ] || [ "$(echo $1 | tr '[:upper:]' '[:lower:]')" == "podman" ]
then
	echo "podman"
	podman machine stop
	podman machine init
	podman machine start
	podman-compose up -d
else
	echo "docker"
	docker-compose up -d
fi
export PGPASSWORD='Intuit01';
psql -U sas -d camunda -h localhost -c "create user camundaapp with password 'Intuit01' valid until 'infinity'; alter user camundaapp with createdb; alter user camundaapp with createrole;"

# creating data capture role
psql -U sas -d camunda -h localhost -c "CREATE ROLE data_capture_role;"
psql -U sas -d camunda -h localhost -c "CREATE ROLE rds_replication;"
psql -U sas -d camunda -h localhost -c "GRANT rds_replication TO data_capture_role;"
psql -U sas -d camunda -h localhost -c "GRANT CONNECT ON DATABASE camunda TO data_capture_role;"
psql -U sas -d camunda -h localhost -c "CREATE SCHEMA IF NOT EXISTS was;"
psql -U sas -d camunda -h localhost -c "GRANT USAGE ON SCHEMA was TO data_capture_role;"

package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum Stores the configuration which is returned to the front end for Date related parameters.
 */
@RequiredArgsConstructor
@Getter
public enum DateOperator {
  BEFORE("LT", "Before"),
  ON_OR_BEFORE("LTE", "On Or Before"),
  AFTER("GT", "After"),
  ON_OR_AFTER("GTE", "On Or After"),
  EQUALS("EQ", "Equals");

  private final String symbol;
  private final String description;

  public static Map<String, String> possibleOperatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (DateOperator s : values()) {
      possibleOperatorValueMap.put(s.symbol, s.description);
    }
    return possibleOperatorValueMap;
  }
}

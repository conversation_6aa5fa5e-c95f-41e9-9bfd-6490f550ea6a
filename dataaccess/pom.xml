<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<!-- Created by Intuit JSK V4 Initialzr 0.5.1.BUILD on 2019-09-06 -->
	<!-- https://jira.intuit.com/projects/ISP/issues/ -->

	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
		<artifactId>workflow-automation-service-aggregator</artifactId>
		<version>1.1.20</version>
	</parent>

	<artifactId>was-dataaccess</artifactId>
	<packaging>jar</packaging>
	<!-- name>demo</name -->
	<!-- description>Demo project for JSK V4 Spring Boot</description -->

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-entity</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-common</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<artifactId>tomcat-embed-core</artifactId>
					<groupId>org.apache.tomcat.embed</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>tomcat-embed-core</artifactId>
					<groupId>org.apache.tomcat.embed</groupId>
				</exclusion>
			</exclusions>
		</dependency>
	<dependency>
		<groupId>org.apache.tomcat.embed</groupId>
		<artifactId>tomcat-embed-core</artifactId>
		<version>9.0.99</version>
	</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.data</groupId>
					<artifactId>spring-data-jpa</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- Using version 2.2.12.RELEASE instead of the latest one that comes with spring-boot-starter-data-jpa
         due to change in the return format and thus backward incompatibility of the latest version -->
		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
			<version>2.2.12.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.intuit.platform.jsk.spring</groupId>
			<artifactId>jsk-config-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.intuit.platform.jsk.spring</groupId>
			<artifactId>jsk-spring-config-idps-client</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.liquibase</groupId>
			<artifactId>liquibase-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.vladmihalcea</groupId>
			<artifactId>hibernate-types-55</artifactId>
		</dependency>

		<dependency>
			<groupId>com.intuit.cognition.datamodeling.codegen.prod</groupId>
			<artifactId>data-modeling-codegen-java</artifactId>
		</dependency>

		<dependency>
			<groupId>com.intuit.app-foundations.guaranteed-consumer-sdk</groupId>
			<artifactId>event-handler</artifactId>
			<version>${gc-event-handler.version}</version>
		</dependency>

	</dependencies>

</project>

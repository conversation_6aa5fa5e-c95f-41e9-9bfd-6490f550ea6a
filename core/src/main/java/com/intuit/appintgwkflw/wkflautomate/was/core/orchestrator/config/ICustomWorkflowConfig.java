package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import java.util.Map;


/**
 * This interface is responsible for returning the correct config based on the entity type
 * This also contains the Map of entities which have been migrated along with the pre-canned templates of the migrated entities
 * for example entry for key "invoice" -> ["invoiceoverduereminder", "invoiceunsentreminder"]
 * the implementation reduces the complexity of the code in the controller and we can easily add new configs
 * */
public interface ICustomWorkflowConfig {

  public Record getRecordObjForType(String recordType);

  public Map<String, ConfigTemplate> getTemplateMap();
}

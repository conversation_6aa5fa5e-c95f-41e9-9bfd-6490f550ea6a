package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.MAX_TRIES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus.ACTIVE;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.OnDemandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.WorkflowVariabilityUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.DefinitionPendingDeletion;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <br>
 *     </>
 *     <p>Checks if all the processes have ended for the realm. If true then it mutates the process
 *     variable accordingly.
 */
@Component
public class ProcessStatusCheckHandler extends WorkflowTaskHandler {

  private final ProcessDetailsRepoService processDetailsRepoService;
  
  private final FeatureManager featureManager;
  private final OnDemandHelper onDemandHelper;

  public ProcessStatusCheckHandler(
          ProcessDetailsRepoService processDetailsRepoService,
          @Qualifier(WorkflowConstants.IXP_MANAGER_BEAN) FeatureManager featureManager, OnDemandHelper onDemandHelper) {
    this.processDetailsRepoService = processDetailsRepoService;
    this.featureManager = featureManager;
    this.onDemandHelper = onDemandHelper;
  }

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_PROCESS_STATUS_CHECKER_ACTION_HANDLER;
  }
  
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    Map<String, Object> responseMap = new HashMap<>();
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

    try {
      Optional<List<ProcessDetails>> incompleteProcessList = getIncompleteProcessList(workerActionRequest, realmId);
      
		/**
		 * filtering out processes which are not in error state as they may never get
		 * signaled in few cases, which means it will keep checking the status and will add the
		 * unnecessary delay in closing the down-grade process as this checks runs thrice at an
		 * interval of 2 minutes.
		 * 
		 * ex: Custom Approval.
		 * 
		 * If process gets deleted and child process calls AS to update the status which
		 * internally calls downstream and which fails with transaction deleted error
		 * and an incident is created. Now if down-grade workflow get triggered, child process has closed and will never get
		 * signaled, even if it get signaled it will fail as transaction is deleted.
		 * So ignoring such error processes in status check.
		 */
	  Optional<List<ProcessDetails>> processesWithoutErrorList = incompleteProcessList.map(list -> list.stream()
				.filter(processDetails -> InternalStatus.ALREADY_ERROR != processDetails.getInternalStatus())
				.collect(Collectors.toList()));
  
      // Mutate the input variable in case of all processes closed. The only running process will be
      // the downgrade process but that is already excluded from the process set
      if (!processesWithoutErrorList.isPresent() || ObjectUtils.isEmpty(processesWithoutErrorList.get())) {
        responseMap.put(WorkflowConstants.PROCESS_STATUS_CLOSE, Boolean.TRUE);
        prepareActivityResponse(responseMap, workerActionRequest.getActivityId(), Boolean.TRUE);
        logInfo(
            "All the running Camunda processes for RealmId=%s closed for downgrade process processId=%s",
            realmId, workerActionRequest.getProcessInstanceId());
      } else {
        responseMap.put(WorkflowConstants.PROCESS_STATUS_CLOSE, Boolean.FALSE);
        logInfo(
            "Camunda processes for RealmId=%s still pending for downgrade process processIds=%s",
            realmId,
            processesWithoutErrorList.get().stream()
                .map(ProcessDetails::getProcessId)
                .collect(Collectors.joining(WorkflowConstants.COMMA)));
        // setting response with activityid_response
        prepareActivityResponse(responseMap, workerActionRequest.getActivityId(), Boolean.FALSE);
      }

    } catch (Exception ex) {
      responseMap.put(WorkflowConstants.PROCESS_STATUS_CLOSE, Boolean.FALSE);
      // setting response with activityid_response
      prepareActivityResponse(responseMap, workerActionRequest.getActivityId(), Boolean.FALSE);
    }

    responseMap.put(MAX_TRIES, updateLoopCount(workerActionRequest));
    return responseMap;
  }

  private Optional<List<ProcessDetails>> getIncompleteProcessList(WorkerActionRequest workerActionRequest,
      String realmId) {
    Optional<List<ProcessDetails>> processes = Optional.empty();
    if (!WorkflowVariabilityUtil.isSubscriptionDataAvailable(workerActionRequest, featureManager)) {
      /**
       * This will find all the processes which are incomplete and will exclude the current running
       * process from the list. TODO: cleanup post 100% rollout of new downgrade flow
       * https://jira.intuit.com/browse/QBOES-22936
       */
      processes =
          processDetailsRepoService.findIncompleteProcesses(
              Long.valueOf(realmId), workerActionRequest.getProcessInstanceId());
    } else {
      /**
       * check status of signalled processes from a list of filtered definitions as part of
       * subscription downgrade. Based on the current active subscriptions, some workflows in the
       * realm may be retained
       */
      List<DefinitionPendingDeletion> definitionsForProcessDeletion = new ArrayList<>();
      definitionsForProcessDeletion.addAll(WorkflowVariabilityUtil.getDefinitionsPendingDeletion(workerActionRequest));

      // Fetching on demand approval system definition
      definitionsForProcessDeletion.addAll(onDemandHelper.fetchOnDemandApprovalDefinition().stream().map(DefinitionPendingDeletion::buildFromDefinitionDetails).collect(
          Collectors.toList()));

      if (ObjectUtils.isNotEmpty(definitionsForProcessDeletion)) {
        // This will find all the processes which are incomplete for the given set of definitions
        // and will exclude the current running process from the list.
        // Adding filter for realm id because we can get on demand approval processes for other realm ids as well since it is a system template
        processes =
            Optional.of(
                processDetailsRepoService
                    .findByDefinitionDetailsListAndProcessStatus(
                        WorkflowVariabilityUtil.getDefinitionList(
                            definitionsForProcessDeletion),
                        ACTIVE)
                    .orElse(Collections.emptyList())
                    .stream()
                    .filter(
                        processDetail ->
                            realmId.equalsIgnoreCase(String.valueOf(processDetail.getOwnerId())) &&
                            !processDetail
                                .getProcessId()
                                .equals(workerActionRequest.getProcessInstanceId()))
                    .collect(Collectors.toList())
            );
      }
    }
    return processes;
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.PROCESS_STATUS, Type.WAS_METRIC, exception);
  }

  /**
   * @param responseMap : Response Map
   * @param activityId : Activity id
   * @param status : True/False
   */
  private void prepareActivityResponse(
      Map<String, Object> responseMap, String activityId, Boolean status) {
    responseMap.put(
        new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
        status.toString());
  }

  /**
   * Helper method to maintain the value of Counter variable for breaking the loop.
   *
   * @param workerActionRequest
   * @return
   */
  private int updateLoopCount(WorkerActionRequest workerActionRequest) {
    int count = 1;
    String counter = workerActionRequest.getInputVariables().get(MAX_TRIES);
    if (StringUtils.isNotEmpty(counter)) {
      count = Integer.valueOf(counter) + 1;
    }
    return count;
  }

  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_UNSUBSCRIBE));
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity;

import com.intuit.v4.workflows.WorkflowStep;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;

/**
 * This class is used to save the meta data needed by the DynamicBpmnWorkflowStepProcessor interface
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class DynamicBpmnWorkflowStepProcessorMetaData {
  private AbstractFlowNodeBuilder flowNodeBuilder;
  private WorkflowStep workflowStep;
  private WorkflowStep parentWorkflowStep;
  private Map<String, WorkflowStep> workflowStepMap;
  private Map<String, String> effectiveParentIdMap;
  private Map<String, String> dynamicActivityIdMap;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
}

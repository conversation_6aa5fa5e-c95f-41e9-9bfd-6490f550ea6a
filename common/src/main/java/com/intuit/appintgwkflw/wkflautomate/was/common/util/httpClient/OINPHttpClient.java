package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.OINP;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * Enabling resiliency for OINP API calls
 */
@AllArgsConstructor
@Retry(name = ResiliencyConstants.HTTP_CLIENT_RETRY)
@Component
public class OINPHttpClient {

  private WASHttpClient client;

  /**
   * Pushing OINP metrics and invokes API
   * @param wasHttpRequest request
   * @return http Response
   */
  @ServiceMetric(serviceName = OINP, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {

    return client.httpResponse(wasHttpRequest);
  }
}

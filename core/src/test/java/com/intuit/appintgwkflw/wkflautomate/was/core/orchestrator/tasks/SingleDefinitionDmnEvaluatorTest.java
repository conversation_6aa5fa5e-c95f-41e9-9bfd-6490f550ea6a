package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler.EvaluateDMNHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.RuleEvaluationTaskTest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerHandlerTestData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;
import org.apache.commons.lang3.BooleanUtils;
import org.camunda.bpm.dmn.engine.DmnDecision;
import org.camunda.bpm.dmn.engine.DmnDecisionTableResult;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionImpl;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableImpl;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableInputImpl;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableOutputImpl;
import org.camunda.bpm.dmn.engine.impl.DmnExpressionImpl;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.commons.utils.IoUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class SingleDefinitionDmnEvaluatorTest {

  private static final Map<String, Object> schema = new HashMap<>();
  private final DmnEngine dmnEngine = Mockito.mock(DmnEngine.class);
  private final SingleDefinitionDmnEvaluator evaluateDMNTask = new SingleDefinitionDmnEvaluator(dmnEngine,dmnEngine);

  private final byte[] dmnData =
      IoUtil.inputStreamAsByteArray(
          RuleEvaluationTaskTest.class
              .getClassLoader()
              .getResourceAsStream("dmn/decision_invoiceapproval.dmn"));

  @Before
  public void setup() {
    schema.put(INTUIT_REALMID, "1234");
    schema.put("undepositedFunds", "500");
  }

  private void setupMocks() {
    DmnDecision dmnDecision = Mockito.mock(DmnDecision.class);
    when(dmnEngine.parseDecisions(Mockito.any(DmnModelInstance.class)))
        .thenReturn(Collections.singletonList(dmnDecision));

    DmnDecisionTableImpl dmnDecisionTableImpl = Mockito.mock(DmnDecisionTableImpl.class);
    when(dmnDecision.getDecisionLogic()).thenReturn(dmnDecisionTableImpl);

    DmnExpressionImpl dmnExpression = Mockito.mock(DmnExpressionImpl.class);
    when(dmnExpression.getExpression()).thenReturn("ParameterName");

    DmnDecisionTableInputImpl dmnDecisionTableInput = Mockito.mock(DmnDecisionTableInputImpl.class);
    List<DmnDecisionTableInputImpl> inputs = Arrays.asList(dmnDecisionTableInput);
    when(dmnDecisionTableImpl.getInputs()).thenReturn(inputs);

    when(dmnDecisionTableInput.getExpression()).thenReturn(dmnExpression);
  }

  @Test
  public void testExecuteWithNonNullVariablesMap() {
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    setupMocks();
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    when(dmnEngine.evaluateDecisionTable(Mockito.any(), Mockito.any(VariableMap.class)))
        .thenReturn(dmnDecisionRuleResults);
    Map<String, Object> map = new HashMap<>();
    map.put("approvalRequired", true);
    when(dmnDecisionRuleResults.getResultList()).thenReturn(Collections.singletonList(map));
    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnData, new EvaluateRuleRequest("def-id", variables), true);
    Assert.assertNotNull(res);
    Assert.assertTrue(res.get(0).containsKey("approvalRequired"));
    Object response = res.get(0).get("approvalRequired");
    Assert.assertTrue(BooleanUtils.toBoolean(response.toString()));
  }

  @Test
  public void testExecuteWithNullVariablesMap() {
    setupMocks();
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    when(dmnEngine.evaluateDecisionTable(Mockito.any(), Mockito.any(VariableMap.class)))
        .thenReturn(dmnDecisionRuleResults);
    Map<String, Object> map = new HashMap<>();
    map.put("approvalRequired", true);
    when(dmnDecisionRuleResults.getResultList()).thenReturn(Collections.singletonList(map));

    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnData,new EvaluateRuleRequest("def-id", new HashMap<>()), false);
    Assert.assertNotNull(res);
    Assert.assertTrue(res.get(0).containsKey("approvalRequired"));
    Object response = res.get(0).get("approvalRequired");
    Assert.assertTrue(BooleanUtils.toBoolean(response.toString()));
  }

  @Test
  public void testExecuteWithVariablesMap() {
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    variables.put("decision", "11");
    setupMocks();
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    when(dmnEngine.evaluateDecisionTable(Mockito.any(), Mockito.any(VariableMap.class)))
        .thenReturn(dmnDecisionRuleResults);
    Map<String, Object> map = new HashMap<>();
    map.put("approvalRequired", true);
    when(dmnDecisionRuleResults.getResultList()).thenReturn(Collections.singletonList(map));

    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnData,new EvaluateRuleRequest("def-id", variables), false);
    Assert.assertNotNull(res);
    Assert.assertTrue(res.get(0).containsKey("approvalRequired"));
    Object response = res.get(0).get("approvalRequired");
    Assert.assertTrue(BooleanUtils.toBoolean(response.toString()));
  }

  @Test
  public void testExecuteWithVariablesMapForMultiCondition_ForActivityIdOutput() {
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    variables.put("decision", "11");
    setupMocks();
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    when(dmnEngine.evaluateDecisionTable(Mockito.any(), Mockito.any(VariableMap.class)))
            .thenReturn(dmnDecisionRuleResults);
    Map<String, Object> decisionResultMap1 = new HashMap<>();
    decisionResultMap1.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "2");
    Map<String, Object> decisionResultMap2 = new HashMap<>();
    decisionResultMap1.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "action-2");
    when(dmnDecisionRuleResults.getResultList())
            .thenReturn(Collections.singletonList(decisionResultMap1))
            .thenReturn(Collections.singletonList(decisionResultMap2));

    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnData,new EvaluateRuleRequest("def-id", variables), false);
    Assert.assertNotNull(res);
    Map<String, Object> expectedResultMap = new HashMap<>();
    expectedResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "action-2");
    Assert.assertEquals(expectedResultMap, res.get(0));
  }

  @Test
  public void testExecuteWithVariablesMapForMultiCondition_ForFalseOutput() {
    Map<String, Object> variables = TriggerHandlerTestData.getVariablesMap();
    variables.put("decision", "11");
    setupMocks();
    DmnDecisionTableResult dmnDecisionRuleResults = Mockito.mock(DmnDecisionTableResult.class);
    when(dmnEngine.evaluateDecisionTable(Mockito.any(), Mockito.any(VariableMap.class)))
            .thenReturn(dmnDecisionRuleResults);
    Map<String, Object> decisionResultMap1 = new HashMap<>();
    decisionResultMap1.put("decisionResult", "1");
    Map<String, Object> decisionResultMap2 = new HashMap<>();
    decisionResultMap1.put("decisionResult", "false");
    when(dmnDecisionRuleResults.getResultList())
            .thenReturn(Collections.singletonList(decisionResultMap1))
            .thenReturn(Collections.singletonList(decisionResultMap2));

    List<Map<String, Object>> res = evaluateDMNTask.evaluateDMN(dmnData,new EvaluateRuleRequest("def-id", variables), false);
    Assert.assertNotNull(res);
    Map<String, Object> expectedResultMap = new HashMap<>();
    expectedResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, "false");
    Assert.assertEquals(expectedResultMap, res.get(0));
  }

  @Test
  public void testdmnResponseConvertor_ForTrueInput() {
    List<Map<String, Object>> decisionResult = new ArrayList<>();
    Map<String, Object> decisionResultMap = new HashMap<>();
    decisionResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, Boolean.TRUE);
    decisionResult.add(decisionResultMap);
    List<Map<String, Object>> actualResult = evaluateDMNTask.dmnResponseConvertor(decisionResult);
    List<Map<String, Object>> expectedResult = new ArrayList<>();
    Map<String, Object> expectedResultMap = new HashMap<>();
    expectedResultMap.put("type", "Boolean");
    expectedResultMap.put("valueInfo", new LinkedHashMap<>());
    expectedResultMap.put("value", Boolean.TRUE);
    expectedResult.add(new HashMap<>());
    expectedResult.stream().findFirst().get().put(WorkflowConstants.CUSTOM_DECISION_RESULT, expectedResultMap);
    Assert.assertEquals(expectedResult, actualResult);
  }

  @Test
  public void testdmnResponseConvertor_ForFalseInput() {
    List<Map<String, Object>> decisionResult = new ArrayList<>();
    Map<String, Object> decisionResultMap = new HashMap<>();
    decisionResultMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, Boolean.FALSE);
    decisionResult.add(decisionResultMap);
    List<Map<String, Object>> actualResult = evaluateDMNTask.dmnResponseConvertor(decisionResult);
    List<Map<String, Object>> expectedResult = new ArrayList<>();
    Map<String, Object> expectedResultMap = new HashMap<>();
    expectedResultMap.put("type", "Boolean");
    expectedResultMap.put("valueInfo", new LinkedHashMap<>());
    expectedResultMap.put("value", Boolean.FALSE);
    expectedResult.add(new HashMap<>());
    expectedResult.stream().findFirst().get().put(WorkflowConstants.CUSTOM_DECISION_RESULT, expectedResultMap);
    Assert.assertEquals(expectedResult, actualResult);
  }

  @Test
  public void testLogForDMN() {
    VariableMap variableMap = Variables.fromMap(schema);
    DmnDecision dmnDecision = mockDmnDecision(true, true);
    evaluateDMNTask.logDmnTable(dmnDecision, variableMap);

    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.info(
          () -> WorkflowLoggerRequest.builder()
              .className(EvaluateDMNHandler.class.getSimpleName())
              .message("test-msg")
              .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
              .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER));
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.info(Mockito.any()));
    }
  }

  @Test
  public void testLogForInvalidDecisionShouldNotThrowException() {
    VariableMap variableMap = Variables.fromMap(schema);
    evaluateDMNTask.logDmnTable(null, variableMap);
//  Nothing to assert
    Assert.assertNotNull(variableMap);
  }

  @Test
  public void testLogForDMNForEmptyDecision() {
    VariableMap variableMap = Variables.fromMap(schema);
    DmnDecision dmnDecision = mockDmnDecision(false, true);
    evaluateDMNTask.logDmnTable(dmnDecision, variableMap);

    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.error(
          () -> WorkflowLoggerRequest.builder()
              .className(EvaluateDMNHandler.class.getSimpleName())
              .message("test-msg")
              .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
              .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER));
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.error(Mockito.any()));
      mocked.verify(Mockito.times(0), () -> WorkflowLogger.info(Mockito.any()));
    }
  }

  @Test
  public void testLogForDMNWithNullExpression() {
    VariableMap variableMap = Variables.fromMap(schema);
    DmnDecision dmnDecision = mockDmnDecision(true, false);
    evaluateDMNTask.logDmnTable(dmnDecision, variableMap);

    try (MockedStatic<WorkflowLogger> mocked = Mockito.mockStatic(WorkflowLogger.class)) {

      WorkflowLogger.error(
          () -> WorkflowLoggerRequest.builder()
              .className(EvaluateDMNHandler.class.getSimpleName())
              .message("test-msg")
              .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
              .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER));
      mocked.verify(Mockito.times(1), () -> WorkflowLogger.error(Mockito.any()));
      mocked.verify(Mockito.times(0), () -> WorkflowLogger.info(Mockito.any()));
    }
  }

  private DmnDecision mockDmnDecision(boolean isOutput, boolean shouldSetExpression) {
    DmnDecisionImpl dmnDecision = new DmnDecisionImpl();
    DmnDecisionTableImpl decisionLogic = new DmnDecisionTableImpl();

    List<DmnDecisionTableInputImpl> dmnDecisionTableInputList = new ArrayList<>();
    DmnDecisionTableInputImpl dmnDecisionTable = new DmnDecisionTableInputImpl();
    dmnDecisionTable.setId("test");
    dmnDecisionTable.setName("undepositedFunds");
    if (shouldSetExpression) {
      DmnExpressionImpl dmnExpression = new DmnExpressionImpl();
      dmnExpression.setExpression("GTE");
      dmnDecisionTable.setExpression(dmnExpression);
    }

    dmnDecisionTable.setInputVariable("input_test1");
    dmnDecisionTableInputList.add(dmnDecisionTable);

    if (isOutput) {
      List<DmnDecisionTableOutputImpl> dmnDecisionTableOutputs = new ArrayList<>();
      DmnDecisionTableOutputImpl dmnDecisionTableOutput = new DmnDecisionTableOutputImpl();
      dmnDecisionTable.setId("test");
      dmnDecisionTable.setName("sendReminder");
      dmnDecisionTableOutputs.add(dmnDecisionTableOutput);
      decisionLogic.setOutputs(dmnDecisionTableOutputs);
    }

    decisionLogic.setInputs(dmnDecisionTableInputList);

    dmnDecision.setDecisionLogic(decisionLogic);

    return dmnDecision;
  }


}
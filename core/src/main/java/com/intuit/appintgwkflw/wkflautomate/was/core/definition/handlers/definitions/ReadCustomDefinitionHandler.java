package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateTriggerBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomAttributesUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;


/** Class for custom workflow definition related methods */
@AllArgsConstructor
@Component
public class ReadCustomDefinitionHandler {
  private final TemplateConditionBuilder templateConditionBuilder;
  private final TemplateActionBuilder templateActionBuilder;
  private final TemplateTriggerBuilder templateTriggerBuilder;
  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * Update workflow step by adding actions and conditions from config. There might be new
   * actions/conditions supported.
   *
   * @param templateName
   * @param recordType
   * @param workflowStep bpmn workflow step
   * @return
   */
  public WorkflowStep updateWorkflowStep(
      String templateName, String recordType, WorkflowStep workflowStep) {
    // return the bpmn step if actions or conditions are empty
    if (ObjectUtils.isEmpty(workflowStep)
        || CollectionUtils.isEmpty(workflowStep.getActions())
        || ObjectUtils.isEmpty(workflowStep.getWorkflowStepCondition())) {
      return workflowStep;
    }
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    if (record == null) {
      WorkflowLogger.logWarn(
          "Read definition requested for invalid record type. RecordType={}", recordType);
      throw new WorkflowGeneralException(WorkflowError.INVALID_RECORD_TYPE);
    }
    workflowStep.setTrigger(getUpdatedTrigger(templateName, record));
    workflowStep.setActions(getUpdatedActions(templateName, workflowStep.getActions(), record));
    workflowStep.setWorkflowStepCondition(
        getUpdatedCondition(templateName, workflowStep.getWorkflowStepCondition(), record));
    return workflowStep;
  }

  /**
   * This function generates the actionGroup object which is to be a part
   * of all the action workflow steps.
   *
   * @param templateName template name
   * @param recordType   record type
   * @return actionGroup object
   */
  public com.intuit.v4.workflows.ActionGroup populateWorkflowStepWithActionGroup(
          String templateName, String recordType) {
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    if (Objects.isNull(record)) {
      WorkflowLogger.logWarn("Read definition requested for invalid record type recordType=%s", recordType);
      throw new WorkflowGeneralException(WorkflowError.INVALID_RECORD_TYPE);
    }
    com.intuit.v4.workflows.ActionGroup stepActionGroup = new com.intuit.v4.workflows.ActionGroup();
    // get the actionkey for the particular definition object
    String actionKey = CustomWorkflowType.templateNameActionKeyMapping().get(templateName);
    ActionGroup recordActionGroup = MultiStepUtil.getFilteredRecordActionGroup(record, actionKey);
    // generate actionGroup object from the config
    com.intuit.v4.workflows.Action action = templateActionBuilder.buildTemplateStepAction(
            record,
            recordActionGroup,
            recordActionGroup.getActionIdMapper().getActionId());
    stepActionGroup.setAction(action);
    stepActionGroup.setActionKey(actionKey);
    return stepActionGroup;
  }

  private Trigger getUpdatedTrigger(String templateName, Record record) {
    return templateTriggerBuilder.build(record, CustomWorkflowType.templateNameActionKeyMapping().get(templateName));
  }

  /**
   * Prepare actions combining definition xml and config actions
   *
   * @param templateName bpmn template
   * @param workflowActions actions from workflow step obtained from parsing definition xml
   * @param record
   * @return
   */
  private List<ActionMapper> getUpdatedActions(
      String templateName, List<ActionMapper> workflowActions, Record record) {
    // Get all actions from config for the record
    List<ActionMapper> configActions = templateActionBuilder.build(record);

    String actionKey = CustomWorkflowType.templateNameActionKeyMapping().get(templateName);

    // The action local id here has company id and UUID appended
    Function<ActionMapper, String> keyMapper =
        actionMapper ->
            actionMapper.getAction().getId().getLocalId().split(WorkflowConstants.UNDERSCORE)[0];

    Map<String, ActionMapper> actionIdToActionMap =
        workflowActions.stream()
            .map(workflowAction -> workflowAction.actionKey(actionKey))
            .collect(Collectors.toMap(keyMapper, Function.identity()));

    // update the record actions with 'fieldValues' from workflow action if action is selected
    return configActions.stream()
        .map(
            configAction ->
                updateAction(
                    configAction,
                    actionIdToActionMap.get(configAction.getAction().getId().getLocalId()),
                    record))
        .collect(Collectors.toList());
  }

  /**
   * Get action updated with latest parameters from config action
   *
   * @param configActionMapper
   * @param workflowActionMapper
   * @return
   */
  private ActionMapper updateAction(
      ActionMapper configActionMapper, ActionMapper workflowActionMapper, Record record) {
    /**
     * Find action from BPMN template based on config action id and add the entire action from
     * config while replacing the field value
     */
    if (workflowActionMapper != null
        && configActionMapper
            .getActionKey()
            .equalsIgnoreCase(workflowActionMapper.getActionKey())) {
      return updateActionParameters(configActionMapper, workflowActionMapper, record);
    }
    return configActionMapper;
  }

  /**
   * Set fieldValues (user selected values) to config action from bpmn action
   *
   * @param configActionMapper
   * @param workflowActionMapper
   * @return
   */
  private ActionMapper updateActionParameters(
      ActionMapper configActionMapper, ActionMapper workflowActionMapper, Record record) {
    // Get action Id. As we are fetching it from config, it will not have company Id and UUID
    // appended.
    String actionId = configActionMapper.getAction().getId().getLocalId();

    // if a parameter has a non-empty fieldValue in definition or was configurable initially then it's
    // fieldValue would be taken from the definition itself.
    // if it was initially unconfigurable and didn't have any fieldValue in definition then the field
    // value from config would be read.
    Map<String, InputParameter> workflowActionParamMap =
        workflowActionMapper.getAction().getParameters().stream()
            .filter(inputParameter -> BooleanUtils.toBoolean(!inputParameter.getFieldValues().isEmpty()
                    || inputParameter.isConfigurable()))
            .collect(Collectors.toMap(InputParameter::getParameterName, Function.identity()));

    Map<String, Parameter> recordActionParamMap =
        getRecordActionParamMap(record, configActionMapper.getActionKey(), actionId);

    List<InputParameter> parameters = new ArrayList<>();
    // Get all parameters from config action and update the field value from workflow action
    // parameter
    for (InputParameter inputParameter : configActionMapper.getAction().getParameters()) {
      InputParameter workflowActionParameter =
          workflowActionParamMap.get(inputParameter.getParameterName());

      if (workflowActionParameter != null) {
        Parameter configParam = recordActionParamMap.get(inputParameter.getParameterName());

        List<String> fieldValues = workflowActionParameter.getFieldValues();

        // Replace help variable process variable names to display names. For example [[DocNumber]]
        // is replaced with [[Invoice Number]] or [[Bill Number]]
        if (CollectionUtils.isNotEmpty(configParam.getHelpVariables())
            && CollectionUtils.isNotEmpty(fieldValues)) {
          List<String> replacedFieldValues =
              CustomWorkflowUtil.getReplacedFieldValuesWithDisplayNames(
                  workflowActionParameter.getFieldValues(), configParam.getHelpVariables());
          inputParameter.setFieldValues(replacedFieldValues);

        } else {
          // Set the user entered field values if action is selected or field Value is not empty.
          // That means for selected action, even if user has entered empty value, it will be
          // honored
          if (workflowActionMapper.getAction().isSelected()
              || CollectionUtils.isNotEmpty(fieldValues)) {
            inputParameter.setFieldValues(fieldValues);
          }
        }
      }

      parameters.add(inputParameter);
    }
    // Update workflow action's parameter list
    /**
     * TODO: We need to track this as there should be a proper way to set this flag as UI uses this
     * value to render. Need to figure out a proper way to set this value properly going forward.
     */
    configActionMapper.getAction().setParameters(parameters);
    // By default if 1 action is there, it should be selected by default
    if (record.getActionGroups().get(0).getActions().stream().count() == 1L) {
      configActionMapper.getAction().setSelected(true);
    } else {
      configActionMapper.getAction().setSelected(workflowActionMapper.getAction().isSelected());
    }

    configActionMapper.getAction().setNexts(workflowActionMapper.getAction().getNexts());
    return configActionMapper;
  }

  /**
   * Fetch latest condition from config and only keep the rule lines fetched from DMN
   *
   * @param workflowCondition condition from BPMN template
   * @return Workflow step condition
   */
  public WorkflowStepCondition getUpdatedCondition(String templateName,
      WorkflowStepCondition workflowCondition, Record record) {
    if (workflowCondition == null) return null;
    CustomWorkflowType customWorkflowType = CustomWorkflowType.getCustomWorkflowForTemplateName(
        templateName);
    // Get uber template conditions
    WorkflowStepCondition templateCondition =
        templateConditionBuilder.build(record, customWorkflowType.getActionKey(),false, null);

    // Set conditions in workflow step
    workflowCondition.setConditionalInputParameters(
        templateCondition.getConditionalInputParameters());

    // During read, filter out rules based on hidden attributes
    // Replace rule with type double and && operator. Such a rule will be converted to BTW operator.
    // For example TxnAmount >= 100 && TxnAmount <= 200 will be returned as TxnAmount BTW 100,200
    for (RuleLine ruleLine : workflowCondition.getRuleLines()) {
      List<Rule> rules = ruleLine.getRules();
      List<Rule> filteredRules = new ArrayList<>();
      for (Rule rule : rules) {
        try {
          Rule updatedRule = getUpdatedRuleLines(rule, record);
          if(Objects.isNull(updatedRule)) {
            continue;
          }
          filteredRules.add(updatedRule);
        } catch (Exception ex) {
          WorkflowLogger.error(
              () -> {
                return WorkflowLoggerRequest.builder()
                    .message(
                        "Exception occurred while reading custom field rules: unsupported parameter type")
                    .stackTrace(ex)
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .className(ReadCustomDefinitionHandler.class.getSimpleName());
              });
          throw new WorkflowGeneralException(
              "Exception occurred while creating custom field rules: unsupported parameter type");
        }
      }
      ruleLine.setRules(filteredRules);
    }
    return workflowCondition;
  }

  /**
   * The first filter matches paramName in rule to attribute name in config if we are able to
   * find the attribute with the paramName in config we return that else there can be two cases:
   * 1. the rule can be a custom field we do a secondary filtering to check against the
   * paramType of rule and a suffix to see if there is a generic custom field attribute for that
   * given record-type
   *
   * <p>2. Throw exception if both level of filtering doesn't return any result
   *
   * <p>For example let's consider Rule 1: TxnAmount GTE 120 this will be filtered with the
   * first filter since paramName in rule will match with Attribute Name in config
   *
   * <p>Rule 2: 3600000000000155715 LTE 200 (paramType -> double) This won't be selected in
   * first filter since there is no such paramName in the config for this customAttribute This
   * will be selected in second filter with paramType+ custom field suffix will match a generic
   * customAtrribute in config
   *
   * @param rule    rule
   * @param record  record object
   * @return        updated rule object
   */
  public Rule getUpdatedRuleLines(Rule rule, Record record) {
    Attribute recordAttribute =
        record.getAttributes().stream()
            .filter(
                attribute -> attribute.getName().equalsIgnoreCase(rule.getParameterName()))
            .findFirst()
            .orElseGet(
                () ->
                    record.getAttributes().stream()
                        .filter(
                            attribute ->
                                attribute
                                    .getName()
                                    .equalsIgnoreCase(
                                        CustomAttributesUtil.getGenericCustomFieldAttributeName(
                                            rule.getParameterType())))
                        .findFirst()
                        .orElseThrow(
                            () ->
                                new WorkflowGeneralException(
                                    WorkflowError.INVALID_RECORD_TYPE)));

    if (BooleanUtils.toBoolean(recordAttribute.getHidden())) {
      return null;
    }
    String conditionalExpression = rule.getConditionalExpression();

    /**
     * Update the parameter name to remove the custom field prefix and return the custom field
     * id
     */
    if (rule.getParameterName().startsWith(WorkflowConstants.CUSTOM_FIELD_PREFIX)) {
      rule.setParameterName(CustomAttributesUtil.getCustomFieldName(rule.getParameterName()));
    }
    if (WorkflowConstants.DOUBLE_MODIFIER.equalsIgnoreCase(recordAttribute.getType())
        && conditionalExpression.contains(WorkflowConstants.AND_MODIFIER)) {
      String expr =
          conditionalExpression
              .replace(WorkflowConstants.AND_MODIFIER, WorkflowConstants.COMMA)
              .replaceAll("[A-Z ]", "");
      rule.setConditionalExpression(
          WorkflowConstants.OPERATOR_BTW + WorkflowConstants.SPACE + expr);
    }
    return rule;
  }

  private Map<String, Parameter> getRecordActionParamMap(
      Record record, String actionKey, String actionId) {
    // Get equivalent action entry from config
    Action recordAction =
       record.getActionGroups().stream()
            .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(actionKey))
            .map(ActionGroup::getActions)
            .flatMap(Collection::stream)
            .filter(action -> action.getId().equalsIgnoreCase(actionId))
            .findFirst()
            .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.ACTION_NOT_FOUND));

    return recordAction.getParameters().stream()
        .collect(Collectors.toMap(Parameter::getName, Function.identity()));
  }
}

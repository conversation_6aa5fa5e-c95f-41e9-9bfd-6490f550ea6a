package com.intuit.appintgwkflw.wkflautomate.was.core.feelprovider;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.apache.commons.collections.CollectionUtils;
import org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunction;
import org.camunda.bpm.dmn.feel.impl.scala.function.FeelCustomFunctionProvider;
import org.jetbrains.annotations.NotNull;

import java.util.Objects;
/**
 * <AUTHOR>
 *     <p>provide custom functions related to lists; in this case "containsAnyElement"
 */
public class CustomListFunctionProvider implements FeelCustomFunctionProvider {
  protected Map<String, CustomFunction> functions = new HashMap<>();
  public static final String CONDITION_ELEMENT_LIST = "conditionElemList";

  public CustomListFunctionProvider() {
    functions.put(WorkflowConstants.CONTAINS_ANY_ELEMENT, listContainsSublistElementHelper());
  }

  private static List<String> getInputList(Object input) {

    if (input instanceof String) {
      return Arrays.asList(((String) input).split(WorkflowConstants.COMMA));
    } else if (input instanceof List) {
      return (List) input;
    }
    return Collections.emptyList();
  }

  private CustomFunction listContainsSublistElementHelper() {
    return CustomFunction.create()
        .setParams(CONDITION_ELEMENT_LIST, WorkflowConstants.INPUT)
        .setFunction(args -> listContainsSublistElement(args))
        .build();
  }

  /**
   * This method checks if any element in the inputList matches any element in the conditionElemList
   * conditionElementList is a list of strings
   * inputList can be of any type - string or list.
   *
   * @param args
   * @return
   * <pre>
   *  e.g.
   *  For conditionElemList = List.of("1", "2"),
   *  inputList = "1,2,3" return true
   *  inputList = List.of("3", "4") return false
   * </pre>
   */
  @VisibleForTesting
  static boolean listContainsSublistElement(List<Object> args) {
    if (Objects.isNull(args) || args.size() < 2) {
      return false;
    }
    List<String> conditionElemList = (List<String>) args.get(0);
    if (CollectionUtils.isEmpty(conditionElemList)) {
      return false;
    }
    Object input = args.get(1);
    if (Objects.isNull(input)) {
      return false;
    }

    return getInputList(input).stream()
        .anyMatch(element -> conditionElemList.stream().anyMatch(element::equalsIgnoreCase));
  }

  @Override
  public Optional<CustomFunction> resolveFunction(String functionName) {
    return Optional.ofNullable(functions.get(functionName));
  }

  @Override
  public Collection<String> getFunctionNames() {
    return functions.keySet();
  }
}

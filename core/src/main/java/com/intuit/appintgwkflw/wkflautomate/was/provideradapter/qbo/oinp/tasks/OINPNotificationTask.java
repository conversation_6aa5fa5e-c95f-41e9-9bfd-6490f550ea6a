package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowBatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.accounts.Persona;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.EventMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.BatchNotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.NotificationTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import static com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qbo.oinp.constants.OinpBridgeConstants.*;

@RequiredArgsConstructor
public class OINPNotificationTask implements Task {

  private final WorkflowBatchNotificationTask workflowBatchNotificationTask;
  private final WorkflowNotificationTask workflowNotificationTask;

  private State state;
  private String externalTaskId;
  private String notificationName;
  private String notificationDataType;
  private Map<String, Object> oinpEventDataMap;

  @Override
  public State execute(State state) {
    this.state = state;
    this.oinpEventDataMap = state.getValue(BRIDGE_OUTPUT_MAP);

    WorkerActionRequest workerActionRequest = state.getValue(WORKER_ACTION_REQUEST);
    this.externalTaskId = workerActionRequest.getTaskId();

    this.notificationName = state.getValue(NOTIFICATION_NAME);
    this.notificationDataType = state.getValue(NOTIFICATION_DATA_TYPE);

    executeNotification();

    return this.state;
  }

  /**
   * Execute notification
   */
  private void executeNotification() {
    boolean isEmail = state.getValue(IS_EMAIL);
    boolean isMobile = state.getValue(IS_MOBILE);

    String[] toList = oinpEventDataMap.get(TO_FIELD).toString().split(TO_FIELD_DELIMITER);

    String status;

    if (toList.length == 0 || (toList.length == 1 && StringUtils.isBlank(toList[0]))) {
      status = ActivityConstants.NO_ACTION;
    } else if (isEmail) {
      status = sendEmailNotification().getStatus();
    } else if (isMobile) {
      status = sendMobileNotification().getStatus();
    } else {
      status = ActivityConstants.NO_ACTION;
    }
    this.state.addValue(NOTIFICATION_TASK_STATUS, status);
  }

  /** @return Notification task response */
  private WorkflowTaskResponse sendEmailNotification() {
    Set<String> authIds = getAuthIdsForEmail();
    if (authIds.size() > 1) {
      return workflowBatchNotificationTask.create(createBatchEmailNotificationTask(authIds));
    }
    return workflowNotificationTask.create(createNotificationTask(authIds.iterator().next()));
  }

  /**
   * returns an authId to be used for sending emails. it sorts the existing authIds and returns 1st
   * in the list. If no authIds present, {@link DEFAULT_AUTH_ID} is returned
   */
  private Set<String> getAuthIdsForEmail() {
    SortedSet<String> authIds = state.getValue(AUTH_IDS);
    return authIds.isEmpty() ? Collections.singleton(DEFAULT_AUTH_ID) : authIds;
  }

  /**
   * Handle mobile notification
   */
  private WorkflowTaskResponse sendMobileNotification() {
    String[] toList = oinpEventDataMap.get(TO_FIELD).toString().split(TO_FIELD_DELIMITER);
    if (toList.length > 1){
      return workflowBatchNotificationTask.create(createBatchNotificationTask(toList));
    }
    return workflowNotificationTask.create(createNotificationTask(toList[0]));
  }

  /**
   * Creates {@link NotificationTask}
   * @param authId populate authId in {@link EventMetaData}
   */
  private NotificationTask createNotificationTask(String authId){
    return NotificationTask.builder()
        .notificationName(notificationName)
        .notificationDataType(notificationDataType)
        .serviceName(OINP_SERVICE_NAME)
        .idempotencyKey(externalTaskId)
        .notificationMetaData(EventMetaData.builder()
            .authId(Long.valueOf(authId)).build())
        .notificationData(new HashMap<>(oinpEventDataMap))
        .build();
  }

  /**
   * Creates {@link BatchNotificationTask}
   * @param authIds list of authIds to be populated in {@link EventMetaData}
   */
  private BatchNotificationTask createBatchNotificationTask(String[] authIds) {
    List<NotificationTask> notificationTasks = new ArrayList<>();
    Arrays.stream(authIds).forEach(
        authId -> {
          NotificationTask notificationTask = createNotificationTask(authId);
          notificationTask.setIdempotencyKey(
              externalTaskId.concat(IDEMPOTENCY_KEY_CONCAT).concat(authId));
          notificationTasks.add(notificationTask);
        }
    );

    return BatchNotificationTask.builder()
        .notificationTaskList(notificationTasks)
        .build();
  }

  /**
   * Creates {@link BatchNotificationTask}
   *
   * @param authIds list of authIds to be populated in {@link EventMetaData}
   */
  private BatchNotificationTask createBatchEmailNotificationTask(Set<String> authIds) {
    List<NotificationTask> notificationTasks = new ArrayList<>();
    Map<String, Persona> iusResponseMap =
        state.getValue(OinpBridgeConstants.IUS_AUTHID_PERSONA_MAP);
    authIds.forEach(
        (authId) -> {
          /*
           * oinp digest functionality groups notifications based on the userId.
           * using personaId in case of consolidated email notification
           * to enable realm specific emails to be sent to the users.
           */
          String userId =
              (boolean) state.getValue(CONSOLIDATE_NOTIFICATIONS)
                  ? iusResponseMap.get(authId).getPersonaId()
                  : authId;
          oinpEventDataMap.replace(TO_FIELD, iusResponseMap.get(authId).getEmail().getEmailId());
          NotificationTask notificationTask = createNotificationTask(userId);
          notificationTask.setIdempotencyKey(
              externalTaskId.concat(IDEMPOTENCY_KEY_CONCAT).concat(userId));
          notificationTasks.add(notificationTask);
        });

    return BatchNotificationTask.builder().notificationTaskList(notificationTasks).build();
  }
}

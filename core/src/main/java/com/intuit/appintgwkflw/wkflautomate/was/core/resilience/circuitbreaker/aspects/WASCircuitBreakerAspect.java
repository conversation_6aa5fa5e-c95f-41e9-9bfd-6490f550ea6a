package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.aspects;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ApplyCircuitBreaker;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowCircuitOpenException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;

import java.util.Optional;
import java.util.function.Supplier;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * Fetches and applies the circuit breaker for the method on which the @ApplyCircuitBreaker annotation is used, if present; else
 * proceeds with the normal call without circuit breaker.
 *
 */
@Aspect
@Component
@AllArgsConstructor
public class WASCircuitBreakerAspect {

    private WASCircuitBreakerService wasCircuitBreakerService;
    private WASCircuitBreakerConfiguration wasCircuitBreakerConfiguration;

    @Around("@annotation(circuitBreakerAction)")
    public Object execute(final ProceedingJoinPoint joinPoint, ApplyCircuitBreaker circuitBreakerAction) {

        // If circuit breaker flag is disabled, do not apply circuit breaker, even if present
        Optional<CircuitBreaker> circuitBreaker =
                wasCircuitBreakerConfiguration.isEnabled() ?
                        wasCircuitBreakerService.getCircuitBreakerForOffering(circuitBreakerAction.action()) : Optional.empty();

        return executeSupplier(circuitBreaker, joinPoint);
    }

    /**
     * If circuit breaker is present, surround the method call with that circuit breaker.
     * Else proceed without circuit breaker.
     *
     * @param circuitBreaker
     * @param joinPoint
     * @return
     */
    public Object executeSupplier(Optional<CircuitBreaker> circuitBreaker, ProceedingJoinPoint joinPoint){
        Supplier<Object> joinPointSupplier = getSupplier(joinPoint);

        if(circuitBreaker.isPresent()) {
            WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Executing call through circuitbreaker=%s", circuitBreaker.get().getName());
            joinPointSupplier = circuitBreaker.get().decorateSupplier(joinPointSupplier);
        }

        try {
            return joinPointSupplier.get();
        }
        catch (CallNotPermittedException e){
            throw new WorkflowCircuitOpenException(WorkflowError.CIRCUIT_OPEN_ERROR, e);
        }
    }

    private Supplier<Object> getSupplier(ProceedingJoinPoint joinPoint) {
        return () -> {
            try {
                return joinPoint.proceed();
            } catch (WorkflowGeneralException e) {
                throw e;
            } catch(Throwable e) {
                throw new WorkflowGeneralException(e);
            }
        };
    }
}

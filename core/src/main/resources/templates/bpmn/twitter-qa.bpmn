<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0j3sn7y" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="3.4.1">
  <bpmn:process id="TwitterQAProcess" name="Twitter QA" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="Post to twitter requested" camunda:formKey="embedded:app:forms/start.html">
      <bpmn:outgoing>SequenceFlow_0rk4308</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0rk4308" sourceRef="StartEvent_1" targetRef="ReviewTweetTask" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_0jxte30" name="Approved?">
      <bpmn:incoming>SequenceFlow_0fo87ep</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1ocpkoo</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_14wjuig</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_0fo87ep" sourceRef="ReviewTweetTask" targetRef="ExclusiveGateway_0jxte30" />
    <bpmn:sequenceFlow id="SequenceFlow_1ocpkoo" name="Yes" sourceRef="ExclusiveGateway_0jxte30" targetRef="Task_0rblw0y">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_14wjuig" name="No" sourceRef="ExclusiveGateway_0jxte30" targetRef="Task_0468nob">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">#{not approved}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1dgq3y5" sourceRef="Task_0rblw0y" targetRef="Task_0tzv1u6" />
    <bpmn:endEvent id="EndEvent_1nz43yd" name="Tweet posted">
      <bpmn:incoming>SequenceFlow_0tv9341</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="EndEvent_053f55r" name="Tweet request rejected">
      <bpmn:incoming>SequenceFlow_0yd76ez</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0tv9341" sourceRef="Task_0tzv1u6" targetRef="EndEvent_1nz43yd" />
    <bpmn:sequenceFlow id="SequenceFlow_0yd76ez" sourceRef="Task_0468nob" targetRef="EndEvent_053f55r" />
    <bpmn:serviceTask id="Task_0rblw0y" name="Post to twitter" camunda:asyncBefore="true" camunda:class="com.camunda.training.CreateTweetDelegate">
      <bpmn:incoming>SequenceFlow_1ocpkoo</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1602ln1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1dgq3y5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Task_0tzv1u6" name="Notify user of post to twitter" camunda:type="external" camunda:topic="notification">
      <bpmn:incoming>SequenceFlow_1dgq3y5</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0tv9341</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:startEvent id="StartEvent_1jqwcp1" name="Tweet submitted by super user">
      <bpmn:outgoing>SequenceFlow_1602ln1</bpmn:outgoing>
      <bpmn:messageEventDefinition messageRef="Message_099ce5p" />
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="SequenceFlow_1602ln1" sourceRef="StartEvent_1jqwcp1" targetRef="Task_0rblw0y" />
    <bpmn:businessRuleTask id="ReviewTweetTask" name="Review Tweet post" camunda:resultVariable="approved" camunda:decisionRef="tweetApproval" camunda:mapDecisionResult="singleEntry">
      <bpmn:incoming>SequenceFlow_0rk4308</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0fo87ep</bpmn:outgoing>
    </bpmn:businessRuleTask>
    <bpmn:serviceTask id="Task_0468nob" name="Notify user of rejection" camunda:type="external" camunda:topic="workflow">
      <bpmn:incoming>SequenceFlow_14wjuig</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0yd76ez</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmn:message id="Message_099ce5p" name="superuserTweet" />
  <bpmn:message id="Message_1lhzt6e" name="tweetWithdrawn" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="TwitterQAProcess">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="329" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="164" y="372" width="67" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0rk4308_di" bpmnElement="SequenceFlow_0rk4308">
        <di:waypoint x="215" y="347" />
        <di:waypoint x="360" y="347" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ExclusiveGateway_0jxte30_di" bpmnElement="ExclusiveGateway_0jxte30" isMarkerVisible="true">
        <dc:Bounds x="595" y="322" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="655" y="340" width="54" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0fo87ep_di" bpmnElement="SequenceFlow_0fo87ep">
        <di:waypoint x="460" y="347" />
        <di:waypoint x="595" y="347" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1ocpkoo_di" bpmnElement="SequenceFlow_1ocpkoo">
        <di:waypoint x="620" y="322" />
        <di:waypoint x="620" y="210" />
        <di:waypoint x="740" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="626" y="263" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_14wjuig_di" bpmnElement="SequenceFlow_14wjuig">
        <di:waypoint x="620" y="372" />
        <di:waypoint x="620" y="470" />
        <di:waypoint x="740" y="470" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="628" y="418" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1dgq3y5_di" bpmnElement="SequenceFlow_1dgq3y5">
        <di:waypoint x="840" y="210" />
        <di:waypoint x="950" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="EndEvent_1nz43yd_di" bpmnElement="EndEvent_1nz43yd">
        <dc:Bounds x="1142" y="192" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1127" y="235" width="66" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_053f55r_di" bpmnElement="EndEvent_053f55r">
        <dc:Bounds x="952" y="452" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="935" y="495" width="70" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0tv9341_di" bpmnElement="SequenceFlow_0tv9341">
        <di:waypoint x="1050" y="210" />
        <di:waypoint x="1142" y="210" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0yd76ez_di" bpmnElement="SequenceFlow_0yd76ez">
        <di:waypoint x="840" y="470" />
        <di:waypoint x="952" y="470" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="ServiceTask_04yp153_di" bpmnElement="Task_0rblw0y">
        <dc:Bounds x="740" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1ta51fh_di" bpmnElement="Task_0tzv1u6">
        <dc:Bounds x="950" y="170" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1nel8qg_di" bpmnElement="StartEvent_1jqwcp1">
        <dc:Bounds x="179" y="82" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="157" y="125" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1602ln1_di" bpmnElement="SequenceFlow_1602ln1">
        <di:waypoint x="215" y="100" />
        <di:waypoint x="478" y="100" />
        <di:waypoint x="478" y="190" />
        <di:waypoint x="740" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BusinessRuleTask_0trbrc0_di" bpmnElement="ReviewTweetTask">
        <dc:Bounds x="360" y="307" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_030w69w_di" bpmnElement="Task_0468nob">
        <dc:Bounds x="740" y="430" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>

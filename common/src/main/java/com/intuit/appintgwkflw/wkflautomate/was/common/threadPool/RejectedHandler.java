package com.intuit.appintgwkflw.wkflautomate.was.common.threadPool;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * This is a custom saturation policy that adds a task back to the executors queue.
 *
 * <AUTHOR>
 */
public final class RejectedHandler implements RejectedExecutionHandler {

  @Override
  public void rejectedExecution(final Runnable runnable, final ThreadPoolExecutor executor) {

    try {
      if (!executor.isShutdown()) {
        executor.getQueue().put(runnable);
      }
    } catch (final InterruptedException e) {
      WorkflowLogger.error(
          () -> WorkflowLoggerRequest.builder()
              .message("Interrupted exception while trying to add to queue")
              .stackTrace(e)
              .className(this.getClass().getSimpleName()));
    }
  }
}

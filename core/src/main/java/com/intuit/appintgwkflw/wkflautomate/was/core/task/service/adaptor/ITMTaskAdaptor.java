package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.ITMGraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.itm.entity.graphql.TaskManagementCreateTaskMutation;
import com.intuit.itm.entity.graphql.TaskManagementTaskQuery;
import com.intuit.itm.entity.graphql.TaskManagementUpdateTaskMutation;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Invokes ITM Service for CRUD operations.
 */
@AllArgsConstructor
@Component
public class ITMTaskAdaptor {

  private ITMGraphqlClient itmGraphqlClient;

  /**
   * Creates a flat task with the ITM service
   *
   * @param taskManagementCreateTaskInput : Details of the task to be created
   * @param customHeaders: Passing the custom headers, which would contain the mandatory parameters
   *     to uniquely identify in the task service downstream
   * @param ownerId
   * @return
   */
  public TaskManagementCreateTaskMutation.Task createTask(
      TaskManagement_CreateTaskInput taskManagementCreateTaskInput,
      Map<String, String> customHeaders,
      String ownerId) {

    TaskManagementCreateTaskMutation.Task createdITMTask =
        itmGraphqlClient.createTask(taskManagementCreateTaskInput, customHeaders);

    WorkflowLogger.logInfo(
        "ITM task create successful. taskId=%s, taskName=%s, targetRealm=%s operation=CREATE_TASK",
        createdITMTask.id().toString(), createdITMTask.name(), ownerId);
    return createdITMTask;
  }

  /**
   * Updates a particular flat task with the ITM service
   *
   * @param taskManagementUpdateTaskInput : Details of the task to be updated
   * @param customHeaders: Passing the custom headers, which would contain the mandatory parameters
   *     to uniquely identify in the task service downstream
   * @param ownerId
   * @return
   */
  public TaskManagementUpdateTaskMutation.Task updateTask(
      TaskManagement_UpdateTaskInput taskManagementUpdateTaskInput,
      Map<String, String> customHeaders,
      String ownerId) {

    TaskManagementUpdateTaskMutation.Task updatedITMTask =
        itmGraphqlClient.updateTask(taskManagementUpdateTaskInput, customHeaders);

    WorkflowLogger.logInfo(
        "ITM task update successful. taskId=%s, taskName=%s, targetRealm=%s operation=UPDATE_TASK",
        updatedITMTask.id().toString(), updatedITMTask.name(), ownerId);
    return updatedITMTask;
  }

  /**
   * Deletes a particular task with the ITM service
   *
   * @param taskId : Task Id to be deleted
   * @param customHeaders: Passing the custom headers, which would contain the mandatory parameters
   *     to uniquely identify in the task service downstream
   * @param ownerId
   * @return
   */
  public void deleteTask(String taskId, Map<String, String> customHeaders, String ownerId) {

    itmGraphqlClient.deleteTask(taskId, customHeaders);

    WorkflowLogger.logInfo(
        "ITM task delete successful. taskId=%s, targetRealm=%s operation=DELETE_TASK",
        taskId, ownerId);
  }

  /**
   * Fetch a particular task with the ITM service
   *
   * @param taskId : Task Id to be deleted
   * @param customHeaders: Passing the custom headers, which would contain the mandatory parameters
   *     to uniquely identify in the task service downstream
   * @param ownerId
   * @return
   */
  public TaskManagementTaskQuery.TaskManagementTask getTask(
      String taskId, Map<String, String> customHeaders, String ownerId) {

    TaskManagementTaskQuery.TaskManagementTask itmTask =
        itmGraphqlClient.getTask(taskId, customHeaders);

    WorkflowLogger.logInfo(
        "ITM task fetch successful. taskId=%s, taskName=%s, targetRealm=%s operation=GET_TASK",
        itmTask.id().toString(), itmTask.name(), ownerId);
    return itmTask;
  }
}

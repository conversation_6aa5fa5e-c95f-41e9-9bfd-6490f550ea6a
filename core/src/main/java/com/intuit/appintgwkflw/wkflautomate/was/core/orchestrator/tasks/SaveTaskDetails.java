package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * Task for saving Bpmn, Dmn with camunda definition deployment Id and trigger details during
 * template save/update
 */
@RequiredArgsConstructor
public class SaveTaskDetails implements Task {

  private final ActivityDetailsRepository activityDetailsRepository;
  private final boolean enableTaskDefinitionSave;

  @Override
  @Transactional
  public State execute(State state) {
    if (!enableTaskDefinitionSave) {
      return state;
    }
    try {
      TemplateDetails templateDetails = state
          .getValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY);
      if (templateDetails != null) {
        List<ActivityDetail> activityDetails = state.getValue(AsyncTaskConstants.TASKS_DETAILS_KEY);
        if (!CollectionUtils.isEmpty(activityDetails)) {
          activityDetails.stream()
              .forEach(activityDetail -> activityDetail.setTemplateDetails(templateDetails));
          activityDetailsRepository.saveAll(activityDetails);
        }
      }
    } catch (Exception e) {
      WorkflowLogger
          .error(() -> WorkflowLoggerRequest.builder().className(this.getClass().getSimpleName())
              .stackTrace(e).message("Error while executing SaveTaskDetails task")
              .downstreamComponentName(DownstreamComponentName.WAS_DB)
              .downstreamServiceName(DownstreamServiceName.WAS_PRE_CANNED_TEMPLATE));
      state.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE, true);
      state.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION, e);
      state.addValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE,
          WorkflowError.TASK_DETAIL_SAVE_EXCEPTION);
      throw e;
    }
    return state;
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_DMN_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_DMN_VARIABLE_VALUE;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.EvaluateRuleResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.dmn.engine.DmnDecision;
import org.camunda.bpm.dmn.engine.DmnEngine;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableImpl;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableInputImpl;
import org.camunda.bpm.dmn.engine.impl.DmnDecisionTableOutputImpl;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class SingleDefinitionDmnEvaluator {

  private final DmnEngine juelDmnEngine;
  private final DmnEngine feelDmnEngine;

  public SingleDefinitionDmnEvaluator(
      @Qualifier(WorkflowConstants.JUEL_DMN_ENGINE) DmnEngine juelDmnEngine,
      @Qualifier(WorkflowConstants.FEEL_DMN_ENGINE) DmnEngine feelDmnEngine) {
    this.juelDmnEngine = juelDmnEngine;
    this.feelDmnEngine = feelDmnEngine;
  }

  /**
   * Method to return the collector used to convert DMN Engine response to a Camunda response.
   *
   * @return Collector
   */
  private static Collector<Entry<String, Object>, ?, Map<String, Object>> getEntryMapCollector() {
    return Collectors.toMap(
        Entry::getKey,
        valueObject ->
            EvaluateRuleResponse.builder()
                .value(valueObject.getValue())
                .type(valueObject.getValue().getClass().getSimpleName())
                .build());
  }

  /**
   * Selects the right DMN engine (JUEL or FEEL) for expression evaluation
   * if inputexpression starts with ${ -> JUEL DMN engine used
   * else FEEL DMN engine
   * @return
   */
  private Boolean checkInputForJuelExpression(List<DmnDecisionTableInputImpl> inputs) {
    return inputs.stream()
        .anyMatch(
            input ->
                input
                    .getExpression()
                    .getExpression()
                    .startsWith(WorkflowConstants.DMN_VAR_OPEN_BRACE));
  }

  private List<Map<String, Object>> evaluateDmnResponse(
      boolean isJuelDmn, DmnDecision dmnDecision, VariableMap variableMap) {
    WorkflowLogger.logInfo("step=dmn_expression_evaluation feel_expression_language=%s", !(isJuelDmn));
    DmnEngine engineToBeUsed = isJuelDmn ? juelDmnEngine : feelDmnEngine;
    return engineToBeUsed.evaluateDecisionTable(dmnDecision, variableMap).getResultList();
  }
  /**
   * Evaluates the DMN using DMN engine
   *
   * @param dmn                 byte data of the DMN
   * @param evaluateRuleRequest {@link EvaluateRuleRequest}
   * @return Result of DMN Evaluation
   */
  @Metric(name = MetricName.SDEF_EVALUATE_DMN, type = Type.APPLICATION_METRIC)
  public List<Map<String, Object>> evaluateDMN(
      final byte[] dmn, final EvaluateRuleRequest evaluateRuleRequest, Boolean noFormatVariables) {
    // The parsing of DMN is independent of the expression language used
    DmnDecision dmnDecision = juelDmnEngine.parseDecisions(BpmnProcessorUtil.readDmn(dmn)).stream()
        .findFirst().get();
    VariableMap variableMap =
        BooleanUtils.isTrue(noFormatVariables) ? getVariablesWithoutConversion(
            evaluateRuleRequest.getVariableMap())
            : getVariables(evaluateRuleRequest.getVariableMap());
    List<DmnDecisionTableInputImpl> inputs =
        ((DmnDecisionTableImpl) dmnDecision.getDecisionLogic()).getInputs();
    boolean isJuelDmn = checkInputForJuelExpression(inputs);
    List<Map<String, Object>> evaluateResponse = new ArrayList<>();
    String dmnResponseResult = WorkflowConstants.INDEX_INITIAL_VALUE;
    List<Pair> pathTakenByDmn = new ArrayList<>();

    //calculate the result for the dmn
    while(StringUtils.isNumeric(dmnResponseResult)) {
      //Put value of index inside the index column
      variableMap.put(WorkflowConstants.INDEX_COLUMN, Integer.parseInt(dmnResponseResult));
      //Currently setting JUEL behaviour; thus TRUE is passed in place of isJuelDmn
      evaluateResponse = evaluateDmnResponse(isJuelDmn, dmnDecision, variableMap);
      dmnResponseResult = String.valueOf(evaluateResponse.stream().findFirst().get()
              .values().stream().findFirst().get());
      pathTakenByDmn.add(new Pair(evaluateResponse.stream().findFirst().get().keySet().stream().findFirst().get(),
              dmnResponseResult));
    }

    WorkflowLogger.logInfo("step=DmnEvaluation status=Completed definitionId=%s dmnPathTaken=%s",
            evaluateRuleRequest.getDefinitionId(),
            pathTakenByDmn);

    logDmnTable(dmnDecision, variableMap);
    return evaluateResponse;
  }

  /**
   * Convert evaluateResponse to Response returned by camunda
   * Response from DMNEngine(evaluateResponse) : {"decisionResult" : true}
   * Response from Camunda: {"decisionresult": {"value": true, "type": "Boolean", "valueInfo": {}}
   */
  public List<Map<String, Object>> dmnResponseConvertor(List<Map<String, Object>> evaluateResponse) {

    evaluateResponse = evaluateResponse.stream()
            .map(
                    evaluateMap ->
                            evaluateMap.entrySet().stream().collect(getEntryMapCollector())).collect(
                    Collectors.toList());
    /**
     * convert the EvaluateResponse internally to a Map<String, Object> for parity downstream evaluation
     * @See checkResultValue in {@link RunTimeServiceImpl}
     */
    //TODO See if this can be merged in a more readable way with the above statement.
    evaluateResponse
            .forEach(response -> response.replaceAll((key, value) -> ObjectConverter.convertObject(
                    value, Map.class)));

    return evaluateResponse;
  }

  /**
   * Creates the variable map used to evaluate DMN from the EvaluateRequest map EvaluateRequest
   * type: {"variables": {"Amount": {"value": 123, "type": "double"}}} VariableMap would be:
   * {"Amount": 123}
   *
   * @param responseVariableMap Process variables
   * @return {@link VariableMap}
   */
  private VariableMap getVariables(final Map<String, Object> responseVariableMap) {
    VariableMap result = Variables.createVariables();
    if (Objects.isNull(responseVariableMap.get(BPMN_DMN_VARIABLES))) {
      return result;
    }
    Map<String, Object> dmnVariables = (Map<String, Object>) responseVariableMap
        .get(BPMN_DMN_VARIABLES);
    dmnVariables
        .forEach(
            (key, val) ->
                result.put(key, ((Map<String, Object>) val).get(BPMN_DMN_VARIABLE_VALUE)));
    return result;
  }

  /**
   * This will get the variables without conversion in case of DMN Handler execution
   *
   * @param responseVariableMap
   * @return
   */
  private VariableMap getVariablesWithoutConversion(final Map<String, Object> responseVariableMap) {
    return Variables.fromMap(responseVariableMap);
  }

  /**
   * @param dmnDecision
   * @param variableMap Log DMN Response based on input, output and variable value For Any
   *                    Exceptions, log it and ignore it.
   */
  @VisibleForTesting
  void logDmnTable(DmnDecision dmnDecision, VariableMap variableMap) {
    if (dmnDecision == null) {
      return;
    }
    try {
      if (dmnDecision.getDecisionLogic() instanceof DmnDecisionTableImpl) {
        DmnDecisionTableImpl dmnDecisionTable = (DmnDecisionTableImpl) dmnDecision.getDecisionLogic();
        List<DmnDecisionTableInputImpl> dmnDecisionTableInputs = dmnDecisionTable.getInputs();
        List<DmnDecisionTableOutputImpl> dmnDecisionTableOutputs = dmnDecisionTable.getOutputs();

        int dmnIndex = Math.min(dmnDecisionTableInputs.size(), dmnDecisionTableOutputs.size());
        for (int i = 0; i < dmnIndex; i++) {
          WorkflowLogger.logInfo(
              "ruleNumber=%s, expression=%s, name=%s, output=%s",
              i, dmnDecisionTableInputs.get(i).getExpression().getExpression(),
              dmnDecisionTableInputs.get(i).getName(),
              dmnDecisionTableOutputs.get(i).getOutputName());
        }
      }
    } catch (Exception e) {
      WorkflowLogger.logError("step=logDmnFailed, error=%s", e);
    }
  }

}
kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  name: rollout
spec:
  template:
    spec:
      containers:
        - args:
            - /bin/sh
            - -c
            - tail -F /app/logs/gc/gc.log
          image: docker.intuit.com/oicp/standard/base/amzn:1.1.46
          name: gc-log
          resources:
            limits:
              cpu: 20m
              memory: 16Mi
            requests:
              cpu: 10m
              memory: 16Mi
          volumeMounts:
            - mountPath: /app/logs/gc/
              name: app-logs-gc
        - name: app
          volumeMounts:
            - mountPath: /app/logs/gc/
              name: app-logs-gc
      volumes:
        - emptyDir:
            sizeLimit: 1000Mi
          name: app-logs-gc
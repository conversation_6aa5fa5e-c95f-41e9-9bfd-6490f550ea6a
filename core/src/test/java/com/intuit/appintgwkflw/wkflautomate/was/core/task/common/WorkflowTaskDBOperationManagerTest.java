package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_COMPLETE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_OPEN;
import static org.mockito.ArgumentMatchers.any;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowHumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TransactionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskDBOperationManagerTest {

  @Mock
  private ActivityProgressDetailsRepository progressDetailRepo;

  @Mock
  private TransactionDetailsRepository txnDetailRepo;

  @InjectMocks
  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @Mock
  private ActivityDetailsRepository activityDetailRepo;

  @Mock
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;

  @Test
  public void saveCreateStateInDB_success() {
    ActivityProgressDetails progressDetail = ActivityProgressDetails.builder().id("id").name("name")
        .attributes("{\"abc\":\"xyz\"}").build();
    WorkflowTaskResponse response = WorkflowTaskResponse.builder().txnId("txn1").status("created")
        .build();
    Mockito.when(txnDetailRepo.save(any(TransactionDetails.class)))
        .thenReturn(TransactionDetails.builder().txnId("txn1").status("created").build());
    Mockito.when(progressDetailRepo.save(any(ActivityProgressDetails.class)))
        .thenReturn(progressDetail);

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).taskAttributes(taskAttributes)
        .build();

    taskDBOperationManager.saveCreateStateInDB(taskRequest, progressDetail, response);
    Mockito.verify(txnDetailRepo, Mockito.times(1)).save(any(TransactionDetails.class));
    Mockito.verify(progressDetailRepo, Mockito.times(1))
        .save(any(ActivityProgressDetails.class));
  }

  /**
   * Save status=Created when skipCallback=true. To skip txnDetailRepo.save
   */
  @Test
  public void saveCreateStateInActivityProgressDB_success_sameTxnId() {

    //Add Batch Notification Task
    ActivityDetail activityDetailNotificationTask =
        ActivityDetail.builder().type(TaskType.BATCH_NOTIFICATION_TASK).build();

    ActivityProgressDetails progressDetailBNT1 = ActivityProgressDetails.builder().id("id3")
        .name("name3")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailNotificationTask)
        .build();

    ActivityProgressDetails progressDetailBNT2 = ActivityProgressDetails.builder().id("id4")
        .name("name4")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailNotificationTask)
        .build();

    List<ActivityProgressDetails> batchNotificationTaskActivityProgressDetails = new ArrayList<>();
    batchNotificationTaskActivityProgressDetails.add(progressDetailBNT1);
    batchNotificationTaskActivityProgressDetails.add(progressDetailBNT2);

    //Add Human Task
    ActivityDetail activityDetailHumanTask =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();

    ActivityProgressDetails progressDetailHT1 = ActivityProgressDetails.builder().id("id1")
        .name("name1")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailHumanTask).build();

    ActivityProgressDetails progressDetailHT2 = ActivityProgressDetails.builder().id("id2")
        .name("name2").status("CREATED")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailHumanTask).build();

    List<ActivityProgressDetails> humanTaskActivityProgressDetails = new ArrayList<>();
    humanTaskActivityProgressDetails.add(progressDetailHT1);
    humanTaskActivityProgressDetails.add(progressDetailHT2);

    List<TransactionDetails> txnDetails = new ArrayList<>();
    txnDetails.add(TransactionDetails.builder().id(1l)
        .activityProgressDetails(humanTaskActivityProgressDetails).build());
    txnDetails.add(TransactionDetails.builder().id(2l)
        .activityProgressDetails(batchNotificationTaskActivityProgressDetails).build());

    Mockito.when(txnDetailRepo.findByTxnId(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(txnDetails));

    Mockito.when(progressDetailRepo.save(any(ActivityProgressDetails.class)))
        .thenReturn(progressDetailHT2);

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes
        .put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, TaskType.HUMAN_TASK.name());

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().txnId("txn1")
        .skipCallback(true).taskType(TaskType.HUMAN_TASK)
        .build();

    ActivityProgressDetails progressDetailResponse = taskDBOperationManager
        .saveCreateStateInActivityProgressDB(progressDetailHT2, taskRequest);
    Assert.assertEquals(1l, progressDetailResponse.getTxnDetails().getId().intValue());
    Mockito.verify(txnDetailRepo, Mockito.times(1)).findByTxnId(Mockito.anyString());
    Mockito.verify(txnDetailRepo, Mockito.never()).save(any(TransactionDetails.class));
    Mockito.verify(progressDetailRepo, Mockito.times(1))
        .save(any(ActivityProgressDetails.class));
  }


  /**
   * Save status=Created when skipCallback=true. To skip txnDetailRepo.save
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void saveCreateStateInActivityProgressDB_txnTaskType_MatchFailed() {

    //Add Batch Notification Task
    ActivityDetail activityDetailNotificationTask =
        ActivityDetail.builder().type(TaskType.BATCH_NOTIFICATION_TASK).build();

    ActivityProgressDetails progressDetailBNT1 = ActivityProgressDetails.builder().id("id3")
        .name("name3")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailNotificationTask)
        .build();

    ActivityProgressDetails progressDetailBNT2 = ActivityProgressDetails.builder().id("id4")
        .name("name4")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailNotificationTask)
        .build();

    List<ActivityProgressDetails> batchNotificationTaskActivityProgressDetails = new ArrayList<>();
    batchNotificationTaskActivityProgressDetails.add(progressDetailBNT1);
    batchNotificationTaskActivityProgressDetails.add(progressDetailBNT2);

    //Add Human Task
    ActivityDetail activityDetailHumanTask =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();

    ActivityProgressDetails progressDetailHT2 = ActivityProgressDetails.builder().id("id2")
        .name("name2").status("CREATED")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailHumanTask).build();

    List<TransactionDetails> txnDetails = new ArrayList<>();
    txnDetails.add(TransactionDetails.builder()
        .activityProgressDetails(batchNotificationTaskActivityProgressDetails).build());

    Mockito.when(txnDetailRepo.findByTxnId(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(txnDetails));

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes
        .put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, TaskType.HUMAN_TASK.name());
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().txnId("txn1").skipCallback(true)
        .taskType(TaskType.HUMAN_TASK)
        .build();

    taskDBOperationManager.saveCreateStateInActivityProgressDB(progressDetailHT2, taskRequest);
  }


  /**
   * Save status=Created when skipCallback=true. To skip txnDetailRepo.save
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void saveCreateStateInActivityProgressDB_txnNotFound() {

    //Add Human Task
    ActivityDetail activityDetailHumanTask =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();

    ActivityProgressDetails progressDetailHT2 = ActivityProgressDetails.builder().id("id2")
        .name("name2").status("CREATED")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailHumanTask).build();

    Mockito.when(txnDetailRepo.findByTxnId(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes
        .put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, TaskType.HUMAN_TASK.name());
    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().txnId("txn1").skipCallback(true)
        .build();

    taskDBOperationManager.saveCreateStateInActivityProgressDB(progressDetailHT2, taskRequest);
  }

  /**
   * Save status=Created when skipCallback=true. To skip txnDetailRepo.save
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void saveCreateStateInActivityProgressDB_taskTypeNotPresent() {

    //Add Human Task
    ActivityDetail activityDetailHumanTask = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();

    ActivityProgressDetails progressDetailHT2 = ActivityProgressDetails.builder().id("id2")
        .name("name2").status("CREATED")
        .attributes("{\"abc\":\"xyz\"}").activityDefinitionDetail(activityDetailHumanTask).build();

    List<TransactionDetails> txnDetails = new ArrayList<>();
    txnDetails.add(TransactionDetails.builder()
        .activityProgressDetails(new ArrayList<>()).build());

    Mockito.when(txnDetailRepo.findByTxnId(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(txnDetails));

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().txnId("txn1")
        .skipCallback(true)
        .build();

    taskDBOperationManager.saveCreateStateInActivityProgressDB(progressDetailHT2, taskRequest);
  }

  @Test(expected = RuntimeException.class)
  public void saveCreateStateInDB_failure() {
    ActivityProgressDetails progressDetail = ActivityProgressDetails.builder().id("id").name("name")
        .attributes("{\"abc\":\"xyz\"}").build();
    WorkflowTaskResponse response = WorkflowTaskResponse.builder().txnId("txn1").status("created")
        .build();

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).taskAttributes(taskAttributes)
        .build();
    Mockito.when(txnDetailRepo.save(any(TransactionDetails.class)))
        .thenThrow(new RuntimeException());
    taskDBOperationManager.saveCreateStateInDB(taskRequest, progressDetail, response);
  }

  @Test
  public void test_prepareActivityProgressAndSave() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).taskAttributes(taskAttributes).build();

    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionDetails defDetail = DefinitionDetails.builder().templateDetails(templateDetails)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(defDetail).build();

    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDetails)
        .build();

    Mockito.when(activityDetailRepo
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString()))
        .thenReturn(Optional.of(activityDetail));

    Mockito.when(progressDetailRepo.save(any(ActivityProgressDetails.class)))
        .thenReturn(ActivityProgressDetails.builder().build());

    taskDBOperationManager.prepareActivityProgressAndSaveInOpenState(taskRequest, processDetails);

    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    Mockito.verify(progressDetailRepo, Mockito.times(1))
        .save(any(ActivityProgressDetails.class));

  }


  public void test_prepareAttributeMap() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).taskAttributes(taskAttributes)
        .build();

    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionDetails defDetail = DefinitionDetails.builder().templateDetails(templateDetails)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(defDetail).build();

    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDetails)
        .build();

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .activityDefinitionDetail(activityDetail)
        .id(taskRequest.getId()).processDetails(processDetails).status(TASK_STATUS_OPEN)
        .name(taskRequest.getActivityName()).build();

    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributeMap", taskRequest,
            activityProgressDetails);
    Assert.assertNotNull(attributes);
    Map<String, Object> attributeMap = ObjectConverter
        .convertObject(attributes, new TypeReference<Map<String, Object>>() {
        });
    Assert.assertNotNull(attributeMap.get("txnMode"));
    Assert.assertNull(attributeMap.get("activityId"));
  }


  @Test(expected = WorkflowGeneralException.class)
  public void test_prepareActivityProgressAndSave_fail() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).taskAttributes(taskAttributes)
        .build();

    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionDetails defDetail = DefinitionDetails.builder().templateDetails(templateDetails)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(defDetail).build();

    Mockito.when(activityDetailRepo
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString()))
        .thenReturn(Optional.ofNullable(null));

    taskDBOperationManager.prepareActivityProgressAndSaveInOpenState(taskRequest, processDetails);

  }

  @Test
  public void markCompleteInDB_success() {
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, new WorkflowHumanTask(null));
    // Add Human Task
    ActivityDetail activityDetailHumanTask =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();

    ActivityProgressDetails progressDetailHT2 =
        ActivityProgressDetails.builder()
            .id("id2")
            .name("name2")
            .status("CREATED")
            .attributes("{\"abc\":\"xyz\"}")
            .activityDefinitionDetail(activityDetailHumanTask)
            .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").status("CREATED").build())
            .build();

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes =
        TaskAttributes.builder()
            .modelAttributes(modelAttributes)
            .variables(runtimeAttributes)
            .runtimeAttributes(runtimeAttributes)
            .build();

    Task taskRequest =
        HumanTask.builder()
            .activityId("actId")
            .activityName("actName")
            .id("id")
            .customerId("customer1")
            .type(TaskType.HUMAN_TASK)
            .txnId("txn1")
            .txnMode("ASYNC")
            .createdBy("TaskMANAGER")
            .taskAttributes(taskAttributes)
            .build();

    taskDBOperationManager.markTxnDetailAndActivityProgressCompleteInDB(
        taskRequest, progressDetailHT2, WorkflowTaskResponse.builder().status("Complete").build());
    Mockito.verify(txnDetailRepo, Mockito.times(1)).save(Mockito.any(TransactionDetails.class));

    Mockito.verify(progressDetailRepo, Mockito.times(1)).save(any(ActivityProgressDetails.class));
  }

  @Test
  public void markActivityProgressCompleteInDB_success() {

    // Add Human Task
    ActivityDetail activityDetailHumanTask = ActivityDetail.builder().type(TaskType.HUMAN_TASK)
        .build();

    ActivityProgressDetails progressDetailHT2 = ActivityProgressDetails.builder().id("id2")
        .name("name2")
        .status("CREATED").attributes("{\"abc\":\"xyz\"}")
        .activityDefinitionDetail(activityDetailHumanTask)
        .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").status("CREATED").build())
        .build();

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes = TaskAttributes.builder()
        .modelAttributes(modelAttributes).variables(runtimeAttributes)
        .runtimeAttributes(runtimeAttributes)
        .build();

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).status(TASK_STATUS_COMPLETE)
        .taskAttributes(taskAttributes).build();

    taskDBOperationManager.markActivityProgressCompleteInDB(taskRequest, progressDetailHT2);
    Mockito.verify(txnDetailRepo, Mockito.never()).save(any(TransactionDetails.class));

    Mockito.verify(progressDetailRepo, Mockito.times(1))
        .save(any(ActivityProgressDetails.class));

  }

  @Test
  public void updateStatusInDB_success() {
    // Add Human Task
    ActivityDetail activityDetailHumanTask =
        ActivityDetail.builder().type(TaskType.HUMAN_TASK).build();
    ActivityProgressDetails progressDetailHT2 =
        ActivityProgressDetails.builder()
            .id("id2")
            .name("name2")
            .status("blocked")
            .attributes("{\"abc\":\"xyz\"}")
            .activityDefinitionDetail(activityDetailHumanTask)
            .txnDetails(TransactionDetails.builder().id(1l).txnId("txn1").status("blocked").build())
            .attributes("{\"abc\":\"def\"}")
            .build();

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    TaskAttributes taskAttributes =
        TaskAttributes.builder()
            .modelAttributes(modelAttributes)
            .variables(runtimeAttributes)
            .runtimeAttributes(runtimeAttributes)
            .build();

    Task taskRequest =
        HumanTask.builder()
            .activityId("actId")
            .activityName("actName")
            .id("id")
            .customerId("customer1")
            .txnId("txn1")
            .txnMode("ASYNC")
            .createdBy("TaskMANAGER")
            .type(TaskType.HUMAN_TASK)
            .status(TASK_STATUS_COMPLETE)
            .taskAttributes(taskAttributes)
            .build();

    Mockito.when(progressDetailRepo.saveAndFlush(any(ActivityProgressDetails.class)))
        .thenReturn(progressDetailHT2);
    Mockito.when(txnDetailRepo.save(any(TransactionDetails.class)))
        .thenReturn(TransactionDetails.builder().id(1l).txnId("txn1").status("CREATED").build());

    taskDBOperationManager.updateStatusInDB(taskRequest, progressDetailHT2);

    Mockito.verify(progressDetailRepo, Mockito.times(1)).saveAndFlush(any(ActivityProgressDetails.class));

    Mockito.verify(txnDetailRepo, Mockito.times(1)).save(any(TransactionDetails.class));
  }

  private TaskAttributes taskAttributes() {
    Map<String, Object> runtimeDefAttributes = new HashMap<>();
    runtimeDefAttributes.put("journalNo", "${journalNo}");
    runtimeDefAttributes.put("assigneeId", "${expertId}");
    runtimeDefAttributes.put("customerId", "${userId}");

    Map<String, String> modelDefAttributes = new HashMap<>();
    modelDefAttributes.put("visibility", "true");
    modelDefAttributes.put("estimate", "3");
    modelDefAttributes.put("taskType", "QB_INVOICE_APPROVAL");

    Map<String, Object> variables = new HashMap<>();
    variables.put("journalNo", "j#150");
    variables.put("abc", "def");
    variables.put("customerId", "12345678");
    variables.put("assigneeId", "assignId");

    return TaskAttributes.builder().modelAttributes(modelDefAttributes)
        .runtimeAttributes(runtimeDefAttributes).variables(variables).build();
  }

  @SuppressWarnings("rawtypes")
  @Test
  public void test_attributes() {
    HumanTask humamTask = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .estimate(3).type(TaskType.HUMAN_TASK)
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .taskAttributes(taskAttributes()).build();

    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes", humamTask);
    Map<String, Object> attributesMap =
        ObjectConverter.fromJson(attributes, new TypeReference<Map<String, Object>>() {
        });

    Assert.assertTrue(attributesMap.containsKey(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES));
    Map runtimeAttrMap = (Map) attributesMap.get(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES);
    Assert.assertEquals(humamTask.getTaskAttributes().getRuntimeAttributes().keySet().size(),
        runtimeAttrMap.size());
    humamTask.getTaskAttributes().getRuntimeAttributes().keySet().stream().forEach(runtimeAttrKey ->
        Assert.assertTrue(runtimeAttrMap.containsKey(runtimeAttrKey)));

  }

  @SuppressWarnings("rawtypes")
  @Test
  public void test_attributes_noRuntime() {
    HumanTask humamTask = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .estimate(3).type(TaskType.HUMAN_TASK)
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .taskAttributes(taskAttributes()).build();
    humamTask.getTaskAttributes().setRuntimeAttributes(null);

    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes", humamTask);
    Map<String, Object> attributesMap =
        ObjectConverter.fromJson(attributes, new TypeReference<Map<String, Object>>() {
        });
    Assert.assertTrue(attributesMap.containsKey(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES));
    Assert.assertEquals(0,
        ((Map) attributesMap.get(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES)).size());
  }


  @SuppressWarnings("rawtypes")
@Test
  public void test_attributes_noVariables() {
    HumanTask humamTask = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .estimate(3).type(TaskType.HUMAN_TASK)
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .taskAttributes(taskAttributes()).build();
    humamTask.getTaskAttributes().setVariables(null);

    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes", humamTask);
    Map<String, Object> attributesMap =
        ObjectConverter.fromJson(attributes, new TypeReference<Map<String, Object>>() {
        });
    Assert.assertTrue(attributesMap.containsKey(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES));
    Assert.assertEquals(0,
        ((Map) attributesMap.get(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES)).size());
  }

  @SuppressWarnings("unchecked")
  @Test
  public void test_mutationAttributes() {
    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes("{\n"
            + "  \"runtimeAttributes\": {\n"
            + "    \"journalNo\": \"j#150\",\n"
            + "    \"assigneeId\": \"Assignee150\",\n"
            + "    \"customerId\": \"User149\",\n"
            + "    \"isVisibility\": false\n"
            + "  }\n"
            + "}")
        .build();

    Map<String, Object> variables = new HashMap<>();
    variables.put("journalNo", "j#150");
    variables.put("abc", "def");
    variables.put("isVisibility", false);
    variables.put("assigneeId", "Assignee149");

    TaskAttributes taskAttributes = TaskAttributes.builder().variables(variables).build();
    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes",
            Task.builder().taskAttributes(taskAttributes).build(), progressDetails);
    Map<String, Object> attributesMap = ObjectConverter.fromJson(attributes,
        new TypeReference<Map<String, Object>>() {
        });
    Map<String, Object> runtimeAttributes = (Map<String, Object>) attributesMap
        .get("runtimeAttributes");
    Assert.assertEquals(5, runtimeAttributes.size());
    Assert.assertTrue(runtimeAttributes.containsKey("journalNo"));
    Assert.assertEquals("j#150", runtimeAttributes.get("journalNo"));
    Assert.assertTrue(runtimeAttributes.containsKey("abc"));
    Assert.assertEquals("def", runtimeAttributes.get("abc"));
    Assert.assertTrue(runtimeAttributes.containsKey("isVisibility"));
    Assert.assertFalse((boolean) runtimeAttributes.get("isVisibility"));
    Assert.assertTrue(runtimeAttributes.containsKey("customerId"));
    Assert.assertEquals("User149", runtimeAttributes.get("customerId"));
    Assert.assertTrue(runtimeAttributes.containsKey("assigneeId"));
    Assert.assertEquals("Assignee149", runtimeAttributes.get("assigneeId"));
  }


  @SuppressWarnings("unchecked")
  @Test
  public void test_mutationAttributes_noVariables() {
    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes("{\n"
            + "  \"runtimeAttributes\": {\n"
            + "    \"journalNo\": \"j#150\",\n"
            + "    \"assigneeId\": \"Assignee150\",\n"
            + "    \"customerId\": \"User149\",\n"
            + "    \"isVisibility\": false\n"
            + "  }\n"
            + "}")
        .build();

    TaskAttributes taskAttributes = TaskAttributes.builder().variables(null).build();
    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes",
            Task.builder().taskAttributes(taskAttributes).build(), progressDetails);
    Map<String, Object> attributesMap = ObjectConverter.fromJson(attributes,
        new TypeReference<Map<String, Object>>() {
        });
    Map<String, Object> runtimeAttributes = (Map<String, Object>) attributesMap
        .get("runtimeAttributes");
    Assert.assertEquals(4, runtimeAttributes.size());
    Assert.assertTrue(runtimeAttributes.containsKey("journalNo"));
    Assert.assertEquals("j#150", runtimeAttributes.get("journalNo"));
    Assert.assertTrue(runtimeAttributes.containsKey("isVisibility"));
    Assert.assertFalse((boolean) runtimeAttributes.get("isVisibility"));
    Assert.assertTrue(runtimeAttributes.containsKey("customerId"));
    Assert.assertEquals("User149", runtimeAttributes.get("customerId"));
    Assert.assertTrue(runtimeAttributes.containsKey("assigneeId"));
    Assert.assertEquals("Assignee150", runtimeAttributes.get("assigneeId"));
  }


  @SuppressWarnings("unchecked")
  @Test
  public void test_mutationAttributes_noSavedAttribute() {
    ActivityProgressDetails progressDetails = ActivityProgressDetails.builder()
        .attributes(null)
        .build();

    Map<String, Object> variables = new HashMap<>();
    variables.put("journalNo", "j#150");
    variables.put("abc", "def");
    variables.put("isVisibility", false);
    variables.put("assigneeId", "Assignee149");

    TaskAttributes taskAttributes = TaskAttributes.builder().variables(variables).build();
    String attributes = ReflectionTestUtils
        .invokeMethod(taskDBOperationManager, "prepareAttributes",
            Task.builder().taskAttributes(taskAttributes).build(), progressDetails);
    Map<String, Object> attributesMap = ObjectConverter.fromJson(attributes,
        new TypeReference<Map<String, Object>>() {
        });
    Map<String, Object> runtimeAttributes = (Map<String, Object>) attributesMap
        .get("runtimeAttributes");
    Assert.assertEquals(4, runtimeAttributes.size());
    Assert.assertTrue(runtimeAttributes.containsKey("journalNo"));
    Assert.assertEquals("j#150", runtimeAttributes.get("journalNo"));
    Assert.assertTrue(runtimeAttributes.containsKey("abc"));
    Assert.assertEquals("def", runtimeAttributes.get("abc"));
    Assert.assertTrue(runtimeAttributes.containsKey("isVisibility"));
    Assert.assertFalse((boolean) runtimeAttributes.get("isVisibility"));
    Assert.assertTrue(runtimeAttributes.containsKey("assigneeId"));
    Assert.assertEquals("Assignee149", runtimeAttributes.get("assigneeId"));
  }

  @Test
  public void test_markActivityProgressFailedInDB() {
    Task taskRequest = Task.builder().status(TASK_STATUS_FAILED).build();
    ActivityProgressDetails progressDtls = ActivityProgressDetails.builder().id("ext1").build();
    Mockito.when(progressDetailRepo.save(progressDtls)).thenReturn(progressDtls);
    taskDBOperationManager.markActivityProgressFailedInDB(taskRequest, progressDtls);
    Mockito.verify(progressDetailRepo, Mockito.times(1)).save(progressDtls);
  }

  @Test
  public void test_markTxnDetailsFailedInDB() {
    Task taskRequest = Task.builder().status(TASK_STATUS_FAILED).build();
    TransactionDetails txnDetails = TransactionDetails.builder().id(1l).txnId("txn1").build();
    Mockito.when(txnDetailRepo.save(txnDetails)).thenReturn(txnDetails);
    taskDBOperationManager.markTxnDetailsFailedInDB(taskRequest, txnDetails);
    Mockito.verify(txnDetailRepo, Mockito.times(1)).save(txnDetails);
  }


  @Test
  public void test_prepareActivityProgressAndSaveInFailedState() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    Task taskRequest = HumanTask.builder().activityId("actId")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).build())
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK).build();
    
    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionDetails defDetail = DefinitionDetails.builder().templateDetails(templateDetails)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(defDetail).build();
    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDetails)
        .build();
    Mockito.when(activityDetailRepo
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString()))
        .thenReturn(Optional.of(activityDetail));
    Mockito.when(progressDetailRepo.save(any(ActivityProgressDetails.class)))
        .thenReturn(ActivityProgressDetails.builder().build());
    taskDBOperationManager.prepareActivityProgressAndSaveInFailedState(taskRequest, processDetails);
    Mockito.verify(progressDetailRepo, Mockito.times(1)).save(any());
  }


  @Test(expected = WorkflowNonRetriableException.class)
  public void test_prepareActivityProgressAndSaveInFailedState_failed() {
    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("journalNo", "j#56");
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put("visibility", "true");

    Task taskRequest = HumanTask.builder().activityId("actId")
        .activityName("actName").id("id").customerId("customer1")
        .txnId("txn1").txnMode("ASYNC").createdBy("TaskMANAGER")
        .type(TaskType.HUMAN_TASK)
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).build()).build();
    
    TemplateDetails templateDetails = TemplateDetails.builder().build();
    DefinitionDetails defDetail = DefinitionDetails.builder().templateDetails(templateDetails)
        .build();
    ProcessDetails processDetails = ProcessDetails.builder().definitionDetails(defDetail).build();
    Mockito.when(activityDetailRepo
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString()))
        .thenReturn(Optional.empty());
    taskDBOperationManager.prepareActivityProgressAndSaveInFailedState(taskRequest, processDetails);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.CompositeStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.NonCompositeStepBuilder;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
class CompositeStepBuilderFactoryTest {

  @Mock
  private CompositeStepBuilder compositeStepBuilder;

  @Mock
  private NonCompositeStepBuilder nonCompositeStepBuilder;

  @InjectMocks
  private CompositeStepBuilderFactory compositeStepBuilderFactory;

  private RuleLine ruleLine;
  private RuleLine.Rule rule;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.initMocks(this);
    ruleLine = new RuleLine();
    rule = new Rule();
    ruleLine.setRules(List.of(rule));
  }

  @Test
  void testCompositeStepBuilder() {
    rule.setParameterType(FieldTypeEnum.DAYS);
    Assertions.assertEquals(compositeStepBuilder, compositeStepBuilderFactory.getHandler(ruleLine));
  }

  @Test
  void testNonCompositeBuilder() {
    rule.setParameterType(FieldTypeEnum.DOUBLE);
    Assertions.assertEquals(nonCompositeStepBuilder,
        compositeStepBuilderFactory.getHandler(ruleLine));
  }
}
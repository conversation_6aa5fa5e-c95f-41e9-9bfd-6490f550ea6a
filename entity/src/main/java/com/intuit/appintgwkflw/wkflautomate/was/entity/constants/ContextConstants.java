package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

import lombok.experimental.UtilityClass;

/** <AUTHOR> */
@UtilityClass
public class ContextConstants {

  public final String IAM_AUTHENTICATION_TYPE = "Intuit_IAM_Authentication";
  public final String TOKEN_TYPE_KEY = "intuit_token_type";
  public final String APP_SECRET_KEY = "intuit_app_secret";
  public final String APP_ID_KEY = "intuit_appid";
  public final String TOKEN_KEY = "intuit_token";
  public final String IAM_TICKET_TYPE = "IAM-Ticket";
  public final String IAM_OFFLINE_TICKET_TYPE = "IAM-Offline-Ticket";
  public final String USERID_KEY = "intuit_userid";
  public final String REALMID_KEY = "intuit_realmid";
}

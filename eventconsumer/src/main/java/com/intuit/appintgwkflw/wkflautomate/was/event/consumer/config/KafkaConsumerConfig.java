package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.SimpleEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventRetryHandler;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.retry.support.RetryTemplate;

/**
 * Kafka consumer factory. This class has all the configuration used by the kafka consumer.
 *
 * <AUTHOR>
 */
@EnableKafka
@Configuration
@AllArgsConstructor
@ConditionalOnExpression("${event.consumer.enabled:false}")
public class KafkaConsumerConfig {

  private EventConfiguration eventConfiguration;
  private SimpleEventPublisher kafkaProducer;
  private ConsumerFactory<String, Result<FormatException, String>> consumerFactory;
  private RetryTemplate retryTemplate;
  private EventRetryHandler eventRetryHandler;


    /**
     * Events that are either processed but are faced with a retryable exception while processing
     * or
     * those events that are not even processed due to an open circuit are
     * pushed to the DLQ for re-processing.
     */
  @Bean("kafkaListenerFactory")
  public ConcurrentKafkaListenerContainerFactory<String, Result<FormatException, String>>
      kafkaListenerContainerFactory() {

    ConcurrentKafkaListenerContainerFactory<String, Result<FormatException, String>> factory =
        new ConcurrentKafkaListenerContainerFactory<>();
    factory.setConsumerFactory(consumerFactory);
    factory.setConcurrency(eventConfiguration.getConsumer().getConcurrency());
    // with MANUAL ackmode, the offsets are committed when all the results from the poll have been
    // processed. With MANUAL_IMMEDIATE mode, the offsets are committed one-by-one.
    factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);

    factory.setRetryTemplate(retryTemplate);
    factory.setRecoveryCallback(
        context -> {
            eventRetryHandler.handleRetryFailure(
              context,
              ((consumerRecord, headers) -> {
                if (eventRetryHandler.isRetryableException(context)
                        || eventRetryHandler.isWorkflowCircuitOpenException(context)) {
                  kafkaProducer.send(
                      consumerRecord.value().get(),
                      headers,
                      consumerRecord.topic().concat(KafkaConstants.KAFKA_DLQ_SUFFIX));
                }
              }));
          return Optional.empty();
        });

    // Stopping consumers from auto-starting for DR
    factory.setAutoStartup(false);
    return factory;
  }
}

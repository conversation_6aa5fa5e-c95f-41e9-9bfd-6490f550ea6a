package com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskLog;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ProcessVariableDetailsResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowHistoryResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.GetProcessDetailsRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;
import java.util.List;
import java.util.Map;

/**
 * This defines the REST APIs for interacting with the history endpoints of the BPMN Engine.
 */
public interface BPMNEngineHistoryServiceRest {

  /**
   * Gets the details of a specific process instance.
   *
   * @param getProcessDetailsRequest the request object
   * @return the process details
   */
  WASHttpResponse<WorkflowHistoryResponse> getProcessDetails(
      GetProcessDetailsRequest getProcessDetailsRequest);
  
  /**
   * Gets the details of a process variables based on filter criteria.
   *
   * @param processVariableDetailsRequest the request object
   * @return the list of process variable details
   */
  WASHttpResponse<List<ProcessVariableDetailsResponse>> getProcessVariableDetails(
			ProcessVariableDetailsRequest processVariableDetailsRequest);
  
  /**
   * Gets the external task log history based on filter criteria.
   * 
   * @param externalTaskLogRequest the request object.
   * @return the list of externalTask logs.
   */
  public WASHttpResponse<List<ExternalTaskLog>> getExternalTaskLogs(
		  ExternalTaskLogRequest externalTaskLogRequest);

    /**
     * Gets the count of the number of times an external task has been executed based on filter criteria.
     *
     * @param externalTaskLogRequest the request object.
     * @return the count of external task executions
     */
    WASHttpResponse<Map<String, Integer>> getExternalTaskCount(
            ExternalTaskLogRequest externalTaskLogRequest);

}

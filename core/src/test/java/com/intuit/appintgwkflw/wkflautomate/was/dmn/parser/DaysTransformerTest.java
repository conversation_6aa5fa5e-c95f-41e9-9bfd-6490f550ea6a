package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DaysTransformerTest {

  private DaysTransformer daysTransformer;
  private final static String NUMBER = "zero";

  String parameterType = "Days";

  @Before
  public void init() {
    daysTransformer = new DaysTransformer();
  }

  @Test
  public void testDataType() {
    String dataType = daysTransformer.getDataType();
    Assert.assertNotNull(dataType);
    Assert.assertEquals(Integer.class.getSimpleName(), dataType);
  }

  @Test
  public void testGetName() {
    DMNSupportedOperator dmnSupportedOperator = daysTransformer.getName();
    Assert.assertNotNull(dmnSupportedOperator);
    Assert.assertEquals(DMNSupportedOperator.DAYS, dmnSupportedOperator);
  }

  @Test
  public void testDefaultRuleBeforeFalse() {
    String defaultRule = daysTransformer.defaultRule("TxnDueDays","-3");
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("BF 3", defaultRule);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testConvertToIntException() {
    daysTransformer.defaultRule("TxnDueDays",NUMBER);
  }

  @Test
  public void testDefaultRuleAfter() {
    String defaultRule = daysTransformer.defaultRule("TxnDueDays","3");
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("AF 3", defaultRule);
  }

  @Test
  public void testDefaultRuleOn() {
    String defaultRule = daysTransformer.defaultRule("TxnDueDays","-0");
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("ON 0", defaultRule);
  }

  @Test
  public void testRuleUIBefore_FeelExprFalse() {
    String parameterName= "TxnDueDays";
    String ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == -3",parameterName);
    Assert.assertNotNull(ruleExpr);
    Assert.assertEquals("BF 3", ruleExpr);
  }

  @Test
  public void testRuleUIBefore_FeelExprTrue() {
    String parameterName= "TxnDueDays";
    String ruleExpr = daysTransformer.transformToUserFriendlyExpression("-3", parameterName);
    Assert.assertEquals("BF 3", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("-3", parameterName);
    Assert.assertEquals("BF 3", ruleExpr);

    //test expr contains parameter name
    ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == -3", parameterName);
    Assert.assertEquals("BF 3", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == -3", parameterName);
    Assert.assertEquals("BF 3", ruleExpr);
  }

  @Test
  public void testRuleUIAfter() {
    String parameterName= "TxnDueDays";
    String ruleExpr = daysTransformer.transformToUserFriendlyExpression("3", parameterName);
    Assert.assertEquals("AF 3", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("3", parameterName);
    Assert.assertEquals("AF 3", ruleExpr);

    //test expr contains parameter name
    ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == 3", parameterName);
    Assert.assertEquals("AF 3", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == 3", parameterName);
    Assert.assertEquals("AF 3", ruleExpr);
  }


  @Test
  public void testRuleUIOn() {
    String parameterName= "TxnDueDays";
    String ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == 0",parameterName);
    Assert.assertEquals("ON 0", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("TxnDueDays == 0",parameterName);
    Assert.assertEquals("ON 0", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("0",parameterName);
    Assert.assertEquals("ON 0", ruleExpr);

    ruleExpr = daysTransformer.transformToUserFriendlyExpression("0",parameterName);
    Assert.assertEquals("ON 0", ruleExpr);
  }

  @Test
  public void testRuleUIEmpty() {
    String parameterName= "TxnAmount";
    String ruleExpr = daysTransformer.transformToUserFriendlyExpression("",parameterName);
    Assert.assertEquals("", ruleExpr);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testRuleUIInvalid() {
    String parameterName= "TxnAmount";
    daysTransformer.transformToUserFriendlyExpression("Amount && 1",parameterName);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testTransformRulesForDMNInvalid() {
    daysTransformer.transformToDmnFriendlyExpression("", "Due Date", parameterType, false);
  }

  @Test
  public void testTransformRulesForDMNBefore() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("BF 3", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == -3", defaultRule);
  }

  @Test
  public void testTransformRulesForDMNAfter() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("AF 3", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == 3", defaultRule);
  }

  @Test
  public void testTransformRulesForDMNOn() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("ON 0", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == 0", defaultRule);
  }

  @Test
  public void testTransformRulesForDMNBefore_ScalaFeelTrue() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("BF 3", "DueDate", parameterType, true);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("-3", defaultRule);
  }
  @Test
  public void testTransformRulesForDMNBefore_ScalaFeelFalse() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("BF 3", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == -3", defaultRule);
  }

  @Test
  public void testTransformRulesForDMNAfter_ScalaFeelTrue() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("AF 3", "DueDate", parameterType, true);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("3", defaultRule);
  }
  @Test
  public void testTransformRulesForDMNAfter_ScalaFeelFalse() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("AF 3", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == 3", defaultRule);
  }

  @Test
  public void testTransformRulesForDMNOn_ScalaFeelTrue() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("ON 0", "DueDate", parameterType, true);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("0", defaultRule);
  }
  @Test
  public void testTransformRulesForDMNOn_ScalaFeelFalse() {
    String defaultRule = daysTransformer.transformToDmnFriendlyExpression("ON 0", "DueDate", parameterType, false);
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("DueDate == 0", defaultRule);
  }

  @Test
  public void testDefaultRuleEveryday() {
    String defaultRule = daysTransformer.defaultRule("EveryDay","1");
    Assert.assertNotNull(defaultRule);
    Assert.assertEquals("ALL 1", defaultRule);
  }



}
l1: appintgwkflw
l2: wkflautomate
git_org: appintgwkflw-wkflautomate
#

service_name: was-event-in
asset_id: 4110678153014047831
asset_alias: Intuit.appintgwkflw.wkflautomate.waseventin
code_repo: github.intuit.com/appintgwkflw-wkflautomate/was-event-in.git
deploy_repo: github.intuit.com/appintgwkflw-wkflautomate/was-event-in-deployment.git
template: JavaSpringMVC
argocd_server: qbo.argocd.tools-k8s-prd.a.intuit.com:443
argocd_project: appintgwkflw-wkflautomate-was-event-in
argocd_project_enabled: true
#


registry: docker.intuit.com
repo: appintgwkflw-wkflautomate/was-event-in/service
## change this to false once ready for PRD
preprodOnly: false
## change this to true for enable Code Analysis stage
SonarQubeAnalysis: false
## change this to true for stop your pipeline in quality gate error
SonarQubeEnforce: false
## change this to true for enabling Jira stories to be transitioned
enableJiraTransition: false
## enable this to true once test suite has matured in confidence to help automate deployments
enableScorecardReadinessCheck: true
## to bypass the scorecard readiness check's fall back, gitOpsApproval, set this to true
skipScorecardManualFallback: false
enableBuildQualityMetrics: true
enableDeployQualityMetrics: true
enableIntegrationTest: false
kubernetesServiceName: was-event-in-desired-service
environments:
  qal-usw2-eks:
    ingress_endpoint: https://appintgwkflw-qal-was-event-in.sbgqboppdusw2.iks2.a.intuit.com
    jacoco_endpoint: https://appintgwkflw-qal-was-event-in-jacoco.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflautomate-waseventin-usw2-qal
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
  e2e-usw2-eks:
    ingress_endpoint: https://appintgwkflw-e2e-was-event-in.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflautomate-waseventin-usw2-e2e
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
  prf-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prf-was-event-in.sbgqboppdusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-ppd-usw2-k8s-65434b49d0262f59.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflautomate-waseventin-usw2-prf
    region: usw2
    iks_type: ppd
    manifest_format: Kustomize
  #   stg-usw2-eks:
  #     ingress_endpoint: https://appintgwkflw-stg-was-event-in.sbgqboprodusw2.iks2.a.intuit.com
  #     cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
  #     namespace: appintgwkflw-wkflautomate-waseventin-usw2-stg
  #     region: usw2
  #     iks_type: prd
  #     manifest_format: Kustomize
  prd-usw2-eks:
    ingress_endpoint: https://appintgwkflw-prd-was-event-in.sbgqboprodusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflautomate-waseventin-usw2-prd
    region: usw2
    iks_type: prd
    manifest_format: Kustomize
  stg-usw2-eks:
    ingress_endpoint: https://appintgwkflw-stg-was-event-in.sbgqboprodusw2.iks2.a.intuit.com
    cluster: https://eksapi-sbg-qbo-prod-usw2-k8s-c679f887bdf1e9b2.elb.us-west-2.amazonaws.com
    namespace: appintgwkflw-wkflautomate-waseventin-usw2-stg
    region: usw2
    iks_type: prd
    manifest_format: Kustomize
api:
  apiName: was-event-in
  groupId: com.intuit.was-event-in
  artifactId: was-event-in
  showApiReference: 'false'
  specLocation: app/api/openapi/openapi.yaml
  convert:
    skip: 'true'
  lint:
    skip: 'true'
  generateAndPublishClient:
    skip: 'true'
    language: java
  generateAndPublishServer:
    skip: 'true'
    language: java
  publishMockDocker:
    skip: 'false'


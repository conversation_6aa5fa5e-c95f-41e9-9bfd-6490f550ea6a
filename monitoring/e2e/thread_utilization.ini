[IPF - was-event-in - E2E - Thread Utilization]
alert_metric_name = mcount(2m, ts(iks.jvm.threads.live.threads and namespace=appintgwkflw-wkflautomate-waseventin-usw2-e2e)) >= 2 and mmedian(20m,ts(iks.jvm.threads.live.threads and namespace=appintgwkflw-wkflautomate-waseventin-usw2-e2e)) > 500 and ts(iks.namespace.app.pod.count and namespace=appintgwkflw-wkflautomate-waseventin-usw2-e2e) = 3
severity = SEVERE
threshold = 
operator = 
alert_target_id = <EMAIL>
wait_before_alert = 15
additional_info =
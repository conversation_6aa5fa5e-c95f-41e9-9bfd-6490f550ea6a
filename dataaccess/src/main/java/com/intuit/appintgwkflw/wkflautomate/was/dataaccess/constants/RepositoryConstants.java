package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants;

import lombok.experimental.UtilityClass;

@UtilityClass
public class RepositoryConstants {

  public static final String CREATED_DATE = "createdDate";
  public static final String CREATED_BY_USER_ID = "createdByUserId";
  public static final String MODIFIED_DATE = "modifiedDate";
  public static final String MODIFIED_BY_USER_ID = "modifiedByUserId";
  public static final String STATUS = "status";
  public static final String OWNER_ID = "ownerId";
  public static final String INTERNAL_STATUS = "internalStatus";
  public static final String MODEL_TYPE = "modelType";
  public static final String RECORD_TYPE = "recordType";
  public static final String TEMPLATE_DETAILS = "templateDetails";
  public static final String PROCESS_STATUS = "processStatus";
  public static final String RECORD_ID = "recordId";
  public static final String WORKFLOW_ID = "workflowId";
  public static final String DEFINITION_KEY = "definitionKey";
  public static final String PROCESS_INSTANCE_ID = "processId";
  public static final String DEFINITION_ID = "definitionId";
  public static final String PARENT_ID = "parentId";
  public static final String OFFERING_ID = "offeringId";
  public static final String DEFINITION_NAME = "definitionName";
  public static final String DESCRIPTION = "description";
  public static final String VERSION = "version";
  public static final String TEMPLATE_ID = "id";
  public static final String TEMPLATE_CATEGORY = "templateCategory";
  public static final String TEMPLATE_NAME = "templateName";
  public static final String PLACEHOLDER_VALUE = "placeholderValue";
  public static final String DEFINITION_TYPE = "definitionType";
  public static final String DEPLOYED_DEFINITION_ID = "deployedDefinitionId";
  public static final String LOOKUP_KEYS = "lookupKeys";
  public static final String ENTITY_VERSION= "entityVersion";
  public static final String ORIGINAL_SETUP_DATE = "originalSetupDate";
  public static final String ORIGINAL_SETUP_USER_ID = "originalSetupUser";
  public static final String TEMPLATE_DETAILS_PATH = TEMPLATE_DETAILS + ".";
  public static final String TEMPLATE_DETAILS_TEMPLATE_ID = TEMPLATE_DETAILS_PATH + TEMPLATE_ID;

  public static final String DEFINITION_DETAILS = "definitionDetails";
}

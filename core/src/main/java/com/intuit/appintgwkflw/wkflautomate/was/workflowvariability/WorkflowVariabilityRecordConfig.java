package com.intuit.appintgwkflw.wkflautomate.was.workflowvariability;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowVariabilityRecordConfig {
  private List<WorkflowVariabilityRecord> inclusion;
  private List<WorkflowVariabilityRecord> exclusion;
}

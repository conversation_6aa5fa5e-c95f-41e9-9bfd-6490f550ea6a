package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> This class used for event scheduler sqs message */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EventScheduleMessage {

  @JsonProperty("MessageId")
  private String messageId;

  @JsonProperty("Message")
  private String messageData;

  @JsonProperty("MessageAttributes")
  private Map<String, TypeValue> messageAttributes;
}

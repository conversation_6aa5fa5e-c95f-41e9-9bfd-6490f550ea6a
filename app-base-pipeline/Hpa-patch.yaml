apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: was-event-in
  name: was-event-in-rollout-hpa
spec:
  behavior:
    scaleDown:
      policies:
        - periodSeconds: 60
          type: Percent
          value: 10
      selectPolicy: Min
      stabilizationWindowSeconds: 300
    scaleUp:
      policies:
        - periodSeconds: 15
          type: Percent
          value: 40
        - periodSeconds: 15
          type: Pods
          value: 2
      selectPolicy: Max
      stabilizationWindowSeconds: 0
  maxReplicas: 10
  metrics:
    - object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: was-event-in
        metric:
          name: namespace_app_pod_cpu_utilization
        target:
          type: Value
          value: 50
      type: Object
    - object:
        describedObject:
          apiVersion: apps/v1
          kind: Deployment
          name: was-event-in
        metric:
          name: namespace_app_container_app_ready_only_avg_cpu_utilization
        target:
          type: Value
          value: 50
      type: Object
  minReplicas: 3
  scaleTargetRef:
    apiVersion: argoproj.io/v1alpha1
    kind: Rollout
    name: was-event-in-rollout
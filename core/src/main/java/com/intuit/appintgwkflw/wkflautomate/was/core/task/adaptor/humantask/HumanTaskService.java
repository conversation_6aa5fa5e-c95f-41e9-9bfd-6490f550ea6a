package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Base class for the downstream implementation of various lifecycle methods of
 *     Human Task
 */
public abstract class HumanTaskService {

  @Autowired protected WorkflowTaskConfig workflowTaskConfig;

  @Autowired protected WASContextHandler contextHandler;

  protected HumanTaskServiceType humanTaskServiceType;

  /**
   * Would return the task config for the specified HumanTaskServiceType, allowing multiple
   * task config to be possible for one TaskType for each downstream service
   *
   * @return
   */
  public WorkflowTaskConfigDetails getTaskConfig() {
    Map<String, WorkflowTaskConfigDetails> downstreamServices =
        workflowTaskConfig.getTaskConfig().get(TaskType.HUMAN_TASK).getDownstreamServices();
    if (Objects.nonNull(downstreamServices)
        && downstreamServices.containsKey(getServiceType().getName())) {
      return downstreamServices.get(getServiceType().getName());
    }
    return workflowTaskConfig.getTaskConfig().get(TaskType.HUMAN_TASK);
  }

  public abstract HumanTaskServiceType getServiceType();

  public abstract WorkflowTaskResponse create(HumanTask humanTask);

  public abstract WorkflowTaskResponse update(HumanTask humanTask);

  public abstract WorkflowTaskResponse complete(HumanTask humanTask);

  public abstract WorkflowTaskResponse failed(HumanTask humanTask);

  public abstract WorkflowTaskResponse get(HumanTask humanTask);

  /**
   * returns the realmId with which task needs to be created with.
   *
   * @param humanTask :: Human Task request
   * @return task owner
   */
  protected String getOwnerId(HumanTask humanTask) {
    String ownerId = contextHandler.get(WASContextEnums.OWNER_ID);
    if (!StringUtils.isEmpty(humanTask.getOwnerId())) {
      ownerId = humanTask.getOwnerId();
    }
    return ownerId;
  }
}

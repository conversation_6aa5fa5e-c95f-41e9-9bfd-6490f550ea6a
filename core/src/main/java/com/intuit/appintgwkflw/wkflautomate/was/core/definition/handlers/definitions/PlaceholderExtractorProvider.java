package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.v4.workflows.Template;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PlaceholderExtractorProvider {
  private final CustomDefinitionPlaceholderExtractor customDefinitionPlaceholderExtractor;
  private final PrecannedDefinitionPlaceholderExtractor precannedDefinitionPlaceholderExtractor;
  private final FeatureFlagManager featureFlagManager;

  public PlaceholderExtractor getPlaceholderExtractor(DefinitionInstance definitionInstance) {
    Template workflowTemplate =
        new Template().name(definitionInstance.getTemplateDetails().getTemplateName());

    if (CustomWorkflowUtil.isCustomWorkflow(workflowTemplate)) {
      return customDefinitionPlaceholderExtractor;
    }
    if (SingleDefinitionUtil.isSingleDefPrecannedCreateAllowed(
        featureFlagManager, workflowTemplate)) {
      return precannedDefinitionPlaceholderExtractor;
    }
    return null;
  }

}

#!/bin/bash -x

if [ "${sonar}" = "true" ] ; then
  ENFORCE=1
else
  ENFORCE=0
fi

#Set SonarQube Server
SONAR_URL="https://sonarqube.tools-k8s.a.intuit.com"

# Run sonar
mvn -T 1C -s settings.xml -Dsonar.host.url=$SONAR_URL sonar:sonar > /tmp/sonar.log

echo "Sonar log:"
echo "##########################################"
cat /tmp/sonar.log
echo "##########################################"

SONAR_STATUS_URL=$(grep -o "$SONAR_URL.*" /tmp/sonar.log | tail -1)
if [[ ! "${SONAR_STATUS_URL}" =~ "${SONAR_URL}/api" ]] ; then
  echo "ERROR: Can't get project URL" 
  echo "Exiting..."
  exit 1
fi

# Get project key
PROJECT_KEY=$(curl -s $SONAR_STATUS_URL | awk -F'"' '{print $18}')

sleep 5 

# Get analysis result
STATUS=$(curl -s $SONAR_URL/api/qualitygates/project_status?projectKey=$PROJECT_KEY | awk -F'"' '{print $6}')
QUALITY_GATE_RESULT=$(curl -s $SONAR_URL/api/qualitygates/project_status?projectKey=$PROJECT_KEY)
if [ "$STATUS" = "OK" ] ; then
  echo "Quality Gate: $STATUS"
  echo "Results: $SONAR_URL/dashboard/index/$PROJECT_KEY"
else
  echo "Quality Gate: $STATUS"
  echo "Results: $SONAR_URL/dashboard/index/$PROJECT_KEY"
  exit $ENFORCE
fi

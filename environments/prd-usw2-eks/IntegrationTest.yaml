apiVersion: argoproj.io/v1alpha1
kind: Workflow
metadata:
  name: integration-test
  annotations:
    argocd.argoproj.io/hook: PostSync
    argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
spec:
  serviceAccountName: workflow-service-account
  entrypoint: main
  templates:
  - name: main
    steps:
    - - name: get-parameters
        template: get-parameters
    - - name: test
        template: test
        arguments:
          parameters:
            - name: image-url
              value: "{{steps.get-parameters.outputs.parameters.image-url}}"
            - name: image-version
              value: "{{steps.get-parameters.outputs.parameters.image-version}}"
  - name: get-parameters
    container:
      image: docker.intuit.com/oicp/standard/base/amzn:latest
      command: ["bash", "-c"]
      args: ["echo -n $(IMAGE_URL) | sed 's/.*\\://' > /tmp/image-version.txt; echo -n $(IMAGE_URL) | sed -r 's/:/\\/test:/g' > /tmp/image-url.txt; sleep 2"]
    outputs:
      parameters:
      - name: image-url
        valueFrom:
          path: /tmp/image-url.txt
      - name: image-version
        valueFrom:
          path: /tmp/image-version.txt
  - name: test
    container:
      # Uncomment once integration test image is built and published
      # image: "{{inputs.parameters.image-url}}"
      image: docker.intuit.com/oicp/standard/base/amzn:latest
      workingDir: "/home/<USER>"
      command: ["bash", "-c"]
      args: ["echo 'Integration test goes here.'; sleep 10"]
    metadata:
      annotations:
        iam.amazonaws.com/irsa-service-account: workflow-service-account
        sidecar.istio.io/inject: 'true'
        policyId: p-37ovkcymhoqz
        alpha.istio.io/identity: Intuit.sandbox.sandbox.testargowfmesh
        traffic.sidecar.istio.io/includeInboundPorts: '8090'
        traffic.sidecar.istio.io/excludeOutboundPorts: 3306,3308,61617,443
        traffic.sidecar.istio.io/excludeOutboundIPRanges: ***************/32
    inputs:
      parameters:
      - name: image-url
      - name: image-version
    outputs:
      artifacts:
      - name: test-results
        # Uncomment once integration test image is built and published
        # path: /usr/src/app
        path: /home/<USER>
        s3:
          useSDKCreds: true
          endpoint: s3.amazonaws.com
          roleARN: PAWS_INT_TEST_IAM_ROLE
          bucket: PAWS_INT_TEST_S3_BUCKET
          key: PAWS_INT_TEST_S3_BUCKET_KEY
          region: PAWS_INT_TEST_S3_BUCKET_REGION
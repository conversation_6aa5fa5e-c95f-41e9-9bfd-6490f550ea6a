[IPF - was-event-in - PRD - Kafka Consumer Template Error]
alert_metric_name = align(1m, sum, ratediff(ts(iks.intuit.dataexchange.pipelineframework.kafka.consumer.template.failure.entry.count.total and namespace=appintgwkflw-wkflautomate-waseventin-usw2-prd)))
severity = SEVERE
threshold = 5
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =



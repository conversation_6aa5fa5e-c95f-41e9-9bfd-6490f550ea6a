// The library version is controlled from the Jenkins configuration
// To force a version add after lib '@' followed by the version.

@Library(value = 'msaas-shared-lib', changelog = false) _
import com.intuit.dev.patterns.jenkins_lib.argocd.*

// https://github.intuit.com/dev-patterns/msaas-jenkins-shared-library/blob/master/src/com/intuit/dev/patterns/jenkins_lib/argocd/ArgoCdOperations.groovy

node {
    // setup the global static configuration
    config = setupMsaasPipeline('msaas-config.yaml')
}

pipeline {

    options {
        preserveStashes(buildCount: 5)
    }

    agent {
        kubernetes {
            label "${config.pod_label}"
            yamlFile 'KubernetesPods.yaml'
        }
    }
    parameters {
        choice(
            choices: ['qal-usw2-eks', 'e2e-usw2-eks', 'prf-usw2-eks', 'prd-usw2-eks', 'all'],
            description: 'Select the environment',
            name: 'environment'
        )
    }
    triggers {
        parameterizedCron('''
            H */4 * * * % environment=prd-usw2-eks
        ''')
    }
    post {
        always {
             sendMetrics(config)
        }
        fixed {
            emailext(
                subject: "Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' ${currentBuild.result}", 
                body: """
                        Job Status for '${env.JOB_NAME} [${env.BUILD_NUMBER}]': ${currentBuild.result}\n\nCheck console output at ${env.BUILD_URL}
                """, 
                to: '<EMAIL>'
            )
        }
        unsuccessful {
            emailext(
                subject: "Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]' ${currentBuild.result}", 
                body: """
                        Job Status for '${env.JOB_NAME} [${env.BUILD_NUMBER}]': ${currentBuild.result}\n\nCheck console output at ${env.BUILD_URL}
                """, 
                to: '<EMAIL>'
            )
        }
    }

    stages {

        stage('qal-usw2-eks') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
                expression {params.environment == 'qal-usw2-eks' || params.environment == 'all'}
            }
            options {
                lock(resource: getEnv(config, 'qal-usw2-eks').namespace, inversePrecedence: true)
                timeout(time: 32, unit: 'MINUTES')
            }
            stages {
                stage('Restart Kubernetes Pods') {
                    steps {
                          script {
                            container('cdtools') {
                             restartPodsWithLogin("qal-usw2-eks")
                          }
                        }
                    }
                }
            }
        }
        stage('e2e-usw2-eks') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
                expression {params.environment == 'e2e-usw2-eks' || params.environment == 'all'}
            }

            options {
                lock(resource: getEnv(config, 'e2e-usw2-eks').namespace, inversePrecedence: true)
                timeout(time: 32, unit: 'MINUTES')
            }
            stages {
                stage('Restart Kubernetes Pods') {
                    steps {
                          script {
                            container('cdtools') {
                             restartPodsWithLogin("e2e-usw2-eks")
                          }
                        }
                    }
                }
            }
        }
        stage('prf-usw2-eks') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                }
                expression {params.environment == 'prf-usw2-eks' || params.environment == 'all'}
            }
            options {
                lock(resource: getEnv(config, 'prf-usw2-eks').namespace, inversePrecedence: true)
                timeout(time: 32, unit: 'MINUTES')
            }
            stages {

                stage('Restart Kubernetes Pods') {
                    steps {
                          script {
                            container('cdtools') {
                             restartPodsWithLogin("prf-usw2-eks")
                          }
                        }
                    }
                }
            }
        }
        stage('PRD primary') {
            when {
                beforeOptions true
                allOf {
                    branch 'master'
                    not {changeRequest()}
                    not {expression {return config.preprodOnly}}
                }
                expression {params.environment == 'prd-usw2-eks' || params.environment == 'all'}
            }
            options {
                lock(resource: getEnv(config, getEnvName(config, 'primary')).namespace, inversePrecedence: true)
                timeout(time: 32, unit: 'MINUTES')
            }
            stages {
                stage('Restart Kubernetes Pods') {
                    steps {
                        script {
                         container('cdtools') {


                            restartPodsWithLogin("prd-usw2-eks")
                         }
                        }
                    }
                }
            }
        }
    }
}

def restartPodsWithLogin(String env) {
   container('cdtools') {
       try {
           new ArgoCdOperations(this).restartDeployment(config, env, "", 60, "Rollout", true)
       } catch (Exception e) {
           echo "Error restarting pods: ${e}"
       }
   }
}


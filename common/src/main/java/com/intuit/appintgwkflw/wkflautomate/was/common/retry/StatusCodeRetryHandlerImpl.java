package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.NoHttpResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;

/**
 * Default implementation for retry
 * This retries for status codes specified in config (500,502,504 currently)
 * <AUTHOR>
 */
@Component
public class StatusCodeRetryHandlerImpl implements RetryHandler {

  @Autowired
  protected RetryConfig retryConfig;

  public void checkAndThrowRetriableException(Exception ex) {

    handleNoHttpResponseException(ex);

    if (ex instanceof HttpStatusCodeException) {
     HttpStatusCodeException exHttp = (HttpStatusCodeException) ex;
     int statusCode = exHttp.getStatusCode().value();
     checkAndThrowRetriableException(statusCode, ex);
   }
 }

 protected void checkAndThrowRetriableException(int statusCode, Exception ex){
   if(retryConfig.getStatusCode().contains(statusCode)) {
     WorkflowLogger.logWarn("Retrying for statusCode=%s errorMessage=%s",
         statusCode, ex.getMessage());
     throw new WorkflowRetriableException(ex);
   }
 }

 protected void handleNoHttpResponseException(Exception ex) {
   // Retry for noHttpResponseException
   // TODO to remove an if clause based on execution pattern in prod
   if (ExceptionUtils.hasCause(ExceptionUtils.getCause(ex),NoHttpResponseException.class)) {
     WorkflowLogger.logInfo("NoHttpResponse caught from cause");
         throw new WorkflowRetriableException(ex);
   } else if (ExceptionUtils.getRootCauseMessage(ex).contains(WorkflowConstants.NO_HTTP_RESPONSE)) {
     WorkflowLogger.logInfo("NoHttpResponse caught from cause message");
         throw new WorkflowRetriableException(ex);
   }

 }

  public RetryHandlerName getName() {
    return RetryHandlerName.STATUS_CODE;
  }
}
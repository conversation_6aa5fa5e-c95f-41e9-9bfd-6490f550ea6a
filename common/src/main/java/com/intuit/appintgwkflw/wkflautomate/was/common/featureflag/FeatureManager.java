package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import com.intuit.identity.exptplatform.assignment.entities.EntityID;

import java.util.Map;

public interface FeatureManager {


    boolean getBoolean(String flag, String ownerId);


    /**
     * Use this method to fetch featureFlags with Context, Make sure you are in valid context.
     *
     * @param flagName
     * @param defaultValue
     * @return
     */
    boolean getBoolean(String flagName, boolean defaultValue);

    /**
     * Use this method to fetch featureFlags with Context and workflowName, Make sure you are in valid context.
     * The workflow names added in ff configuration will only be allowed for single definition creation
     * @param flagName
     * @param defaultValue
     * @return
     */
    boolean getBoolean(String flagName, boolean defaultValue, String workflowName);

    /**
     * Use this method to fetch featureFlags with Context and workflowName and OwnerId when the context is not set
     * The workflow names added in ff configuration will only be allowed for single definition creation
     * @param flagName
     * @param defaultValue
     * @return
     */
    boolean getBoolean(String flagName, boolean defaultValue, String workflowName, Long ownerId);

    boolean getBoolean(String flagName, String defaultValue, EntityID entityId, Map<String, Object> contextMap);

    String getString(String flagName, String defaultValue, EntityID entityId, Map<String, Object> contextMap);

    String getString(String flagName, String defaultValue, Map<String, Object> contextMap, Long ownerId);

    boolean getBoolean(String flagName, boolean defaultValue, Map<String, Object> contextMap, Long ownerId);

}
package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config.PushTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.util.Collections;
import java.util.Map;

import static org.mockito.Mockito.never;

@RunWith(SpringRunner.class)
public class CamundaPushTaskConsumerTest {

    @Mock
    private Acknowledgment acknowledgment;
    @Mock private EventProcessor eventProcessor;
    @Mock
    private PushTaskConfig pushTaskConfig;
    @InjectMocks
    private CamundaPushTaskConsumer consumer;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testReceive(){
        Worker workerTopicConfig = new Worker();
        workerTopicConfig.setLockDuration(10000);
        Mockito.when(pushTaskConfig.getTopics()).thenReturn(Map.of("test-topic", workerTopicConfig));
        try {
            consumer.receive(
                    getSuccessMessage(), new MessageHeaders(Map.of("kafka_acknowledgment", "true",
                            "kafka_receivedTimestamp", Instant.now().toEpochMilli())), acknowledgment);
            Mockito.verify(eventProcessor).process(Mockito.any(), Mockito.any());
        } catch (Exception e) {

        }
    }

    @Test
    public void testReceiveSkip(){
        Worker workerTopicConfig = new Worker();
        workerTopicConfig.setLockDuration(10000);
        Mockito.when(pushTaskConfig.getTopics()).thenReturn(Map.of("test-topic", workerTopicConfig));
        try {
            consumer.receive(
                    getSuccessMessage(), new MessageHeaders(Map.of("kafka_acknowledgment", "true",
                            "kafka_receivedTimestamp", Instant.now().toEpochMilli())), acknowledgment);
            Mockito.verifyNoInteractions(eventProcessor);
        } catch (Exception e) {

        }
    }

    @Test
    public void testReceive_assert(){
        try {
            consumer.receive(
                    getSuccessMessage(), new MessageHeaders(Map.of("kafka_acknowledgment", "true",
                            "kafka_receivedTimestamp", Instant.now().toEpochMilli())), acknowledgment);
            Mockito.verifyNoInteractions(eventProcessor);
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test(expected = Exception.class)
    public void testReceiveFormatException(){
        consumer.receive(
                getFailureMessage(), new MessageHeaders(Collections.emptyMap()), acknowledgment);
        Mockito.verify(eventProcessor, never()).process(Mockito.any(), Mockito.any());
    }

    private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
        ExternalTaskEvent externalTaskEvent = ExternalTaskEvent.builder()
                .id("1234")
                .topicName("test-topic")
                .build();
        Result<FormatException, String> body = new Result.Success<FormatException, String>(ObjectConverter.toJson(externalTaskEvent));
        return new ConsumerRecord<>(
                "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
    }

    private ConsumerRecord<String, Result<FormatException, String>> getFailureMessage() {
        Result<FormatException, String> body =
                new Result.Failure<FormatException, String>(new FormatException());
        return new ConsumerRecord<>(
                "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
    }

}

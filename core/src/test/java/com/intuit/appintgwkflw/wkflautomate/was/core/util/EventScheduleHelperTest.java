package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.ACTIVE;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mockStatic;

import java.util.*;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeleteEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import org.apache.commons.collections.CollectionUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.json.JSONObject;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleWorkflowAction;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;

@RunWith(MockitoJUnitRunner.class)
public class EventScheduleHelperTest {

  private EventScheduleConfig eventScheduleConfig = new EventScheduleConfig();
  @InjectMocks private EventScheduleHelper eventScheduleHelper;
  @Mock private EventScheduleService eventScheduleService;
  @Mock private SchedulerDetailsRepository schedulerDetailsRepository;
  @Mock private FeatureFlagManager featureFlagManager;
  @Mock private ActionModelToScheduleRequestMapper actionModelToScheduleRequestMapper;
  @Mock private SchedulingService schedulingService;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  private static final String CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML =
          TestHelper.readResourceAsString("bpmn/customScheduledActionsTest2.bpmn");
  String CUSTOM_PLACEHOLDER_VALUES_PATH = "placeholder/custom_placeholder_value_invoice_with_recurrence.json";

  private final String realmId = "12345";

  @Before
  public void setUp() {
    schedulerDetailsRepository = mock(SchedulerDetailsRepository.class);
    eventScheduleHelper =
        new EventScheduleHelper(
            eventScheduleConfig, eventScheduleService, schedulingService,schedulerDetailsRepository, featureFlagManager, actionModelToScheduleRequestMapper, definitionDetailsRepository);
    List<SchedulerDetails> schedulerDetails = new ArrayList<>();
    SchedulerDetails details = new SchedulerDetails();
    details.setSchedulerId("1234");
    schedulerDetails.add(details);
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
        .thenReturn(Optional.of(schedulerDetails));
  }

  @Test
  public void testIsEventScheduleEnabled_false_ConfigNull() {
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingConfigEnabled());
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_false_ConfigNull() {
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something", realmId));
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_true_ConfigEnabled() {
    eventScheduleConfig.setEnabled(true);
    Assert.assertTrue(eventScheduleHelper.isEventSchedulingConfigEnabled());
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_false_WorkflowEmptyOrNull() {
    eventScheduleConfig.setEnabled(true);
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something", realmId));
    eventScheduleConfig.setWorkflow(new HashMap<>());
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something", realmId));
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_false_WorkflowDisabled() {
    eventScheduleConfig.setEnabled(true);
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    scheduleWorkflowConfigMap.put("something", new EventScheduleWorkflowConfig());
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something", realmId));
    // different key not present
    Assert.assertFalse(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something1", realmId));
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_true_WorkflowEnabled() {
    eventScheduleConfig.setEnabled(true);
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED, false, "something", Long.valueOf(realmId)))
        .thenReturn(true);

    Assert.assertTrue(eventScheduleHelper.isEventSchedulingEnabledForWorkflow("something", realmId));
  }

  @Test
  public void testIsEventScheduleEnabledForWorkflow_true_ScheduledActionsWorkflowEnabled() {
    eventScheduleConfig.setEnabled(true);
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put(CUSTOM_SCHEDULED_ACTIONS.getName(), workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertTrue(
        eventScheduleHelper.isEventSchedulingEnabledForWorkflow(
            CUSTOM_SCHEDULED_ACTIONS.getName(), realmId));
    Mockito.verifyNoInteractions(featureFlagManager);
  }

  @Test
  public void getScheduleActionForWorkflowScheduling_empty_ConfigIsNull() {
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActionsForScheduling(SchedulingMetaData.builder().workflowName(null).build()).isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflowScheduling_empty_WorkflowEmptyOrNull() {
    eventScheduleConfig.setWorkflow(new HashMap<>());
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActionsForScheduling(SchedulingMetaData.builder().workflowName("something").build()).isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_WorkflowConfigNotPresentScheduling() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something1", workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActionsForScheduling(SchedulingMetaData.builder().workflowName("something").build()).isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_WorkflowConfigPresentScheduleActionNullOrEmptyScheduling() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActionsForScheduling(SchedulingMetaData.builder().workflowName("something").build()).isEmpty());
    workflowConfig.setScheduleActions(new HashMap<>());
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActionsForScheduling(SchedulingMetaData.builder().workflowName("something").build()).isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_ConfigIsNull() {
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActions(new Definition(),"something").isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_WorkflowEmptyOrNull() {
    eventScheduleConfig.setWorkflow(new HashMap<>());
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActions(new Definition(), "something").isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_WorkflowConfigNotPresent() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something1", workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActions(new Definition(), "something").isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_empty_WorkflowConfigPresentScheduleActionNullOrEmpty() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActions(new Definition(), "something").isEmpty());
    workflowConfig.setScheduleActions(new HashMap<>());
    Assert.assertTrue(eventScheduleHelper.getWorkflowScheduleActions(new Definition(),"something").isEmpty());
  }

  @Test
  public void getScheduleActionForWorkflow_true_WorkflowConfigPresentScheduleActions() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventscheduleWorkflowAction.setNoOfDaysToBeAddedToStartDate(1);
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);

    eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventscheduleWorkflowAction.setStartDate("2023-2-17");
    eventScheduleWorkflowActionsMap.put("customWait", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
            eventScheduleHelper.getWorkflowScheduleActions(new Definition(),"something").get();
    Assert.assertEquals(
            eventScheduleWorkflowActionsMap.size(), eventScheduleWorkflowActionModels.size());
    Assert.assertEquals(new LocalDate().plusDays(1).toString(), eventScheduleWorkflowActionModels.get(0).getStartDate().toString());
    Assert.assertNull(eventScheduleWorkflowActionModels.get(0).getEndDate());
  }

  @Test
  public void getScheduleActionForWorkflow_true_WorkflowConfigPresentScheduleActions_WithRecurrence() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowAction.setNoOfDaysToBeAddedToStartDate(0);
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);

    eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowAction.setStartDate("2023-2-17");
    eventScheduleWorkflowActionsMap.put("customWait", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    Definition definition = new Definition();
	RecurrenceRule recurrenceRule = new RecurrenceRule();
	recurrenceRule.startDate(new DateTime("2023-04-26")).interval(1).recurType(RecurTypeEnum.MONTHLY);
	recurrenceRule.setEndDate(new DateTime(LocalDate.now().plusDays(1).toDate()));
	definition.setRecurrence(recurrenceRule);
    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
            eventScheduleHelper.getWorkflowScheduleActions(definition,"something").get();
    Assert.assertTrue(
            eventScheduleWorkflowActionModels.size() == eventScheduleWorkflowActionsMap.size());
    Assert.assertEquals("2023-04-26", eventScheduleWorkflowActionModels.get(0).getStartDate().toString());
    Assert.assertEquals(RecurTypeEnum.MONTHLY, eventScheduleWorkflowActionModels.get(0).getRecurrenceRule().getRecurType());
    Assert.assertEquals(new LocalDate().plusDays(1).toString(), eventScheduleWorkflowActionModels.get(0).getEndDate().toString());
  }

  @Test
  public void testPrepareEventScheduleTasks() {
    // no config present for null workflow name
    Assert.assertTrue(CollectionUtils.isEmpty(eventScheduleHelper.prepareScheduleCreateTasks(null, null)));
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventscheduleWorkflowAction.setNoOfDaysToBeAddedToStartDate(1);
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);

    eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventscheduleWorkflowAction.setStartDate("2023-2-17");
    eventScheduleWorkflowActionsMap.put("customWait", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234");
    Mockito.when(
            featureFlagManager.getBoolean(
                WorkflowConstants.ESS_TRIGGER_ENABLED, false, "something", Long.valueOf("1234")))
        .thenReturn(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    when(definitionInstance.getDefinition()).thenReturn(definition);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);
    List<Task> taskList = eventScheduleHelper.prepareScheduleCreateTasks(state, "something");
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
    Assert.assertEquals(2, taskList.size());
  }

  @Test
  public void testPrepareScheduleCreateTasksForScheduleActions() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();

    eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventscheduleWorkflowAction.setStartDate("2023-2-17");
    eventScheduleWorkflowActionsMap.put("customWait", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234");
    Definition definition = new Definition();
    definition.setDefinitionKey("key");
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setRecurrence(new RecurrenceRule());
    Task task = eventScheduleHelper.prepareSchedulingCreateTask(state, SchedulingMetaData.builder()
            .status(ACTIVE)
            .definitionKey("key")
            .recurrenceRule(new RecurrenceRule())
            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build());
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
    Assert.assertNotNull(task);
  }

  @Test
  public void testPrepareEventScheduleTasks_ffNotEnabled() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowAction.setNoOfDaysToBeAddedToStartDate(1);
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);

    eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowAction.setStartDate("2023-2-17");
    eventScheduleWorkflowActionsMap.put("customWait", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234");

    Assert.assertTrue(CollectionUtils.isEmpty(
        eventScheduleHelper.prepareScheduleCreateTasks(state, "something")));
  }

  @Test
  public void testPrepareUpdateScheduleTasks() {
    Assert.assertNotNull(eventScheduleHelper.prepareScheduleStatusUpdateTask(null, null, null));
    State state = new State();
    eventScheduleHelper.prepareScheduleStatusUpdateTask(state, ScheduleStatus.DELETED, "def-123");
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS));
  }

  @Test
  public void testPrepareDeleteScheduleTasks() {
    DeleteEventSchedulingTask task = eventScheduleHelper.prepareSchedulingDeleteTask(new State(), "customReminder_1232312_123123123");
    Assert.assertNotNull(task);
  }

  @Test
  public void testPrepareUpdateScheduleTasksForEmptyScheduleIds() {
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
        .thenReturn(Optional.empty());
    State state = new State();
    eventScheduleHelper.prepareScheduleStatusUpdateTask(state, ScheduleStatus.DELETED, "def-123");
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS));
  }

  @Test
  public void testGetScheduleIds_notNull() {
    Assert.assertNotNull(eventScheduleHelper.getScheduleIdsForDefinition("def123"));
  }

  @Test
  public void testGetScheduleIds_null_returnEmpty() {
    Assert.assertEquals(eventScheduleHelper.getScheduleIdsForDefinition(null).size(),0);
  }
  @Test
  public void testGetScheduleIds_Empty() {
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
        .thenReturn(Optional.empty());
    Assert.assertEquals(0, eventScheduleHelper.getScheduleIdsForDefinition("def123").size());
  }

  @Test
  public void testPrepareUpdateScheduleTasksForDisabledConfigNotPresent() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    UpdateEventScheduleTask updateStatusEventSchedulerTask =
        eventScheduleHelper.prepareScheduleStatusUpdateTask(
            new State(),
            EventScheduleMetaData.builder()
                .workflowName("something")
                .definitionId("def123")
                .scheduleStatus(ScheduleStatus.ACTIVE)
                .build());
    Assert.assertNull(updateStatusEventSchedulerTask);
  }

  @Test
  public void testPrepareUpdateScheduleTasksForDisabledConfigPresent() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED, false, "something", Long.valueOf(realmId)))
        .thenReturn(true);

    UpdateEventScheduleTask updateStatusEventSchedulerTask =
        eventScheduleHelper.prepareScheduleStatusUpdateTask(
           state,
            EventScheduleMetaData.builder()
                .workflowName("something")
                .definitionId("def123")
                .scheduleStatus(ScheduleStatus.DELETED)
                .build());
    Assert.assertNotNull(updateStatusEventSchedulerTask);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetails(Mockito.any());
  }

  @Test
  public void testPrepareUpdateScheduleTasksForDisabledConfigPresentEmptyState() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    eventScheduleHelper.prepareScheduleStatusUpdateTask(
        null,
        EventScheduleMetaData.builder()
            .workflowName("something")
            .definitionId("def123")
            .scheduleStatus(ScheduleStatus.ACTIVE)
            .build());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .findByDefinitionDetails(Mockito.any());
  }

  @Test
  public void testPrepareUpdateScheduleTasksForDisabledConfigPresentNullMetaData() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    eventScheduleHelper.prepareScheduleStatusUpdateTask(new State(), null);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .findByDefinitionDetails(Mockito.any());
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTask() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);

    State state = new State();
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    when(definitionInstance.getDefinition()).thenReturn(definition);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);

    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED, false, "something", Long.valueOf(realmId)))
        .thenReturn(true);

    List<Task> updateTasks  =
        eventScheduleHelper.prepareScheduleUpdateTasks(
                state,
            EventScheduleMetaData.builder()
                .workflowName("something")
                .definitionId("def123")
                .scheduleStatus(ScheduleStatus.ACTIVE)
                .build());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetails(Mockito.any());
    Assert.assertEquals(2, updateTasks.size());
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP));
    Assert.assertEquals(List.of("1234"), state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS));
    Assert.assertTrue(state.getValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testPrepareUpdateSchedulingTaskInSchedulingSvcNoDefinitionId() {
    State state = new State();
    eventScheduleHelper.prepareSchedulingUpdateTask(
      state,
      SchedulingMetaData.builder()
              .status(ACTIVE)
              .definitionKey("key")
              .recurrenceRule(new RecurrenceRule())
              .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build(), false);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testPrepareUpdateSchedulingTaskInSchedulingSvcNoDefinitionKey() {
    State state = new State();
    eventScheduleHelper.prepareSchedulingUpdateTask(
            state,
            SchedulingMetaData.builder()
                    .status(ACTIVE)
                    .definitionId("key")
                    .recurrenceRule(new RecurrenceRule())
                    .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build(), false);
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTaskInSchedulingSvc() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);

    State state = new State();
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);

    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    Task updateTasks  =
            eventScheduleHelper.prepareSchedulingUpdateTask(
                    state,
                    SchedulingMetaData.builder()
                            .status(ACTIVE)
                            .definitionKey("key")
                            .definitionId("id")
                            .recurrenceRule(new RecurrenceRule())
                            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build(), false);
    Assert.assertNotNull(updateTasks);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTaskInSchedulingSvcNoDefinitionKey() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);

    State state = new State();
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);

    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Task updateTasks  =
            eventScheduleHelper.prepareSchedulingUpdateTask(
                    state,
                    SchedulingMetaData.builder()
                            .status(ACTIVE)
                            .definitionId("id")
                            .definitionKey("key")
                            .recurrenceRule(new RecurrenceRule())
                            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build(), false);
    Assert.assertNotNull(updateTasks);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
    SchedulingMetaData schedulingMetaData = state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA);
    Assert.assertEquals("key", schedulingMetaData.getDefinitionKey());
  }

  @Test
  public void testPrepareUpdateSchedulingTaskWithMigration() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventscheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventscheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);

    State state = new State();
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);

    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Task updateTasks  =
            eventScheduleHelper.prepareSchedulingUpdateTask(
                    state,
                    SchedulingMetaData.builder()
                            .status(ACTIVE)
                            .definitionId("id")
                            .definitionKey("key")
                            .recurrenceRule(new RecurrenceRule())
                            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build(), true);
    Assert.assertNotNull(updateTasks);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.IS_MIGRATED_TO_SCHEDULING));
    SchedulingMetaData schedulingMetaData = state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA);
    Assert.assertEquals("key", schedulingMetaData.getDefinitionKey());
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTask_emptySchedulerDetails() {
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetails(Mockito.any()))
        .thenReturn(Optional.empty());
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    State state = new State();
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Definition definition = new Definition();
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    when(definitionInstance.getDefinition()).thenReturn(definition);
    state.addValue(AsyncTaskConstants.DEFINITION_INSTANCE, definitionInstance);
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED, false, "something", Long.valueOf(realmId)))
        .thenReturn(true);
    List<Task> updateTasks =
        eventScheduleHelper.prepareScheduleUpdateTasks(
            state,
            EventScheduleMetaData.builder()
                .workflowName("something")
                .definitionId("def123")
                .scheduleStatus(ScheduleStatus.ACTIVE)
                .build());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetails(Mockito.any());
    Assert.assertEquals(2, updateTasks.size());
    Assert.assertEquals(
        0, new HashMap<>(state.getValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP)).size());
    Assert.assertEquals(
        0, new ArrayList<>(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS)).size());
    Assert.assertTrue(state.getValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE));
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST));
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTaskNotEnabled() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("something", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    List<Task> updateTasks =
        eventScheduleHelper.prepareScheduleUpdateTasks(
            new State(),
            EventScheduleMetaData.builder()
                .workflowName("something")
                .definitionId("def123")
                .build());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .findByDefinitionDetails(Mockito.any());
    Assert.assertTrue(CollectionUtils.isEmpty(updateTasks));
  }

  @Test
  public void testPrepareUpdateSchedulerDetailsTaskNullMetaData() {
    List<Task> updateTasks = eventScheduleHelper.prepareScheduleUpdateTasks(new State(), null);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .findByDefinitionDetails(Mockito.any());
    Assert.assertTrue(CollectionUtils.isEmpty(updateTasks));
  }

  @Test
  public void deleteAllSchedules() {
    List<DefinitionDetails> definitionDetails =
        List.of(DefinitionDetails.builder().definitionId("def123").build());
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    EventScheduleData eventScheduleData = new EventScheduleData();
    eventScheduleData.setId("sched124");
    eventScheduleData.setStatus(ScheduleStatus.DELETED);
    eventScheduleResponse.setData(List.of(eventScheduleData));
    Mockito.when(eventScheduleService.updateSchedules(Mockito.anyList(), Mockito.any()))
        .thenReturn(List.of(eventScheduleResponse));
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetailsIn(definitionDetails))
        .thenReturn(
            Optional.of(List.of(SchedulerDetails.builder().schedulerId("sched124").build())));
    eventScheduleHelper.deleteAllSchedules(definitionDetails, "12345");
    Mockito.verify(eventScheduleService, Mockito.times(1))
        .updateSchedules(Mockito.anyList(), Mockito.any());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetailsIn(definitionDetails);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .deleteAll(Mockito.any());
  }

  @Test
  public void deleteAllSchedulesNoSchedules() {
    List<DefinitionDetails> definitionDetails =
        List.of(DefinitionDetails.builder().definitionId("def123").build());
    Mockito.when(schedulerDetailsRepository.findByDefinitionDetailsIn(definitionDetails))
        .thenReturn(
            Optional.empty());
    eventScheduleHelper.deleteAllSchedules(definitionDetails, "12345");
    Mockito.verify(eventScheduleService, Mockito.times(0))
        .updateSchedules(Mockito.anyList(), Mockito.any());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .findByDefinitionDetailsIn(definitionDetails);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .deleteAll(Mockito.any());
  }

  @Test
  public void deleteAllSchedulesNoDefinitions() {
    eventScheduleHelper.deleteAllSchedules(null, "12345");
    Mockito.verify(eventScheduleService, Mockito.times(0))
        .updateSchedules(Mockito.anyList(), Mockito.any());
    Mockito.verify(schedulerDetailsRepository, Mockito.times(0))
        .findByDefinitionDetailsIn(Mockito.anyList());
  }

  @Test
  public void deleteASchedulesFromESSWithNoSchedules() {
    eventScheduleHelper.deleteSchedulesFromESS(null, "12345");
    Mockito.verify(eventScheduleService, Mockito.times(0))
        .updateSchedules(Mockito.anyList(), Mockito.any());
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToESS() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customReminder", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
        .thenReturn(
            DefinitionDetails.builder()
                .definitionId("def123")
                .ownerId(123L)
                .templateDetails(TemplateDetails.builder().templateName("customReminder").build())
                .build());
    Mockito.when(
            featureFlagManager.getBoolean(
                WorkflowConstants.ESS_TRIGGER_ENABLED,
                false,
                "customReminder",
                Long.valueOf("123")))
        .thenReturn(true);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .saveAll(Mockito.any());
    Mockito.verify(eventScheduleService, Mockito.times(1))
        .createSchedules(Mockito.anyList(), Mockito.anyString());
  }


  @Test
  public void test_MigrateWorkflowScheduleActionsToESS_scheduleCreationFailed() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customReminder", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
        .thenReturn(
            DefinitionDetails.builder()
                .definitionId("def123")
                .ownerId(123L)
                .templateDetails(TemplateDetails.builder().templateName("customReminder").build())
                .build());
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED,
            false,
            "customReminder",
            Long.valueOf("123")))
        .thenReturn(true);
    // event scheduler response

    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenThrow(new WorkflowGeneralException("test"));
    eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
    Mockito.verify(eventScheduleService, Mockito.times(1))
        .createSchedules(Mockito.anyList(), Mockito.anyString());
  }
  @Test
  public void test_MigrateWorkflowScheduleActionsToESS_saveScheduleFailedDB() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customReminder", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
        .thenReturn(
            DefinitionDetails.builder()
                .definitionId("def123")
                .ownerId(123L)
                .templateDetails(TemplateDetails.builder().templateName("customReminder").build())
                .build());
    Mockito.when(
        featureFlagManager.getBoolean(
            WorkflowConstants.ESS_TRIGGER_ENABLED,
            false,
            "customReminder",
            Long.valueOf("123")))
        .thenReturn(true);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    Mockito.when(eventScheduleService.createSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    Mockito.when(schedulerDetailsRepository.saveAll(Mockito.anyList())).thenThrow(new RuntimeException("test"));
    eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
    Mockito.verify(schedulerDetailsRepository, Mockito.times(1))
        .saveAll(Mockito.any());
    Mockito.verify(eventScheduleService, Mockito.times(1))
        .createSchedules(Mockito.anyList(), Mockito.anyString());
    Mockito.verify(eventScheduleService, Mockito.times(1))
        .updateSchedules(Mockito.anyList(), Mockito.anyString());
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToESS_ffDisbaled() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customReminder", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
        .thenReturn(
            DefinitionDetails.builder()
                .definitionId("def123")
                .ownerId(123L)
                .templateDetails(TemplateDetails.builder().templateName("customReminder").build())
                .build());
    Mockito.when(
            featureFlagManager.getBoolean(
                WorkflowConstants.ESS_TRIGGER_ENABLED,
                false,
                "customReminder",
                Long.valueOf("123")))
        .thenReturn(false);
    try {
      eventScheduleHelper.migrateWorkflowScheduleActionsToESS(definitionInstance);
      Assert.fail("Exception should be thrown");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Mockito.verify(eventScheduleService, Mockito.times(0))
          .createSchedules(Mockito.anyList(), Mockito.anyString());
    }
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToSchedulingSvc() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    when(schedulerDetailsRepository.setMigrationFlagForDefinitionId(any(), any())).thenReturn(1);
    SchedulingSvcRequest schedulingSvcRequest= new SchedulingSvcRequest();
    schedulingSvcRequest.setScheduleInfo(new ScheduleInfo());
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(),any())).thenReturn(schedulingSvcRequest);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
    when(schedulingService.createSchedules(any(), any())).thenReturn(Collections.singletonList(schedulingSvcResponse));
    eventScheduleHelper.migrateScheduleActionsFromEssToSchedulingSvc(definitionInstance);
    verify(schedulerDetailsRepository, times(1))
            .setMigrationFlagForDefinitionId(any(), any());
    verify(schedulingService, times(1))
            .createSchedules(anyList(), anyString());
    verify(schedulingService, times(0)).deleteSchedules(any(), any());
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToSchedulingSvc_SchedulingCallFailed() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    SchedulingSvcRequest schedulingSvcRequest= new SchedulingSvcRequest();
    schedulingSvcRequest.setScheduleInfo(new ScheduleInfo());
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(),any())).thenReturn(schedulingSvcRequest);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    when(schedulingService.createSchedules(any(), any())).thenThrow(new WorkflowGeneralException("test"));
    when(schedulerDetailsRepository.setMigrationFlagForDefinitionId(any(), any())).thenReturn(1);
    try{
      eventScheduleHelper.migrateScheduleActionsFromEssToSchedulingSvc(definitionInstance);
      Assert.fail();
    }catch (WorkflowGeneralException e){
      verify(schedulingService, times(1))
              .createSchedules(anyList(), anyString());
      verify(schedulingService, times(0)).deleteSchedules(any(), any());
      verify(schedulerDetailsRepository, times(1)).setMigrationFlagForDefinitionId(any(), any());
      assertEquals(WorkflowError.SCHEDULING_MIGRATION_FAILED, e.getWorkflowError());
    }
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToSchedulingSvc_SchedulingCallFailedWithRetryableException() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    SchedulingSvcRequest schedulingSvcRequest= new SchedulingSvcRequest();
    schedulingSvcRequest.setScheduleInfo(new ScheduleInfo());
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(),any())).thenReturn(schedulingSvcRequest);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    when(schedulingService.createSchedules(any(), any())).thenThrow(new WorkflowRetriableException("test"));
    try{
      eventScheduleHelper.migrateScheduleActionsFromEssToSchedulingSvc(definitionInstance);
      Assert.fail();
    }catch (WorkflowGeneralException e){
      verify(schedulingService, times(1))
              .createSchedules(anyList(), anyString());
      verify(schedulingService, times(0)).deleteSchedules(any(), any());
      assertEquals(WorkflowError.SCHEDULING_MIGRATION_FAILED, e.getWorkflowError());
    }
  }

  @Test
  public void test_MigrateWorkflowScheduleActionsToSchedulingSvc_SchedulerDBCallFailed() {
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    when(schedulerDetailsRepository.setMigrationFlagForDefinitionId(any(), any())).thenThrow(new WorkflowGeneralException("test"));
    SchedulingSvcRequest schedulingSvcRequest= new SchedulingSvcRequest();
    schedulingSvcRequest.setScheduleInfo(new ScheduleInfo());
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(),any())).thenReturn(schedulingSvcRequest);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    SchedulingSvcResponse schedulingSvcResponse = new SchedulingSvcResponse();
    when(schedulingService.createSchedules(any(), any())).thenReturn(Collections.singletonList(schedulingSvcResponse));
    try{
      eventScheduleHelper.migrateScheduleActionsFromEssToSchedulingSvc(definitionInstance);
      Assert.fail();
    }catch (WorkflowGeneralException e){
      verify(schedulingService, times(1))
              .createSchedules(anyList(), anyString());
      verify(schedulerDetailsRepository, times(1))
              .setMigrationFlagForDefinitionId(any(), any());
      verify(schedulingService, times(1)).deleteSchedules(any(), any());
      assertEquals(WorkflowError.SCHEDULING_MIGRATION_FAILED, e.getWorkflowError());
    }
  }

  @Test
  public void test_cleanUpSchedulingSvcMigration_success(){
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    when(schedulerDetailsRepository.deleteByDefinitionDetailsIn(any())).thenReturn(1L);
    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    eventScheduleHelper.cleanUpSchedulingSvcMigration(definitionInstance);
    verify(schedulerDetailsRepository, times(1))
            .deleteByDefinitionDetailsIn(any());
    verify(definitionDetailsRepository, times(1)).setDefinitionDataAndPlaceholderValues(any(), any(), any());
    verify(eventScheduleService, times(1)).updateSchedules(any(), any());
  }

  @Test
  public void test_cleanUpSchedulingSvcMigration_ESSCallFailed(){
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    when(eventScheduleService.updateSchedules(any(), any())).thenThrow(new WorkflowGeneralException("test"));
    try{
      eventScheduleHelper.cleanUpSchedulingSvcMigration(definitionInstance);
      Assert.fail();
    }catch (WorkflowGeneralException e){
      verify(eventScheduleService, times(1)).updateSchedules(any(), any());
      verify(schedulerDetailsRepository, times(0))
              .deleteByDefinitionDetailsIn(any());
      verify(definitionDetailsRepository, times(0)).setDefinitionDataAndPlaceholderValues(any(), any(), any());
      assertEquals(WorkflowError.SCHEDULING_MIGRATION_FAILED, e.getWorkflowError());
    }
  }

  @Test
  public void test_cleanUpSchedulingSvcMigration_DBCallFailed(){
    Map<String, EventScheduleWorkflowConfig> scheduleWorkflowConfigMap = new HashMap<>();
    EventScheduleWorkflowConfig workflowConfig = new EventScheduleWorkflowConfig();
    scheduleWorkflowConfigMap.put("customScheduledActions", workflowConfig);
    Map<String, EventScheduleWorkflowAction> eventScheduleWorkflowActionsMap = new HashMap<>();
    EventScheduleWorkflowAction eventScheduleWorkflowAction = new EventScheduleWorkflowAction();
    eventScheduleWorkflowActionsMap.put("customStart", eventScheduleWorkflowAction);
    workflowConfig.setScheduleActions(eventScheduleWorkflowActionsMap);
    eventScheduleConfig.setWorkflow(scheduleWorkflowConfigMap);
    eventScheduleConfig.setEnabled(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    Mockito.when(definitionInstance.getDefinitionDetails())
            .thenReturn(
                    DefinitionDetails.builder()
                            .definitionId("def123")
                            .ownerId(123L)
                            .definitionKey("key1")
                            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status.ENABLED)
                            .templateDetails(TemplateDetails.builder().templateName("customScheduledActions").build())
                            .definitionData(CUSTOM_WORKFLOW_STATEMENTS_BPMN_XML.getBytes())
                            .placeholderValue(TestHelper.readResourceAsString(CUSTOM_PLACEHOLDER_VALUES_PATH))
                            .build());

    when(schedulerDetailsRepository.deleteByDefinitionDetailsIn(any())).thenReturn(1L);
    when(schedulerDetailsRepository.deleteByDefinitionDetailsIn(any())).thenThrow(new WorkflowGeneralException("test"));
    try{
      eventScheduleHelper.cleanUpSchedulingSvcMigration(definitionInstance);
      Assert.fail();
    }catch (WorkflowGeneralException e){
      verify(eventScheduleService, times(2)).updateSchedules(any(), any());
      verify(schedulerDetailsRepository, times(1))
              .deleteByDefinitionDetailsIn(any());
      assertEquals(WorkflowError.SCHEDULING_MIGRATION_FAILED, e.getWorkflowError());
    }
  }

  @Test
  public void test_updateDefinitionForSchedulingMigration(){
    eventScheduleHelper.updateDefinitionForSchedulingMigration("definitionId", new DefinitionDetails(), new byte[0], "placeholderValues");
    verify(definitionDetailsRepository, times(1)).setDefinitionDataAndPlaceholderValues(any(), any(), any());
    verify(schedulerDetailsRepository, times(1)).deleteByDefinitionDetailsIn(any());
  }

  @Test
  public void test_updateDefinitionForSchedulingMigration_schedulerDetailsCallFailed(){
    doThrow(new RuntimeException("test")).when(schedulerDetailsRepository).deleteByDefinitionDetailsIn(any());
    try{
      eventScheduleHelper.updateDefinitionForSchedulingMigration("definitionId", new DefinitionDetails(), new byte[0], "placeholderValues");
      Assert.fail();
    }catch (Exception e){
      verify(definitionDetailsRepository, times(0)).setDefinitionDataAndPlaceholderValues(any(), any(), any());
      verify(schedulerDetailsRepository, times(1)).deleteByDefinitionDetailsIn(any());
    }
  }

  @Test
  public void testPrepareUpdateTasksForEnableDisableCommand_SchedulingServiceEnabled() {
    when(schedulingService.isEnabled(any(DefinitionDetails.class), any())).thenReturn(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setOwnerId(423423423L);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);

    List<Task> tasks = eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(new State(), definitionInstance, EventScheduleMetaData.builder().build(), SchedulingMetaData.builder().definitionId("id").definitionKey("key").build());

    assertEquals(1, tasks.size());
    verify(schedulingService, times(1)).isEnabled(any(DefinitionDetails.class), any());
    verify(schedulingService, times(0)).isMigrated(any());
  }

  @Test
  public void testPrepareUpdateTasksForEnableDisableCommand_SchedulingServiceNOTEnabled() {
    when(schedulingService.isEnabled(any(DefinitionDetails.class), any())).thenReturn(false);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setOwnerId(423423423L);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);

    List<Task> tasks = eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(new State(), definitionInstance, EventScheduleMetaData.builder().workflowName("customScheduledActions").build(), SchedulingMetaData.builder().definitionId("id").definitionKey("key").build());

    assertEquals(1, tasks.size());
    verify(schedulingService, times(1)).isEnabled(any(DefinitionDetails.class), any());
    verify(schedulingService, times(1)).isMigrated(any());
  }

  @Test
  public void testPrepareUpdateTasksForEnableDisableCommand_MigrationEnabled() {
    when(schedulingService.isEnabled(any(DefinitionDetails.class), any())).thenReturn(false);
    when(schedulingService.isMigrated(any())).thenReturn(true);
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setOwnerId(423423423L);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);

    List<Task> tasks = eventScheduleHelper.prepareUpdateTasksForEnableDisableCommand(new State(), definitionInstance, EventScheduleMetaData.builder().workflowName("customScheduledActions").build(), SchedulingMetaData.builder().definitionId("id").definitionKey("key").build());

    assertEquals(2, tasks.size());
    verify(schedulingService, times(1)).isEnabled(any(DefinitionDetails.class), any());
    verify(schedulingService, times(1)).isMigrated(any());
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.intuit.paved-road.appintgwkflw-wkflautomate</groupId>
  <artifactId>was-event-in-package</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <properties>
    <dockerfile-maven-version>1.4.0</dockerfile-maven-version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <application.run.user>bootapp</application.run.user>
    <docker.repository>docker.intuit.com</docker.repository>
    <docker.path>appintgwkflw-wkflautomate/was-event-in/service</docker.path>
    <docker.image>was-event-in</docker.image>
    <timestamp>${maven.build.timestamp}</timestamp>
    <maven.build.timestamp.format>yyyyMMddHHmm</maven.build.timestamp.format>
    <git-commit-id-plugin.version>2.2.4</git-commit-id-plugin.version>
    <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
  </properties>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>properties-maven-plugin</artifactId>
        <version>1.0.0</version>
        <executions>
          <execution>
            <phase>initialize</phase>
            <goals>
              <goal>read-project-properties</goal>
            </goals>
            <configuration>
              <files>
                <file>../project.properties</file>
              </files>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <executions>
          <execution>
            <id>filter-resources</id>
            <phase>process-resources</phase>
            <goals>
              <goal>copy-resources</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}</outputDirectory>
              <resources>
                <resource>
                  <filtering>true</filtering>
                  <directory>${basedir}</directory>
                  <include>build.params.json</include>
                  <include>contrast.conf</include>
                </resource>
              </resources>
              <useDefaultDelimiters>true</useDefaultDelimiters>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>${maven-deploy-plugin.version}</version>
        <executions>
          <execution>
            <id>default-deploy</id>
            <phase>never</phase>
          </execution>
        </executions>
      </plugin>
    </plugins>
    <pluginManagement>
      <plugins>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>
		      org.codehaus.mojo
		    </groupId>
                    <artifactId>
		      exec-maven-plugin
		    </artifactId>
                    <versionRange>
	              [1.5.0,)
		    </versionRange>
                    <goals>
                      <goal>exec</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore/>
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
  <repositories>
    <repository>
      <id>central-mirror</id>
      <url>https://artifact.intuit.com/artifactory/maven-proxy</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>central-mirror</id>
      <url>https://artifact.intuit.com/artifactory/maven-proxy</url>
    </pluginRepository>
  </pluginRepositories>
  <distributionManagement>
    <snapshotRepository>
      <id>scm.dev.snap.repo</id>
      <name>SNAPSHOT REPO</name>
      <!--<url>https://artifact.intuit.com/artifactory/IBP.Intuit-Snapshots/Your.BU.or.group.SNAPSHOT.upload.path</url>-->
      <!--This is not used due to maven-deploy-plugin being disabled by phase=never-->
      <url>https://artifact.intuit.com/artifactory/IBP.Intuit-Snapshots</url>
    </snapshotRepository>
    <repository>
      <id>scm.int.rel.repo</id>
      <name>RELEASE REPO</name>
      <!--<url>https://artifact.intuit.com/artifactory/IBP.Intuit-Releases/Your.BU.or.group.RELEASE.upload.path</url>-->
      <!--This is not used due to maven-deploy-plugin being disabled by phase=never-->
      <url>https://artifact.intuit.com/artifactory/IBP.Intuit-Releases</url>
    </repository>
  </distributionManagement>
</project>

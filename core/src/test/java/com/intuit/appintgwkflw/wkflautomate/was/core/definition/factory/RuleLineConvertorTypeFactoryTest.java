package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiSplitConditionRuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepConditionRuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.RuleLineBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineParser;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import java.nio.charset.Charset;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RuleLineConvertorTypeFactoryTest {

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT =
      TestHelper.readResourceAsString("dmn/customApprovalWorkflowWithMultiSplit.dmn");

  RuleLineConvertorTypeFactory ruleLineConvertorTypeFactory;

  @Mock
  MultiStepConditionRuleLineBuilder multiStepConditionRuleLineBuilder;

  @Mock
  MultiSplitConditionRuleLineBuilder multiSplitConditionRuleLineBuilder;

  @InjectMocks
  MultiConditionRuleLineParser multiConditionRuleLineParser;

  @InjectMocks
  private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;

  private DmnModelInstance dmnModelInstance;
  private DmnModelInstance dmnModelInstanceWithMultiSplit;

  @Before
  public void init() {
    ruleLineConvertorTypeFactory = new RuleLineConvertorTypeFactory(
        multiSplitConditionRuleLineBuilder, multiStepConditionRuleLineBuilder);

    dmnModelInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

    dmnModelInstanceWithMultiSplit = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT, Charset.defaultCharset()));
  }

  @Test
  public void test_getHandlerMultiStepConditionRuleLineBuilder() {
    Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap =
        buildIndexToYesAndNoRulesMap(false);
    RuleLineBuilder ruleLineBuilder = ruleLineConvertorTypeFactory.getHandler(
        indexToYesAndNoDmnRulesMap);
    Assert.assertTrue(ruleLineBuilder instanceof MultiStepConditionRuleLineBuilder);
  }

  @Test
  public void test_getHandlerMultiSplitConditionRuleLineBuilder() {
    Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap =
        buildIndexToYesAndNoRulesMap(true);
    RuleLineBuilder ruleLineBuilder = ruleLineConvertorTypeFactory.getHandler(
        indexToYesAndNoDmnRulesMap);
    Assert.assertTrue(ruleLineBuilder instanceof MultiSplitConditionRuleLineBuilder);
  }

  Map<String, Map<String, List<List<Rule>>>> buildIndexToYesAndNoRulesMap(boolean isMultiSplit) {
    Collection<DecisionTable> decisionTables = getDecisionTable(isMultiSplit);
    DecisionTable decisionTable = decisionTables.stream().findFirst().get();
    Map<String, Map<String, DmnHeader>> dmnHeadersMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(
        decisionTable);
    Map<String, Map<String, List<List<Rule>>>> indexToRulesMap = multiConditionRuleLineParser.parseDecisionTable(
        decisionTable, dmnHeadersMap);
    return indexToRulesMap;
  }

  Collection<DecisionTable> getDecisionTable(boolean isMultiSplit) {
    if (isMultiSplit) {
      return dmnModelInstanceWithMultiSplit.getModelElementsByType(DecisionTable.class);
    }
    return dmnModelInstance.getModelElementsByType(DecisionTable.class);
  }


}


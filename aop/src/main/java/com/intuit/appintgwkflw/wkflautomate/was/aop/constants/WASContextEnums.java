package com.intuit.appintgwkflw.wkflautomate.was.aop.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/** Author: <PERSON><PERSON> Date: 24/12/19 Description: */
@AllArgsConstructor
@Getter
public enum WASContextEnums {
  AUTHORIZATION_HEADER("Authorization"),
  INTUIT_TID("tid"),
  INTUIT_REALMID("intuit_realmid"),
  OWNER_ID("ownerId"),
  RECORD_ID("recordId"),
  RECORD_TYPE("recordType"),
  PROCESS_INSTANCE_ID("processInstanceId"),
  DEFINITION_ID("definitionId"),
  TEMPLATE_ID("templateId"),
  CLUSTER_ID("clusterId"),
  HANDLER_ID("handlerId"),
  IDEMPOTENCY_KEY("idempotencyKey"),
  IS_EVENT("isEvent"),
  ENTITY_ID("entityId"),
  OFFERING_ID("offeringId"),
  WORKFLOW("workflow"),
  WORKFLOW_ID_KEY("workflowIdKey"),
  MESSAGE_EVENT("messageEvent"),
  IS_DLQ("isDLQ"),
  ACTIVITY_ID("activityId"),
  INTUIT_WAS_LOCALE("intuit_was_locale"),
  NONE(""),
  TASK_TYPE("taskType"),
  EVENT_TYPE("eventType"),
  TAGS_VERSION("tagsVersion"),
  APP_ID("intuit_appid"),
  INTUIT_USERID("intuit_userid");
	

  private String value;
}

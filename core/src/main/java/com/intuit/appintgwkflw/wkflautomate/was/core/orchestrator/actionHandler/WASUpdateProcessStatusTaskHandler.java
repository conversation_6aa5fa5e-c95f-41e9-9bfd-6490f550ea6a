package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UPDATE_PROCESS_STATUS_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper.DomainEventService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *     <p>Updates the process-status of the completed tasks in the process details entity in WAS DB.
 */
@Component
@AllArgsConstructor
public class WASUpdateProcessStatusTaskHandler extends WorkflowTaskHandler {

  private ProcessDetailsRepoService processDetailsRepoService;
  private DomainEventService domainEventService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_UPDATE_PROCESS_STATUS_ACTION_HANDLER;
  }

  @SuppressWarnings("serial")
  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    String processInstanceID = workerActionRequest.getProcessInstanceId();
    ProcessDetails processDetails =
        processDetailsRepoService.findByProcessId(processInstanceID).orElse(null);

    // Case where process is already deleted in downgrade flow from WAS DB
    if (ObjectUtils.isEmpty(processDetails)) {
      WorkflowLogger.warn(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Process already deleted=%s", processInstanceID)
                  .downstreamComponentName(DownstreamComponentName.WAS_DB)
                  .downstreamServiceName(DownstreamServiceName.UPDATE_PROCESSS_STATUS)
                  .className(this.getClass().getSimpleName()));
      return new HashMap<>(
          ImmutableMap.of(
              new StringBuilder(workerActionRequest.getActivityId())
                  .append(UNDERSCORE)
                  .append(RESPONSE.getName())
                  .toString(),
              Boolean.TRUE.toString()));
    }

    // Will call respective method if domain events publishing is enabled
    int updated =
        domainEventService.updateStatus(
            processDetails,
            ProcessStatus.ENDED,
            domainEventService.prepareEntityHeaders(workerActionRequest),
            EntityChangeAction.UPDATE,
            workerActionRequest.getVariableMap());

    // if not updated throw error
    WorkflowVerfiy.verify(updated == 0, UPDATE_PROCESS_STATUS_FAILURE, processInstanceID);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Process status updated succesfully for processId=%s, definitionId=%s, definitionCreatedByUserId=%s, activityId=%s",
                    processInstanceID,
                    processDetails.getDefinitionDetails().getDefinitionId(),
                    processDetails.getDefinitionDetails().getCreatedByUserId(),
                    workerActionRequest.getActivityId())
                .downstreamComponentName(DownstreamComponentName.WAS_DB)
                .downstreamServiceName(DownstreamServiceName.UPDATE_PROCESSS_STATUS)
                .className(this.getClass().getSimpleName()));

    // setting response with activityid_response
    return new HashMap<>(
        ImmutableMap.of(
            new StringBuilder(workerActionRequest.getActivityId())
                .append(UNDERSCORE)
                .append(RESPONSE.getName())
                .toString(),
            Boolean.TRUE.toString()));
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.UPDATE_PROCESS_STATUS, Type.WAS_METRIC, exception);
  }
}

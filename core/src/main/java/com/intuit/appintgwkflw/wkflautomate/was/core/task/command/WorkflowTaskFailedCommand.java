package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StateTransitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Failed Command marks activity fail in DB and calls downstream to handle the same.
 *
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WorkflowTaskFailedCommand extends WorkflowTaskCommand {

  public TaskCommand command() {
    return TaskCommand.FAILED;
  }

  private WorkflowTaskDBOperationManager dbOperationManager;
  private ActivityProgressDetailsRepository progressDetailRepo;
  private ProcessDetailsRepository processDetailRepo;

  @SuppressWarnings({"unchecked"})
  @Override
  public WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest) {

    WorkflowTaskResponse response = WorkflowTaskResponse.builder()
        .status(ActivityConstants.TASK_STATUS_FAILED).build();
    Task task = prepareTaskRequest(taskRequest);
    String eventType = InternalEventsUtil.commandToEventType(command());
    progressDetailRepo
        .findById(taskRequest.getId()).ifPresentOrElse(activityProgressDetails -> {
        	
      if (null != activityProgressDetails.getTxnDetails()) {
        /**
         * Could be present only for case Update/Complete.
         */
        taskRequest.setTxnId(activityProgressDetails.getTxnDetails().getTxnId());
      }

      //Update Status in Activity DB table only.
      dbOperationManager.markActivityProgressFailedInDB(task, activityProgressDetails);

      if (!taskRequest.isSkipCallback() && !StringUtils.isEmpty(taskRequest.getTxnId())) {
        /**
         * For FAILED status from user event, downstream would be invoked.
         */
        WorkflowTasks.getWorkflowTask(task.getType()).failed(task);
        response.setTxnId(activityProgressDetails.getTxnDetails().getTxnId());
        dbOperationManager.markTxnDetailsFailedInDB(task, activityProgressDetails.getTxnDetails());
      } else if (!taskRequest.isSkipTxnDBUpdate() && 
    		  null != activityProgressDetails.getTxnDetails()) {
        /**
         * For Failure due to downstream call (5xx, 4xx).
         */
        dbOperationManager.markTxnDetailsFailedInDB(task, activityProgressDetails.getTxnDetails());
      }
      if (StateTransitionServiceHelper.isStateTransitionPublishEnabled(taskRequest, eventType)) {
          stateTransitionService.publishEvent(taskRequest, 
        		  activityProgressDetails.getProcessDetails(), eventType);
      }
    }, () -> {
      /**
       * Create and Save in DB with failed status.
       */
      ProcessDetails processDetails = processDetailRepo.findById(taskRequest.getProcessInstanceId())
          .orElseThrow(() -> new WorkflowNonRetriableException(
              WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR));
      dbOperationManager.prepareActivityProgressAndSaveInFailedState(task, processDetails);
      if (StateTransitionServiceHelper.isStateTransitionPublishEnabled(taskRequest, eventType)) {
          stateTransitionService.publishEvent(taskRequest, processDetails, eventType);
      }
    });
    return response;
  }

}
package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import lombok.*;

/** <AUTHOR> */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SchedulingSvcRequest {
    private String referenceId;
    private String type;
    private String useCase;
    private Status status;
    private String metadata;
    private Integer priority;
    private ScheduleInfo scheduleInfo;
    private Boolean migration;
}

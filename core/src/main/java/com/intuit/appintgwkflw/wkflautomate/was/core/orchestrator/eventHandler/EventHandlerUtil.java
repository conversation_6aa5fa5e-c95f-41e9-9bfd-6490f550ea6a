package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class EventHandlerUtil {


  /**
   * Populate MDC context with ProcessDetails record.
   *
   * @param contextHandler :: MDC Context.
   * @param processDetails :: DB record of processInstance.
   */
  public static void populateContextFromProcessDetails(WASContextHandler contextHandler,
      ProcessDetails processDetails) {
    /**
     * Metric Logging of workflowName.
     */
    contextHandler.addKey(WASContextEnums.WORKFLOW,
        processDetails.getDefinitionDetails().getTemplateDetails().getTemplateName());
  }

}

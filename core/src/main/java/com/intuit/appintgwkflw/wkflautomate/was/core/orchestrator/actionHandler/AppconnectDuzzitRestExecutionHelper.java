package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.COLON;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.IDEMPOTENCY_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static java.lang.Boolean.TRUE;
import static org.apache.commons.lang3.StringUtils.isEmpty;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.AppConnectWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.AppConnectExecuteDuzzitRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request.WorkflowTaskHandlerInput;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.WorkflowTaskHandlerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import lombok.AllArgsConstructor;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * Helper for calling AppConnect Rest API for WorkerActionRequest
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class AppconnectDuzzitRestExecutionHelper {

  private final AuthDetailsService authDetailsService;
  private final AppConnectWASClient wasHttpClient;
  private final AppConnectWorkflowTaskHandlerHelper taskHandlerHelper;
  private final AppConnectConfig appConnectConfig;

  public Map<String, Object> executeWorkflowActionRequest(
      WorkerActionRequest workerActionRequest, Optional<Map<String, ParameterDetails>> parameterDetailsMap) {
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);

    AppConnectExecuteDuzzitRequest actionRequest =
        prepareExecuteDuzzitRequest(workerActionRequest, parameterDetailsMap, authDetails);

    return executeAppConnectRequest(actionRequest, workerActionRequest, authDetails);
  }

  public Map<String, Object> executeWorkflowActionRequest(
          WorkerActionRequest workerActionRequest, Optional<Map<String, ParameterDetails>> parameterDetailsMap, List<WorkflowTaskHandlerInput> additionalTaskHandlerInputs) {
    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);

    AppConnectExecuteDuzzitRequest actionRequest =
            prepareExecuteDuzzitRequest(workerActionRequest, parameterDetailsMap, authDetails);

    Optional.ofNullable(actionRequest)
            .map(AppConnectExecuteDuzzitRequest::getInputs)
            .ifPresent(inputs -> {
              Set<String> existingInputKeys = inputs.keySet();

              additionalTaskHandlerInputs.forEach(additionalInput -> {
                if (!existingInputKeys.contains(additionalInput.getName())) {
                  WorkflowLogger.logInfo("Adding Additional Task Handler Input name: %s", additionalInput.getName());
                  inputs.add(additionalInput.getName(), additionalInput.getValue());
                }
              });
            });

    return executeAppConnectRequest(actionRequest, workerActionRequest, authDetails);
  }

  Map<String, Object> executeAppConnectRequest(
      AppConnectExecuteDuzzitRequest actionRequest,
      WorkerActionRequest workerActionRequest,
      AuthDetails authDetails) {
    WASHttpRequest<MultiValueMap<String, String>, WorkflowTaskHandlerResponse> wasHttpRequest =
        WASHttpRequest.<MultiValueMap<String, String>, WorkflowTaskHandlerResponse>builder()
            .url(actionRequest.getEndpoint())
            .httpMethod(HttpMethod.POST)
            .requestHeaders(prepareRequestHeaders(actionRequest, authDetails))
            .request(actionRequest.getInputs())
            .retryHandler(RetryHandlerName.APPCONNECT_DOWNSTREAM_ERRORS)
            .responseType(new ParameterizedTypeReference<>() {})
            .build();

    WASHttpResponse<WorkflowTaskHandlerResponse> response =
        wasHttpClient.httpResponse(wasHttpRequest);
    // if error occurred  in executing external task throw exception
    WorkflowVerfiy.verify(
        (!response.isSuccess2xx()
            || null == response.getResponse()
            || isEmpty(response.getResponse().getSuccess())
            || !TRUE.toString().equals(response.getResponse().getSuccess())),
        taskHandlerHelper.getAppConnectDuzzitException(
            workerActionRequest.getHandlerId(), response));

    return taskHandlerHelper.prepareTaskHandlerResponse(workerActionRequest, response);
  }

  private HttpHeaders prepareRequestHeaders(AppConnectExecuteDuzzitRequest actionRequest, final AuthDetails authDetails) {
    // prepare request headers
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    // call offline ticket to renew the realm ticket
    requestHeaders.set(WorkflowConstants.AUTHORIZATION_HEADER,
        authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));

    requestHeaders.add(IDEMPOTENCY_KEY,
        new StringBuilder(actionRequest.getExternalTaskId())
            .append(COLON)
            .append(actionRequest.getInstanceId())
            .toString());

    // populate retry of response in case of error at appconnect
    requestHeaders.add(WorkflowConstants.RETRY_ON_ERROR, Boolean.TRUE.toString());

    return requestHeaders;
  }

  /**
   * prepare appconnect workflow task handler request
   *
   * @param workerActionRequest input workerActionRequest
   * @param parameterDetailsMap input parameterDetailsMap from task
   * @return AppConnectTaskHandlerRequest
   */
  private AppConnectExecuteDuzzitRequest prepareExecuteDuzzitRequest(
      final WorkerActionRequest workerActionRequest,
      final Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap,
      final AuthDetails authDetails) {

    return AppConnectExecuteDuzzitRequest.builder()
        .endpoint(prepareRequestEndpoint(workerActionRequest.getHandlerId(), authDetails))
        .realmId(workerActionRequest.getInputVariables().get(INTUIT_REALMID))
        .instanceId(workerActionRequest.getProcessInstanceId())
        .providerAppId(appConnectConfig.getProviderAppId())
        .inputs(prepareTaskHandlerInputs(workerActionRequest, parameterDetailsMap))
        .externalTaskId(workerActionRequest.getTaskId())
        .build();
  }

  /**
   * prepares body of the http request
   *
   * @param workerActionRequest input workerActionRequest
   * @param parameterDetailsMap input parameterDetailsMap from task
   * @return MultiValueMap<String, String>
   */
  private MultiValueMap<String, String> prepareTaskHandlerInputs(final WorkerActionRequest workerActionRequest,
      final Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap) {
    List<WorkflowTaskHandlerInput> payloadData = taskHandlerHelper.prepareTaskHandlerInputs(workerActionRequest, parameterDetailsMap);
    MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
    payloadData
        .stream()
        .forEach(input -> {
          body.add(input.getName(), input.getValue());
        });
    return body;
  }

  /**
   * prepares endpoint url of the duzzit to be executed
   *
   * @param handlerId input workerActionRequest handlerId
   * @return String
   */
  String prepareRequestEndpoint(final String handlerId, final AuthDetails authDetails) {
    return appConnectConfig.getConnectorEndpoint()
        .concat(handlerId)
        .concat(WorkflowConstants.APPEND_SUBSCRIPTION_ID)
        .concat(authDetails.getSubscriptionId());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.getGlobalId;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BaseTemplateElementType;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

/**
 * This class includes test cases for BusinessRuleTaskWorkflowStepProcessor.
 *
 * <AUTHOR>
 * */
@RunWith(MockitoJUnitRunner.class)
public class BusinessRuleTaskWorkflowStepProcessorTest {

  @InjectMocks
  BusinessRuleTaskWorkflowStepProcessor businessRuleTaskWorkflowStepProcessor;

  private AbstractFlowNodeBuilder flowNodeBuilder;
  private WorkflowStep workflowStep;
  private WorkflowStep parentWorkflowStep;
  private Map<String, WorkflowStep> workflowStepMap;
  private Map<String, String> dynamicActivityIdMap;
  private Map<String, String> effectiveParentIdMap;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData;

  @Before
  public void setUp() {
    flowNodeBuilder = Bpmn.createExecutableProcess().startEvent("effectiveParentId");
    workflowStep = new WorkflowStep();
    workflowStep.setId(getGlobalId("1234"));
    parentWorkflowStep = new WorkflowStep();
    workflowStepMap = new HashMap<>();
    dynamicActivityIdMap = new HashMap<>();
    effectiveParentIdMap = new HashMap<>();
    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent().businessRuleTask("precannedConditionId").done();
    dynamicBpmnWorkflowStepProcessorMetaData =
        DynamicBpmnWorkflowStepProcessorMetaData.builder()
            .flowNodeBuilder(flowNodeBuilder)
            .workflowStep(workflowStep)
            .parentWorkflowStep(parentWorkflowStep)
            .workflowStepMap(workflowStepMap)
            .effectiveParentIdMap(effectiveParentIdMap)
            .baseTemplateBpmnModelInstance(baseTemplateBpmnModelInstance)
            .build();
  }

  @Test
  public void testGetStepType() {
    Assert.assertEquals(
        StepTypeEnum.CONDITION, businessRuleTaskWorkflowStepProcessor.getStepType());
  }

  @Test
  public void testProcessDynamicBpmnStepForParentActionStepType() {
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    workflowStepCondition.setId(getGlobalId("precannedConditionId"));

    workflowStep.setWorkflowStepCondition(workflowStepCondition);
    parentWorkflowStep.setStepType(StepTypeEnum.ACTION);
    parentWorkflowStep.setActionGroup(new ActionGroup());

    effectiveParentIdMap.put(workflowStep.getId().getLocalId(), "effectiveParentId");
    dynamicActivityIdMap.put("precannedConditionId", "precannedConditionId");
    dynamicActivityIdMap.put("effectiveParentId", "effectiveParentId");
    dynamicActivityIdMap.put("1234", "precannedConditionId");

    CamundaProperty camundaProperty =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperty.class);
    CamundaProperties camundaProperties =
        baseTemplateBpmnModelInstance.newInstance(CamundaProperties.class);
    camundaProperty.setCamundaName("elementType");
    camundaProperty.setCamundaValue(BaseTemplateElementType.EXPLICIT.getName());
    camundaProperties.addChildElement(camundaProperty);
    ExtensionElements extensionElements =
        baseTemplateBpmnModelInstance.newInstance(ExtensionElements.class);
    extensionElements.addChildElement(camundaProperties);

    baseTemplateBpmnModelInstance
        .getModelElementById("precannedConditionId")
        .addChildElement(extensionElements);

    dynamicBpmnWorkflowStepProcessorMetaData.setWorkflowStep(workflowStep);
    dynamicBpmnWorkflowStepProcessorMetaData.setParentWorkflowStep(parentWorkflowStep);
    dynamicBpmnWorkflowStepProcessorMetaData.setEffectiveParentIdMap(effectiveParentIdMap);
    dynamicBpmnWorkflowStepProcessorMetaData.setBaseTemplateBpmnModelInstance(
        baseTemplateBpmnModelInstance);
    dynamicBpmnWorkflowStepProcessorMetaData.setDynamicActivityIdMap(dynamicActivityIdMap);

    businessRuleTaskWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);

    BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("effectiveParentId"),
            bpmnModelInstance.getModelElementById(dynamicActivityIdMap.get(
                    workflowStep.getId().getLocalId()))));
  }

  @Test
  public void testProcessDynamicBpmnStepForParentConditionStepType() {
    parentWorkflowStep.setStepType(StepTypeEnum.CONDITION);

    dynamicBpmnWorkflowStepProcessorMetaData.setParentWorkflowStep(parentWorkflowStep);
    businessRuleTaskWorkflowStepProcessor.processDynamicBpmnStep(
        dynamicBpmnWorkflowStepProcessorMetaData);

    BpmnModelInstance bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertFalse(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("effectiveParentId"),
            bpmnModelInstance.getModelElementById(workflowStep.getId().getLocalId())));
  }

  @Test
  public void testGetDynamicActivityId_forActionParent() {
    dynamicActivityIdMap.put("decisionElement", "businessRuleTask-1");
    dynamicActivityIdMap.put("sendForApproval", "callActivity-1");

    WorkflowStep effectiveParentWorkflowStep = new WorkflowStep();
    effectiveParentWorkflowStep.setStepType(StepTypeEnum.ACTION);
    effectiveParentWorkflowStep.setActionGroup(new ActionGroup());
    Assert.assertEquals(
        "businessRuleTask-2",
            businessRuleTaskWorkflowStepProcessor.getDynamicActivityId(
                    dynamicActivityIdMap, effectiveParentWorkflowStep));
  }

  @Test
  public void testGetDynamicActivityId_forConditionParent() {
    dynamicActivityIdMap.put("decisionElement", "businessRuleTask-1");
    dynamicActivityIdMap.put("sendForApproval", "callActivity-1");

    WorkflowStep effectiveParentWorkflowStep = new WorkflowStep();
    effectiveParentWorkflowStep.setStepType(StepTypeEnum.CONDITION);
    Assert.assertEquals(
            "businessRuleTask-1",
            businessRuleTaskWorkflowStepProcessor.getDynamicActivityId(
                    dynamicActivityIdMap, effectiveParentWorkflowStep));
  }

  @Test
  public void testGetDynamicActivityId_forNullParent() {
    dynamicActivityIdMap.put("sendForApproval", "callActivity-1");
    Assert.assertEquals(
            "businessRuleTask-1",
            businessRuleTaskWorkflowStepProcessor.getDynamicActivityId(
                    dynamicActivityIdMap, null));
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * This annotation should be used on fields which might contain PII information and should not be
 * printed in application logs. By default, the annotated field's value will be converted to a
 * default masked value.
 * A custom mask value can also be provided while using the annotation:- @RedactSensitiveField("customMaskValue")
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RedactSensitiveField {
    String value() default WorkflowConstants.DEFAULT_MASK_VALUE;
}
kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  labels:
    env: e2e-usw2-eks
  name: was-event-in-rollout
spec:
  strategy:
    canary:
      canaryMetadata:
        annotations:
          role: canary
        labels:
          role: canary
      stableMetadata:
        annotations:
          role: stable
        labels:
          role: stable
      steps:
      - setWeight: 25
      - pause:
          duration: 5m
      - setWeight: 50
      - pause:
          duration: 5m
  # Set replicas to null when enabling HPA, sample: 'replicas: null'
  replicas: null
  template:
    metadata:
      labels:
        env: e2e-usw2-eks
        splunk-index: e2e
      annotations:
        iam.amazonaws.com/role: k8s-appintgwkflw-wkflautomate-waseventin-usw2-e2e
    spec:
      containers:
      - name: app
        env:
        - name: APP_ENV
          value: e2e
        - name: APP_CONFIG_NAME
          value: waseventin
        - name: RUNTIME_ID
          value: was-event-in-runtime-customer
        - name: MESSAGE_MESH_SRC_TOPICS
          value: was-event-in-replay
        - name: EVENT_BUS_IDPS_POLICY_ID
          value: p-u2m5l8r6kz9n
        - name: MESSAGE_MESH_SRC_CONSUMER_GROUP
          value: was-event-in-processor-group-e2e-customer # This needs to be created somewhere else
        - name: MESSAGE_MESH_PIPELINE_FILENAME
          value: sample-pipeline.yml
        - name: SPRING_CONFIG_IDPS_POLICY_ID
          value: p-vkvgmu5wnx0r
        - name: HOSTED_CONFIG_BRANCH
          value: e2e
        resources:
          limits:
            cpu: 2000m
            memory: 6Gi
          requests:
            cpu: 1500m
            memory: 2Gi
      initContainers:
      - name: segment-app-init
        env:
        - name: APP_ENV
          value: e2e
        - name: SEGMENT_CLUSTER_ROLE_ARN
          value: arn:aws:iam::914122326290:role/shared.sbg-qbo-ppd-usw2-k8s
        - name: SEGMENT_IDPS_APPLIANCE
          value: MSaaSBU02-PRODUCTION-RJPH0E.pd.idps.a.intuit.com
        - name: SEGMENT_IDPS_POLICY_ID
          value: p-zb0qgnak4k1g
        - name: AWS_SDK_LOAD_CONFIG
          value: "1"
        - name: AWS_ROLE_SESSION_NAME
          value: kiam-kiam

package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import java.util.Map;

public interface RuleEvaluationHandler {

  /**
   *
   * @return return appId for initialization
   */
  String getName();


  /**
   * @param evaluateRulesMessage: contains  entity payload map
   * @return Response: Evaluates the DMN and gives back the response with result
   */
   WorkflowGenericResponse evaluateRules(Map<String, Object> evaluateRulesMessage);
}

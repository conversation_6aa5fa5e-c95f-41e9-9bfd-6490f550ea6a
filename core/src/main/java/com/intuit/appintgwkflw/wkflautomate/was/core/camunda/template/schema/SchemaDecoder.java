package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import static com.intuit.appintgwkflw.wkflautomate.was.core.util.SystemTagUtil.validateTemplateTagVersion;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.TAG;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.VERSION_KEY;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.CurrentStepDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.xml.ModelInstance;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WORKFLOW_NAME;

/** <AUTHOR> */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SchemaDecoder {

  /**
   * Filters the parameters which are required by UI from the template schema
   *
   * @param schema schema map which will contain the parameters
   * @return Parameters which are required by UI
   */
  public static Optional<Map<String, ParameterDetails>> getParametersForUI(
      Map<String, String> schema) {
    return fetchFromParams(schema, (entry) -> entry.getValue().isRequiredByUI());
  }

  /**
   * Filters the parameters which are required by Handler from the template schema
   *
   * @param schema schema map which will contain the parameters
   * @return Parameters which are required by Handler
   */
  public static Optional<Map<String, ParameterDetails>> getParametersForHandler(
      Map<String, String> schema) {
    return fetchFromParams(schema, (entry) -> entry.getValue().isRequiredByHandler());
  }

  /**
   * Get handlerDetails from the Schema
   *
   * @param schema map containing the input-output parameters
   * @return {@link HandlerDetails}
   */
  public static Optional<HandlerDetails> getHandlerDetails(Map<String, String> schema) {
    return convertFromSchema(schema, WorkFlowVariables.HANDLER_DETAILS_KEY, HandlerDetails.class);
  }

  /**
   * Get handlerDetails from the Schema
   *
   * @param extensionElements ExtensionElements containing the input-output parameters
   * @return {@link HandlerDetails}
   */
  public static Optional<HandlerDetails> getHandlerDetailsFromExtensionElements(
      ExtensionElements extensionElements) {
    if (null != extensionElements) {
      Collection<CamundaProperty> camundaProperties =
          extensionElements
              .getElementsQuery()
              .filterByType(CamundaProperties.class)
              .singleResult()
              .getCamundaProperties();
      for (CamundaProperty camundaProperty : camundaProperties) {
        if (WorkFlowVariables.HANDLER_DETAILS_KEY
            .getName()
            .equals(camundaProperty.getCamundaName())) {
          return Optional.ofNullable(
              ObjectConverter.fromJson(camundaProperty.getCamundaValue(), HandlerDetails.class));
        }
      }
    }
    return Optional.empty();
  }

  /**
   * Get taskDetails object
   *
   * @param schema map containing the input-output parameters
   * @return {@link TaskDetails}
   */
  public static Optional<TaskDetails> getTaskDetails(Map<String, String> schema) {
    return convertFromSchema(schema, WorkFlowVariables.TASK_DETAILS_KEY, TaskDetails.class);
  }

  /**
   * Get List of stepDetails for the step
   *
   * @param schema map containing the input-output parameters
   * @return map of stepId -> list of bpmn elements associated
   */
  public static Optional<Map<String, Set<String>>> getStepDetails(Map<String, String> schema) {
    return convertFromSchema(
        schema,
        WorkFlowVariables.STEP_DETAILS_KEY,
        new TypeReference<LinkedHashMap<String, HashSet<String>>>() {});
  }

  /**
   * Get List of Recurrence Elements for the template
   *
   * @param schema map containing the input-output parameters
   * @return map of stepId -> list of bpmn elements associated
   *     <p>recurrenceRule : {"recurrenceStartDate":"elementId1","recurrenceSchedule":"elementId2"}
   */
  public static Optional<Map<String, String>> getRecurrenceElementDetails(
      Map<String, String> schema) {
    return convertFromSchema(
        schema,
        WorkFlowVariables.RECURRENCE_DETAILS_KEY,
        new TypeReference<LinkedHashMap<String, String>>() {});
  }

  /**
   * Helper to convert form map to object
   *
   * @param schema map containing the input-output parameters
   * @param key schema key
   * @param valueType Object to convert into
   * @param <T>
   * @return
   */
  private static <T> Optional<T> convertFromSchema(
      Map<String, String> schema, WorkFlowVariables key, Class<T> valueType) {

    WorkflowVerfiy.verifyNull(schema, WorkflowError.INVALID_INPUT);
    // if key not found in schema return empty Optional
    return StringUtils.isBlank(schema.get(key.getName()))
            ? Optional.empty()
            : Optional.ofNullable(ObjectConverter.fromJson(schema.get(key.getName()), valueType));
  }

  /**
   * @param schema map containing the input-output parameters
   * @param key schema key
   * @param valueType Object to convert into
   * @param <T>
   * @return
   */
  @SuppressWarnings("rawtypes")
  private static <T> Optional<T> convertFromSchema(
      Map<String, String> schema, WorkFlowVariables key, TypeReference valueType) {

    WorkflowVerfiy.verifyNull(schema, WorkflowError.INVALID_INPUT);
    // if key not found in schema return empty Optional
    return StringUtils.isBlank(schema.get(key.getName()))
        ? Optional.empty()
        : Optional.ofNullable(ObjectConverter.fromJson(schema.get(key.getName()), valueType));
  }

  /**
   * Helper to convert parameterDetails
   *
   * @param schema map containing the input-output parameters
   * @param predicate filter
   * @return
   */
  private static Optional<Map<String, ParameterDetails>> fetchFromParams(
      Map<String, String> schema, Predicate<Entry<String, ParameterDetails>> predicate) {
    Optional<Map<String, ParameterDetails>> parametersDetailsMapOptional =
        convertFromSchema(
            schema,
            WorkFlowVariables.PARAMETERS_KEY,
            new TypeReference<Map<String, ParameterDetails>>() {});
    return parametersDetailsMapOptional.map(
        parameterDetailsMap ->
            parameterDetailsMap.entrySet().stream()
                .filter(predicate)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  /*
   * <AUTHOR>
   * get Template name given the template xml file and template type i.e BPMN or DMN
   *
   */
  public static String getTemplateName(ModelInstance template, ModelType modelType) {
    if (ModelType.BPMN.equals(modelType)) {
      Optional<Process> bpmnProcess =
          template.getModelElementsByType(Process.class).stream().findFirst();
      if (bpmnProcess.isPresent()) {
        return bpmnProcess.get().getId();
      }
    } else if (ModelType.DMN.equals(modelType)) {
      Optional<Decision> dmnProcess =
          template.getModelElementsByType(Decision.class).stream().findFirst();
      if (dmnProcess.isPresent()) {
        return dmnProcess.get().getId();
      }
    }
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Template name not present"));
    return null;
  }

  public static String getTemplateDisplayName(
      String templateName, BpmnModelInstance modelInstance) {
    Optional<Process> bpmnProcess =
        modelInstance.getModelElementsByType(Process.class).stream().findFirst();
    if (bpmnProcess.isPresent()) {
      return bpmnProcess.get().getName();
    }

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Template Display name not present for={}", templateName));
    return null;
  }

  /**
   * Get currentStepDetails object
   *
   * @param schema map containing the input-output parameters
   * @return {@link CurrentStepDetails}
   */
  public static Optional<CurrentStepDetails> getCurrentStepDetails(Map<String, String> schema) {
    return convertFromSchema(
        schema, WorkFlowVariables.CURRENT_STEP_DETAILS, CurrentStepDetails.class);
  }

  /**
   * Returns workflow name from parent Process Element in BPMN. This is for reading the
   * workflow-name key in the bpmn xml for legacy pre-canned templates.
   *
   * @param modelInstance : {@link BpmnModelInstance}
   * @return
   */
  public static String getWorkflowNameFromBpmnXml(BpmnModelInstance modelInstance) {
    String workflowName = null;
    Optional<Process> process =
        modelInstance.getModelElementsByType(Process.class).stream().findFirst();
    if (process.isPresent()) {
      Optional<CamundaProperty> workflowNameOptional =
          BpmnProcessorUtil.getCamundaProperty(process.get(), WORKFLOW_NAME);
      if (workflowNameOptional.isPresent()) {
        workflowName = workflowNameOptional.get().getCamundaValue();
      }
    }
    return workflowName;
  }

  public static String getAndValidateTemplateTagVersion(BpmnModelInstance modelInstance) {
    String tagVersion = StringUtils.EMPTY;
    Optional<Process> process =
        modelInstance.getModelElementsByType(Process.class).stream().findFirst();
    if (process.isPresent() && Objects.nonNull((process.get().getExtensionElements()))) {
      Optional<CamundaProperty> tagVersionOptional =
          BpmnProcessorUtil.getCamundaProperty(process.get(), TAG);
      if (tagVersionOptional.isPresent()) {
        Map<String, String> reducedVersionTag =
            ObjectConverter.fromJson(tagVersionOptional.get().getCamundaValue(), Map.class);
        if (reducedVersionTag.containsKey(VERSION_KEY)) {
          tagVersion = reducedVersionTag.get(VERSION_KEY);
          validateTemplateTagVersion(tagVersion);
        }
      }
    }
    return tagVersion;
  }
}

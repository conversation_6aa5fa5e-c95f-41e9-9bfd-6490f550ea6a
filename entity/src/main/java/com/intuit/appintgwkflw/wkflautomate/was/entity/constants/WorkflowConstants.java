package com.intuit.appintgwkflw.wkflautomate.was.entity.constants;

/** <AUTHOR> */
public final class WorkflowConstants {
  public static final String APP_CONNECT_ACTION_KEY = "action";
  public static final String ACTIVATE = "activate";
  public static final String DEACTIVATE = "deactivate";
  public static final String UNSUBSCRIBE = "unsubscribe";
  public static final String APP_CONNECT_WORKFLOW_NAME = "BPMN Workflow";
  public static final String WORKFLOW= "workflow";
  public static final String DMN_TYPE = ".dmn";
  public static final String BPMN_TYPE = ".bpmn";
  public static final String BPMN_TEMP_FILE_KEY = "bpmnTemp";
  public static final String DMN_TEMP_FILE_KEY = "dmnTemp";
  public static final String COMMA = ",";
  public static final String EQUAL = "=";
  public static final String AUTHORIZATION_HEADER = "Authorization";
  public static final String INTUIT_TID = "intuit_tid";
  public static final String WAS_REST_TEMPLATE = "wasRestTemplate";
  public static final String ACTION_NAME = "actionName";
  public static final String TASK_HANDLER = "taskHandler";
  public static final String HANDLER_ID = "handlerId";
  public static final String HANDLER_DETAILS = "handlerDetails";
  public static final String DELAY = "delay";
  public static final String TASK_DETAILS = "taskDetails";

  public static final String WORKFLOW_FILTER = "workflowFilter";
  public static final String RECORD_TYPE = "recordType";
  public static final String HANDLER_SCOPE = "handlerScope";
  public static final String OWNER_ID = "ownerId";
  public static final String APP_ID = "intuit_appid";
  public static final String DESCRIPTION = "description";
  public static final String TEMPLATE_REQ = "TEMPLATE_REQ";
  public static final String TEMPLATE_TYPE_ID = "/workflows/Template";
  public static final String DEFINITION_TYPE_ID = "/workflows/Definition";
  public static final String BPMN_DMN_VARIABLE_TYPE = "type";
  public static final String BPMN_DMN_VARIABLE_VALUE = "value";
  public static final String DMN_INPUT_TYPE_REF = "typeRef";
  public static final String ID = "id";
  public static final String DETAILS = "details";
  public static final String EVENT_HEADERS = "eventHeaders";
  public static final String ENTITY_CHANGE_TYPE = "entityChangeType";
  public static final String ENTITY_TYPE = "entityType";

  public static final String DISABLE_SALES_SETTINGS = "disableSalesSettings";
  public static final String ENTITY = "entity";
  public static final String DMN_VAR_OPEN_BRACE = "${";
  public static final String DMN_VAR_CLOSE_BRACE = "}";
  public static final String BPMN_DMN_VAR_SEPARATOR = "_";
  public static final int BPMN_DMN_MAX_VAR_NESTING = 5;
  public static final String RESPONSE_IDENTIFIER = "RESPONSE";
  public static final String UNDERSCORE = "_";
  public static final String WORKFLOW_STEP_IDENTIFIER = "workflowSteps";
  public static final String BPMN_DMN_VARIABLES = "variables";
  public static final String BPMN_MESSAGE_REF = "messageRef";
  public static final String BPMN_MESSAGE_NAME = "name";
  public static final String FILTER_TYPE_NAME = "name";
  public static final String FILTER_TYPE_CATEGORY = "category";
  public static final int MAX_PROCESS_PER_ENTITY_WORKFLOW = 1;
  public static final String SELECT_ALL = "SELECT_ALL";
  public static final String STRING_MODIFIER = "string";
  public static final String LIST_MODIFIER = "list";
  public static final String DOUBLE_MODIFIER = "double";
  public static final String JUEL_EXPRESSION_LANGUAGE = "juel";
  public static final String AND_MODIFIER = "&&";
  public static final String CONTAINS_OPERATOR = "CONTAINS";
  public static final String ANY_MATCH = "ANY_MATCH";
  public static final String NO_MATCH = "NO_MATCH";
  public static final String NOT_CONTAINS = "NOT_CONTAINS";
  public static final String SPACE = " ";
  public static final String OR_MODIFIER = "||";
  public static final String DOT_OPERATOR = ".";
  public static final String NOT_OPERATOR = "!";
  public static final String KEYWORD_ALL = "ALL";
  public static final String OPERATOR_BTW = "BTW";
  public static final String WHITESPACE_REGEX = "\\s+";
  public static final String PARENTHESIS_REGEX = "\\((.*?)\\)";
  public static final String QUOTE = "\"";
  public static final String DEFAULT_NUMBER_CONDITION = "GTE 0";
  public static final String INTUIT_USERID = "intuit_userid";
  public static final String INTUIT_REALMID = "intuit_realmid";
  public static final String INTUIT_TOKEN = "intuit_token";
  public static final String TRIGGERED_BY_INTUIT_USERID = "triggered_by_intuit_userid";
  public static final String STRING_TYPE_CAMUNDA = "String";
  public static final String INTEGER_TYPE_CAMUNDA = "integer";
  public static final String OBJECT_TYPE_CAMUNDA = "Object";
  public static final String GET_KEYWORD = "GET";
  public static final String COLON = ":";
  public static final String SUB_QUERIES_IDENTIFIER = "subQueries";
  public static final String FILTER_IDENTIFIER = "where";
  public static final String IDEMPOTENCY_KEY = "idempotency_key";
  public static final String DISABLE_IN_PROGRESS = "Definition Disable in Progress";
  public static final String DELETE_IN_PROGRESS = "Definition Delete in Progress";
  public static final String DEFINITION_ENABLED_SUCCESSFULLY = "Definition enabled Successfully";
  public static final String WORKFLOW_ALREADY_ACTIVE = "workflow already active";
  public static final String WORKFLOW_ALREADY_INACTIVE = "workflow already inactive";
  public static final String REQUEST = "REQUEST";
  public static final String RESPONSE = "RESPONSE";
  public static final String RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND";
  public static final String DEFINITION_NOT_FOUND = "No process definition found";
  public static final String DEPLOYMENT_NOT_FOUND = "No matching definition with id %s";
  public static final String EQUALS_OPERATOR = "EQUALS";
  public static final String CAMUNDA_DISABLE_MESSAGE_NAME = "disable";
  public static final String CAMUNDA_DELETED_MESSAGE_NAME = "deleted";
  public static final String CAMUNDA_DELETED_VOIDED_DISABLED_MESSAGE_NAME = "deleted_voided_disable";
  public static final String EQ = "EQ";
  public static final String RETRY_ON_ERROR = "Retry-On-Error";
  public static final String WORKFLOW_ID = "workflowId";
  public static final String PROCESS_STATUS_CLOSE = "isProcessClosed";
  public static final String MAX_TRIES = "maxTries";
  public static final String SYSTEM_OWNER_ID = String.valueOf(Long.MIN_VALUE);
  public static final String NON_WAS_WORKFLOW_TYPE = "simple";
  public static final String ACTIVE_WORKFLOW = "active";
  public static final String ID_KEY = "Id";
  public static final String ROOT_PROCESS_INSTANCE_ID = "rootProcessInstanceId";
  public static final String CALLED_PROCESS_CREATION_RESULT = "calledProcessCreationResult";
  public static final String ROOT_DEFINITION_KEY = "rootDefinitionKey";
  public static final String ACTIVITY_ID = "activityId";
  public static final String OFFERING_ID = "intuit_was_offeringid";
  public static final String EVENT_TYPE_DOWNGRADED = "downgraded";
  public static final String BUILD_CUSTOM_WORKFLOW = "build_custom_workflow";
  public static final String BUILD_MULTI_CONDITION_WORKFLOW = "build_multi_condition_workflow";
  public static final String DECISION = "decision";
  public static final String DECISION_TABLE = "decisionTable_1";
  public static final String INPUT = "input";
  public static final String OUTPUT = "output";
  public static final String INPUT_EXPRESSION = "inputExpression_";
  public static final String DECISION_RULE = "DecisionRule_1";
  public static final String INPUT_ENTRY = "inputEntry_";
  public static final String OUTPUT_ENTRY = "outputEntry_";
  public static final String BY = "by";
  public static final String SOURCE = "source";
  public static final String VARIABLES = "variables";
  public static final String PROCESS_ENGINE_EXCEPTION = "ProcessEngineException";
  public static final String OPTIMISTIC_LOCKING_EXCEPTION = "optimisticlockingexception";
  public static final String NAMESPACE_ID = "namespaceId";
  public static final String NAMESPACE_VALUE_50M = "50000000";
  /**
   * From below link
   * @link https://github.com/camunda/camunda-bpm-platform/blob/4dab4f8be57ac96e4e0c5567d614569dbc027bb7/engine-rest/engine-rest/src/main/java/org/camunda/bpm/engine/rest/sub/impl/AbstractVariablesResource.java#L254
   * it was identified OptimisticLockingException can be wrapped up under different exceptions.
   * Hence, using contains check on errorMessage for text, optimisticlockingexception will not work here.
   *
   * With following link
   * @link https://github.com/camunda/camunda-bpm-platform/blob/4dab4f8be57ac96e4e0c5567d614569dbc027bb7/engine/src/main/java/org/camunda/bpm/engine/impl/db/EnginePersistenceLogger.java#L138
   * it was identified Error code will be 005 suffixed for Optimistic locking exception if thrown from ProcessEngine Persistence layer.
   * For Persistence layer, code will be ENGINE-03005.
   * It was observed error code ENGINE-03005 is received for /message API, /execution Update API and In ExternalTask SDK(not relevant for API in-place retries) as well.
   * Hence, will be using text, ENGINE-03005 for identifying Optimistic locking exception and handling the same.
   */
  public static final String OPTIMISTIC_LOCKING_EXCEPTION_CODE = "ENGINE-03005";
  public static final String WAS_ACCESS_ERROR_CODE = "IAC-001002";
  public static final String CUSTOM_DECISION_RESULT = "decisionResult";
  public static final String CUSTOM_DECISION_ELEMENT = "decisionElement";
  public static final String CUSTOM_START_EVENT = "customStartEvent";
  public static final String CUSTOM_START = "customStart";
  public static final String HYPHEN = "-";
  public static final String BACKSLASH = "/";
  public static final String APPEND_SUBSCRIPTION_ID = "?subscriptionId=";
  public static final String FILTER_CONDITION = "FilterCondition";
  public static final String CONDITION_RULES = "rules";
  public static final String FILTER_RECORD_TYPE = "FilterRecordType";
  public static final String PLACEHOLDER_IDENTIFIER = "${";
  public static final String PLACEHOLDER_REGEX = "[${}]";
  public static final String CLOSE_TASK = "CloseTask";
  public static final String CREATE_TASK = "createTask";
  public static final String SEND_FOR_REMINDER_ACTION_ID = "sendForReminder";
  public static final String SEND_EXTERNAL_TASK = "sendExternalEmail";
  public static final String FILTER_CLOSE_TASK_CONDITIONS = "FilterCloseTaskConditions";
  public static final String IS_RECURRING = "isRecurring";
  public static final String RECUR_FREQUENCY = "recurFrequency";
  public static final String PROJECT_SERVICE = "PROJECT_SERVICE";
  public static final String STAGE = "STAGE";
  public static final String QBO = "QBO";
  public static final String OINP = "OINP";
  public static final String RULE_EVALUATE_EXCEPTION = "Rule evaluation exception";
  public static final String INPUT_CANNOT_BE_NULL_OR_EMPTY = "Input cannot be null or empty";
  public static final String INPUT_CANNOT_BE_NULL = "input cannot be null";
  public static final String PROCESSING_EVENT_FAILED = "Processing event failed";
  public static final String OFFLINE_TICKET_CLIENT_EXCEPTION = "Could not obtain authorization header; statusCode=%s";
  public static final String MESSAGE_PLACE_HOLDER = "{0}{1}";
  public static final String MESSAGE_PLACE_HOLDER_WITH_UNDERSCORE = "{0}_{1}";
  public static final String MESSAGE_PLACE_HOLDER_WITH_SPACE = "{0} {1}";
  public static final String APP_CONNECT_WORKFLOW_RESPONSE_ID = "66666";
  public static final String TRIGGER = "trigger";
  public static final String CONDITION = "condition";
  public static final String ACTION = "action";
  public static final String EXTERNAL_TASK = "externalTask";
  public static final String SERVICE_TASK = "serviceTask";
  public static final String SERVICE_TASK_TEST = "serviceTaskTest";
  public static final String USER_NOT_IN_REALM_EXCEPION = "UserNotInRealmException";
  public static final String INVALID_TICKET_EXCEPION = "InvalidTicketException";
  public static final String HTTPS_PROTOCOL = "https://";
  public static final String ACCESS_V2_TICKET_ENDPOINT = "/v2/tickets";
  public static final String SWIMLANE = "swimlane";
  public static final String FIELDS = "fields";
  public static final String PERSONA_ID = "persona_id";
  public static final String REALM_ID = "realm_id";
  public static final String CURRENT_USER_PERSONA = "CURRENT_USER_PERSONA";
  public static final String CURRENT_USER_GLOBAL = "CURRENT_USER_GLOBAL";
  public static final String ZERO = "0";
  public static final String ONE = "1";
  public static final String WORKFLOW_DEFINITION = "workflowDefinition";
  public static final String BLANK = "";
  public static final String PARAMETERS = "parameters";
  public static final String USER_VARIABLES = "user_variables";

  public static final String CONFIG_PARAMETER_VARIABLES = "config_parameter_variables";
  public static final String USER_META_DATA = "user_meta_data";
  // JIRA to clean up the flag https://jira.intuit.com/browse/QBOES-9640
  public static final String SINGLE_DEF_WORKFLOW_CREATE_PRECANNED =
      "qbo-adv-single-definition-create-precanned";
  public static final String BANK_DEPOSIT_REMINDER_TEMPLATE = "depositbankreminder";
  public static final String SELECTED = "selected";
  public static final String PROCESS_VARIABLES = "process_variables";
  public static final String SEQUENCE_VARIABLES = "sequence_variables";
  public static final String CONFIG_PROCESS_VARIABLES = "config_process_variables";
  public static final String RULE_LINE_VARIABLES = "rule_line_variables";
  public static final String BPMN_PLACEHOLDER_VALUES = "bpmn_placeholder_values";
  public static final String DMN_PLACEHOLDER_VALUES = "dmn_placeholder_values";
  public static final String ACTION_SELECTED = "selected";
  public static final String ACTION_PARAMETERS = "parameters";
  public static final String ACTION_UNSELECTED = "false";
  public static final String DMN_TASKS = "dmnTasks";
  public static final String FIELD_VALUE = "fieldValue";
  public static final String WORKFLOW_TRANSITION_EVENTS = "workflowTransitionEvents";
  public static final String WORKFLOW_TRANSITION_EVENTS_TEST = "workflowTransitionEventsTest";
  public static final String DEFAULT_OFFERING = "default";
  public static final String ACTION_KEY = "actionKey";
  public static final String UPDATE_TIME_VARIABLE = "updateTime";
  public static final String ACTIVITY_STATUS_VARIABLE = "status";
  public static final String EXT_ACTIVITY_STATUS_VARIABLE = "externalActivityStatus";
  public static final String ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP = "sequenceVariableMap";
  public static final String MIGRATE_EVENT_TYPE = "migrate";
  public static final String DEFINITION_KEY = "definitionKey";
  public static final String RECURRENCE_START_DATE = "recurrenceStartDate";
  public static final String RECURRENCE_SCHEDULE = "recurrenceSchedule";
  public static final String TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
  public static final String RECURRENCE_RECUR_TYPE = "recurType";
  public static final String RECURRENCE_INTERVAL = "interval";
  public static final String RECURRENCE_ACTIVE = "active";
  public static final String RECURRENCE_START_DATE_KEYWORD = "startDate";
  public static final String RECURRENCE_END_DATE_KEYWORD = "endDate";
  public static final String RECURRENCE_TIME_KEYWORD = "recurrenceTime";
  public static final String RECURRENCE_TIME_ZONE_KEYWORD = "timeZone";
  public static final String RECURRENCE_MINUTES_KEYWORD = "minutes";
  public static final String RECURRENCE_HOURS_KEYWORD = "hours";
  public static final String RECURRENCE_DAYS_OF_MONTH = "daysOfMonth";
  public static final String RECURRENCE_DAY_OF_MONTH = "dayOfMonth";
  public static final String RECURRENCE_DAYS_OF_WEEK = "daysOfWeek";
  public static final String RECURRENCE_WEEKS_OF_MONTH = "weeksOfMonth";
  public static final String RECURRENCE_WEEK_OF_MONTH = "weekOfMonth";
  public static final String RECURRENCE_MONTHS_OF_YEAR = "monthsOfYear";
  public static final String RECURRENCE_MONTH_OF_YEAR = "monthOfYear";
  public static final String CALENDAR_MONTH = "monthOfYear";
  public static final String CALENDAR_DAY = "dayOfMonth";
  public static final String CALENDAR_YEAR = "year";
  public static final String ENTITY_CHANGE_TYPE_CREATED = "created";
  public static final String WORKFLOW_NAME = "workflowName";
  public static final String TAG="tags";
  public static final String SYSTEM_TAG ="system_tag";
  public static final String VERSION_KEY = "version";
  public static final String INVALID_STRING_TAG="invalid_string_tag";
  public static final String INTUIT_WAS_LOCALE = "intuit_was_locale";
  public static final String DEFAULT_MASK_VALUE = "*****";
  public static final String RESPONSE_PAYLOAD = "Response Payload :: %s";
  public static final String JSONB_EXTRACT_PATH_TEXT= "jsonb_extract_path_text";
  public static final String CUSTOM_FIELD_PREFIX= "CF";
  public static final String CUSTOM_FIELD_SUFFIX= "CustomField";
  public static final Integer OPERATOR_INDEX = 0;
  public static final Integer VALUE_INDEX = 1;
  public static final String WORKFLOW_STEP_CONDITION_TYPE = "conditionType";
  public static final String INDEX_INITIAL_VALUE = "0";
  public static final String INDEX_COLUMN = "Index";
  public static final String VALUE_IDENTIFIER = "value";
  public static final String EXTERNAL_TASK_REST_HANDLER = "externalTaskRestHandler";
  public static final String USER_ID = "userId";
  public static final String TICKET_KEYWORD = "ticket";
  public static final String ON_BEHALF_OF = "onBehalfOf";
  public static final String ASSIGNEE = "assignee";
  public static final String INTUIT_WORKFLOWS = "intuit-workflows";
  public static final String REGISTER_NOTIFICATIONS_WEBHOOKS = "register-notification-webhooks";
  public static final String HANDLER_DUZZIT_URL = "/%s/api/%s";
  public static final String DOLLAR = "$";
  public static final String ENTITY_OPERATION = "entityOperation";
  public static final String DEPLOYMENT_NAME = "deployment-name";
  public static final String OBJ_TYPE_NAME = "objectTypeName";
  public static final String SERIALIZATION_DATA_FORMAT = "serializationDataFormat";
  public static final String VALUE_INFO = "valueInfo";
  public static final String JAVA_UTIL = "java.util";
  public static final String JAVA_UTIL_ALL = "java.util.*";
  public static final String CUSTOM_WAIT_EVENT = "customWait";
  public static final String CUSTOM_REMINDER = "reminder";
  public static final String CUSTOM_RECUR_EVENT = "customRecur";
  public static final String NEW_CUSTOM_START = "newCustomStart";
  public static final String PROCESS_IDENTIFIER = "process";
  public static final String TEMPLATE_DATA = "templateData";
  public static final String TEMPLATE_QUERY = "template";
  public static final Integer VALUE_ONE = 1;
  public static final String PROCESS_END_IDENTIFIER = "end";
  public static final String START = "start";
  public static final String END = "end";
  public static final String USER_ATTRIBUTES = "userAttributes";
  public static final String DECISION_RESULT_SEQUENCE_FLOW_EXPRESSION = "${decisionResult == '%s'}";
  public static final Integer START_INDEX_VALUE = 0;
  public static final String YES_RULE = "yes";
  public static final String NO_RULE = "no";
  public static final String NO_HTTP_RESPONSE = "NoHttpResponseException";
  public static final String CUSTOM_REMINDER_START_DUZZIT = "custom-reminder-start-process.json";
  public static final String CUSTOM_REMINDER_WAIT_DUZZIT = "custom-reminder-wait.json";
  public static final String CUSTOM_REMINDER_RECUR_DUZZIT = "custom-reminder-recur-process.json";
  public static final String JSON_EXTENSION = ".json";
  public static final String API_SEGMENT = "api";
  public static final String DEFAULT_KEY = "default";
  public static final String ACTIVITY_PROGRESS_DETAILS = "activityProgressDetails";
  public static final String RUNTIME_ATTRIBUTES = "runtimeAttributes";
  public static final String CALLED_ELEMENT_LATEST_BINDING = "latest";
  public static final String WORKFLOW_STEP_NEXTS = "nexts";
  public static final Integer DYNAMIC_BPMN_MIN_NUMBER_OF_APPROVERS = 6;
  public static final String ESS_TRIGGER_ENABLED = "qbo-adv-ess-trigger-enabled";
  public static final String EVENT = "EVENT";
  public static final String DYNAMIC_BPMN_BASE_TEMPLATE_RESOURCE_PATH = "baseTemplates/";
  public static final String ELEMENT_TYPE = "elementType";


  // Joda DateTime fields
  public static final String CALENDAR_CENTURY_OF_ERA = "centuryOfEra";
  public static final String CALENDAR_YEAR_OF_ERA = "yearOfEra";
  public static final String CALENDAR_YEAR_OF_CENTURY = "yearOfCentury";
  public static final String CALENDAR_WEEK_YEAR = "weekyear";
  public static final String CALENDAR_MONTH_OF_YEAR = "monthOfYear";
  public static final String CALENDAR_WEEK_OF_WEEK_YEAR = "weekOfWeekyear";
  public static final String CALENDAR_HOUR_OF_DAY = "hourOfDay";
  public static final String CALENDAR_MINUTE_OF_HOUR = "minuteOfHour";
  public static final String CALENDAR_SECOND_OF_MINUTE = "secondOfMinute";
  public static final String CALENDAR_MILLIS_OF_SECOND = "millisOfSecond";
  public static final String CALENDAR_DAY_OF_WEEK = "dayOfWeek";
  public static final String CALENDAR_ERA = "era";
  public static final String CALENDAR_DAY_OF_YEAR = "dayOfYear";
  public static final String CALENDAR_MILLIS_OF_DAY = "millisOfDay";
  public static final String CALENDAR_SECOND_OF_DAY = "secondOfDay";
  public static final String CALENDAR_MINUTE_OF_DAY = "minuteOfDay";
  public static final String CALENDAR_ZONE = "zone";
  public static final String CALENDAR_MILLIS = "millis";
  public static final String CALENDAR_AFTER_NOW = "beforeNow";
  public static final String CALENDAR_BEFORE_NOW = "afterNow";

  public static final String WAS_TRIGGER = "WAS_TRIGGER";
  public static final String PARAMETER_NAME = "parameterName";
  public static final String PARAMETER_TYPE = "parameterType";
  public static final String CONDITIONAL_EXPRESSION = "conditionalExpression";
  public static final String EXISTING_SUBSCRIPTION_FOUND_APPCONNECT_ERROR_CODE = "IAC-002325";
  public static final String ENABLE_ALL_REALMS = "-1";
  public static final String CONTENT_TYPE_JSON = "application/json";
  public static final String CONNECTOR_TID = "connector-";
  public static final String XLSX = "xlsx";

  public static final String MIGRATED_DEFINITION = "migratedDefinition";
  public static final String AUTH_DETAILS = "authDetails";
  public static final String TEMPLATE_ID = "templateID";
  public static final String DOC_NUMBER = "DocNumber";
  public static final String CUSTOM_TASK_PLACEHOLDER_EXTRACTION_ENABLED = "qbo-adv-was-custom-task-placeholder-extraction-enabled";

  public static final String DEFINITION_DETAILS_PENDING_DELETION = "definitionDetailsPendingDeletion";
  public static final String SUBSCRIPTIONS = "subscriptions";
  public static final String WORKFLOW_VARIABILITY_DOWNGRADE_FF = "SBSEG-wkflatmnsvc-qbo-adv-enable-workflow-variability-downgrade";

  public static final String CONFIG_MERGE_FF_ENABLED = "qbo-adv-sbseg-config-merge-enabled";

  public static final String AUTH_DETAILS_AVAILABLE = "authDetailsAvailable";
  public static final String JUEL_DMN_ENGINE = "juelDmnEngine";
  public static final String FEEL_DMN_ENGINE = "feelDmnEngine";
  public static final String FEEL_EXPR_SUPPORTED = "qbo-adv-feel-expr-supported";
  public static final String FEEL_EXPRESSION_LANGUAGE = "feel";
  public static final String NOT_OP = "not";
  public static final String GTE_OP = "GTE";
  public static final String LTE_OP = "LTE";

  public static final String CONTAINS_ANY_ELEMENT = "containsAnyElement";
  public static final String SQS_APPROXIMATE_RECEIVE_COUNT = "ApproximateReceiveCount";
  public static  final String SQS_EXECUTOR_THREAD = "sqs-executor-thread";
  public static  final String SQS_EXECUTOR_THREAD_BEAN = "sqs-executor-thread-bean";
  public static final String CUSTOM_REMINDER_TEMPLATE="customReminder";
  public static final String CUSTOM_APPROVAL_TEMPLATE="customApproval";
  public static final String IXP_MANAGER_BEAN = "IXPManager";

  public static  final String BULK_DEF_MIGRATION_SERVICE = "bulkDefinitionMigrationServiceImpl";
  public static  final String BULK_DEF_MIGRATION_ESS_SERVICE = "bulkDefinitionMigrationToESSServiceImpl";

  public static final String WORKER_RETRY_CONFIG_ENABLED_FF = "SBSEG-wkflatmnsvc-qbo-adv-enable-worker-retry";
  public static final String DUZZIT_SOURCE_KEY = "Source";
  public static final String DUZZIT_SOURCE_VALUE = "WAS";
  public static final String RESOURCE_ID_VALUE = "resourceId";

  public static final String DEFINITION_ID = "id";
  public static final String OBFUSCATE = "obfuscate";

  public static final String V1 = "v1";
  public static final String LOCALE = "locale";
  public static final String DOMAIN_EVENT_TRIGGER = "qbo-adv-domain-event-trigger";

  public static final String CUSTOM_TEMPLATE_CATEGORY = "CUSTOM";

  public static final String PROCESS_ID = "processId";
  public static final String ENTITY_ID = "entityId";
  public static final String INTUIT_OFFERING_HEADER = "intuit_offeringId";

  public static final String SYSTEM = "SYSTEM";
  public static final String SINGLE = "SINGLE";

  public static final String ON_DEMAND_APPROVAL="onDemandApproval";

  public static final String ON_DEMAND_APPROVAL_REQUEST_TYPE = "ON_DEMAND_APPROVAL";
  public static final String CUSTOM_WORKFLOW_REQUEST_TYPE = "CUSTOM_WORKFLOW";
  public static final String THIRD_PARTY_TXN_REQUEST_TYPE = "THIRD_PARTY_APPROVAL";
  public static final String BPMN_DETAILS_KEY = "bpmnDetailsKey";
  public static final String APPROVAL_WORKFLOW_TYPE = "Approval";
  public static final String APPROVER_DETAILS = "approverDetails";

  public static final String CUSTOM_REMINDER_START_TOTAL_PAGES = "totalPages";
  public static final String CUSTOM_REMINDER_START_PAGE_SIZE = "pageSize";
  public static final String CUSTOM_REMINDER_START_MAXIMUM_RESULTS = "maxResults";
  public static final String IS_RECURRING_ENABLED = "isRecurring";
  public static final String MAX_SCHEDULE_COUNT = "maxScheduleCount";
  public static final String SEND_COMPANY_EMAIL = "sendCompanyEmail";
  public static final String APPROVAL = "approval";
  public static final String PROCESS_EXPIRY_TIME_DURATION = "processExpiryTimeDuration";
  public static final String PROCESS_TIMER_DURATION_DAYS_PREFIX = "P";
  public static final String PROCESS_TIMER_DURATION_DAYS = "D";
  public static final String APPROVAL_GROUP_BAD_REQUEST_ERROR_CODE = "A30";
  public static final String EVERY_DAY = "Everyday";

  public static  final String NUMA_EXECUTOR_THREAD_BEAN = "numa-executor-thread-bean";
  public static  final String NUMA_EXECUTOR_THREAD = "numa-executor-thread";

  public static final String NUMAFLOW_ENABLED_FF = "qbo-adv-numaflow-%s-enabled"; // here %s defines the entityEventType.
  public static  final String CAMUNDA_PUSH_TASK_EXECUTOR_THREAD = "camunda-push-task-executor-thread";
  public static  final String CAMUNDA_PUSH_TASK_EXECUTOR_THREAD_BEAN = "camunda-push-task-executor-thread-bean";
  public static  final String KAFKA_RECEIVED_TIMESTAMP = "kafka_receivedTimestamp";
  public static final String INVOKE_SCHEDULING_FLOW_FF= "SBSEG-wkflatmnsvc-invoke-scheduling-flow-%s";
  public static final String TIME_BASED_REMINDERS_FF = "SBSEG-wkflatmnsvc-time-based-reminders-precanned";
  public static final String GC_EXECUTOR_THREAD_BEAN = "gc-executor-thread-bean";
  public static final String GC_ERROR_HANDLING_ENABLED_FF = "qbo-adv-gc-error-handling-enabled";
  public static final String APPCONNECT_WORKFLOW_UPDATE_DISABLED_FF = "SBSEG-wkflatmnsvc-qbo-adv-mcr-appconnect-update-disabled";
  public static final String ONDEMAND_FLOW_ENABLED_FF = "qbo-adv-ondemand-flow-enabled";
  public static final String SCHEDULING_ERROR_PATTERN = "Error=(\\{.*?\\})";
  public static final String DEVOPS_ROLE = "devops";
  public static final String DEFAULT_LOCALE = "en_US";
  public static final String RBAC_CREATE_UPDATE_FF = "SBSEG-QBO-was-rbac-create-update";
  }

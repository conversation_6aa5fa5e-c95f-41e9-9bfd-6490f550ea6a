package com.intuit.pavedroad.appintgwkflwwkflautomate.waseventinapp.rest.controllers;

import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

public class AppStatusControllerTest {

    @Test
    public void healthCheckTest() {
        AppStatusController appStatusController = new AppStatusController();
        ResponseEntity<String> responseEntity = appStatusController.healthCheck();
        Assert.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        Assert.assertEquals("Health Check Ok", responseEntity.getBody());
    }
}
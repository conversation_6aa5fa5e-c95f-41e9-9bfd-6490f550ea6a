package com.intuit.appintgwkflw.wkflautomate.was.common.annotations;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ApplyCircuitBreaker {

  CircuitBreakerActionType action() default CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT;

}

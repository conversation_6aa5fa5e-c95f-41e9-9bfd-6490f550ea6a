package com.intuit.appintgwkflw.wkflautomate.was.event.gc.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.MessageHeaders;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GCUtilTest {
    @Mock
    private WASContextHandler contextHandler;

    @Mock
    private GCConfig gcConfig;

    @Mock
    private FeatureFlagManager featureFlagManager;

    @Mock
    private MessageHeaders headers;

    private GCUtil gcUtil;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        gcUtil = new GCUtil(contextHandler, gcConfig, featureFlagManager);
    }

    @Test
    public void testIsGCEnabled_ServiceDisabled() {
        when(gcConfig.isServiceEnabled()).thenReturn(false);
        EventEntityType eventEntityType = EventEntityType.SCHEDULING;
        assertFalse(gcUtil.isGCEnabled(eventEntityType));
    }

    @Test
    public void testIsGCEnabled_ServiceEnabled_FeatureFlagFalse() {
        when(gcConfig.isServiceEnabled()).thenReturn(true);
        when(featureFlagManager.getBooleanWithContextMap(any(String.class), any(Boolean.class), any(Map.class), any())).thenReturn(false);
        EventEntityType eventEntityType = EventEntityType.SCHEDULING;
        assertFalse(gcUtil.isGCEnabled(eventEntityType));
    }

    @Test
    public void testIsGCEnabled_ServiceEnabled_FeatureFlagTrue() {
        when(gcConfig.isServiceEnabled()).thenReturn(true);
        when(featureFlagManager.getBooleanWithContextMap(any(String.class), any(Boolean.class), any(Map.class), any())).thenReturn(true);
        EventEntityType eventEntityType = EventEntityType.SCHEDULING;
        assertTrue(gcUtil.isGCEnabled(eventEntityType));
    }
}

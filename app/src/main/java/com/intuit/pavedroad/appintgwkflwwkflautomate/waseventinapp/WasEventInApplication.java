/**
 * Copyright 2015-2020 Intuit Inc. All rights reserved. Unauthorized reproduction
 * is a violation of applicable law. This material contains certain
 * confidential or proprietary information and trade secrets of Intuit Inc.
 */
package com.intuit.pavedroad.appintgwkflwwkflautomate.waseventinapp;

import com.intuit.dataexchange.enrichment.pipelineframework.utils.PipelineGraphSelector;
import java.net.URL;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableScheduling;


@SpringBootApplication(scanBasePackages = "${ipf.runtime.basePackages}")
@Slf4j
@EnableCaching
@EnableScheduling
public class WasEventInApplication {

    public static ConfigurableApplicationContext run(String[] args) {
        return SpringApplication.run(WasEventInApplication.class, args);
    }

    public static void main(String[] args) {
        run(args);  //NOSONAR
        log.info("The Application has started...");
    }
}
//
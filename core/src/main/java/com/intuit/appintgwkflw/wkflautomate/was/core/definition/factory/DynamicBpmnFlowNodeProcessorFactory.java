package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * This factory is used to provide the java objects of type DynamicBpmnFlowNodeProcessor class for a
 * given flow node in BPMN.
 *
 * <AUTHOR>
 */
@Component
public class DynamicBpmnFlowNodeProcessorFactory {

  private final Map<String, DynamicBpmnFlowNodeProcessor> flowNodeProcessorMap = new HashMap<>();

  /**
   * This constructor is used to initialize the map of flow node processors.
   *
   * @param flowNodeProcessorList List of DynamicBpmnFlowNodeProcessor
   */
  @Autowired
  public DynamicBpmnFlowNodeProcessorFactory(
      List<DynamicBpmnFlowNodeProcessor> flowNodeProcessorList) {
    flowNodeProcessorList.forEach(
        flowNodeProcessor ->
            flowNodeProcessorMap.put(flowNodeProcessor.getType().getName(), flowNodeProcessor));
  }

  /**
   * This method returns the java object of type DynamicBpmnFlowNodeProcessor for a given flow node
   * in BPMN.
   *
   * @param flowNode FlowNode
   * @return DynamicBpmnFlowNodeProcessor
   */
  public DynamicBpmnFlowNodeProcessor getProcessorFromFlowNode(FlowNode flowNode) {
    DynamicBpmnFlowNodeProcessor dynamicBpmnFlowNodeProcessor =
        flowNodeProcessorMap.get(flowNode.getElementType().getTypeName());
    if (Objects.isNull(dynamicBpmnFlowNodeProcessor)) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_BPMN_ELEMENT);
    }
    return dynamicBpmnFlowNodeProcessor;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.CompositeStepBuilderFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.RuleLineConvertorTypeFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiConditionPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineParser;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DaysTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DefaultDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.ListTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.RuleLine.Rule;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepDefinitionConditionBuilderTest {

  @InjectMocks
  private MultiStepDefinitionConditionBuilder multiStepDefinitionConditionBuilder;

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/multiConditionDMN.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML_FEEL =
      TestHelper.readResourceAsString("dmn/multiConditionDMNFeel.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_WITH_NO_RULES =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_WITH_NO_CONDITION =
      TestHelper.readResourceAsString("dmn/customWorkflowEmptyCondition.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT =
      TestHelper.readResourceAsString("dmn/customApprovalWorkflowWithMultiSplit.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_FOR_RECURRING_REMINDER =
      TestHelper.readResourceAsString("dmn/singleConditionRecurringReminder.dmn");

  private static final String CUSTOM_WORKFLOW_DMN_FOR_MULTI_CONDITION_RECURRING_REMINDER =
      TestHelper.readResourceAsString("dmn/multiConditionRecurringReminder.dmn");

  private DmnModelInstance dmnModelInstance;
  private DmnModelInstance dmnModelInstanceFeel;
  private DmnModelInstance emptyDmnModelInstance;
  private DmnModelInstance emptyConditionDmnModelInstance;
  private DmnModelInstance dmnModelInstanceWithMultiSplit;
  private DmnModelInstance dmnModelInstanceForRecurringReminder;
  private DmnModelInstance dmnModelInstanceForMultiConditionRecurringReminder;

  @Mock
  private FeatureFlagManager featureFlagManager;
  private CustomWorkflowConfig customWorkflowConfig;
  private BusinessRuleTaskHandler businessRuleTaskHandler;
  @Mock
  private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  private CustomWorkflowDecisionHandler customWorkflowDecisionHandler;
  private MultiStepRuleLineBuilder multiStepRuleLineBuilder;
  @InjectMocks
  private MultiConditionRuleLineParser multiConditionProcessor;
  @Mock
  private WASContextHandler wasContextHandler;
  private ReadCustomDefinitionHandler readCustomDefinitionHandler;
  @Mock
  private TemplateConditionBuilder templateConditionBuilder;
  @Mock
  private TemplateActionBuilder templateActionBuilder;
  @Mock
  private TemplateTriggerBuilder templateTriggerBuilder;
  private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
  @Mock
  private MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor;
  private RuleLineConvertorTypeFactory ruleLineConvertorTypeFactory;
  @Mock
  private MultiSplitConditionRuleLineBuilder multiSplitConditionRuleLineBuilder;
  @Mock
  private MultiStepConditionRuleLineBuilder multiStepConditionRuleLineBuilder;
  @Mock
  CompositeStepBuilderFactory compositeStepBuilderFactory;

  @Before
  @SneakyThrows
  public void setup() {
    customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
    dmnModelInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
    dmnModelInstanceFeel = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML_FEEL, Charset.defaultCharset()));
    emptyDmnModelInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_NO_RULES, Charset.defaultCharset()));
    emptyConditionDmnModelInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_NO_CONDITION, Charset.defaultCharset()));
    dmnModelInstanceWithMultiSplit = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_WITH_MULTI_SPLIT, Charset.defaultCharset()));
    dmnModelInstanceForRecurringReminder = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_FOR_RECURRING_REMINDER,
            Charset.defaultCharset()));
    dmnModelInstanceForMultiConditionRecurringReminder = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_FOR_MULTI_CONDITION_RECURRING_REMINDER,
            Charset.defaultCharset()));

    readCustomDefinitionHandler = new ReadCustomDefinitionHandler(
        templateConditionBuilder,
        templateActionBuilder,
        templateTriggerBuilder,
        customWorkflowConfig
    );
    multiStepRuleLineBuilder = new MultiStepRuleLineBuilder(
        readCustomDefinitionHandler, featureFlagManager);
    multiStepConditionRuleLineBuilder = new MultiStepConditionRuleLineBuilder(wasContextHandler,
        multiStepRuleLineBuilder, compositeStepBuilderFactory);
    multiSplitConditionRuleLineBuilder = new MultiSplitConditionRuleLineBuilder(wasContextHandler,
        multiStepRuleLineBuilder, compositeStepBuilderFactory);
    ruleLineConvertorTypeFactory = new RuleLineConvertorTypeFactory(
        multiSplitConditionRuleLineBuilder, multiStepConditionRuleLineBuilder);
    customWorkflowDecisionHandler = new CustomWorkflowDecisionHandler(
        customWorkflowConfig,
        featureFlagManager);
    businessRuleTaskHandler = new BusinessRuleTaskHandler(
        workflowGlobalConfiguration,
        customWorkflowConfig,
        featureFlagManager);
    multiConditionRuleLineProcessor = new MultiConditionRuleLineProcessor(
        customWorkflowConfig,
        businessRuleTaskHandler,
        customWorkflowDecisionHandler,
        multiConditionPlaceholderExtractor,
        featureFlagManager);
    multiStepDefinitionConditionBuilder = new MultiStepDefinitionConditionBuilder(
        multiConditionProcessor,
        customWorkflowConfig,
        multiConditionRuleLineProcessor,
        ruleLineConvertorTypeFactory);
  }

  @Test
  public void testProcessWorkflowStepforJuel() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstance)
        .childActivityInstances(new HashMap<>())
        .build();
    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
        .globalId(null)
        .build();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST,
        new ListTransformer());
    when(compositeStepBuilderFactory.getHandler(any())).thenReturn(new NonCompositeStepBuilder());
    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
    Assert.assertEquals(4, actionIdtoStepIdMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-2"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-3"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-4"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-5"));

    Assert.assertNotNull(workflowSteps);
    Assert.assertEquals(3, workflowSteps.size());

    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("TxnAmount",
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals(">= 100",
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE,
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(0).getNext());
    Assert.assertEquals(2, workflowSteps.get(0).getNext().size());
    Assert.assertNotNull(workflowSteps.get(0).getNext().get(0).getLabel());
    Assert.assertNotNull(workflowSteps.get(0).getNext().get(1).getLabel());

    Assert.assertNotNull(workflowSteps.get(1).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(1).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("TxnBalanceAmount",
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals(">= 1000",
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE,
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(1).getNext());
    Assert.assertEquals(2, workflowSteps.get(1).getNext().size());
    Assert.assertNotNull(workflowSteps.get(1).getNext().get(0).getLabel());
    Assert.assertNotNull(workflowSteps.get(1).getNext().get(1).getLabel());

    Assert.assertNotNull(workflowSteps.get(2).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(2).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("Location",
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals("CONTAINS 1",
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.LIST,
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(2).getNext());
    Assert.assertEquals(2, workflowSteps.get(2).getNext().size());
    Assert.assertNotNull(workflowSteps.get(2).getNext().get(0).getLabel());
    Assert.assertNotNull(workflowSteps.get(2).getNext().get(1).getLabel());
  }

  @Test
  public void testProcessWorkflowStepForRecurringReminder() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstanceForRecurringReminder)
        .childActivityInstances(new HashMap<>())
        .build();

    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
        .globalId(null)
        .build();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DAYS,
        new DaysTransformer());
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("realmId");
    when(compositeStepBuilderFactory.getHandler(any())).thenReturn(new NonCompositeStepBuilder())
        .thenReturn(new CompositeStepBuilder());

    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);

    Assert.assertEquals(1, workflowSteps.size());
    Assert.assertEquals(1, compositeStepIdToWorkflowStepConditionMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("callActivity-1"));

    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
    List<RuleLine> ruleLines = workflowSteps.get(0).getWorkflowStepCondition().getRuleLines();
    Assert.assertNotNull(ruleLines);

    List<Rule> rules = ruleLines.get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
    Assert.assertEquals("BTW 20000,20010", rules.get(0).getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE, rules.get(0).getParameterType());
    Assert.assertNotNull(workflowSteps.get(0).getNext());
    Assert.assertEquals(1, workflowSteps.get(0).getNext().size());

  }

  @Test
  public void testProcessWorkflowStepForMultiConditionRecurringReminder() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstanceForMultiConditionRecurringReminder)
        .childActivityInstances(new HashMap<>())
        .build();

    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
        .globalId(null)
        .build();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DAYS,
        new DaysTransformer());
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("realmId");
    when(compositeStepBuilderFactory.getHandler(any())).thenReturn(new NonCompositeStepBuilder())
        .thenReturn(new CompositeStepBuilder());

    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);

    Assert.assertEquals(1, workflowSteps.size());
    Assert.assertEquals(2, compositeStepIdToWorkflowStepConditionMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("callActivity-1"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("callActivity-2"));
    for (WorkflowStepCondition condition : compositeStepIdToWorkflowStepConditionMap.values()) {
      List<Rule> rules = condition.getRuleLines(0).getRules();
      Assert.assertEquals(1, rules.size());
      Assert.assertEquals(FieldTypeEnum.DAYS, rules.get(0).getParameterType());
    }

    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
    List<RuleLine> ruleLines = workflowSteps.get(0).getWorkflowStepCondition().getRuleLines();
    Assert.assertNotNull(ruleLines);

    List<Rule> rules = ruleLines.get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
    Assert.assertEquals("BTW 20000,20010", rules.get(0).getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE, rules.get(0).getParameterType());
    Assert.assertNotNull(workflowSteps.get(0).getNext());
    Assert.assertEquals(2, workflowSteps.get(0).getNext().size());
  }


  @Test
  public void testProcessWorkflowStepForFeel() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstanceFeel)
        .childActivityInstances(new HashMap<>())
        .build();

    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
        .globalId(null)
        .build();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST,
        new ListTransformer());
    when(compositeStepBuilderFactory.getHandler(any())).thenReturn(new NonCompositeStepBuilder());

    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
    Assert.assertEquals(2, actionIdtoStepIdMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("sendForApproval-2"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("sendForApproval-3"));

    Assert.assertNotNull(workflowSteps);
    Assert.assertEquals(2, workflowSteps.size());

    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
    List<RuleLine> ruleLines = workflowSteps.get(0).getWorkflowStepCondition().getRuleLines();
    Assert.assertNotNull(ruleLines);

    List<Rule> rules = ruleLines.get(0).getRules();
    Assert.assertEquals(1, rules.size());
    Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
    Assert.assertEquals("BTW 0,1000", rules.get(0).getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE, rules.get(0).getParameterType());
    Assert.assertNotNull(workflowSteps.get(0).getNext());
    Assert.assertEquals(2, workflowSteps.get(0).getNext().size());
    Assert.assertNotNull(workflowSteps.get(0).getNext().get(0).getLabel());
    Assert.assertNotNull(workflowSteps.get(0).getNext().get(1).getLabel());

    Assert.assertNotNull(workflowSteps.get(1).getWorkflowStepCondition());
    ruleLines = workflowSteps.get(1).getWorkflowStepCondition().getRuleLines();
    Assert.assertNotNull(ruleLines);
    rules = ruleLines.get(0).getRules();
    Assert.assertEquals(2, rules.size());
    Assert.assertNotNull(ruleLines.get(0).getRules());
    Assert.assertEquals("TxnAmount", rules.get(0).getParameterName());
    Assert.assertEquals("BTW 1001,5000", rules.get(0).getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE, rules.get(0).getParameterType());
    Assert.assertEquals("Location", rules.get(1).getParameterName());
    Assert.assertEquals("CONTAINS 1", rules.get(1).getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.LIST, rules.get(1).getParameterType());

    Assert.assertNotNull(workflowSteps.get(1).getNext());
    Assert.assertEquals(1, workflowSteps.get(1).getNext().size());
    Assert.assertNotNull(workflowSteps.get(1).getNext().get(0).getLabel());
  }

  @Test
  public void testProcessWorkflowStepWithEmptyConditionForJuel() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(emptyConditionDmnModelInstance)
        .childActivityInstances(new HashMap<>())
        .build();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .globalId(null)
        .build();
    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
    Assert.assertEquals(1, actionIdtoStepIdMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-3"));
    Assert.assertNotNull(workflowSteps);
    Assert.assertEquals(0, workflowSteps.size());
  }

  @Test
  public void testProcessWorkflowStepWithEmptyConditionForFeel() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(emptyConditionDmnModelInstance)
        .childActivityInstances(new HashMap<>())
        .build();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .globalId(null)
        .build();
    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
    Assert.assertEquals(1, actionIdtoStepIdMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("action-3"));
    Assert.assertNotNull(workflowSteps);
    Assert.assertEquals(0, workflowSteps.size());
  }

  @Test
  public void testProcessWorkflowStepforMultiSplitDefinition() {
    Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    Template template = new Template();
    template.setRecordType(RecordType.INVOICE.name());
    ActivityInstance dmnActivityInstance = ActivityInstance.builder()
        .dmnModelInstance(dmnModelInstanceWithMultiSplit)
        .childActivityInstances(new HashMap<>())
        .build();
    Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
    MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
        .workflowSteps(workflowSteps)
        .activityInstance(dmnActivityInstance)
        .template(template)
        .actionIdToStepIdMap(actionIdtoStepIdMap)
        .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
        .globalId(null)
        .build();
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.DEFAULT,
        new DefaultDataTypeTransformer(workflowGlobalConfiguration));
    DMNDataTypeTransformers.addTransformer(DMNSupportedOperator.LIST,
        new ListTransformer());
    when(compositeStepBuilderFactory.getHandler(any())).thenReturn(new NonCompositeStepBuilder());
    multiStepDefinitionConditionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
    Assert.assertEquals(3, actionIdtoStepIdMap.size());
    Assert.assertNotNull(actionIdtoStepIdMap.get("sendForApproval-2"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("sendForApproval-3"));
    Assert.assertNotNull(actionIdtoStepIdMap.get("sendForApproval-4"));

    Assert.assertNotNull(workflowSteps);
    Assert.assertEquals(3, workflowSteps.size());

    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("TxnAmount",
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals("< 50.0",
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE,
        workflowSteps.get(0).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(0).getNext());
    Assert.assertEquals(1, workflowSteps.get(0).getNext().size());
    Assert.assertNotNull(workflowSteps.get(0).getNext().get(0).getLabel());

    Assert.assertNotNull(workflowSteps.get(1).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(1).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("TxnAmount",
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals("BTW >=50.0,<=75.0",
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE,
        workflowSteps.get(1).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(1).getNext());
    Assert.assertEquals(1, workflowSteps.get(1).getNext().size());
    Assert.assertNotNull(workflowSteps.get(1).getNext().get(0).getLabel());

    Assert.assertNotNull(workflowSteps.get(2).getWorkflowStepCondition());
    Assert.assertNotNull(workflowSteps.get(2).getWorkflowStepCondition().getRuleLines());
    Assert.assertNotNull(
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules());
    Assert.assertEquals("TxnAmount",
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterName());
    Assert.assertEquals("BTW >=75.0,<=100.0",
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getConditionalExpression());
    Assert.assertEquals(FieldTypeEnum.DOUBLE,
        workflowSteps.get(2).getWorkflowStepCondition().getRuleLines().get(0).getRules().get(0)
            .getParameterType());
    Assert.assertNotNull(workflowSteps.get(2).getNext());
    Assert.assertEquals(1, workflowSteps.get(2).getNext().size());
    Assert.assertNotNull(workflowSteps.get(2).getNext().get(0).getLabel());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.GetSubscriptionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.request.State;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class GetSubscriptionDetailsTaskTest {

  private GetSubscriptionDetailsTask getSubscriptionDetailsTask;
  private AuthHelper authHelper = Mockito.mock(AuthHelper.class);

  @Test
  public void testExecute() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);
    String realmId = "123";
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("134");
    Mockito.when(appConnectService.getSubscriptionForApp(eq(realmId)))
        .thenReturn(getSubscriptionResponse.getId());
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    Assert.assertEquals(
        getSubscriptionResponse.getId(),
        getSubscriptionDetailsTask.execute(state).getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
  }

  @Test
  public void testExecuteAuthDetailsPresent() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);

    String realmId = "123";

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subscriptionId");
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenReturn(Optional.ofNullable(Arrays.asList(authDetails)));

    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("ID");

    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
        .thenReturn(getSubscriptionResponse.getId());

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    State response = getSubscriptionDetailsTask.execute(state);
    Assert.assertNotEquals(
        "subscriptionId", response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertEquals("ID", response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertNotNull(response.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
  }

  @Test
  public void testExecuteAuthDetailsPresentAndNoMismatch() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);

    String realmId = "123";

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subscriptionId");
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenReturn(Optional.ofNullable(Arrays.asList(authDetails)));

    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("subscriptionId");

    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
        .thenReturn(getSubscriptionResponse.getId());

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    State response = getSubscriptionDetailsTask.execute(state);
    Assert.assertEquals(
        "subscriptionId", response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertNotNull(response.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
  }

  @Test
  public void testExecuteAuthDetailsPresentAndNullSubsc() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);

    String realmId = "123";

    AuthDetails authDetails = new AuthDetails();
    authDetails.setSubscriptionId("subscriptionId");
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenReturn(Optional.ofNullable(authDetails));
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenReturn(Optional.ofNullable(Arrays.asList(authDetails)));

    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("subscriptionId");

    Mockito.when(appConnectService.getSubscriptionForApp(Mockito.any()))
        .thenReturn(getSubscriptionResponse.getId());

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    State response = getSubscriptionDetailsTask.execute(state);
    Assert.assertEquals(
        "subscriptionId", response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertNotNull(response.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
  }

  @Test
  public void testExecuteAuthDetailsException() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);

    String realmId = "123";
    Mockito.when(authHelper.getAuthDetailsFromList(any())).thenThrow(new RuntimeException());
    Mockito.when(authDetailsRepository.findAuthDetailsByOwnerId(Mockito.anyLong()))
        .thenThrow(new RuntimeException());

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);

    State response = getSubscriptionDetailsTask.execute(state);
    Assert.assertNull("subscriptionId", response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertNull(response.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
  }

  @Test
  public void testExecuteWhenTwoSubscriptionCreationCallsAreMade() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);
    String realmId = "123";
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("134");
    Mockito.when(appConnectService.getSubscriptionForApp(eq(realmId)))
        .thenReturn(null)
        .thenReturn(getSubscriptionResponse.getId());
    String exceptionFromAppconnect = "{\"errors\":[{\"message\": \"Existing subscription found... cannot create a new subscription\",\"code\": \"IAC-002325\",\"details\": \"subscriptionId=123; state=NEW\",\"moreInfo\": \"subscriptionId=123; state=NEW\",\"infoLink\": \"\",\"type\": \"INVALID_REQUEST\"}]}";
    WorkflowGeneralException exception = new WorkflowGeneralException("W067",
        exceptionFromAppconnect);
    Mockito.when(appConnectService.createSubscriptionForApp(eq(realmId)))
        .thenThrow(exception);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    state.addValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD, true);

    Assert.assertEquals(
        getSubscriptionResponse.getId(),
        getSubscriptionDetailsTask.execute(state).getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Mockito.verify(appConnectService, Mockito.times(2))
        .getSubscriptionForApp(eq(realmId));
  }

  @Test
  public void testExecuteExceptionWhileCreatingSubscription() {
    AppConnectService appConnectService = Mockito.mock(AppConnectServiceImpl.class);
    AuthDetailsRepository authDetailsRepository = Mockito.mock(AuthDetailsRepository.class);
    getSubscriptionDetailsTask =
        new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper);
    String realmId = "123";
    GetSubscriptionResponse getSubscriptionResponse = new GetSubscriptionResponse();
    getSubscriptionResponse.setId("134");
    Mockito.when(appConnectService.getSubscriptionForApp(eq(realmId)))
        .thenReturn(null);
    String exceptionFromAppconnect = "{\\\"errors\\\":[{\\\"message\\\":\\\"Could not create connection\\\",\\\"code\\\":\\\"IAC-002702\\\",\\\"details\\\":\\\"responseCode=401; responseMessage=Access denied as the user is not the company admin to create robot offline ticket\\\",\\\"moreInfo\\\":\\\"responseCode=401; responseMessage=Access denied as the user is not the company admin to create robot offline ticket\\\",\\\"infoLink\\\":\\\"\\\",\\\"type\\\":\\\"PROCESSING_ERROR\\\"}]}";
    WorkflowGeneralException exception = new WorkflowGeneralException("W067",
        exceptionFromAppconnect);
    Mockito.when(appConnectService.createSubscriptionForApp(eq(realmId)))
        .thenThrow(exception);
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, realmId);
    state.addValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD, true);

    State response = getSubscriptionDetailsTask.execute(state);
    Assert.assertNull(response.getValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY));
    Assert.assertNull(response.getValue(AsyncTaskConstants.AUTH_DETAILS_KEY));
    Mockito.verify(appConnectService, Mockito.times(1))
        .getSubscriptionForApp(eq(realmId));
  }
}

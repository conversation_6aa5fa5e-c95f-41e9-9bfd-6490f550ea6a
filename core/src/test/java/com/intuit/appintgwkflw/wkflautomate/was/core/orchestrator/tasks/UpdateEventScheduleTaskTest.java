 package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.DailyRecurrenceProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence.RecurrenceHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleError;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.EventSchedule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UpdateEventScheduleTaskTest {

  private UpdateEventScheduleTask updateStatusEventSchedulerTask;
  private State input;
  private EventScheduleService eventScheduleService;
  private DailyRecurrenceProcessor mockDailyRecurrenceProcessor;
  private EventScheduleConfig eventScheduleConfig;

  @Before
  public void setup() {
    input = new State();
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "1234");
    eventScheduleService = Mockito.mock(EventScheduleServiceImpl.class);
    mockDailyRecurrenceProcessor = Mockito.mock(DailyRecurrenceProcessor.class);
    eventScheduleConfig = Mockito.mock(EventScheduleConfig.class);
    updateStatusEventSchedulerTask = new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig);
    RecurrenceHandler.addHandler(RecurTypeEnum.DAILY, mockDailyRecurrenceProcessor);
    when(mockDailyRecurrenceProcessor.buildESSRecurrencePattern(any()))
        .thenReturn(new RecurrencePattern().interval(3).type(RecurrencePatternType.DAILY));
  }

  @Test
  public void testExecute_realmId_null_empty() {
    try {
      updateStatusEventSchedulerTask.execute(input);

      Assert.fail("Should not fail here");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.INPUT_INVALID);
    }
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "");
    try {
      updateStatusEventSchedulerTask.execute(input);

      Assert.fail("Should not fail here");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.INPUT_INVALID);
    }
  }

  @Test
  public void testExecute_status_null() {
    try {
      input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
      updateStatusEventSchedulerTask.execute(input);
      Assert.fail("Should not fail here");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.INPUT_INVALID);
    }
  }

  @Test
  public void testExecute() {
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData scheduleData = new EventScheduleData();
    scheduleData.setId("customStart-11111");
    eventScheduleData.add(scheduleData);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);
    Mockito.when(eventScheduleService.updateSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("customStart-11111"));
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    try {
      updateStatusEventSchedulerTask.execute(input);
    } catch (Exception e) {
      Assert.fail("Exception should not be thrown");
    }
  }

  @Test
  public void testExecuteWithNoSchedulerIds() {
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    try {
      updateStatusEventSchedulerTask.execute(input);
    } catch (Exception e) {
      Assert.fail("Exception should not be thrown");
    }
  }

  @Test
  public void testExecuteWithEmptySchedulerIds() {
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, new ArrayList<>());
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    try {
      updateStatusEventSchedulerTask.execute(input);
    } catch (Exception e) {
      Assert.fail("Exception should not be thrown");
    }
  }

  @Test
  public void testExecute_errors() {
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleError> errors = new ArrayList<>();
    EventScheduleError eventScheduleError = new EventScheduleError();
    eventScheduleError.setMessage("internal error");
    errors.add(eventScheduleError);
    eventScheduleResponse.setErrors(errors);
    eventScheduleResponses.add(eventScheduleResponse);
    Mockito.when(eventScheduleService.updateSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    List<SchedulerDetails> schedulerDetails = new ArrayList<>();
    SchedulerDetails details = new SchedulerDetails();
    details.setSchedulerId("customStart-11111");
    schedulerDetails.add(details);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("customStart-11111"));
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    try {
      updateStatusEventSchedulerTask.execute(input);
      Assert.fail("Should not fail here");
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getWorkflowError(), WorkflowError.EVENT_SCHEDULE_CALL_FAILURE);
    }
  }

  @Test
  public void testExecute_errors_invalid_id() {
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleError> errors = new ArrayList<>();
    EventScheduleError eventScheduleError = new EventScheduleError();
    eventScheduleError.setMessage("scheduleId is invalid");
    errors.add(eventScheduleError);
    eventScheduleResponse.setErrors(errors);
    eventScheduleResponses.add(eventScheduleResponse);
    Mockito.when(eventScheduleService.updateSchedules(Mockito.anyList(), Mockito.anyString()))
        .thenReturn(eventScheduleResponses);
    List<SchedulerDetails> schedulerDetails = new ArrayList<>();
    SchedulerDetails details = new SchedulerDetails();
    details.setSchedulerId("customStart-11111");
    schedulerDetails.add(details);
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("customStart-11111"));
    input.addValue(AsyncTaskConstants.REALM_ID_KEY, "relm-124");
    input.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.DELETED);
    try {
      updateStatusEventSchedulerTask.execute(input);
    } catch (WorkflowGeneralException e) {
      Assert.fail("Exception should not be thrown");
    }
  }

  @Test
  public void testPrepareEventSchedulePayload_scheduleStatusUpdate() {
    input.addValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE, false);
    List<EventSchedule> eventSchedules =
        updateStatusEventSchedulerTask.prepareEventSchedulePayload(
            List.of("customStart-11111"), ScheduleStatus.ACTIVE, "realm123", input);
    Assert.assertEquals(1, eventSchedules.size());
    Assert.assertNull(eventSchedules.get(0).getRecurrence());
    Assert.assertEquals(ScheduleStatus.ACTIVE, eventSchedules.get(0).getStatus());
  }

  @Test
  public void testPrepareEventSchedulePayload_completeScheduleUpdate() {
    String scheduleId = "customStart-11111";
    input.addValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE, true);
    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
        List.of(
            new EventScheduleWorkflowActionModel(
                "customScheduledActions_customStart",
                new LocalDate("2023-2-21"),
                new RecurrenceRule()
                    .interval(3)
                    .recurType(RecurTypeEnum.DAILY)
                    .startDate(new DateTime("2023-2-21"))));

    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(eventScheduleWorkflowActionModels));

    Map<String, SchedulerDetails> schedulerDetailsMap = new HashMap<>();
    SchedulerDetails schedulerDetails = new SchedulerDetails();
    schedulerDetails.setSchedulerId(scheduleId);
    schedulerDetails.setOwnerId(Long.valueOf("1234"));
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_SCHEDULEDACTIONS_CUSTOM_START);
    schedulerDetailsMap.put(scheduleId, schedulerDetails);

    input.addValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP, schedulerDetailsMap);
    List<EventSchedule> eventSchedules =
        updateStatusEventSchedulerTask.prepareEventSchedulePayload(
            List.of(scheduleId), ScheduleStatus.ACTIVE, "1234", input);
    Assert.assertEquals(1, eventSchedules.size());
    Assert.assertNotNull(eventSchedules.get(0).getRecurrence().getPattern());
    Assert.assertEquals(
        new Integer(3), eventSchedules.get(0).getRecurrence().getPattern().getInterval());
    Assert.assertEquals(
        RecurrencePatternType.DAILY, eventSchedules.get(0).getRecurrence().getPattern().getType());
    Assert.assertEquals(ScheduleStatus.ACTIVE, eventSchedules.get(0).getStatus());
  }

  @Test
  public void
      testPrepareEventSchedulePayload_completeScheduleUpdate_emptyEventScheduleWorkflowActionModelMap() {
    String scheduleId = "customStart-11111";
    input.addValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE, true);
    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(List.of()));

    Map<String, SchedulerDetails> schedulerDetailsMap = new HashMap<>();
    SchedulerDetails schedulerDetails = new SchedulerDetails();
    schedulerDetails.setSchedulerId(scheduleId);
    schedulerDetails.setOwnerId(Long.valueOf("1234"));
    schedulerDetails.setSchedulerAction(SchedulerAction.CUSTOM_SCHEDULEDACTIONS_CUSTOM_START);
    schedulerDetailsMap.put(scheduleId, schedulerDetails);

    input.addValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP, schedulerDetailsMap);
    List<EventSchedule> eventSchedules =
        updateStatusEventSchedulerTask.prepareEventSchedulePayload(
            List.of(scheduleId), ScheduleStatus.ACTIVE, "1234", input);
    Assert.assertEquals(1, eventSchedules.size());
    Assert.assertNull(eventSchedules.get(0).getRecurrence());
    Assert.assertEquals(ScheduleStatus.ACTIVE, eventSchedules.get(0).getStatus());
  }

  @Test
  public void testPrepareEventSchedulePayload_completeScheduleUpdate_emptySchedulerDetailsMap() {
    String scheduleId = "customStart-11111";
    input.addValue(AsyncTaskConstants.IS_UPDATE_COMPLETE_SCHEDULE, true);
    List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
        List.of(
            new EventScheduleWorkflowActionModel(
                "customScheduledActions_customStart",
                new LocalDate("2023-2-21"),
                new RecurrenceRule()
                    .interval(3)
                    .recurType(RecurTypeEnum.DAILY)
                    .startDate(new DateTime("2023-2-21"))));

    input.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(eventScheduleWorkflowActionModels));

    Map<String, SchedulerDetails> schedulerDetailsMap = new HashMap<>();

    input.addValue(AsyncTaskConstants.EVENT_SCHEDULER_DETAILS_MAP, schedulerDetailsMap);
    List<EventSchedule> eventSchedules =
        updateStatusEventSchedulerTask.prepareEventSchedulePayload(
            List.of(scheduleId), ScheduleStatus.ACTIVE, "1234", input);
    Assert.assertEquals(1, eventSchedules.size());
    Assert.assertNull(eventSchedules.get(0).getRecurrence());
    Assert.assertEquals(ScheduleStatus.ACTIVE, eventSchedules.get(0).getStatus());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
public class WorkflowTaskProcessorTest {

    private static final String INVOICE_APPROVAL_ONLY_BPMN =
            "src/test/resources/bpmn/InvoiceApproval_onlyBpmn.bpmn";
    private static final String TEST_IES_ONBOARDING_BPMN =
            "src/test/resources/bpmn/testIESOnboarding.bpmn";
    private static final String TEST_OFFER_AND_BI_STAGE_BPMN =
            "src/test/resources/bpmn/offerAndBIStage.bpmn";
    private static final String WORKITEM_BPMN = "src/test/resources/bpmn/WorkItem.bpmn";
    private static final String INVALID_WORKITEM_BPMN =
            "src/test/resources/bpmn/InvalidWorkItem.bpmn";

    private static final String TEST_BPMN = "src/test/resources/bpmn/testLoopEvents.bpmn";

    @Test
    public void processBPMNTasks_notasks() throws Exception {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile)) {
            ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn));
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForServiceTask(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals("Size should be zero.", 0, activityIdToActivityDetailsMap.size());
        }
    }

    @Test
    public void processBPMNTasks_systemTask_and_humanTask() throws Exception {
        File bpmnFile = new File(WORKITEM_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {

            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForServiceTask(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals("Size should not be zero.", 3, activityIdToActivityDetailsMap.size());
            Assert.assertTrue(
                    "Human Task should be present",
                    activityIdToActivityDetailsMap.values().stream()
                            .anyMatch(activityDetail -> TaskType.HUMAN_TASK.equals(activityDetail.getType())));
            Assert.assertTrue(
                    "System Task should be present",
                    activityIdToActivityDetailsMap.values().stream()
                            .anyMatch(activityDetail -> TaskType.SYSTEM_TASK.equals(activityDetail.getType())));

            ActivityDetail humanTask =
                    activityIdToActivityDetailsMap.values().stream()
                            .filter(activityDetail -> TaskType.HUMAN_TASK.equals(activityDetail.getType()))
                            .findAny()
                            .get();

            Assert.assertEquals(activityIdToActivityDetailsMap.values().stream()
                    .filter(activityDetail -> TaskType.HUMAN_TASK.equals(activityDetail.getType()))
                            .map(ActivityDetail::getActivityName).collect(Collectors.toList()),
                    List.of("Work task 2", "Work task 1"));

            Assert.assertEquals("should be serviceTask", "serviceTask", humanTask.getActivityType());

            ActivityDetail systemTask =
                    activityIdToActivityDetailsMap.values().stream()
                            .filter(activityDetail -> TaskType.SYSTEM_TASK.equals(activityDetail.getType()))
                            .findAny()
                            .get();
            Assert.assertEquals(
                    "System Task activityName not set correctly.",
                    "WorkItem 3",
                    systemTask.getActivityName());
            Assert.assertEquals("should be serviceTask", "serviceTask", systemTask.getActivityType());

        }
    }

    @Test(expected = WorkflowGeneralException.class)
    public void processBPMNTasks_invalidTasksDetails() throws Exception {
        File bpmnFile = new File(INVALID_WORKITEM_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForServiceTask(bpmnModelInstance, activityIdToActivityDetailsMap);
        }
    }

    @Test
    @SneakyThrows
    public void testCodeUsingNewBPMN() {
        File bpmnFile = new File(TEST_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertNotNull(activityIdToActivityDetailsMap.values());
            Assert.assertFalse(activityIdToActivityDetailsMap.isEmpty());
            Assert.assertEquals(4, activityIdToActivityDetailsMap.size());
        }
    }

    @Test
    @SneakyThrows
    public void testCodeExistingBPMN() {
        File bpmnFile = new File(WORKITEM_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertNotNull(activityIdToActivityDetailsMap);
            Assert.assertEquals(0, activityIdToActivityDetailsMap.size());
        }
    }

    @Test
    @SneakyThrows
    public void testWithInitialStartEventInBPMN() {
        File bpmnFile = new File(WORKITEM_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(0, activityIdToActivityDetailsMap.size());
        }
    }

    @Test
    @SneakyThrows
    public void testWithMultipleStartEventInBPMN() {
        File bpmnFile = new File(TEST_IES_ONBOARDING_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(10, activityIdToActivityDetailsMap.size());
            List<ActivityDetail> startEvents = activityIdToActivityDetailsMap.values().stream().filter(activityDetail ->
                    activityDetail.getActivityType().equals(BpmnComponentType.START_EVENT.getName())).collect(Collectors.toList());
            Assert.assertEquals(5, startEvents.size());
        }
    }

    @Test
    @SneakyThrows
    public void testWithMultipleStartEventInBPMNWithInitialStartEventAsFalse() {
        File bpmnFile = new File(TEST_IES_ONBOARDING_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(10, activityIdToActivityDetailsMap.size());
            List<ActivityDetail> startEvents = activityIdToActivityDetailsMap.values().stream().filter(activityDetail ->
                    activityDetail.getActivityType().equals(BpmnComponentType.START_EVENT.getName())).collect(Collectors.toList());
            Assert.assertEquals(5, startEvents.size());
        }
    }

    @Test
    @SneakyThrows
    public void testProcessInitialStartEventElementWithActivityDetailsNotPresentForInitialStartEvent() {
        File bpmnFile = new File(TEST_IES_ONBOARDING_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processBPMNElementsForDomainEvents(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(10, activityIdToActivityDetailsMap.size());
            List<ActivityDetail> startEvents = activityIdToActivityDetailsMap.values().stream().filter(activityDetail ->
                    activityDetail.getActivityType().equals(BpmnComponentType.START_EVENT.getName())).collect(Collectors.toList());
            Assert.assertEquals(5, startEvents.size());

            WorkflowTaskProcessor.processInitialStartEventElement(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(11, activityIdToActivityDetailsMap.size());
            startEvents = activityIdToActivityDetailsMap.values().stream().filter(activityDetail ->
                    activityDetail.getActivityType().equals(BpmnComponentType.START_EVENT.getName())).collect(Collectors.toList());
            Assert.assertEquals(6, startEvents.size());

        }
    }

    @Test
    @SneakyThrows
    public void testProcessInitialStartEventElementWithOutStartableEventsInStartEvent() {
        File bpmnFile = new File(TEST_OFFER_AND_BI_STAGE_BPMN);
        try (FileInputStream fisBpmn = new FileInputStream(bpmnFile);
             ByteArrayInputStream bis = new ByteArrayInputStream(IOUtils.toByteArray(fisBpmn))) {
            final BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(bis);
            Map<String, ActivityDetail> activityIdToActivityDetailsMap = new HashMap<>();
            WorkflowTaskProcessor.processInitialStartEventElement(bpmnModelInstance, activityIdToActivityDetailsMap);
            Assert.assertEquals(0, activityIdToActivityDetailsMap.size());
        }
    }

}

package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.service.DomainEventTestService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class DomainEventTestServiceImpl implements DomainEventTestService {

  private DomainEventRepository domainEventRepository;

  @Override
  public DomainEvent getEventById(String eventId) {
    return domainEventRepository.findByEventId(eventId);
  }

  @Override
  public List<DomainEvent> getEventsByPartitionKey(String partitionKey) {
    List<DomainEvent> domainEvent =
        domainEventRepository.findDomainEventByPartitionKey(partitionKey).orElse(null);
    return domainEvent;
  }

  @Override
  public long getCountByEventsPublishedByPartitionKey(String partitionKey) {
    return domainEventRepository.countByPartitionKey(partitionKey);
  }
}

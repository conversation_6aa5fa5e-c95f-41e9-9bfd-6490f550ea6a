package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask;

import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> stripathy1 ExternalTaskCompleted will be part of all publishers who are
 *     publishing to Event bus. It extends WorkflowMetaDataEntity Currently this POJO is written
 *     manually, later after IEDM onboarding the schema will be moved to IEDM. These POJO will be
 *     automatially gennerated and can be deleted from this folder
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalTaskCompleted {
  private String status;
  private String errorMessage;
  private String errorDetails;
  private Long extendDuration;
  private Integer retries;
  @RedactSensitiveField
  private Map<String, Object> variables;
  @RedactSensitiveField
  private Map<String, Object> localVariables;
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.BusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.ConditionalElementHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.ExclusiveGatewayHandler;
import java.text.MessageFormat;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.ExclusiveGateway;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class ConditionalElementFactory {

  private BusinessRuleTaskHandler businessRuleTaskHandler;

  private ExclusiveGatewayHandler exclusiveGatewayHandler;

  /**
   * @param element which decides which handler to return
   * @return {@link ConditionalElementHandler}
   */
  public ConditionalElementHandler getHandler(BaseElement element) {
    if (element instanceof BusinessRuleTask) {
      return businessRuleTaskHandler;
    } else if (element instanceof ExclusiveGateway) {
      return exclusiveGatewayHandler;
    }
    throw new IllegalArgumentException(
        MessageFormat.format("{0} not supported", element.getElementType().getTypeName()));
  }
}

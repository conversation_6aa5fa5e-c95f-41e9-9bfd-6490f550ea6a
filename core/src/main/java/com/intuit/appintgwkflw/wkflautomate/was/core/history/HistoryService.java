package com.intuit.appintgwkflw.wkflautomate.was.core.history;

import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ProcessVariableDetailsRequest;

/**
 * This service deals with the history data of workflows.
 */
public interface HistoryService {

  /**
   * Gets the process details for a specific process instance.
   *
   * @param processInstanceId the process instance id
   * @return the process details
   */
  WorkflowGenericResponse getProcessDetails(String processInstanceId);
  
	/**
	 * Gets the process variable details based on the given filter.
	 *
	 * @param processVariableDetailsRequest process variable details request
	 * 
	 * @return the process Variable details
	 */
	WorkflowGenericResponse getProcessVariableDetails(
			ProcessVariableDetailsRequest processVariableDetailsRequest);
}

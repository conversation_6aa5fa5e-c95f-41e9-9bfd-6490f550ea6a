package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_CREATED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_OPEN;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TransactionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * <p>
 * This class objective is to manage DB transaction for Activity and Transaction Lifecycle
 * Management.
 */

@Component
@AllArgsConstructor
public class WorkflowTaskDBOperationManager {

  private ActivityProgressDetailsRepository progressDetailRepo;

  private TransactionDetailsRepository txnDetailRepo;

  private ActivityDetailsRepository activityDetailRepo;
  private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;

  /**
   * Saves the transaction and ActivityDetails both in DB with CREATED state. skipCallback = false,
   * downstream call was made so save status in DB. skipCallback = true, fetch txnDetail record from
   * DB and save as reference for new activityProgressDetail record.
   *
   * @param progressDetail - ActivityProgress Detail record.
   * @param response       - taskAdaptor response.
   */
  @Transactional
  public ActivityProgressDetails saveCreateStateInDB(Task task,
      ActivityProgressDetails progressDetail,
      WorkflowTaskResponse response) {
    // Save Data in Transaction Progress Detail table.
    TransactionDetails txnDetail = txnDetailRepo.save(prepareTransactionRecord(response));
    // Update DB with created state and txn with created state.
    progressDetail.setTxnDetails(txnDetail);
    progressDetail.setStatus(TASK_STATUS_CREATED);
    progressDetail.setAttributes(prepareAttributes(task));
    return progressDetailRepo.save(progressDetail);
  }


  /**
   * Save create state in ActivityProgressDB fetching reference from TxnDetail DB. QBO
   * skipcallback=true, CreateCommand case.
   *
   * @param progressDetail - ActivityProgress Detail record.
   * @param taskRequest    : WorkflowTaskRequest.
   * @return
   */
  @Transactional
  public ActivityProgressDetails saveCreateStateInActivityProgressDB(
      ActivityProgressDetails progressDetail,
      final WorkflowTaskRequest taskRequest) {
    // Save Data in Transaction Progress Detail table.
    //Fetch List of transactions using TransactionId.
    List<TransactionDetails> txnDetails = txnDetailRepo
        .findByTxnId(taskRequest.getTxnId())
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.TRANSACTION_DETAIL_NOT_FOUND));

    //Filter transactions using activityType. 
    //There can be multiple transaction with same txnId but will differ by activityType. 
    TransactionDetails txnDetail = txnDetails.stream().filter(txnDetailObj -> {
      TaskType txnTaskType = txnDetailObj.getActivityProgressDetails().stream()
          .map(activityProgressDetail ->
              activityProgressDetail.getActivityDefinitionDetail().getType()).findFirst()
          .orElseThrow(
              () -> new WorkflowNonRetriableException(
                  WorkflowError.ACTIVITY_PROGRESS_DETAIL_NOT_FOUND));
      return taskRequest.getTaskType().equals(txnTaskType);
    }).findFirst()
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.TRANSACTION_DETAIL_NOT_FOUND));
    // Update DB with created state and txn with created state.
    progressDetail.setTxnDetails(txnDetail);
    progressDetail.setStatus(TASK_STATUS_CREATED);
    ActivityProgressDetails activityProgressDetails = progressDetailRepo.save(progressDetail);

    // Save to Outbox Table
    activityRuntimeDomainEventHandler.publish(
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(activityProgressDetails)
            .build());
    return activityProgressDetails;
  }

  /**
   * Prepares transaction record to save in DB.
   *
   * @param response - TaskAdaptor response.
   * @return
   */
  private TransactionDetails prepareTransactionRecord(WorkflowTaskResponse response) {
    return TransactionDetails.builder().txnId(response.getTxnId())
        .status(response.getStatus()).build();
  }


  /**
   * Prepares runtime activity Progress record in Open state to save in DB.
   *
   * @param task    : WorkflowTaskRequest.
   * @param processDetails : Process Instance record pojo.
   * @return
   */
  public ActivityProgressDetails prepareActivityProgressAndSaveInOpenState(
      Task task, ProcessDetails processDetails) {
    ActivityDetail activityDetail = activityDetailRepo
        .findByTemplateDetailsAndActivityId(
            processDetails.getDefinitionDetails().getTemplateDetails(),
            task.getActivityId())
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .activityDefinitionDetail(activityDetail)
        .id(task.getId()).processDetails(processDetails).status(TASK_STATUS_OPEN)
        .name(task.getTaskName())
        .attributes(prepareAttributes(task)).build();
    return progressDetailRepo.save(activityProgressDetails);
  }


  /**
   * Prepares attributes json string using runtimeAttributes def and execution variables.
   *
   * @param task :: Task pojo having taskAttributes.
   * @return Map of Key(runtimeAttributes), Value(variables.get(key))
   */
  private String prepareAttributes(Task task) {
    TaskAttributes taskAttributes = task.getTaskAttributes();
    Map<String, Object> runtimeAttributes = new HashMap<>();
    Map<String, Object> variables = taskAttributes.getVariables();
    if (MapUtils.isNotEmpty(taskAttributes.getRuntimeAttributes())
        && MapUtils.isNotEmpty(variables)) {
      /**
       * Map being prepared with runtimeAttribute key and its correponding value from taskAttribute.variables.
       */
      taskAttributes
          .getRuntimeAttributes().keySet()
          .stream().filter(variables::containsKey)
          .forEach(key -> runtimeAttributes.put(key, variables.get(key)));
    }
    return ObjectConverter.toJson(Map.of(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES,
        runtimeAttributes));
  }

  /**
   * Saves attribute received in event in DB column attributes as <status>:<attributeMap> Status is
   * updated in transaction and activityProgressDetails DB both.
   *
   * @param taskRequest: WorkflowTaskRequest.
   * @param activityProgressDetails - activityProgressDetails record instance.
   * @return
   */
  @Transactional
  public void updateStatusInDB(Task taskRequest, ActivityProgressDetails activityProgressDetails) {
    TransactionDetails txnDetails = activityProgressDetails.getTxnDetails();
    txnDetails.setStatus(taskRequest.getStatus());
    txnDetailRepo.save(txnDetails);
    activityProgressDetails.setAttributes(prepareAttributes(taskRequest, activityProgressDetails));
    activityProgressDetails.setStatus(taskRequest.getStatus());

    // Publish Domain Event
    activityRuntimeDomainEventHandler.publish(
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(
                cloneActivityProgressDetails(
                    progressDetailRepo.saveAndFlush(activityProgressDetails)))
            .build());
  }

  /**
   * This method is used to reflect the latest values from the db. Fields using @version annotation
   * will update the db by auto incrementing db. Using the copy constructor with flushed values will
   * help to get the latest version value without making db get call.
   *
   * @param activityProgressDetails
   * @return
   */
  private ActivityProgressDetails cloneActivityProgressDetails(
      ActivityProgressDetails activityProgressDetails) {
    ActivityProgressDetails activityProgressDetailResponse =
        new ActivityProgressDetails(activityProgressDetails);
    return activityProgressDetailResponse;
  }

  /**
   * Preparing attributes for Mutation calls. Every execution variable will be added to attributes
   * in DB.
   *
   * @param taskRequest             :: Task with taskAttribute.variables to be upsert as attributes
   *                                in DB.
   * @param activityProgressDetails :: Save DB instance
   * @return attributes in json String.
   */
  private String prepareAttributes(Task taskRequest, ActivityProgressDetails activityProgressDetails) {
    String attributes = activityProgressDetails.getAttributes();
    Map<String, Object> variables = taskRequest.getTaskAttributes().getVariables();
    /**
     * If no variables to update, will keep attributes as it in DB.
     */
    if (MapUtils.isNotEmpty(variables)) {
      Map<String, Object> runtimeAttributes = new HashMap<>();
      /**
       * Fetch runtimeAttribute map from attributes saved in DB.
       */
      if (StringUtils.isNotEmpty(attributes)) {
    	  WorkflowActivityAttributes activityAttributes = ObjectConverter.fromJson(attributes,
            new TypeReference<WorkflowActivityAttributes>() {
            });
        runtimeAttributes = Optional.ofNullable(activityAttributes)
        		 	.map(activityAttribute -> Optional.ofNullable(activityAttribute.getRuntimeAttributes())
        		 			.orElse(new HashMap<>()))
        		 	.orElse(new HashMap<>());
      }
      /**
       * Override values with variables received in Mutation request.
       */
      runtimeAttributes.putAll(variables);
      attributes = ObjectConverter.toJson(Map.of(ActivityConstants.ACTIVITY_RUNTIME_ATTRIBUTES,
          runtimeAttributes));
    }
    return attributes;
  }

  /**
   * Updates TransactionDetail and ActivityProgress record with Complete state in DB. This is done
   * within same transaction.
   *
   * @param taskRequest : WorkflowTaskRequest.
   * @param activityProgressDetails - activityProgressDetails record instance.
   * @return
   */
  @Transactional
  public void markTxnDetailAndActivityProgressCompleteInDB(
      Task taskRequest,
      ActivityProgressDetails activityProgressDetails,
      WorkflowTaskResponse response) {
    Timestamp endTime = new Timestamp(System.currentTimeMillis());
    TransactionDetails txnDetails = activityProgressDetails.getTxnDetails();
    txnDetails.setStatus(response.getStatus());
    txnDetails.setEndTime(endTime);
    txnDetailRepo.save(txnDetails);
    activityProgressDetails.setAttributes(prepareAttributes(taskRequest, activityProgressDetails));
    activityProgressDetails.setStatus(ActivityConstants.TASK_STATUS_COMPLETE);
    activityProgressDetails.setEndTime(endTime);
    ActivityProgressDetails activityDetails =
        progressDetailRepo.save(activityProgressDetails);
    // Save to Outbox Table
    activityRuntimeDomainEventHandler.publish(
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(activityDetails)
            .build());
  }

  /**
   * Update ActivityProgress record in DB with Complete state.
   *
   * @param taskRequest : WorkflowTaskRequest.
   * @param activityProgressDetails - activityProgressDetails record instance.
   * @return
   */
  public void markActivityProgressCompleteInDB(
      Task taskRequest, ActivityProgressDetails activityProgressDetails) {
    activityProgressDetails.setAttributes(prepareAttributes(taskRequest, activityProgressDetails));
    activityProgressDetails.setStatus(taskRequest.getStatus());
    activityProgressDetails.setEndTime(new Timestamp(System.currentTimeMillis()));
    ActivityProgressDetails activityDetails =
        progressDetailRepo.save(activityProgressDetails);
    // Save to Outbox Table
    activityRuntimeDomainEventHandler.publish(
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .entityChangeAction(EntityChangeAction.UPDATE)
            .request(activityDetails)
            .build());
  }

  /**
   * Update ActivityProgress record in DB with Failed state.
   *
   * @param taskRequest             : WorkflowTaskRequest.
   * @param activityProgressDetails - activityProgressDetails record instance.
   */
  public void markActivityProgressFailedInDB(Task taskRequest,
      ActivityProgressDetails activityProgressDetails) {
    activityProgressDetails.setStatus(taskRequest.getStatus());
    progressDetailRepo.save(activityProgressDetails);
  }

  /**
   * Update TransactionDetails record in DB with Failed state.
   *
   * @param taskRequest             : WorkflowTaskRequest.
   * @param txnDetails - activityProgressDetails record instance.
   */
  public void markTxnDetailsFailedInDB(Task taskRequest,
      TransactionDetails txnDetails) {
    txnDetails.setStatus(taskRequest.getStatus());
    txnDetailRepo.save(txnDetails);
  }


  /**
   * Prepares activity Progress record and save in Failed state.
   *
   * @param task    : WorkflowTaskRequest.
   * @param processDetails : Process Instance record pojo.
   * @return
   */
  public ActivityProgressDetails prepareActivityProgressAndSaveInFailedState(
      Task task, ProcessDetails processDetails) {
    ActivityDetail activityDetail = activityDetailRepo
        .findByTemplateDetailsAndActivityId(
            processDetails.getDefinitionDetails().getTemplateDetails(),
            task.getActivityId())
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));

    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder()
        .activityDefinitionDetail(activityDetail)
        .id(task.getId()).processDetails(processDetails).status(TASK_STATUS_FAILED)
        .name(task.getTaskName()).build();
    return progressDetailRepo.save(activityProgressDetails);
  }

}
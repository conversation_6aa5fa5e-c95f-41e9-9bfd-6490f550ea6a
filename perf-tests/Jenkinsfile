@Library(value = 'msaas-shared-lib', changelog = false) _


node {
    // setup the global static configuration
    config = setupMsaasPipeline('msaas-config.yaml')
    config['sloAnalyzerVersion']="3b86823"
}

def testParamMap = [rampUpInMins: "5", testdurationInMins:"30"]

pipeline {
    agent {
        kubernetes {
            label "${config.pod_label}"
            yamlFile 'KubernetesPods.yaml'
        }
    }
     environment {
         PERF_BRIDGE_TOKEN = credentials('ibp-perf-bridge-token')
         WAVEFRONT_TOKEN = credentials('ibp-wavefront-creds')
    }
    stages {
        stage('Perf Test Setup') {
            steps {                    
                loadTest(config, null, testParamMap);
            }            
        }
        stage('Perf Analysis') {
            steps {
                container('podman') {
                    perfSLOAnalyzer(config, "Perf Test Setup")
                }
            }
        }
    }
}


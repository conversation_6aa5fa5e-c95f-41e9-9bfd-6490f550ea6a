package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.KafkaReceivedEventHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.config.PushTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EventProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import javax.annotation.PostConstruct;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * <p>Event consumer that listens to external-task topic asycnhronously and submit the task for further processing
 */
@Component
@ConditionalOnExpression(
        "${event.consumer.enabled:false} && ${push-task.enabled:false}")
public class CamundaPushTaskConsumer {

    private final EventProcessor eventProcessor;
    private final ThreadPoolExecutor threadPoolExecutor;
    private final WASContextHandler wasContextHandler;
    private PushTaskConfig pushTaskConfig;

    public CamundaPushTaskConsumer(EventProcessor eventProcessor,
                                   @Qualifier(WorkflowConstants.CAMUNDA_PUSH_TASK_EXECUTOR_THREAD_BEAN) ThreadPoolExecutor threadPoolExecutor,
                                   WASContextHandler wasContextHandler,
                                   PushTaskConfig pushTaskConfig) {
        this.threadPoolExecutor = threadPoolExecutor;
        this.eventProcessor = eventProcessor;
        this.wasContextHandler = wasContextHandler;
        this.pushTaskConfig = pushTaskConfig;

    }

    @PostConstruct
    public void init() {
        EventingLoggerUtil.logInfo(
                "Starting Kafka Consumer for Push Task", this.getClass().getSimpleName());
    }

    /**
     * Receive an event and manually commit i.e. acknowledge Kafka offset.
     *
     * @param consumerRecord consumed record
     * @param messageHeaders message headers
     * @param acknowledgment manual acknowledge-er
     */
    @KafkaListener(
            topics = "#{eventConsumerUtil.getFilteredTopicList('externalTaskPush',false)}",
            groupId = "${event.consumer.groupId}",
            containerFactory = "kafkaListenerFactory")
    @EventListener(name = MetricName.CAMUNDA_EXTERNAL_TASK_PUSH_EVENT, type = Type.EVENT_METRIC)
    public void receive(
            ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
            @Headers MessageHeaders messageHeaders,
            Acknowledgment acknowledgment) {

        if (skipEvent(messageHeaders, consumerRecord.value().get())) {
            EventingLoggerUtil.logInfo(
                    "Skipping external task event, worker has already started processing it ", this.getClass().getSimpleName());
            return;
        }
        EventingLoggerUtil.logDebug("Reading message in CamundaPushTaskConsumer", this.getClass().getSimpleName());
        threadPoolExecutor.submit(() -> process(messageHeaders, consumerRecord));
    }

    /**
     * Here we are processing the message in async way
     * @param messageHeaders
     * @param consumerRecord
     */

    protected void process(@Headers MessageHeaders messageHeaders, ConsumerRecord<String, Result<FormatException, String>> consumerRecord) {
        try {
            processEventHeaders(consumerRecord, messageHeaders);
            eventProcessor.process(messageHeaders, consumerRecord.value().get());
        } finally {
            EventingLoggerUtil.logInfo("Clearing WAS Context", this.getClass().getSimpleName());
            wasContextHandler.clear();
        }
    }

    /**
     * Processing event headers
     * @param record
     * @param messageHeaders
     */
    private void processEventHeaders(
            ConsumerRecord<String, Result<FormatException, String>> record,
            MessageHeaders messageHeaders) {

        Map<String, String> headers = EventUtil.transformHeader(messageHeaders);
        EventUtil.populateEventContext(wasContextHandler, headers);
    }

    /**
     * This function is responsible for checking if time is more then the lock duration
     * after which camunda worker will pick the task
     *
     * @param messageHeaders
     * @param event
     * @return true/false
     */
    private boolean skipEvent(@Headers MessageHeaders messageHeaders, String event) {

        // Getting message publish time from kafka header
        Map<String, String> headers = EventUtil.transformHeader(messageHeaders);
        KafkaReceivedEventHeader kafkaReceivedEventHeader = ObjectConverter.convertObject(headers, KafkaReceivedEventHeader.class);
        long kafkaReceivedTimestamp = Optional.ofNullable(kafkaReceivedEventHeader)
                .map(KafkaReceivedEventHeader::getAdditionalModelAttributes)
                .map(attributes -> attributes.get(WorkflowConstants.KAFKA_RECEIVED_TIMESTAMP))
                .map(timestamp -> Long.parseLong((String) timestamp))
                .orElse(0L);
        ExternalTaskEvent externalTaskEvent = ObjectConverter.fromJson(event, ExternalTaskEvent.class);
        if(Objects.isNull(externalTaskEvent)){
            EventingLoggerUtil.logError("step=CamundaPushTaskConsumer externalTaskEvent is null or empty", this.getClass().getSimpleName());
            return true;
        }
        //get lock duration by topic
        long lockDuration = Optional.ofNullable(pushTaskConfig.getTopics())
                .map(topics -> topics.get(externalTaskEvent.getTopicName()))
                .map(Worker::getLockDuration)
                .orElse(0L);
        return (Instant.now().toEpochMilli() - kafkaReceivedTimestamp) > lockDuration;
    }
}

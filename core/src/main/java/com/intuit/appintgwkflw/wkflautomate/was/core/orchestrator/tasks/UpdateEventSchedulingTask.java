package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.*;

/**
 * Task to update event schedules in the scheduling service.
 * <AUTHOR>
 */
@AllArgsConstructor
public class UpdateEventSchedulingTask implements Task {
    private final SchedulingService schedulingService;
    private final ActionModelToScheduleRequestMapper actionModelToCreateScheduleRequestMapper;

    /**
     * Executes the task to update event schedules in the scheduling service.
     *
     * @param state The current state of the task execution.
     * @return The updated state after execution.
     */
    @Override
    public State execute(State state) {
        String realmId = state.getValue(AsyncTaskConstants.REALM_ID_KEY);
        SchedulingMetaData schedulingMetaData = state.getValue(AsyncTaskConstants.SCHEDULING_META_DATA);
        WorkflowVerfiy.verify(
                StringUtils.isBlank(realmId) || schedulingMetaData.getStatus() == null,
                WorkflowError.INPUT_INVALID,
                "realmId or schedule status");

        Optional<List<EventScheduleWorkflowActionModel>> optionalEventScheduleWorkflowActionModels =
                state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST);
        if (ObjectUtils.isEmpty(optionalEventScheduleWorkflowActionModels) || optionalEventScheduleWorkflowActionModels.isEmpty()) {
            WorkflowLogger.logInfo("Schedule Action Models are empty");
            return state;
        }
        List<EventScheduleWorkflowActionModel> eventScheduleWorkflowActionModels =
                optionalEventScheduleWorkflowActionModels.orElse(Collections.emptyList());
        if(ObjectUtils.isEmpty(schedulingMetaData.getDefinitionKey()))
            schedulingMetaData.setDefinitionKey(state.getValue(DEFINITION_KEY));
        List<String> scheduleIds = SchedulingServiceUtil.getScheduleIds(eventScheduleWorkflowActionModels, schedulingMetaData.getDefinitionKey());
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return state;
        }
        // make call to Scheduling Service
        List<SchedulingSvcResponse> eventScheduleUpdateResponses =
                schedulingService.updateSchedules(
                        SchedulingServiceUtil.getSchedulingSvcRequestsPayload(
                                eventScheduleWorkflowActionModels, actionModelToCreateScheduleRequestMapper, schedulingMetaData, state), realmId);
        /*
        Currently the rollback is not handled globally in update-definition flow.
        In case the scheduling-svc call fails, we are throwing exception so that error response is returned and UI displays error.
        But the tasks already executed in RX execution chain will not be rolled back.
         */
        WorkflowVerfiy.verify(
                CollectionUtils.isEmpty(eventScheduleUpdateResponses),
                WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                "No schedules are updated");
        WorkflowVerfiy.verify(
                eventScheduleUpdateResponses.size() != eventScheduleWorkflowActionModels.size(),
                WorkflowError.EVENT_SCHEDULING_CALL_FAILURE,
                "Schedules Not updated for all actions");
        return state;
    }
}

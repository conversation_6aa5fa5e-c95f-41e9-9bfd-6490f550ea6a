package com.intuit.appintgwkflw.wkflautomate.was.core.mappers;

import static java.util.Objects.nonNull;

import java.time.*;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;

import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.EndType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.RecurrenceType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.WeekOfMonth;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.ScheduleInfo;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;

/** <AUTHOR> */
@Mapper(componentModel = "spring",  imports = {Status.class, LocalTime.class, ObjectUtils.class}, injectionStrategy = InjectionStrategy.FIELD)
public abstract class ActionModelToScheduleRequestMapper {

    @Mappings({
            @Mapping(source = "eventScheduleWorkflowActionModel.recurrenceRule", target = "scheduleInfo")
    })
    public abstract SchedulingSvcRequest convertToScheduleRequest(EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel, Status status);

    @Mappings({
            @Mapping(source = "recurType", target = "recurrenceType"),
            @Mapping(source = "monthOfYear", target = "monthOfYear", qualifiedByName = "monthOfYearEnumToInteger"),
            @Mapping(source = "monthsOfYear", target = "monthsOfYear", qualifiedByName = "monthsOfYearEnumToInteger"),
            @Mapping(source = "daysOfWeek", target = "daysOfWeek", qualifiedByName = "daysOfWeekMapping"),
            @Mapping(source = "daysOfMonth", target = "daysOfMonth", qualifiedByName = "daysOfMonthMapping"),
            @Mapping(source = "weeksOfMonth", target = "weeksOfMonth", qualifiedByName = "weeksOfMonthMapping"),
            @Mapping(source = "timeZone", target = "zoneId", qualifiedByName = "stringToZoneId"),
            @Mapping(source="recurrenceTime", target = "time", qualifiedByName = "extractLocalTime"),
            @Mapping(source = "endDate", target = "endDateTime", qualifiedByName = "buildEndDateTime"),
            @Mapping(target = "endType",  expression = "java(mapEndType(recurrenceRule.getEndDate(),recurrenceRule.getOccurrences()))")
    })
    public abstract ScheduleInfo convertToScheduleInfo(RecurrenceRule recurrenceRule);

    @ValueMappings({
            @ValueMapping(source = "BIMONTHLY", target = "MONTHLY"),
            @ValueMapping(source = "ANY_ACTIVITY", target = "DAILY"),
            @ValueMapping(source = "EVERY_PAYROLL_RUN", target = "WEEKLY"),
            @ValueMapping(source = "PAYROLL_RULES", target = "WEEKLY"),
            @ValueMapping(source = "OTHER", target = "YEARLY")
    })
    public abstract RecurrenceType mapRecurTypeEnumToRecurrenceType(RecurTypeEnum recurTypeEnum);

    @Mappings({
            @Mapping(target = "dayOfWeek", ignore = true),
            @Mapping(target = "dayOfMonth", ignore = true),
            @Mapping(target = "monthOfYear", ignore = true),
            @Mapping(target = "startDate", source = "startDate"),
    })
    public abstract void addStartDateFromActionModel(@MappingTarget ScheduleInfo scheduleInfo, EventScheduleWorkflowActionModel eventScheduleWorkflowActionModel);


    @Named("daysOfWeekEnumToDayOfWeek")
    static DayOfWeek stringToEnum(List<DayOfWeekEnum> values) {
        return (CollectionUtils.isNotEmpty(values)) ? DayOfWeek.valueOf(values.get(0).toString()) : null;
    }

    @Named("monthOfYearEnumToInteger")
    static Integer monthOfYearEnumToInteger(MonthsOfYearEnum value) {
        return Objects.nonNull(value) ? value.ordinal() + 1 : null;
    }

    @Named("monthsOfYearEnumToInteger")
    static List<Integer> monthsOfYearEnumToInteger(List<MonthsOfYearEnum> values) {
        return CollectionUtils.isEmpty(values) ? null : values.stream().map(value -> value.ordinal() + 1).collect(Collectors.toList());
    }

    @Named("daysOfWeekMapping")
    static List<DayOfWeekEnum> daysOfWeekMapping(List<DayOfWeekEnum> values) {
        return CollectionUtils.isEmpty(values) ? null : values;
    }

    @Named("daysOfMonthMapping")
    static List<Integer> daysOfMonthMapping(List<Integer> values) {
        return CollectionUtils.isEmpty(values) ? null : values.stream()
                .map(i -> {
                    /*
                    31 is considered as either 31st day of month or last day of month  -whichever is applicable.
                    Setting this to -1 to represent last day of month
                     */
                    if (i == 31) {
                        return -1;
                    }
                    return i;
                }).collect(Collectors.toList());
    }

    @Named("weeksOfMonthMapping")
    static List<WeekOfMonthEnum> weeksOfMonthMapping(List<WeekOfMonthEnum> values) {
        return CollectionUtils.isEmpty(values) ? null : values;
    }

    @Named("buildEndDateTime")
    static LocalDateTime buildEndDateTime(DateTime endDate){
        return endDate != null ? LocalDateTime.of(endDate.getYear(), endDate.getMonthOfYear(), endDate.getDayOfMonth(), 23, 59, 59) : null;
    }

    @ValueMappings({
            @ValueMapping(source = "DAY", target = "FIRST"),
            @ValueMapping(source = "OTHER", target = "FIRST")
    })
    public abstract WeekOfMonth mapWeekOfMonthEnumToWeekOfMonth(WeekOfMonthEnum weekOfMonthEnum);

    @Named("stringToZoneId")
    static ZoneId stringToZoneId(String zoneId) {
        return Objects.nonNull(zoneId) ? ZoneId.of(zoneId) : null;
    }
    
    @Named("buildDateTime")
    static DateTime buildDateTime(DateTime startDate, TimeDuration recurrenceTime) {
        Optional<TimeDuration> optionalRecurrenceTime = Optional.ofNullable(recurrenceTime);

        if (Objects.isNull(startDate)) {
            return  startDate;
        }
        
        // If recurrenceTime is null, return start date
        if (!optionalRecurrenceTime.isPresent()) {
            return  startDate.toLocalDateTime().toDateTime();
        }

        int hours = optionalRecurrenceTime.map(time -> time.getHours()).orElse(0);
        int minutes = optionalRecurrenceTime.map(minute -> minute.getMinutes()).orElse(0);

        // Build the duration based on recurrenceTime
        Duration duration = Duration.standardHours(hours).plus(Duration.standardMinutes(minutes));

		// Add the duration to the start DateTime to get the end DateTime
		return startDate.toLocalDateTime().plus(duration.toPeriod()).toDateTime();

    }
    
    @Named("mapEndType")
	static EndType mapEndType(DateTime endDate,Integer occurences) {
		if(nonNull(endDate)) {
			return EndType.END_BY_DATE;
		}else if(nonNull(occurences)) {
			return EndType.END_AFTER_OCCURRENCES;
		}
		return EndType.NO_END_DATE;
	}

    @Named("extractLocalTime")
    static LocalTime extractLocalTime(TimeDuration recurrenceTime) {
        if (ObjectUtils.isEmpty(recurrenceTime) || ObjectUtils.isEmpty(recurrenceTime.getHours()) || ObjectUtils.isEmpty(recurrenceTime.getMinutes())) {
            return null;
        }
        return LocalTime.of(recurrenceTime.getHours(), recurrenceTime.getMinutes());
    }
}

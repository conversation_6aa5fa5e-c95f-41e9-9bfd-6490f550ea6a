package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.HeaderPopulator;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlResponse.WASV4GraphqlResponseBuilder;
import com.intuit.v4.Authorization;
import com.intuit.v4.Context;
import com.intuit.v4.Error;
import com.intuit.v4.ErrorCodeEnum;
import com.intuit.v4.RequestContext;
import com.intuit.v4.Result;
import com.intuit.v4.interaction.InteractionResults;
import com.intuit.v4.util.RequestMetrics;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *     <p>Client to call any downstream v4 remote service.
 */
public abstract class GraphqlClient {

  private static final String TARGET_REALMID = "wfm_targetrealmid";

  protected static final String GRAPHQL_CALL_ERROR_MESSAGE =
      "Error in making Graphql call. exception=%s";

  @Autowired private OfflineTicketClient offlineTicketClient;

  @Autowired private WASContextHandler contextHandler;

  @Autowired private HeaderPopulator headerPopulator;
  
  private Map<String, Context> urlContextMap = new ConcurrentHashMap<>();
  
  protected Context getContext(String url){
	  return urlContextMap
			  .computeIfAbsent(url, this::prepareContext);
  }
  
  protected Context prepareContext(String url){
	  Context context = new Context();
	  context.remote().setAddress(url);
	  return context;
  }

  /**
   * performs the mutation on the given graphql endpoint
   *
   * @param wasGraphqlRequest input WAS graphql request
   * @return {@link WASV4GraphqlResponse}
   */
  abstract <T> WASV4GraphqlResponse<T> write(WASV4GraphqlRequest wasGraphqlRequest);

  /**
   * query the graphql end point with the given query request
   *
   * @param wasGraphqlRequest input WAS graphql request
   * @return {@link WASV4GraphqlResponse}
   */
  abstract <T> WASV4GraphqlResponse<T> read(WASV4GraphqlRequest wasGraphqlRequest);

  /**
   * create the Request context with the Intuit Authorization header based on {@link V4AuthType}. if
   * auth type is system prepares system offline auth header and if auth type is user prepares user
   * context auth header using {@link WASContextHandler}
   *
   * @param wasGraphqlRequest input WAS graphql request
   * @return {@link RequestContext}
   */
  protected RequestContext getContext(WASV4GraphqlRequest wasGraphqlRequest) {
    String tid = contextHandler.get(WASContextEnums.INTUIT_TID);
    // if tid not found in context generate a uuid
    String intuit_tid = StringUtils.isBlank(tid) ? UUID.randomUUID().toString() : tid;
    RequestContext requestContext = getContext(wasGraphqlRequest.getUrl()).getRequestContext(intuit_tid);

    // populate auth details
    populateAuthorization(wasGraphqlRequest, requestContext);

    if (StringUtils.isNotBlank(wasGraphqlRequest.getTargetRealmId())) {
      requestContext.setCustomHeaders(
          Collections.singletonMap(TARGET_REALMID, wasGraphqlRequest.getTargetRealmId()));
    }

    // populate custom headers
    populateCustomHeaders(wasGraphqlRequest, requestContext);

    return requestContext;
  }

  /**
   * @param wasGraphqlRequest graphql request
   * @param requestContext v4 request context
   */
  private void populateCustomHeaders(
      WASV4GraphqlRequest wasGraphqlRequest, RequestContext requestContext) {

    if (CollectionUtils.isEmpty(wasGraphqlRequest.getCustomHeaders())) {
      return;
    }

    if (!CollectionUtils.isEmpty(requestContext.getCustomHeaders())) {
      requestContext.getCustomHeaders().putAll(wasGraphqlRequest.getCustomHeaders());
    } else {
      requestContext.setCustomHeaders(wasGraphqlRequest.getCustomHeaders());
    }
  }

  /**
   * populate Authorization in request context and supports System offline and User context Auth.
   *
   * @param wasGraphqlRequest input WAS graphql request
   * @param requestContext graphql request context
   * @throws WorkflowGeneralException if auth is not supported or is empty
   */
  private void populateAuthorization(
      WASV4GraphqlRequest wasGraphqlRequest, RequestContext requestContext) {
    WorkflowVerfiy.verify(
        Objects.isNull(wasGraphqlRequest.getAuthType()), WorkflowError.INVALID_GRAPHQL_AUTH_TYPE);
    
    switch (wasGraphqlRequest.getAuthType()) {
      case SYSTEM_OFFLINE:
        requestContext.setAuthorization(
            new Authorization(offlineTicketClient.getSystemOfflineHeadersForOfflineJob()));
        break;
      case USER:
        requestContext.setAuthorization(
            new Authorization(
                headerPopulator.constructAuthzHeader(
                    contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER))));
        break;
      default:
        WorkflowVerfiy.verify(true, WorkflowError.INVALID_GRAPHQL_AUTH_TYPE);
        break;
    }
  }

  /**
   * build error response.
   *
   * @param exception any exception
   * @return {@link WASV4GraphqlResponse}
   */
  protected <T> WASV4GraphqlResponse<T> buildErrorResponse(Exception exception) {
    WASV4GraphqlResponseBuilder<T> responseBuilder = WASV4GraphqlResponse.builder();
    responseBuilder.error(true);
    responseBuilder.errors(
        Arrays.asList(
            new Error()
                .code(ErrorCodeEnum.PLT500.value())
                .type(Error.ErrorTypeEnum.SYSTEM_ERROR)
                .message(exception.getMessage())));
    return responseBuilder.build();
  }


  /**
   * build List response using interaction result.
   *
   * @param interactionResults v4 interaction result
   * @return {@link WASV4GraphqlResponseBuilder}
   */
  protected <T> WASV4GraphqlResponse<T> buildListResponse(InteractionResults interactionResults) {
    WASV4GraphqlResponseBuilder<T> responseBuilder = WASV4GraphqlResponse.builder();
    if(!checkAndPopulateError(interactionResults, responseBuilder)){
      populateListResponse(responseBuilder, interactionResults);
    }
    responseBuilder.requestMetrics(populateRequestMetrics(interactionResults));
    return responseBuilder.build();
  }

  /**
   * build response using interaction result.
   *
   * @param interactionResults v4 interaction result
   * @return {@link WASV4GraphqlResponseBuilder}
   */
  protected  <T> WASV4GraphqlResponse<T> buildResponse(InteractionResults interactionResults) {
    WASV4GraphqlResponseBuilder<T> responseBuilder = WASV4GraphqlResponse.builder();
    if(! checkAndPopulateError(interactionResults, responseBuilder)){
      responseBuilder.response(interactionResults.getSingleResult());
    }
    responseBuilder.requestMetrics(populateRequestMetrics(interactionResults));
    return responseBuilder.build();
  }

  /**
   * prepare list of errors thrown by downstream.
   *
   * @param interactionResults v4 interaction result
   * @param responseBuilder graphql response builder
   * @return list of error {@link List<Error>}
   */
  @SuppressWarnings("deprecation")
  protected List<Error> populateError(InteractionResults interactionResults) {
    List<Error> errors = new ArrayList<Error>();

    if (null == interactionResults) {
      return errors;
    }

    Iterator<Result> resultItr = interactionResults.iterator();

    if (null == resultItr) {
      return errors;
    }

    while (resultItr.hasNext()) {
      Result result = resultItr.next();

      List<Error> errorList = null != result && result.isErrorsSet() ? result.getErrors() : null;
      if (!CollectionUtils.isEmpty(errorList)) {
        errors.addAll(errorList);
      }

      // for internal v4 graphql exception error
      if (null != result.getError()) {
        errors.add(result.getError());
      }
    }
    logError(
        String.format("Error in making Graphql call. response=%s", ObjectConverter.toJson(errors)));
    return errors;
  }

  /**
   * logs error via {@link WorkflowLogger}
   *
   * @param errorMessage error message
   */
  protected void logError(String errorMessage) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(errorMessage)
                .className(V4GraphqlClient.class.getSimpleName()));
  }

  /**
   * Checks if there are errors and populates error.
   */
  private <T> boolean checkAndPopulateError(InteractionResults interactionResults,
      WASV4GraphqlResponseBuilder<T> responseBuilder) {
    if (interactionResults == null
        || interactionResults.hasError()
        || interactionResults.hasException()) {
      responseBuilder.error(true);
      responseBuilder.errors(populateError(interactionResults));
      return true;
    }
    return false;
  }

  /**
   * returns request metrics like tid,sdk ver etc
   *
   * @param interactionResults interaction result from v4
   * @return {@link RequestMetrics}
   */
  protected RequestMetrics populateRequestMetrics(InteractionResults interactionResults) {
    return Optional.ofNullable(interactionResults.getContext())
        .map(reqLogger -> reqLogger.getRequestLogger())
        .map(reqMetrics -> reqMetrics.getRequestMetrics())
        .orElse(null);
  }

  /**
   * Populates response in case of List
   * @param responseBuilder response object
   * @param interactionResults response from service
   * @param <T> Generic type response of type List<?>
   */
  protected <T> void populateListResponse(WASV4GraphqlResponseBuilder<T> responseBuilder, InteractionResults interactionResults){
    Collection<Result> resultList = interactionResults.getEntities();
    if (!CollectionUtils.isEmpty(resultList)) {
      responseBuilder.response((T)resultList.toArray(new Result[0])[0].getData());
    }
    else {
      responseBuilder.response((T)(Collections.emptyList()));
    }
  }
}

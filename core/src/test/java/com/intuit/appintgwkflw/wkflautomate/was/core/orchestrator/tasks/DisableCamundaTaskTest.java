package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.CAMUNDA_MESSAGE_NAME;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DisableCamundaTaskTest {

  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest =
      Mockito.mock(BPMNEngineRunTimeServiceRest.class);

  private TriggerDetailsRepository triggerDetailsRepository =
      Mockito.mock(TriggerDetailsRepository.class);

  private MessageCamundaTask task =
      new MessageCamundaTask(
          bpmnEngineRunTimeServiceRest, triggerDetailsRepository, "request", "response");

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void processDefinitionIdNotPresent() {
    State inputRequest = new State();
    task.execute(inputRequest);
  }

  @Test
  public void testSignal() {
    State inputRequest = new State();
    inputRequest.addValue("request", "123");
    inputRequest.addValue(DEFINITION_ID_KEY, "id");
    inputRequest.addValue(CAMUNDA_MESSAGE_NAME, "delete");
    List<TriggerDetails> list = new ArrayList<>();
    TriggerDetails triggerDetails = new TriggerDetails();
    triggerDetails.setTriggerName("deleted_voided");
    list.add(triggerDetails);
    Mockito.when(bpmnEngineRunTimeServiceRest.correlateMessage(Mockito.any())).thenReturn(true);
    Mockito.when(triggerDetailsRepository.findTriggerDetailsByDefinitionId(Mockito.anyString()))
        .thenReturn(Optional.ofNullable(list));
    try {
      task.execute(inputRequest);
    } catch (Exception e) {
      Assert.fail();
    }
  }
}

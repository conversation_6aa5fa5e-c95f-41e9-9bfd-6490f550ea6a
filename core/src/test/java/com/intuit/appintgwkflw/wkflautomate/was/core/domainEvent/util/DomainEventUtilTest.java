package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import org.junit.Assert;
import org.junit.Test;

import java.util.Collections;

/**
 * <AUTHOR>
 */
public class DomainEventUtilTest {


  @Test
  public void testGetDomainEventRequest() {
    DomainEntityRequest request =
        DomainEventUtil.getDomainEventRequest(
            EventHeaderEntity.builder().build(),
            ProcessDetails.builder().build(),
            EntityChangeAction.CREATE, Collections.emptyMap());
    Assert.assertNotNull(request);
    Assert.assertEquals(EntityChangeAction.CREATE, request.getEntityChangeAction());
  }

  @Test
  public void testGetEntityChangeActionForActivityRuntimeEventsStart() {
    EntityChangeAction response =
        DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
            WorkflowStateTransitionEvents.builder().eventType("start").build());
    Assert.assertEquals(EntityChangeAction.CREATE, response);
  }

  @Test
  public void testGetEntityChangeActionForActivityRuntimeEventsEnd() {
    EntityChangeAction response =
        DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
            WorkflowStateTransitionEvents.builder().eventType("end").build());
    Assert.assertEquals(EntityChangeAction.UPDATE, response);
  }

  @Test
  public void testGetEntityChangeActionForActivityRuntimeEventsCreate() {
    EntityChangeAction response =
        DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
            WorkflowStateTransitionEvents.builder().eventType("create").build());
    Assert.assertEquals(EntityChangeAction.UPDATE, response);
  }

  @Test
  public void testGetEntityChangeActionForActivityRuntimeEventsComplete() {
    EntityChangeAction response =
        DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
            WorkflowStateTransitionEvents.builder().eventType("completed").build());
    Assert.assertEquals(EntityChangeAction.UPDATE, response);
  }

  @Test
  public void testGetEntityChangeActionForActivityRuntimeEventsFreeText() {
    EntityChangeAction response =
        DomainEventUtil.getEntityChangeActionForActivityRuntimeEvents(
            WorkflowStateTransitionEvents.builder().eventType("default").build());
    Assert.assertEquals(EntityChangeAction.UPDATE, response);
  }

  @Test
  public void testProcessDetails() {
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ProcessDetails.builder().build()).build();
    DomainEventName domainEventName = DomainEventUtil.getName(request);
    Assert.assertEquals(DomainEventName.PROCESS, domainEventName);
  }

  @Test
  public void testDefinitionDetails() {
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(DefinitionDetails.builder().build()).build();
    DomainEventName domainEventName = DomainEventUtil.getName(request);
    Assert.assertEquals(DomainEventName.DEFINITION, domainEventName);
  }

  @Test
  public void testTemplateDetails() {
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(TemplateDetails.builder().build()).build();
    DomainEventName domainEventName = DomainEventUtil.getName(request);
    Assert.assertEquals(DomainEventName.TEMPLATE, domainEventName);
  }

  @Test
  public void testActivityDetails() {
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ActivityDetail.builder().build()).build();
    DomainEventName domainEventName = DomainEventUtil.getName(request);
    Assert.assertEquals(DomainEventName.ACTIVITY, domainEventName);
  }

  @Test
  public void testActivityProgressDetails() {
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ActivityProgressDetails.builder().build()).build();
    DomainEventName domainEventName = DomainEventUtil.getName(request);
    Assert.assertEquals(DomainEventName.ACTIVITY_RUNTIME, domainEventName);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testInvalidRequest() {
    DomainEntityRequest request = DomainEntityRequest.builder().build();
    DomainEventUtil.getName(request);
  }
}

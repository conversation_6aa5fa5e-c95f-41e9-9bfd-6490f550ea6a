package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnExtensionElementsHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.builder.AbstractFlowNodeBuilder;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.EscalationEventDefinition;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.MessageEventDefinition;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * This class is used to test the EndEventFlowNodeProcessor class.
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class EndEventFlowNodeProcessorTest {

  @InjectMocks EndEventFlowNodeProcessor endEventFlowNodeProcessor;

  @Mock DynamicBpmnExtensionElementsHelper dynamicBpmnExtensionElementsHelper;

  private FlowNode flowNode;
  private FlowNode baseTemplateFlowNode;
  private BpmnModelInstance bpmnModelInstance;
  private BpmnModelInstance baseTemplateBpmnModelInstance;
  private SubProcess subProcess;
  private SubProcess baseTemplateSubprocess;

  @Before
  public void setup() {
    bpmnModelInstance = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").endEvent("endEvent").done();
  }

  @Test
  public void testGetType() {
    Assert.assertEquals(BpmnComponentType.END_EVENT, endEventFlowNodeProcessor.getType());
  }

  @Test
  public void testAddExtensionElementsWithAllParametersAsNull() {
    endEventFlowNodeProcessor.addExtensionElements(null, null, null, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.never())
        .addAllValidExtensionElements(
            any(FlowNode.class), any(FlowNode.class), any(BpmnModelInstance.class));
  }

  @Test
  public void testAddExtensionElementsWithNullBaseTemplateFlowNodeAndNullBpmnModelInstance() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").endEvent("endEvent").done();
    flowNode = bpmnModelInstance.getModelElementById("endEvent");
    endEventFlowNodeProcessor.addExtensionElements(
        flowNode, null, null, baseTemplateBpmnModelInstance);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddExtensionElementsWithNullFlowNodeAndNullBaseTemplateBpmnModelInstance() {
    bpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").endEvent("endEvent").done();
    baseTemplateFlowNode = baseTemplateBpmnModelInstance.getModelElementById("endEvent");
    endEventFlowNodeProcessor.addExtensionElements(
        null, baseTemplateFlowNode, bpmnModelInstance, null);
    Mockito.verify(dynamicBpmnExtensionElementsHelper, Mockito.times(1))
        .addAllValidExtensionElements(any(), any(), any());
  }

  @Test
  public void testAddEventToSubProcessWithMessageEventDefinition() {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> processOptional = processes.stream().findFirst();
    processOptional.ifPresent(
        process -> process.builder().eventSubProcess().startEvent("subProcessStartNode").done());
    subProcess =
        bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst().get();

    Collection<Process> baseTemplateProcesses =
        baseTemplateBpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> baseTemplateProcessOptional = baseTemplateProcesses.stream().findFirst();
    baseTemplateProcessOptional.ifPresent(
        process ->
            process
                .builder()
                .eventSubProcess()
                .startEvent("subProcessStartNode")
                .endEvent("baseTemplateSubProcessEndEvent")
                .name("dummyName")
                .messageEventDefinition()
                .message("dummyMessage")
                .done());
    baseTemplateSubprocess =
        baseTemplateBpmnModelInstance.getModelElementsByType(SubProcess.class).stream()
            .findFirst()
            .get();

    StartEvent subProcessSourceNode =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst().get();

    endEventFlowNodeProcessor.addEventToSubProcess(
        subProcessSourceNode, subProcess, baseTemplateSubprocess, null);

    Optional<StartEvent> subprocessStartEvent =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessStartEvent.isPresent());

    Optional<EndEvent> subprocessEndEvent =
        subProcess.getChildElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessEndEvent.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            subprocessStartEvent.get(), subprocessEndEvent.get()));
  }

  @Test
  public void testAddEventToSubProcessWithoutMessageEventDefinition() {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> processOptional = processes.stream().findFirst();
    processOptional.ifPresent(
        process -> process.builder().eventSubProcess().startEvent("subProcessStartNode").done());
    subProcess =
        bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst().get();

    Collection<Process> baseTemplateProcesses =
        baseTemplateBpmnModelInstance.getModelElementsByType(Process.class);
    Optional<Process> baseTemplateProcessOptional = baseTemplateProcesses.stream().findFirst();
    baseTemplateProcessOptional.ifPresent(
        process ->
            process
                .builder()
                .eventSubProcess()
                .startEvent("subProcessStartNode")
                .endEvent("baseTemplateSubProcessEndEvent")
                .name("dummyName")
                .done());
    baseTemplateSubprocess =
        baseTemplateBpmnModelInstance.getModelElementsByType(SubProcess.class).stream()
            .findFirst()
            .get();

    StartEvent subProcessSourceNode =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst().get();

    endEventFlowNodeProcessor.addEventToSubProcess(
        subProcessSourceNode, subProcess, baseTemplateSubprocess, null);

    Optional<StartEvent> subprocessStartEvent =
        subProcess.getChildElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessStartEvent.isPresent());

    Optional<EndEvent> subprocessEndEvent =
        subProcess.getChildElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(subprocessEndEvent.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            subprocessStartEvent.get(), subprocessEndEvent.get()));
  }

  @Test
  public void
      testAddImplicitEventWithNullOutgoingSequenceFlowConditionAndNullMessageEventDefinition() {

    StartEvent sourceFlowNode = bpmnModelInstance.getModelElementById("startEvent");

    StartEvent baseTemplateSourceFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    EndEvent baseTemplateTargetFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("endEvent");
    Optional<SequenceFlow> baseTemplateSequenceFlow =
        baseTemplateSourceFlowNode.getOutgoing().stream().findFirst();

    endEventFlowNodeProcessor.addImplicitEvents(
        sourceFlowNode,
        baseTemplateTargetFlowNode,
        baseTemplateSequenceFlow.get(),
        bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    Optional<EndEvent> optionalTargetFlowNode =
        bpmnModelInstance.getModelElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(optionalTargetFlowNode.isPresent());
    Assert.assertEquals(baseTemplateTargetFlowNode.getId(), optionalTargetFlowNode.get().getId());

    Optional<StartEvent> optionalSourceFlowNode =
        bpmnModelInstance.getModelElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(optionalSourceFlowNode.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            optionalSourceFlowNode.get(), optionalTargetFlowNode.get()));
  }

  @Test
  public void testAddImplicitEventWithNullMessageEventDefinitionAndEscalationEventDefinition() {

    StartEvent sourceFlowNode = bpmnModelInstance.getModelElementById("startEvent");

    StartEvent baseTemplateSourceFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    EndEvent baseTemplateTargetFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("endEvent");
    SequenceFlow baseTemplateSequenceFlow =
        baseTemplateSourceFlowNode.getOutgoing().stream().findFirst().get();
    baseTemplateSequenceFlow.setName("dummyName");
    baseTemplateSequenceFlow.setTextContent("dummyTextContent");

    endEventFlowNodeProcessor.addImplicitEvents(
        sourceFlowNode,
        baseTemplateTargetFlowNode,
        baseTemplateSequenceFlow,
        bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    Optional<EndEvent> optionalTargetFlowNode =
        bpmnModelInstance.getModelElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(optionalTargetFlowNode.isPresent());
    Assert.assertEquals(baseTemplateTargetFlowNode.getId(), optionalTargetFlowNode.get().getId());

    Optional<StartEvent> optionalSourceFlowNode =
        bpmnModelInstance.getModelElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(optionalSourceFlowNode.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            optionalSourceFlowNode.get(), optionalTargetFlowNode.get()));

    SequenceFlow expectedSequenceFlow =
        optionalSourceFlowNode.get().getOutgoing().stream().findFirst().get();
    Assert.assertEquals(baseTemplateSequenceFlow.getName(), expectedSequenceFlow.getName());
    Assert.assertEquals(
        baseTemplateSequenceFlow.getTextContent(), expectedSequenceFlow.getTextContent());
  }

  @Test
  public void testAddImplicitEventWithNonNullMessageEventDefinition() {

    StartEvent sourceFlowNode = bpmnModelInstance.getModelElementById("startEvent");

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess()
            .startEvent("startEvent")
            .endEvent("endEvent")
            .messageEventDefinition()
            .message("dummyMessage")
            .camundaTopic("dummyTopic")
            .camundaType("dummyType")
            .done();

    StartEvent baseTemplateSourceFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    EndEvent baseTemplateTargetFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("endEvent");

    SequenceFlow baseTemplateSequenceFlow =
        baseTemplateSourceFlowNode.getOutgoing().stream().findFirst().get();
    baseTemplateSequenceFlow.setName("dummyName");
    baseTemplateSequenceFlow.setTextContent("dummyTextContent");

    endEventFlowNodeProcessor.addImplicitEvents(
        sourceFlowNode,
        baseTemplateTargetFlowNode,
        baseTemplateSequenceFlow,
        bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    Optional<EndEvent> optionalTargetFlowNode =
        bpmnModelInstance.getModelElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(optionalTargetFlowNode.isPresent());
    Assert.assertEquals(baseTemplateTargetFlowNode.getId(), optionalTargetFlowNode.get().getId());

    Optional<StartEvent> optionalSourceFlowNode =
        bpmnModelInstance.getModelElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(optionalSourceFlowNode.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            optionalSourceFlowNode.get(), optionalTargetFlowNode.get()));

    SequenceFlow expectedSequenceFlow =
        optionalSourceFlowNode.get().getOutgoing().stream().findFirst().get();
    Assert.assertEquals(baseTemplateSequenceFlow.getName(), expectedSequenceFlow.getName());
    Assert.assertEquals(
        baseTemplateSequenceFlow.getTextContent(), expectedSequenceFlow.getTextContent());

    Optional<MessageEventDefinition> expectedMessageEventDefinition =
        optionalTargetFlowNode.get().getChildElementsByType(MessageEventDefinition.class).stream()
            .findFirst();
    Assert.assertTrue(expectedMessageEventDefinition.isPresent());
    Assert.assertEquals(
        "dummyMessage", expectedMessageEventDefinition.get().getMessage().getName());
    Assert.assertEquals("dummyTopic", expectedMessageEventDefinition.get().getCamundaTopic());
    Assert.assertEquals("dummyType", expectedMessageEventDefinition.get().getCamundaType());
  }

  @Test
  public void testAddImplicitEventWithNonNullEscalationEventDefinition() {

    StartEvent sourceFlowNode = bpmnModelInstance.getModelElementById("startEvent");

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess()
            .startEvent("startEvent")
            .endEvent("endEvent")
            .escalation("dummyEscalation")
            .done();

    StartEvent baseTemplateSourceFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("startEvent");
    EndEvent baseTemplateTargetFlowNode =
        baseTemplateBpmnModelInstance.getModelElementById("endEvent");

    SequenceFlow baseTemplateSequenceFlow =
        baseTemplateSourceFlowNode.getOutgoing().stream().findFirst().get();
    baseTemplateSequenceFlow.setName("dummyName");
    baseTemplateSequenceFlow.setTextContent("dummyTextContent");

    endEventFlowNodeProcessor.addImplicitEvents(
        sourceFlowNode,
        baseTemplateTargetFlowNode,
        baseTemplateSequenceFlow,
        bpmnModelInstance,
        baseTemplateBpmnModelInstance);

    Optional<EndEvent> optionalTargetFlowNode =
        bpmnModelInstance.getModelElementsByType(EndEvent.class).stream().findFirst();
    Assert.assertTrue(optionalTargetFlowNode.isPresent());
    Assert.assertEquals(baseTemplateTargetFlowNode.getId(), optionalTargetFlowNode.get().getId());

    Optional<StartEvent> optionalSourceFlowNode =
        bpmnModelInstance.getModelElementsByType(StartEvent.class).stream().findFirst();
    Assert.assertTrue(optionalSourceFlowNode.isPresent());

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            optionalSourceFlowNode.get(), optionalTargetFlowNode.get()));

    SequenceFlow expectedSequenceFlow =
        optionalSourceFlowNode.get().getOutgoing().stream().findFirst().get();
    Assert.assertEquals(baseTemplateSequenceFlow.getName(), expectedSequenceFlow.getName());
    Assert.assertEquals(
        baseTemplateSequenceFlow.getTextContent(), expectedSequenceFlow.getTextContent());

    Optional<EscalationEventDefinition> expectedEscalationEventDefinition =
        optionalTargetFlowNode
            .get()
            .getChildElementsByType(EscalationEventDefinition.class)
            .stream()
            .findFirst();
    Assert.assertTrue(expectedEscalationEventDefinition.isPresent());
    Assert.assertEquals(
        "dummyEscalation",
        expectedEscalationEventDefinition.get().getEscalation().getEscalationCode());
  }

  @Test
  public void testCreateEndEvent() {

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess().startEvent("startEvent").endEvent("endEvent").done();

    AbstractFlowNodeBuilder flowNodeBuilder =
        Bpmn.createExecutableProcess().startEvent("startEvent").name("startEventName");

    Map<String, String> dynamicActivityIdMap = new HashMap<>();
    dynamicActivityIdMap.put("startEvent", "startEvent");

    endEventFlowNodeProcessor.createEndEvent(
        "startEvent", flowNodeBuilder, baseTemplateBpmnModelInstance, dynamicActivityIdMap);

    bpmnModelInstance = flowNodeBuilder.done();

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("startEvent"),
            bpmnModelInstance.getModelElementById("endEvent")));
  }

  @Test
  public void testCreateEndEventWithMessageEndEvent() {

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess()
            .startEvent("startEvent")
            .endEvent("endEvent")
            .messageEventDefinition()
            .message("dummyMessage")
            .camundaTopic("dummyTopic")
            .camundaType("dummyType")
            .done();

    AbstractFlowNodeBuilder flowNodeBuilder =
        Bpmn.createExecutableProcess().startEvent("startEvent").name("startEventName");

    Map<String, String> dynamicActivityIdMap = new HashMap<>();
    dynamicActivityIdMap.put("startEvent", "startEvent");

    endEventFlowNodeProcessor.createEndEvent(
        "startEvent", flowNodeBuilder, baseTemplateBpmnModelInstance, dynamicActivityIdMap);

    bpmnModelInstance = flowNodeBuilder.done();

    EndEvent endEvent = bpmnModelInstance.getModelElementById("endEvent");

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("startEvent"), endEvent));

    Assert.assertEquals(1, endEvent.getChildElementsByType(MessageEventDefinition.class).size());
    Assert.assertEquals(
        "dummyMessage",
        endEvent
            .getChildElementsByType(MessageEventDefinition.class)
            .iterator()
            .next()
            .getMessage()
            .getName());
    Assert.assertEquals(
        "dummyTopic",
        endEvent
            .getChildElementsByType(MessageEventDefinition.class)
            .iterator()
            .next()
            .getCamundaTopic());
    Assert.assertEquals(
        "dummyType",
        endEvent
            .getChildElementsByType(MessageEventDefinition.class)
            .iterator()
            .next()
            .getCamundaType());
  }

  @Test
  public void testCreateEndEventWithEscalationEndEvent() {

    baseTemplateBpmnModelInstance =
        Bpmn.createExecutableProcess()
            .startEvent("startEvent")
            .endEvent("endEvent")
            .escalation("dummyEscalation")
            .done();

    AbstractFlowNodeBuilder flowNodeBuilder =
        Bpmn.createExecutableProcess().startEvent("startEvent").name("startEventName");

    Map<String, String> dynamicActivityIdMap = new HashMap<>();
    dynamicActivityIdMap.put("startEvent", "startEvent");

    endEventFlowNodeProcessor.createEndEvent(
        "startEvent", flowNodeBuilder, baseTemplateBpmnModelInstance, dynamicActivityIdMap);

    bpmnModelInstance = flowNodeBuilder.done();

    EndEvent endEvent = bpmnModelInstance.getModelElementById("endEvent");

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(
            bpmnModelInstance.getModelElementById("startEvent"), endEvent));

    Assert.assertEquals(1, endEvent.getChildElementsByType(EscalationEventDefinition.class).size());
    Assert.assertEquals(
        "dummyEscalation",
        endEvent
            .getChildElementsByType(EscalationEventDefinition.class)
            .iterator()
            .next()
            .getEscalation()
            .getEscalationCode());
  }
}

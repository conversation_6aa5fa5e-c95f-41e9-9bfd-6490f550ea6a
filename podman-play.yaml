# Save the output of this file and use kubectl create -f to import
# it into Kubernetes.
#
# Created with podman-3.4.4

# NOTE: If you generated this yaml from an unprivileged and rootless podman container on an SELinux
# enabled system, check the podman generate kube man page for steps to follow to ensure that your pod/container
# has the right permissions to access the volumes added.
apiVersion: v1
kind: Service
metadata:
  labels:
    app: ipf-execution-runtime
  name: ipf-execution-runtime-service
spec:
  ports:
    - name: "8443"
      nodePort: 8443
      port: 8443
      targetPort: 8443
    - name: "8000"
      nodePort: 8000
      port: 8000
      targetPort: 8000
  selector:
    app: runtime
  type: NodePort
---
apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: "2022-01-21T18:33:36Z"
  labels:
    app: ipf-execution-runtime
  name: ipf-execution-runtime-pod
spec:
  containers:
    - image: docker.io/wurstmeister/zookeeper:latest
      name: zookeeper
      ports:
        - containerPort: 2181
          hostPort: 2181
    - image: docker.io/wurstmeister/kafka:latest
      name: kafka
      env:
        - name: <PERSON><PERSON>KA_ADVERTISED_LISTENERS
          value: INTERNAL://kafka:9092,OUTSIDE://kafka:9094
        - name: KAFKA_LISTENERS
          value: INTERNAL://:9092,OUTSIDE://:9094
        - name: KAFKA_LISTENER_SECURITY_PROTOCOL_MAP
          value: INTERNAL:PLAINTEXT,OUTSIDE:PLAINTEXT
        - name: KAFKA_INTER_BROKER_LISTENER_NAME
          value: INTERNAL
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: localhost:2181
        - name: KAFKA_CREATE_TOPICS
          value: "ipf-demo-source-dev:1:1, ipf-demo-sink-dev:1:1, idx-tracing-topic:1:1"
      ports:
        - containerPort: 9094
          hostPort: 9094
    - image: obsidiandynamics/kafdrop:latest
      name: kafdrop
      ports:
        - containerPort: 9000
          hostPort: 9000
      env:
        - name: KAFKA_BROKERCONNECT
          value: localhost:9092
        - name: JVM_OPTS
          value: "-Xms16M -Xmx48M -Xss180K -XX:-TieredCompilation -XX:+UseStringDeduplication -noverify"
    - image: docker.intuit.com/fdp/enrichment/ipf-runtime-image:0.4.20
      name: runtime
      env:
        - name: APP_ENV
          value: dev
        - name: APP_CONFIG_NAME
          value: messagemeshconfig
        - name: MESSAGE_MESH_SRC_TOPICS
          value: ipf-demo-source-dev
        - name: MESSAGE_MESH_SRC_CONSUMER_GROUP
          value: consumer-testprocessor
        - name: MESSAGE_MESH_PIPELINE_FILENAME
          value: ipf-demo-pipeline.yml, ipf-demo-pipeline-processors-config.yml, core-config.yml
        - name: SPRING_CONFIG_IDPS_KEY_ID
          value: v2-faf8a6f02b5a9
        - name: ASSET_ID
          value: 4110678153014047831
        - name: APP_ID
          value: Intuit.appintgwkflw.wkflautomate.waseventin
        - name: APP_SECRET
          value: '{secret}idps:/messageMesh/appSecret'
        - name: HOSTED_CONFIG_BRANCH
          value: dev
        - name: RUNTIME_ID
          value: idx-demo-runtime
      ports:
        - containerPort: 8443
          hostPort: 8443
        - containerPort: 8000
          hostPort: 8000
      volumeMounts:
        - mountPath: /app/config #mount path is the destination inside the Pod a volume gets mounted to
          name: ipf-execution-runtime-config
        - mountPath: /app/processors
          name: ipf-execution-runtime-processors
        - mountPath: /app/resources/keys/
          name: app-resources-keys
  volumes:
    #config
    - hostPath:
        path: /var/vmhost/was-event-in/config #specifies path in local repo where configs are placed
        type: Directory
      name: ipf-execution-runtime-config
    #processors
    - hostPath:
        path: /var/vmhost/was-event-in/processors
        type: Directory
      name: ipf-execution-runtime-processors
    #resources
    - hostPath:
        path: /var/vmhost/app/resources #this host path only works if your pem files are stored in the same dir as your repos under the path i.e. ${HOME}/{Path to local repos}/app/resources
        type: Directory
      name: app-resources-keys









































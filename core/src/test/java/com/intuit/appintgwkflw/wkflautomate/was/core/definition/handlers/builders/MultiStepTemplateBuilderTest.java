package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ProcessWorkflowStepFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepCompositeBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepConditionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiWorkflowStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class MultiStepTemplateBuilderTest {
    public static String RECORD_TYPE_INVOICE = "invoice";
    public static String APPROVAL_ACTION_KEY = "approval";
    public static String REMINDER_ACTION_KEY = "reminder";
    public static String INVALID_RECORD_TYPE = "invalid";
    public static String INVOICE_APPROVAL_PRECANNED = "invoiceapproval-multicondition";
    @MockBean WASContextHandler wasContextHandler;
    private ProcessWorkflowStepFactory processWorkflowStepFactory;
    @InjectMocks private MultiStepTemplateBuilder multiStepTemplateBuilder;
    private TemplateConditionBuilder templateConditionBuilder;
    private TemplateActionBuilder templateActionBuilder;
    private MultiWorkflowStepBuilder multiWorkflowStepBuilder;
    private MultiStepConditionBuilder multiStepConditionBuilder;
    private MultiStepActionBuilder multiStepActionBuilder;
    private MultiStepCompositeBuilder multiStepCompositeBuilder;
    private CustomWorkflowConfig customWorkflowConfig;
    @Mock private FeatureFlagManager featureFlagManager;
    @Mock private TemplateLabelsService templateLabelsService;
    @Spy private TranslationService translationService = TestHelper.initTranslationService();

    @Before
    @SneakyThrows
    public void setup() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        templateActionBuilder = new TemplateActionBuilder(wasContextHandler,translationService);
        templateConditionBuilder =
                new TemplateConditionBuilder(customWorkflowConfig, wasContextHandler,translationService, featureFlagManager);
        multiStepActionBuilder = new MultiStepActionBuilder(wasContextHandler, templateActionBuilder);
        multiStepConditionBuilder = new MultiStepConditionBuilder(wasContextHandler, templateConditionBuilder);
        multiStepCompositeBuilder = new MultiStepCompositeBuilder(multiStepConditionBuilder, multiStepActionBuilder);
        processWorkflowStepFactory = new ProcessWorkflowStepFactory(multiStepConditionBuilder, multiStepActionBuilder, multiStepCompositeBuilder);
        multiStepTemplateBuilder =
                new MultiStepTemplateBuilder(
                        templateConditionBuilder,
                        templateActionBuilder,
                        customWorkflowConfig,
                        wasContextHandler,
                        featureFlagManager,translationService, templateLabelsService, processWorkflowStepFactory);
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testTemplateBuild() {
        Template template = multiStepTemplateBuilder.build("QBO", RECORD_TYPE_INVOICE, APPROVAL_ACTION_KEY, false);
        Assert.assertNotNull(template);
        // assertions for workflowSteps
        Assert.assertNotNull(template.getWorkflowSteps());
        Assert.assertEquals(6, template.getWorkflowSteps().size());
        Assert.assertNotNull(template.getWorkflowSteps().get(0));
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getTrigger());
        Assert.assertEquals(StepTypeEnum.CONDITION, template.getWorkflowSteps().get(0).getStepType());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getWorkflowStepCondition());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getWorkflowStepCondition().getConditionalInputParameters());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getNext());
        Assert.assertEquals(2, template.getWorkflowSteps().get(0).getNext().size());
        // assertions for templateData
        Assert.assertNotNull(template.getTemplateData());
        Assert.assertNotNull(template.getTemplateData().getWorkflowStepCondition());
        Assert.assertNotNull(template.getTemplateData().getActionGroup());
        Assert.assertEquals(1, template.getTemplateData().getActionGroup().size());
        Assert.assertEquals(APPROVAL_ACTION_KEY, template.getTemplateData().getActionGroup().get(0).getActionKey());
        Assert.assertNotNull(template.getTemplateData().getActionGroup().get(0).getAction());
        Assert.assertNotNull(template.getTemplateData().getActionGroup().get(0).getAction().getSubActions());
        Assert.assertEquals(2, template.getTemplateData().getActionGroup().get(0).getAction().getSubActions().size());
        Assert.assertEquals("build_custom_workflow", template.getName());
        Assert.assertEquals(RECORD_TYPE_INVOICE, template.getRecordType());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testTemplateBuildWithNoActionKey() {
        try {
            multiStepTemplateBuilder.build("QBO", RECORD_TYPE_INVOICE, "", false);
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException error) {
            Assert.assertEquals(WorkflowError.STEPS_NOT_FOUND_IN_TEMPLATE_CONFIG, error.getWorkflowError());
            throw error;
        }
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testTemplateBuildWithInvalidActionGroup() {
        try {
            multiStepTemplateBuilder.build("QBO", RECORD_TYPE_INVOICE, "reminder", false);
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException error) {
            Assert.assertEquals(WorkflowError.STEPS_NOT_FOUND_IN_TEMPLATE_CONFIG, error.getWorkflowError());
            throw error;
        }
    }

    @Test
    public void testGetConfigTemplateByIdWithInvalidTemplateId() {
        Template template = multiStepTemplateBuilder.getConfigTemplateById(INVALID_RECORD_TYPE);
        Assert.assertNull(template);
    }

    @Test
    public void testGetConfigTemplateById() {
        Template template = multiStepTemplateBuilder.getConfigTemplateById(INVOICE_APPROVAL_PRECANNED);
        Assert.assertNotNull(template);
        Assert.assertEquals(template.getRecordType(), "Invoice");
        // assertions for workflowSteps
        Assert.assertNotNull(template.getWorkflowSteps());
        Assert.assertEquals(6, template.getWorkflowSteps().size());
        Assert.assertNotNull(template.getWorkflowSteps().get(0));
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getTrigger());
        Assert.assertEquals(StepTypeEnum.CONDITION, template.getWorkflowSteps().get(0).getStepType());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getWorkflowStepCondition());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getWorkflowStepCondition().getConditionalInputParameters());
        Assert.assertNotNull(template.getWorkflowSteps().get(0).getNext());
        Assert.assertEquals(2, template.getWorkflowSteps().get(0).getNext().size());
        // assertions for templateData
        Assert.assertNotNull(template.getTemplateData());
        Assert.assertNotNull(template.getTemplateData().getWorkflowStepCondition());
        Assert.assertNotNull(template.getTemplateData().getActionGroup());
        Assert.assertNotNull(template.getName());
        Assert.assertEquals(INVOICE_APPROVAL_PRECANNED, template.getName());
    }
}

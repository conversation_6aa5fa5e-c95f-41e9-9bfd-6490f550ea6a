package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
/**
 * This class is used to store the response after calling the appconnect custom reminder duzzits
 * (customWait or customStart)
 */
public class AppConnectFetchTransactionsResponse {
  private WorkflowTaskHandlerHeaders header;

  private String success;

  private String error;

  @JsonProperty("Output")
  private List<Map<String, String>> output;
}

package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * This enum is used as a registry for Downgrade/Cleanup Message Event 1. deleted_voided_disable :
 * For invoice Approval to maintain backward compatibility with the template 2. cleanup : For all
 * other and future templates
 */
@RequiredArgsConstructor
@Getter
public enum DeleteAllMessageEnum {
  DELETE_ALL_SIGNAL("deleted_voided_disable"),
  CLEANUP_SIGNAL("cleanup");
  private final String messageName;
}

package com.intuit.appintgwkflw.wkflautomate.was.connector.config;

/**
 * <AUTHOR>
 */
public class ConnectorConstants {
  public static final String VARIABLE_REGEX = "^[\\w.-]+\\[[\\w.-]+]$";
  public static final String KEY = "key";
  public static final String VALUE = "value";
  public static final String BRACKET_REGEX = "\\[";
  public static final String OPEN_BRACKET = "[";
  public static final String CLOSE_BRACKET = "]";
  public static final String INVALID_VALUE_ERROR = "INVALID_VARIABLE_VALUE";
  public static final String MULTIPLE_CONFIGS_ERROR = "Multiple configs exists";
  public static final String NO_CONFIG_PRESENT = "No config present";
}

package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum containing the information on what elements to be characterized as Trigger,Condition or
 * Actions.
 */
@AllArgsConstructor
@Getter
@ToString
public enum WorkflowStepTypeEnum {
  START_EVENT("startEvent", WorkflowConstants.TRIGGER),
  BOUNDARY_EVENT("boundaryEvent", WorkflowConstants.TRIGGER),
  RECEIVE_TASK("receiveTask", WorkflowConstants.TRIGGER),
  TIMER_EVENT("timerEvent", WorkflowConstants.TRIGGER),
  PARALLEL_GATEWAY("parallelGateway", WorkflowConstants.CONDITION),
  EXCLUSIVE_GATEWAY("exclusiveGateway", WorkflowConstants.CONDITION),
  BUSINESS_RULE_TASK("businessRuleTask", WorkflowConstants.CONDITION),
  SEND_TASK("sendTask", "action"),
  SERVICE_TASK("serviceTask", "action");

  private String elementName;
  private String workflowStepElementType;
  private static Map<String, String> elementTypeMap;

  public static String getType(String type) {
    return getElementType().get(type);
  }

  public static Map<String, String> getElementType() {
    if (elementTypeMap == null) {
      elementTypeMap = new HashMap<>();
      for (WorkflowStepTypeEnum s : values()) {
        elementTypeMap.put(s.elementName, s.workflowStepElementType);
      }
    }
    return elementTypeMap;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.common.helper;

import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.v4.Query;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.CompoundExpression;
import com.intuit.v4.query.FilterExpression;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import org.apache.commons.io.IOUtils;

public class TestHelper {

    public static QueryHelper mockQueryHelperWithFilterDefintionQuery() {
        Query query = new Query();
        Query.PreparedQuery filterQuery = new Query.PreparedQuery();
        filterQuery.type("/workflows/WorkflowStep");
        filterQuery.name("Not WorkflowStep");
        filterQuery.setProperties(0, "id");
        FilterExpression filterExpression = new FilterExpression();
        FilterExpression filterExpressionValue = new FilterExpression();
        filterExpression.add("op", "in");
        filterExpression.addArgs(filterExpressionValue);

        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        preparedQuery.setType("/workflows/Definition");
        preparedQuery.setName("definitions");
        preparedQuery.setSubQueries(Collections.singletonList(filterQuery));
        preparedQuery.setWhere(filterExpression);
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        return queryHelper;
    }

    public static QueryHelper mockQueryHelperWithoutFilterDefintionQuery() {
        Query query = new Query();
        Query.PreparedQuery filterQuery = new Query.PreparedQuery();
        filterQuery.type("/workflows/WorkflowStep");
        filterQuery.name("Not WorkflowStep");
        filterQuery.setProperties(0, "id");
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        preparedQuery.setType("/workflows/Definition");
        preparedQuery.setName("definitions");
        preparedQuery.setSubQueries(Collections.singletonList(filterQuery));
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        return queryHelper;
    }

    public static QueryHelper mockQueryHelperWithCompoundFilterDefinitionQuery() {
        Query query = new Query();
        Query.PreparedQuery filterQuery = new Query.PreparedQuery();
        filterQuery.type("/workflows/WorkflowStep");
        filterQuery.name("Not WorkflowStep");
        filterQuery.setProperties(0, "id");

        CompoundExpression compoundExpression = new CompoundExpression();

        FilterExpression filterExpression = new FilterExpression();
        FilterExpression filterExpressionValue = new FilterExpression();
        filterExpression.add("op", "in");
        filterExpression.addArgs(filterExpressionValue);

        compoundExpression.add("op", "&&");
        compoundExpression.addArgs(filterExpression);
        compoundExpression.addArgs(filterExpression);

        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        preparedQuery.setType("/workflows/Definition");
        preparedQuery.setName("definitions");
        preparedQuery.setSubQueries(Collections.singletonList(filterQuery));
        preparedQuery.setWhere(compoundExpression);
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        return queryHelper;
    }

    public static String readResourceAsString(String path) {
        try (InputStream stream = Resources.getResource(path).openStream()) {
            return IOUtils.toString(stream, Charsets.UTF_8);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

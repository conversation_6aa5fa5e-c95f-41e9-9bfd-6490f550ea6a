package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.StateTransitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Create Command is responsible to create entry of transaction and activity in DB to maintain
 * state.
 *
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WorkflowTaskCreateCommand extends WorkflowTaskCommand {

  private ProcessDetailsRepository processDetailRepo;

  private ActivityProgressDetailsRepository progressDetailRepo;

  private WorkflowTaskDBOperationManager taskDBOperationManager;

  public TaskCommand command() {
    return TaskCommand.CREATE;
  }

  @SuppressWarnings("rawtypes")
  @Override
  public WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest) {

    ProcessDetails processDetails = processDetailRepo
        .findById(taskRequest.getProcessInstanceId())
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR));

    Optional<ActivityProgressDetails> activityProgressDetailOptional = progressDetailRepo
        .findById(taskRequest.getId());

    Task task = prepareTaskRequest(taskRequest);
    WorkflowTask workflowTask = WorkflowTasks.getWorkflowTask(task.getType());
    WorkflowTaskResponse createResponse = null;
    if (activityProgressDetailOptional.isPresent()) {
      /**
       * If task is present in DB, there can be multiple scenarios to handle.
       * Lock Expiry.
       * FMEA Cases like status = OPEN or status = CREATED or status=FAILED.
       */
      createResponse = handleCreateOnRetry(taskRequest, activityProgressDetailOptional.get(), task,
          workflowTask);
    } else {
      /**
       * Normal handling of the request.
       */
      ActivityProgressDetails progressDetails = taskDBOperationManager
          .prepareActivityProgressAndSaveInOpenState(task, processDetails);
      createResponse = handleCreate(taskRequest, task, workflowTask, progressDetails);

    }
    // Publish event of task creation.
    taskRequest.setTxnId(createResponse.getTxnId());
    String eventType = InternalEventsUtil.commandToEventType(command());
    if (StateTransitionServiceHelper.isStateTransitionPublishEnabled(taskRequest, eventType)) {
      stateTransitionService.publishEvent(taskRequest, processDetails,eventType);
    }
    return createResponse;
  }

  /**
   * When already there's a record of activity present in DB, it will be a FMEA case to handle. 
   * For FMEA Scenario or Extend Lock expire, Db record is fetched and handled.
   * Case 1: If Record present in both table - prepare response, publish event and return.
   * Case 2:
   * If Record is not present in transaction table, Task is in "OPEN" or "FAILED" state and is being retried.
   * When Record is not present in transaction table, get and check from downstream.
   * If task available in downstream, save the same in DB table, publish event and return.
   * If task not available in downstream, make create call at downstream, save in DB, publish event and return.
   *
   * @param taskRequest     : Command Task Request
   * @param progressDetails : Activity runtime DB record.
   * @param task            : TaskRequest POJO
   * @param workflowTask    : Downstream Adaptor.
   * @return
   */
  @SuppressWarnings("rawtypes")
  private WorkflowTaskResponse handleCreateOnRetry(WorkflowTaskRequest taskRequest,
      ActivityProgressDetails progressDetails, Task task, WorkflowTask workflowTask) {
    WorkflowTaskResponse createResponse;
    /**
     * If transaction detail is present,
     * then it is a case of FetchNLock lock expiry or FMEA for EventPublish failure.
     * Other FMEA scenarios handled at processRequest for simplification.
     */
    if (null != progressDetails.getTxnDetails() && !taskRequest.isInvokeDownstreamOnRetry()) {
      taskRequest.setTxnId(progressDetails.getTxnDetails().getTxnId());
      if (ActivityConstants.TASK_STATUS_FAILED.equals(progressDetails.getStatus())) {
        /**
         * EventPublish failed at last retry.
         * Failed status to Created Status from Camunda Cockpit retry.
         */
        progressDetails = taskDBOperationManager
            .saveCreateStateInActivityProgressDB(progressDetails, taskRequest);
      }

      createResponse = WorkflowTaskResponse.builder()
          .status(progressDetails.getStatus())
          .txnId(progressDetails.getTxnDetails().getTxnId())
          .responseMap(responseMap(taskRequest, progressDetails.getTxnDetails().getTxnId()))
          .build();
    } else {
      createResponse = handleOpenTask(taskRequest, progressDetails, task, workflowTask);
    }
    return createResponse;
  }


  /**
   * For FMEA case, retry handling when status is OPEN.
   *
   * @param taskRequest     : Command Task Request
   * @param progressDetails : ActivityProgress record in DB.
   * @param task            : TaskRequest POJO
   * @param workflowTask    : Downstream Adaptor.
   * @return WorkflowTaskResponse - response having txnId.
   */
  @SuppressWarnings({"unchecked", "rawtypes"})
  private WorkflowTaskResponse handleOpenTask(WorkflowTaskRequest taskRequest,
      ActivityProgressDetails progressDetails, Task task, WorkflowTask workflowTask) {
    WorkflowTaskResponse createResponse;
    if (taskRequest.isSkipCallback()) {
      /**
       * For QBO case on skipcallback, only DB save is required.
       */
      createResponse = handleSkipCallbackCreate(taskRequest, progressDetails);
    } else {
      /**
       * For other cases,
       *  Make Get call from TaskAdaptor with resiliency.
       *  Check the txnId, 
       *  if present - save record in transaction DB and return
       *  if absent - make create call in downstream and save record in transaction DB.
       */
      WorkflowTaskResponse taskGetResponse = workflowTask.get(task);
      if (!StringUtils.isEmpty(taskGetResponse.getTxnId())) {
        taskDBOperationManager.saveCreateStateInDB(task, progressDetails, taskGetResponse);
        createResponse = taskGetResponse;
      } else {
        createResponse = handleCreate(taskRequest, task, workflowTask, progressDetails);
      }
    }
    return createResponse;
  }

  /**
   * Get ActivityProgressDetail from Optional instance or throw Exception.
   *
   * @param activityProgressDetailOptional
   * @return ActivityProgressDetail instance.
   */
  ActivityProgressDetails getActivityProgressDetail(
      Optional<ActivityProgressDetails> optionalActivityProgressDetail) {
    return optionalActivityProgressDetail.orElseThrow(() ->
        new WorkflowNonRetriableException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));
  }

  /**
   * Handles create call to downstream, updates txnId in Camunda and save status in DB.
   *
   * @param taskRequest     : Command Request POJO having skipCallback and txnId.
   * @param task            : task request pojo required downstream adaptor
   * @param workflowTask    : downstream adaptor.
   * @param progressDetails : ProgressDetail record from DB in Open.
   */
  @SuppressWarnings({"rawtypes", "unchecked"})
  private WorkflowTaskResponse handleCreate(WorkflowTaskRequest taskRequest, Task task,
      WorkflowTask workflowTask,
      ActivityProgressDetails progressDetails) {
    WorkflowTaskResponse createResponse = null;
    if (!taskRequest.isSkipCallback()) {
      // call adapter with resiliency - to be handled by Http Resiliency framework.
      createResponse = workflowTask.create(task);
      taskDBOperationManager.saveCreateStateInDB(task, progressDetails, createResponse);
    } else {
      createResponse = handleSkipCallbackCreate(taskRequest, progressDetails);
    }
    return createResponse;
  }

  /**
   * For QBO case, skipcallback will be true on transaction complete signal received. On
   * Skipcallback, only DB save is required.
   *
   * @param taskRequest     : Command Request POJO having skipCallback and txnId.
   * @param progressDetails : ProgressDetail record from DB in Open.
   * @return
   */
  private WorkflowTaskResponse handleSkipCallbackCreate(WorkflowTaskRequest taskRequest,
      ActivityProgressDetails progressDetails) {
    WorkflowTaskResponse createResponse;
    taskDBOperationManager.saveCreateStateInActivityProgressDB(progressDetails,
        taskRequest);
    createResponse = WorkflowTaskResponse.builder()
        .status(ActivityConstants.TASK_STATUS_CREATED)
        .txnId(taskRequest.getTxnId())
        .responseMap(responseMap(taskRequest, taskRequest.getTxnId()))
        .build();
    return createResponse;
  }

  public Map<String, Object> responseMap(WorkflowTaskRequest taskRequest, String txnId) {
    Map<String, Object> responseMap = new HashMap<>();
    responseMap.put(WorkflowTaskUtil.getTransactionVariable(taskRequest), txnId);
    return responseMap;
  }

}

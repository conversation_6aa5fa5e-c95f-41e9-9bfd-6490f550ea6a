[IDX - SPP - NONPCI - Pod Memory Utilization]
alert_metric_name = mcount(2m, ts("heapster.pod.memory.working_set", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd)) >= 2 and mcount(2m, ts("heapster.pod.memory.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd)) >= 2 and ts("heapster.pod.memory.working_set", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd) / ts("heapster.pod.memory.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd) * 100
severity = SEVERE
threshold = 80
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =
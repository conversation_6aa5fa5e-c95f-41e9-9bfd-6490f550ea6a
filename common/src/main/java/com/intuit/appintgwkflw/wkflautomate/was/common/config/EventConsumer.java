package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> Config class for EventConsumer. It reads all the properties from Spring config.
 */
@Getter
@Setter
public class EventConsumer {
  private boolean enabled;
  private boolean enableSecondary;
  private String groupId;
  private int concurrency;
  private String autoOffsetReset;
  private int maxPartitionFetchBytes;
  private int sessionTimeoutMs;
  private int fetchMinBytes;
  private int fetchMaxWaitMs;
  private int receiveBufferBytes;
  private int reconnectBackoffMs;
  private int retryBackoffMs;
  private int maxPollRecords;
  private int heartbeatIntervalMs;
  private ConsumerRetryConfig retryConfig;
  private Map<String, List<String>> entityTopicsMapping;
  private Map<String, String> config;
}

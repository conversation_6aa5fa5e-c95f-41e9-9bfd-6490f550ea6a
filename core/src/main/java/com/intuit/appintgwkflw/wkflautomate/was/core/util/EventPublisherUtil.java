package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class EventPublisherUtil {

  private final OfferingConfig offeringConfig;

  public String getOfferingId() {
    // Get offeringId from context
    Optional<String> offeringId = WASContext.getOfferingId();
    if (offeringId.isPresent()) return offeringId.get();

    return offeringConfig == null || offeringConfig.getDefaultOffering() == null
        ? StringUtils.EMPTY
        : offeringConfig.getDefaultOffering();
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_CONDITION_ID_DMN;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.SEND_FOR_APPROVAL_ACTION_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.getGlobalId;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

@Import(MultiStepValidator.class)
@RunWith(SpringRunner.class)
public class MultiStepValidatorTest {

  List<TriggerDetails> templateTriggerDetailsList;
  List<TriggerDetails> childTemplateTriggerDetailsList;
  @MockBean
  MultiStepConfig multiStepConfig;
  @InjectMocks
  private MultiStepValidator multiStepValidator;
  private List<WorkflowStep> workflowStepList;
  private Map<String, WorkflowStep> workflowStepMap;
  private Definition definition;

  @Before
  public void setUp() {
    workflowStepList = new ArrayList<>();
    workflowStepMap = new HashMap<>();
    definition = TestHelper.mockDefinitionEntity();
    templateTriggerDetailsList = new ArrayList<>();
    childTemplateTriggerDetailsList = new ArrayList<>();
    multiStepValidator = new MultiStepValidator(multiStepConfig);
    when(multiStepConfig.getMaxNumberOfSteps()).thenReturn(50);
  }

  private WorkflowStep getConditionalWorkflowStep(String workflowId, String yesPathId,
      String noPathId) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(workflowId));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(CUSTOM_WORKFLOW_WITH_CUSTOM_FIELD_ACTION)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression("GT 500")
                    .parameterName("TxnAmount").parameterType(FieldTypeEnum.STRING));
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition().id(getGlobalId(CUSTOM_WORKFLOW_CONDITION_ID_DMN))
            .ruleLine(ruleLineDmn)
            .description("description");
    workflowStep.workflowStepCondition(conditionDmn);
    workflowStep.setNext(Collections.emptyList());
    if (Objects.nonNull(yesPathId)) {
      WorkflowStep.StepNext stepNext = new WorkflowStep.StepNext();
      stepNext.label(NextLabelEnum.YES);
      stepNext.setWorkflowStepId(
          String.valueOf(new WorkflowStep().id(getGlobalId(yesPathId)).getId()));
      workflowStep.getNext().add(stepNext);
    }
    if (Objects.nonNull(noPathId)) {
      WorkflowStep.StepNext stepNext = new WorkflowStep.StepNext();
      stepNext.label(NextLabelEnum.NO);
      stepNext.setWorkflowStepId(
          String.valueOf(new WorkflowStep().id(getGlobalId(noPathId)).getId()));
      workflowStep.getNext().add(stepNext);
    }
    workflowStep.setStepType(StepTypeEnum.CONDITION);
    return workflowStep;
  }

  private WorkflowStep getActionWorkflowStep(String workflowId, String yesPathId, String noPathId) {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(workflowId));

    Action action =
        new Action()
            .id(getGlobalId(SEND_FOR_APPROVAL_ACTION_ID))
            .parameter(new InputParameter().parameterName("Assignee").fieldValue("111111"));

    ActionGroup actionGroup = new ActionGroup();
    actionGroup.setAction(action);
    workflowStep.setActionGroup(actionGroup);

    workflowStep.setNext(Collections.emptyList());
    if (Objects.nonNull(yesPathId)) {
      WorkflowStep.StepNext stepNext = new WorkflowStep.StepNext();
      stepNext.label(NextLabelEnum.YES);
      stepNext.setWorkflowStepId(String.valueOf(getGlobalId(yesPathId)));
      workflowStep.getNext().add(stepNext);
    }
    if (Objects.nonNull(noPathId)) {
      WorkflowStep.StepNext stepNext = new WorkflowStep.StepNext();
      stepNext.label(NextLabelEnum.NO);
      stepNext.setWorkflowStepId(String.valueOf(getGlobalId(noPathId)));
      workflowStep.getNext().add(stepNext);
    }
    workflowStep.setStepType(StepTypeEnum.ACTION);
    return workflowStep;
  }


  @Test()
  @DisplayName("Happy case for the multi condition invoice approval workflow.")
  public void testValidateMultiConditionPayload_Success() {

    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "sendForApproval-1", "decisionElement-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "decisionElement-2", "sendForApproval-2"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-2", "sendForApproval-3", "sendForApproval-4"));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-4", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    when(multiStepConfig.getMaxNumberOfApprovers())
        .thenReturn(6);

    assertDoesNotThrow(() -> multiStepValidator.validatePayload(definition, workflowStepMap));
  }


  @Test()
  @DisplayName("Validate if no condition step and more than one action steps in workflowStepList throws the correct exception.")
  public void testConditionalWorkflowStepsNotPresentWithMoreThanOneActionSteps() {

    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-4", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.CONDITION_WORKFLOW_STEP_NOT_FOUND.getErrorMessage());
  }

  @Test()
  @DisplayName("Validate if no condition step and single action step in workflowStepList does not throws exception.")
  public void testConditionalWorkflowStepsNotPresentWithSingleActionStep() {

    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    when(multiStepConfig.getMaxNumberOfApprovers())
        .thenReturn(6);

    assertDoesNotThrow(() -> multiStepValidator.validatePayload(definition, workflowStepMap));
  }

  @Test()
  @DisplayName("Throw error if the there are no workflow steps present.")
  public void testEmptyWorkflowSteps() {
    definition.setWorkflowSteps(workflowStepList);
    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.STEP_DETAILS_NOT_FOUND.getErrorMessage());
  }

  @Test()
  @DisplayName("Test case to validate if the number of approvers is more than 6, it throws the correct exception.")
  public void testTooManyApprovers() {

    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "decisionElement-1", "sendForApproval-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "decisionElement-2", "sendForApproval-2"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-2", "decisionElement-3", "sendForApproval-3"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-3", "decisionElement-4", "sendForApproval-4"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-4", "decisionElement-5", "sendForApproval-5"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-5", "sendForApproval-6", "sendForApproval-7"));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-4", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-5", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-6", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-7", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.APPROVAL_WORKFLOWS_APPROVERS_LIMIT_EXCEEDED.getErrorMessage());
  }

  @Test()
  @DisplayName("Test case to validate if the number of steps is more than the allowed limit(50), it throws the correct exception.")
  public void testTooManyWorkflowSteps() {

    int stepCount = 1;
    while(stepCount < 52) {
      workflowStepList.add(getActionWorkflowStep("sendForApproval-" + stepCount, null, null));
      stepCount++;
    }

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.APPROVAL_WORKFLOWS_STEPS_LIMIT_EXCEEDED.getErrorMessage());
  }

  @Test()
  @DisplayName("Test case to validate if a conditional workflow step has no next path, it throws the correct exception.")
  public void testMissingNextPathForConditionElementsError() {

    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "decisionElement-1", "sendForApproval-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "decisionElement-2", "sendForApproval-2"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-2", "decisionElement-3", "sendForApproval-3"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-3", "decisionElement-4", "sendForApproval-4"));
    workflowStepList.add(getConditionalWorkflowStep("decisionElement-4", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-4", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.MISSING_NEXT_PATH_ERROR.getErrorMessage());
  }

  @Test
  @DisplayName("Test case to check if an exception is thrown when a condition step has an invalid child workflowStep ID")
  public void testUnusedChildWorkflowStepForConditionBlock() {

    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "sendForApproval-1", "decisionElement-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "decisionElement-2", "sendForApproval-2"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-2", "sendForApproval-3", "sendForApproval-5"));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-4", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.UNUSED_WORKFLOW_STEP.getErrorMessage());
  }

  @Test
  @DisplayName("Test case to check if an exception is thrown when a action step has an invalid child workflowStep ID")
  public void testUnusedChildWorkflowStepForActionBlock() {

    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "sendForApproval-1", "decisionElement-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "sendForApproval-2", "sendForApproval-3"));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", "sendForApproval-4", null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.UNUSED_WORKFLOW_STEP.getErrorMessage());
  }

  @Test
  @DisplayName("Test case to check if an error is thrown when the workflow contains a cyclic dependency.")
  public void testCyclicWorkflowStepsNotAllowed() {
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement", "sendForApproval-1", "decisionElement-1"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-1", "decisionElement-2", "sendForApproval-2"));
    workflowStepList.add(
        getConditionalWorkflowStep("decisionElement-2", "sendForApproval-3", "decisionElement"));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-1", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-2", null, null));
    workflowStepList.add(getActionWorkflowStep("sendForApproval-3", null, null));

    definition.setWorkflowSteps(workflowStepList);
    workflowStepList.forEach(
        workflowStep -> workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validatePayload(definition, workflowStepMap),
        WorkflowError.CYCLIC_WORKFLOW_STEPS_NOT_ALLOWED.getErrorMessage());
  }

  @Test
  public void testIsMultiConditionPayloadValid_happyCase(){
    definition = TestHelper.mockMultiConditionDefinitionEntity();
    when(multiStepConfig.getMaxNumberOfApprovers()).thenReturn(6);
    Assert.assertTrue(multiStepValidator.validateMultiConditionPayload(definition));
  }

  @Test
  public void testIsMultiConditionPayloadValid_failsValidation(){
    definition = TestHelper.mockMultiConditionDefinitionEntityWithMoreThan6Approvers();
    when(multiStepConfig.getMaxNumberOfApprovers()).thenReturn(6);

    assertThrows(WorkflowGeneralException.class, () ->
                    multiStepValidator.validateMultiConditionPayload(definition),
            WorkflowError.APPROVAL_WORKFLOWS_APPROVERS_LIMIT_EXCEEDED.getErrorMessage());
  }

  @Test
  public void testMultiConditionCustomReminderWorkflow_failsValidation(){
    definition = TestHelper.mockMultiStepRecurringReminderDefinitionEntity();
    when(multiStepConfig.getMaxNumberOfApprovers()).thenReturn(0);

    assertThrows(WorkflowGeneralException.class, () ->
            multiStepValidator.validateMultiConditionPayload(definition),
        WorkflowError.COMPOSITE_WORKFLOW_STEP_NOT_LEAF_NODE.getErrorMessage());
  }
}
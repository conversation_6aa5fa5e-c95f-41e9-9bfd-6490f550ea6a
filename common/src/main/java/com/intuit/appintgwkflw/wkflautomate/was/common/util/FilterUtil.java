package com.intuit.appintgwkflw.wkflautomate.was.common.util;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.Expression;
import lombok.experimental.UtilityClass;

@UtilityClass
public class FilterUtil {

    /**
     * @param query : GraphQl Query with expression in filterBy as Input
     * @return : Output to the expression with filters
     */
    public static Expression getQueryFilterExpression(QueryHelper query) {
        return (Expression) query.getPreparedQuery().get(WorkflowConstants.FILTER_IDENTIFIER);
    }
}

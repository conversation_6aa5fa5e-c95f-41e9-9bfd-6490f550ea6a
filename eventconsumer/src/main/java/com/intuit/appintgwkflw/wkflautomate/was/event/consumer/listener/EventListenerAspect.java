package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener;

import static java.util.Objects.isNull;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.*;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.MaskedObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.KafkaReceivedEventHeader;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation.EventValidator;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.time.Instant;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 *     <p>It will auto fill the context info required for events and will auto acknowledge the event
 *     post processing.
 */
@Aspect
@Component
@AllArgsConstructor
public final class EventListenerAspect {

  @Autowired private WASContextHandler contextHandler;
  @Autowired private MetricLogger metricLogger;

  @SuppressWarnings("unchecked")
  @Around("@annotation(eventListener)")
  public Object event(final ProceedingJoinPoint joinPoint, final EventListener eventListener)
      throws Throwable {

    final Instant start = Instant.now();

    Acknowledgment acknowledgment = null;
    MessageHeaders messageHeaders = null;
    ConsumerRecord<String, Result<FormatException, String>> record = null;

    try {
      final Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
      final Object[] args = joinPoint.getArgs();

      final Parameter[] parameters = method.getParameters();
      if (parameters.length > 2) {
        if (ConsumerRecord.class.isAssignableFrom(parameters[0].getType())) {
          record = ((ConsumerRecord<String, Result<FormatException, String>>) args[0]);
        }
        if (MessageHeaders.class.isAssignableFrom(parameters[1].getType())) {
          messageHeaders = ((MessageHeaders) args[1]);
        }
        if (Acknowledgment.class.isAssignableFrom(parameters[2].getType())) {
          acknowledgment = ((Acknowledgment) args[2]);
        }
      }
      processEventHeaders(acknowledgment, record, messageHeaders);
      Object o = joinPoint.proceed();
      acknowledgment.acknowledge();
      return o;
    } catch (WorkflowRetriableException ex) {
      metricLogger.logErrorMetric(eventListener.name(), eventListener.type(), ex);
      EventingLoggerUtil.logWarning(
          "A retryable exception occurred while processing the event. Error=%s",
          this.getClass().getSimpleName(), ex);
      throw ex;
    } catch (WorkflowCircuitOpenException ex) {
      metricLogger.logErrorMetric(eventListener.name(), eventListener.type(), ex);
      EventingLoggerUtil.logWarning(
              ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Processing of event was skipped. Error=%s",
              this.getClass().getSimpleName(), ex);
      throw ex;
    } catch (Exception ex) {
      metricLogger.logErrorMetric(eventListener.name(), eventListener.type(), ex);
      if (record != null && record.value() != null) {
        boolean isWarning = metricLogger.getIsWarning(ex);
        if(isWarning){
          EventingLoggerUtil.logWarning(
                  "An exception occurred while processing the event. body=%s header=%s",
                  this.getClass().getSimpleName(),
                  ex,
                  record.value().get(),
                  ObjectConverter.toJson(EventUtil.transformHeader(messageHeaders)));
        }else{
          EventingLoggerUtil.logError(
                  "An exception occurred while processing the event. body=%s header=%s",
                  ex,
                  this.getClass().getSimpleName(),
                  record.value().get(),
                  ObjectConverter.toJson(EventUtil.transformHeader(messageHeaders)));
        }
      }
      acknowledgment.acknowledge();

      // Throwing runtime exceptions for DLQ's results in retries even if exception is
      // non-retriable
      if (EventUtil.isDLQ(messageHeaders.get(KafkaConstants.KAFKA_TOPIC_HEADER))) {
        WorkflowLogger.logWarn(ex, "A non retriable occured for in event in the DLQ");
        return null;
      }
      throw ex;

    } finally {
      metricLogger.logLatencyMetric(eventListener.name(), eventListener.type(), start);
      contextHandler.clear();
    }
  }

  /**
   * It validate the event params,check and log failure,log Event details,populate context info and
   * validate headers.
   *
   * @param acknowledgment {@link Acknowledgment}
   * @param record {@link ConsumerRecord}
   * @param messageHeaders {@link MessageHeaders}
   */
  private void processEventHeaders(
      Acknowledgment acknowledgment,
      ConsumerRecord<String, Result<FormatException, String>> record,
      MessageHeaders messageHeaders) {

    validateParams(acknowledgment, record, messageHeaders);
    checkAndLogFailure(record);
    Map<String, String> headers = EventUtil.transformHeader(messageHeaders);
    EventUtil.populateEventContext(contextHandler, headers);
    logEvent(headers, record);
    EventValidator.validate(headers);
    setOfferingId(headers);
  }

  private void setOfferingId(Map<String, String> headers) {
    String offeringId =
        EventUtil.convertHeaderToString(headers.get(EventHeaderConstants.OFFERING_ID));
    WASContext.setOfferingId(offeringId);
  }

  /**
   * validate the params and throw exception if null
   *
   * @param acknowledgment {@link Acknowledgment}
   * @param record {@link ConsumerRecord}
   * @param messageHeaders {@link MessageHeaders}
   */
  private void validateParams(
      Acknowledgment acknowledgment,
      ConsumerRecord<String, Result<FormatException, String>> record,
      MessageHeaders messageHeaders) {
    WorkflowVerfiy.verify(isNull(record), WorkflowError.INPUT_INVALID, "consumerRecord");
    WorkflowVerfiy.verify(isNull(messageHeaders), WorkflowError.INPUT_INVALID, "messageHeaders");
    WorkflowVerfiy.verify(isNull(acknowledgment), WorkflowError.INPUT_INVALID, "acknowledgment");
  }

  /**
   * throw exception in case of failure.
   *
   * @param record consumer record
   */
  private void checkAndLogFailure(ConsumerRecord<String, Result<FormatException, String>> record) {
    if (record.value().isFailure()) {
      EventingLoggerUtil.logError(
          WorkflowError.INCORRECT_EVENT_PAYLOAD.getErrorMessage(),
          this.getClass().getSimpleName(),
          record.value().getFailure());
      throw new WorkflowGeneralException(WorkflowError.INCORRECT_EVENT_PAYLOAD);
    }
  }

  // Log header
  private void logEvent(
      Map<String, String> headers,
      ConsumerRecord<String, Result<FormatException, String>> record) {
    KafkaReceivedEventHeader kafkaReceivedEventHeader = ObjectConverter.convertObject(headers, KafkaReceivedEventHeader.class);
    String maskedHeaders = MaskedObjectConverter.toJson(kafkaReceivedEventHeader);
    EventingLoggerUtil.logInfo(
        "Received event with headers=%s timeStamp=%s timeStampType=%s",
        this.getClass().getSimpleName(), maskedHeaders, record.timestamp(), record.timestampType());
  }
}

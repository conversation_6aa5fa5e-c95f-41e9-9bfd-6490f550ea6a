package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiStepPlaceholderSubstitutor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepDefinitionActionBuilderTest {
    private MultiStepDefinitionActionBuilder multiStepDefinitionActionBuilder;
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    private TemplateConditionBuilder templateConditionBuilder;
    private TemplateActionBuilder templateActionBuilder;
    private TemplateTriggerBuilder templateTriggerBuilder;
    private CustomWorkflowConfig customWorkflowConfig;
    private MultiStepPlaceholderSubstitutor multiStepPlaceholderSubstitutor;
    @Mock
    private WASContextHandler wasContextHandler;
    @Mock
    private TranslationService translationService;

    @Mock
    private FeatureFlagManager featureFlagManager;

    @Before
    @SneakyThrows
    public void setup() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        templateConditionBuilder = new TemplateConditionBuilder(
                customWorkflowConfig,
                wasContextHandler,
                translationService, featureFlagManager);
        templateActionBuilder = new TemplateActionBuilder(
                wasContextHandler,
                translationService);
        templateTriggerBuilder = new TemplateTriggerBuilder(wasContextHandler);
        multiStepPlaceholderSubstitutor = new MultiStepPlaceholderSubstitutor(customWorkflowConfig);
        readCustomDefinitionHandler = new ReadCustomDefinitionHandler(
                templateConditionBuilder,
                templateActionBuilder,
                templateTriggerBuilder,
                customWorkflowConfig);
        multiStepDefinitionActionBuilder = new MultiStepDefinitionActionBuilder(
                readCustomDefinitionHandler,
                multiStepPlaceholderSubstitutor);
        Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("34343433341111133333");
    }

    @Test
    public void testProcessWorkflowStep() {
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
        List<WorkflowStep> workflowSteps = new ArrayList<>();
        Template template = new Template();
        template.setName("customApproval");
        template.setRecordType("invoice");
        MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
                .workflowSteps(workflowSteps)
                .activityInstance(activityInstance)
                .template(template)
                .actionIdToStepIdMap(actionIdtoStepIdMap)
                .compositeStepIdToWorkflowStepConditionMap(new HashMap<>())
                .globalId(null)
                .build();

        multiStepDefinitionActionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
        Assert.assertEquals(1, workflowSteps.size());
        Assert.assertNotNull(workflowSteps.get(0).getActionGroup());
        Assert.assertEquals("approval", workflowSteps.get(0).getActionGroup().getActionKey());
        Assert.assertEquals("Send For Approval", workflowSteps.get(0).getActionGroup().getAction().getName());
        Assert.assertEquals(1, workflowSteps.get(0).getActionGroup().getAction().getParameters().size());
        Assert.assertNotNull(workflowSteps.get(0).getActionGroup().getAction().getParameters().get(0).getFieldValues());
        Assert.assertEquals("9130359273350856", workflowSteps.get(0).getActionGroup().getAction().getParameters().get(0).getFieldValues().get(0));
        Assert.assertEquals(true, workflowSteps.get(0).getActionGroup().getAction().get("selected"));

        Assert.assertNotNull(workflowSteps.get(0).getActionGroup().getAction().getSubActions());
        Assert.assertEquals(2, workflowSteps.get(0).getActionGroup().getAction().getSubActions().size());

        Assert.assertEquals("Create a task", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(0).getName());
        Assert.assertEquals(2, workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(0).getParameters().size());
        Assert.assertEquals("9130359273349816", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(0).getParameters().get(0).getFieldValues().get(0));
        Assert.assertEquals("Approval due for Invoice [[Invoice Number]]", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(0).getParameters().get(1).getFieldValues().get(0));

        Assert.assertEquals("Send a company email", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(1).getName());
        Assert.assertEquals(5, workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(1).getParameters().size());
        Assert.assertEquals("[[Company Email]]", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(1).getParameters().get(3).getFieldValues().get(0));
        Assert.assertEquals("Review Invoice [[Invoice Number]]", workflowSteps.get(0).getActionGroup().getAction().getSubActions().get(1).getParameters().get(4).getFieldValues().get(0));

        Assert.assertNotNull(workflowSteps.get(0).getNext());
        Assert.assertEquals(0, workflowSteps.get(0).getNext().size());
    }

    @Test
    public void testProcessWorkflowStepForCompositeStepType() {
        ActivityInstance activityInstance = TestHelper.mockActivityInstance();
        Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
        GlobalId id = GlobalId.builder()
            .setRealmId("test")
            .setLocalId("sendForReminder")
            .setTypeId("test")
            .build();
        List<WorkflowStep> workflowSteps = new ArrayList<>();
        Template template = new Template();
        template.setName("customReminder");
        template.setRecordType("invoice");

        Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap = new HashMap<>();
        compositeStepIdToWorkflowStepConditionMap.put(id, new WorkflowStepCondition());
        MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
            .workflowSteps(workflowSteps)
            .activityInstance(activityInstance)
            .template(template)
            .actionIdToStepIdMap(actionIdtoStepIdMap)
            .compositeStepIdToWorkflowStepConditionMap(compositeStepIdToWorkflowStepConditionMap)
            .globalId(id)
            .build();

        multiStepDefinitionActionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
        Assert.assertEquals(1, workflowSteps.size());
        Assert.assertEquals(StepTypeEnum.WORFKLOWSTEP, workflowSteps.get(0).getStepType());
        Assert.assertNotNull(workflowSteps.get(0).getWorkflowStepCondition());
        Assert.assertNotNull(workflowSteps.get(0).getActionGroup());
    }

    @Test
    public void testProcessWorkflowStepWithNullActivityInstance() {
        ActivityInstance activityInstance = null;
        Map<String, GlobalId> actionIdtoStepIdMap = new HashMap<>();
        List<WorkflowStep> workflowSteps = new ArrayList<>();
        Template template = new Template();
        MultiStepWorkflowEntity multiStepWorkflowEntity = MultiStepWorkflowEntity.builder()
                .workflowSteps(workflowSteps)
                .activityInstance(activityInstance)
                .template(template)
                .actionIdToStepIdMap(actionIdtoStepIdMap)
                .globalId(null)
                .build();
        multiStepDefinitionActionBuilder.buildWorkflowStep(multiStepWorkflowEntity);
        Assert.assertEquals(0, workflowSteps.size());
    }
}

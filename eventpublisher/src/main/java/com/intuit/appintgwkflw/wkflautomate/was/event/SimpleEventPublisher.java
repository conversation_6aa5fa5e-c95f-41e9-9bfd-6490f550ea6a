package com.intuit.appintgwkflw.wkflautomate.was.event;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants.KAFKA_HEADERS_OVERRIDE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.SendResult;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.util.concurrent.ListenableFuture;

/** <AUTHOR> Event publisher for testing event consumption and publishing to DLQ */
@Component
@RequiredArgsConstructor
public class SimpleEventPublisher {
  private final ApplicationContext applicationContext;

  /**
   * Publishes a message with the given headers and body. This method transforms lower case HTTP
   * Headers to the case used by WAS Kafka Headers.
   *
   * @param body
   * @param headers
   */
  public void send(String body, Map<String, String> headers) {
    KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);

    try {
      MessageHeaderAccessor accessor = getHeaders(headers);

      Message<String> message = MessageBuilder.withPayload(body).setHeaders(accessor).build();
      ListenableFuture<SendResult<String, String>> resp = kafkaTemplate.send(message);
      RecordMetadata metaData = resp.get().getRecordMetadata();
      Long timeStamp = metaData.timestamp();

      WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
          .message("Message has been published for metaData=%s timeStamp=%s", metaData, timeStamp)
          .downstreamComponentName(DownstreamComponentName.WAS)
          .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR));
    } catch (Exception e) {
      // If Kafka is down we get EventBusConnectionException which is a SocketTimeoutException
      // InterruptedException and ExecutionException are thrown by Listenablefuture.get()
      // Catching all the Kafka publish related exceptions and throwing RetriableException to kick in retry mechanism
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Message failed to publish for topic=%s",
                      headers.get(KafkaHeaders.TOPIC))
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .stackTrace(e)
                  .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR));
      throw new WorkflowEventException(
          new WorkflowRetriableException(WorkflowError.KAFKA_PUBLISH_ERROR, e));
    }
  }

  /**
   * Publishes a message with the given headers and body to the specified topic. Removes any Kafka
   * specific headers if present
   *
   * @param record
   * @param headers
   * @param topicName
   */
  public void send(String record, Map<String, String> headers, String topicName) {
    KafkaTemplate<String, String> kafkaTemplate = applicationContext.getBean(KafkaTemplate.class);
    try {
      Message<String> message =
          MessageBuilder.withPayload(record)
              .copyHeadersIfAbsent(headers)
              .removeHeaders("kafka_") // removes the default headers added by kafka
              .setHeader(KafkaHeaders.TOPIC, topicName)
              .build();

      ListenableFuture<SendResult<String, String>> response = kafkaTemplate.send(message);
      String metaData = response.get().getRecordMetadata().toString();
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Message has been published for metaData=%s", metaData)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR));
    } catch (Exception e) {
      // If Kafka is down we get EventBusConnectionException which is a SocketTimeoutException
      // InterruptedException and ExecutionException are thrown by Listenablefuture.get()
      // Catching all the Kafka publish related exceptions and throwing RetriableException to kick in retry mechanism
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Message failed to publish for topic=%s", topicName)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .stackTrace(e)
                  .downstreamServiceName(DownstreamServiceName.WAS_EVENT_PROCESSOR));
      throw new WorkflowEventException(
          new WorkflowRetriableException(WorkflowError.KAFKA_PUBLISH_ERROR, e));
    }
  }

  /**
   * Converts HTTP headers to Kafka headers. Changes the case of headers
   *
   * @param headers
   * @return
   */
  private MessageHeaderAccessor getHeaders(Map<String, String> headers) {
    MessageHeaderAccessor accessor = new MessageHeaderAccessor();
    accessor.setHeader(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString().getBytes());
    accessor.setHeader(
        EventHeaderConstants.IDEMPOTENCY_KEY, UUID.randomUUID().toString().getBytes());
    addHeader(accessor, EventHeaderConstants.ENTITY_ID, headers);
    addHeader(accessor, EventHeaderConstants.OWNER_ID, headers);
    addHeader(accessor, EventHeaderConstants.OFFERING_ID, headers);
    addHeader(accessor, KafkaHeaders.TOPIC, headers);
    addHeader(accessor, EventHeaderConstants.DOMAIN_EVENT, headers);
    addHeader(accessor, EventHeaderConstants.INTUIT_USER_ID, headers);
    addOverrideHeaders(accessor, headers);
    WorkflowLogger.logInfo(
        "headers in the events are %s ",
        accessor
            .toMap()
            .entrySet()
            .stream()
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining(",")));
    return accessor;
  }


  private void addHeader(
      MessageHeaderAccessor accessor, String header, Map<String, String> headers) {

    String value = headers.get(header.toLowerCase());
    if (StringUtils.isNotBlank(value)) {
      accessor.setHeader(header, value);
    }
  }

  /**
   * Parses kafka_headers_override http header key and splits the string into kv map and adds them as
   * kafka headers to the message
   * Example input
   * kafka_headers_override: ss=3d9d53d9-9e1d-4469-9fac-82deb06a1ad3,idempotenceKey=6ea1c1db-8c9b-4a3d-94be-1a3384d3c345,entityVersion=1.0.0,actorId=SYSTEM,targetAssetAlias=intuit-workflows/vep-int-project-service,entityId=d44b524a-ebb7-11ea-b673-822e74e83abe:BANL160c98318.locale021655e-476f-4be1-a571-1664c3671406,eventType=DOMAIN_EVENT,offeringId=vep,idempotenceKey=47fe0bec-a129-424b-8585-bf85d0049dcd,publishingAssetAlias=Intuit.appintgwkflw.wkflautomate.wf
   */
  private void addOverrideHeaders(MessageHeaderAccessor accessor, Map<String, String> headers) {

    String overrideKafkaHeaderString = headers.getOrDefault(KAFKA_HEADERS_OVERRIDE, StringUtils.EMPTY);

    try {
      Map<String, String> overrideKafkaHeaders = parseKvMapString(overrideKafkaHeaderString,
          WorkflowConstants.COMMA, WorkflowConstants.EQUAL);
      overrideKafkaHeaders.forEach(accessor::setHeader);
    } catch (Exception e) {
      EventingLoggerUtil.logWarning(
          "kafka_headers_override value parsing failed for value=%s", overrideKafkaHeaderString, e);
      // throw/handle failure error https://jira.intuit.com/browse/QBOES-9217
    }
  }

  static Map<String, String> parseKvMapString(String value, String kvPairDelimiter, String kvDelimiter) {
    return Arrays.stream(value.split(kvPairDelimiter))
        .map(kvString -> kvString.split(kvDelimiter))
        .filter(kvArray -> kvArray.length == 2)
        .filter(kvArray -> StringUtils.isNoneBlank(kvArray[0], kvArray[1]))
        .collect(Collectors.toMap(kvArray -> kvArray[0], kvArray -> kvArray[1]));
  }
}

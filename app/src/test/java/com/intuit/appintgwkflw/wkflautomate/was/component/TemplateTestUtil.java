package com.intuit.appintgwkflw.wkflautomate.was.component;


import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;

@Slf4j
@Component
public class TemplateTestUtil {

    private static final String CUSTOM_REMINDER_BPMN = "src/test/resources/bpmn/customReminder.bpmn";

    private static final String CUSTOM_REMINDER_DMN = "src/test/resources/dmn/decision_customReminder.dmn";

    public static String createCustomTemplate(WebClient webClient) {

        try {
            String templateMetadata = "{\n" +
                    "  \"status\": \"enabled\",\n" +
                    "  \"creatorType\": \"system\",\n" +
                    "  \"allowMultipleDefs\": true,\n" +
                    "  \"templateCategory\": \"CUSTOM\",\n" +
                    "  \"definitionType\": \"SINGLE\"\n" +
                    "}";

            MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();
            formData.add("template_metadata", templateMetadata);
            formData.add("files", new FileSystemResource(CUSTOM_REMINDER_BPMN));
            formData.add("files", new FileSystemResource(CUSTOM_REMINDER_DMN));

            return webClient.post()
                    .uri("/v1/template/save")
                    .contentType(MediaType.MULTIPART_FORM_DATA)
                    .body(BodyInserters.fromMultipartData(formData))
                    .header("intuit_tid", "save-template")
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();

        } catch (final Exception e) {
            WorkflowLogger.error(
                    () ->
                            WorkflowLoggerRequest.builder()
                                    .message(WorkflowError.TEMPLATE_SAVE_EXCEPTION.getErrorDescription())
                                    .stackTrace(e)
                                    .downstreamComponentName(DownstreamComponentName.WAS_DB)
                                    .downstreamServiceName(DownstreamServiceName.TEMPLATE_DETAILS));
        }

        return null;
    }

}
package com.intuit.appintgwkflw.wkflautomate.was.event.numaflow.async;

import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> This class creates worker thread pool for NumaFlow.
 */
@Configuration
@Getter
@Setter
@ConfigurationProperties(prefix = "numaflow.thread-pool")
@ConditionalOnProperty(name = "numaflow.handler", havingValue = "was-sink")
public class NumaFlowWorkerThreadPool {
  private int minThreads;
  private int maxThreads;
  private int keepAliveTimeInSec;
  private int queueSize;

  @Bean(name = WorkflowConstants.NUMA_EXECUTOR_THREAD_BEAN)
  public ThreadPoolExecutor numaThreadPool() {
    return ThreadPoolExecutorFactory.createExecutor(
        WorkflowConstants.NUMA_EXECUTOR_THREAD,
        queueSize,
        minThreads,
        maxThreads,
        keepAliveTimeInSec);

  }
}

[IPF - was-event-in - QAL - Kafka Producer Error]
alert_metric_name = rawsum(align(60s, sum, ratediff(ts("iks.intuit.dataexchange.pipelineframework.kafka.publish.exit.duration.seconds.count" and flow.status="ERROR" and  namespace=appintgwkflw-wkflautomate-waseventin-usw2-qal))))
severity = SEVERE
threshold = 0
operator = >
alert_target_id = <EMAIL>
wait_before_alert = 3
additional_info =
custom_tags = idx.pipeline.framework


package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.oinp.OINPEventRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;

public class OINPHttpClientTest {

  @Mock
  WASHttpClient wasHttpClient;
  @InjectMocks
  private OINPHttpClient client;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testOINPHttpClient(){

    WASHttpRequest<OINPEventRequest, Object> wasHttpRequest =
        WASHttpRequest.<OINPEventRequest, Object>builder()
            .httpMethod(HttpMethod.POST)
            .request(OINPEventRequest.builder().build())
            .responseType(new ParameterizedTypeReference<Object>() {})
            .url("https://oinp.intuit.com")
            .build();
    WASHttpResponse mockResponse = Mockito.mock(WASHttpResponse.class);
    Mockito.when(mockResponse.isSuccess2xx()).thenReturn(true);
    Mockito.when(wasHttpClient.httpResponse(wasHttpRequest)).thenReturn(mockResponse);
    WASHttpResponse<Object> response = wasHttpClient.httpResponse(wasHttpRequest);
    Assert.assertTrue(response.isSuccess2xx());
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.helper;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ParameterDetailsConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.FilterExpression;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ObjectUtils;

public class TestHelper {
  public static final String REALM_ID = "realm";
  public static final String TEMPLATE_ID = "template-id";
  public static final String DEFINITION_ID = "definition-id";
  public static final String WORKFLOW_STEP_ID = "step-id";
  public static final String TRIGGER_ID = "waitForTimerToElapse1_invoiceApproval_companyId_uuid";
  public static final String ACTION_ID = "sendApprovalEmail_invoiceApproval_companyId_uuid";
  public static final String CONDITION_ID_XOR =
      "evaluateUserDefinedAction_invoiceApproval_companyId_uuid";
  public static final String CONDITION_ID_DMN =
      "invoiceApprovalDecision_invoiceApproval_companyId_uuid";
  public static final String MAPPED_KEY_DMN = "approvalRequired";
  public static final String MAPPED_KEY_XOR = "sendReminderEmail_invoiceApproval_companyId_uuid";
  public static final String DEFINITION_NAME = "defintion-1";
  public static final String TEMPLATE_NAME = "template-1";
  public static final String DEF_ID = "def_id";

  public static Definition mockDefinitionEntity() {
    WorkflowStep workflowStep =
        new WorkflowStep().id(getGlobalId(DefinitionTestConstants.WORKFLOW_STEP_ID));
    Trigger trigger =
        new Trigger()
            .id(getGlobalId(DefinitionTestConstants.TRIGGER_ID))
            .parameter(
                new InputParameter()
                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
        new Action()
            .id(getGlobalId(DefinitionTestConstants.ACTION_ID))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression(">500")
                    .parameterName("amountEvaluation"));
    RuleLine ruleLineXOR =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_XOR)
            .rule(new RuleLine.Rule().conditionalExpression("true"));
    WorkflowStepCondition conditionXOR =
        new WorkflowStepCondition()
            .id(getGlobalId(DefinitionTestConstants.CONDITION_ID_XOR))
            .ruleLine(ruleLineXOR);
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn);
    workflowStep =
        workflowStep.workflowStepCondition(conditionXOR).workflowStepCondition(conditionDmn);
    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID)))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(workflowStep);
  }

  public static Authorization mockAuthorization(String realmId) {
    Authorization authorization = new Authorization();
    authorization.putRealm(realmId);
    authorization.putAuthId("453");
    return authorization;
  }

  public static GlobalId getGlobalId(String localId) {
    return GlobalId.builder()
        .setRealmId(DefinitionTestConstants.REALM_ID)
        .setLocalId(localId)
        .build();
  }

  public static DefinitionDetails mockDefinitionDetails(
      Definition definition, TemplateDetails bpmnTemplateDetail, Authorization authorization) {
    return DefinitionDetails.builder()
        .definitionId("def-id")
        .templateDetails(bpmnTemplateDetail)
        .ownerId(Long.valueOf(authorization.getRealm()))
        .workflowId("workFlowId")
        .build();
  }

  public static DefinitionDetails mockWithDifferentDefinitionDetails(
      TemplateDetails bpmnTemplateDetail, Authorization authorization) {
    return DefinitionDetails.builder()
        .definitionId("1234")
        .templateDetails(bpmnTemplateDetail)
        .ownerId(Long.valueOf(authorization.getRealm()))
        .workflowId("workFlowId")
        .build();
  }

  public static Template mockTemplateEntity() {
    WorkflowStep workflowStep = new WorkflowStep().id(getGlobalId(WORKFLOW_STEP_ID));
    Trigger trigger =
        new Trigger()
            .id(getGlobalId(TRIGGER_ID))
            .parameter(
                new InputParameter()
                    .parameterName(ParameterDetailsConstants.WAIT_TIME.getValue())
                    .fieldValue("10"));
    workflowStep.setTrigger(trigger);
    Action action =
        new Action()
            .id(getGlobalId(ACTION_ID))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));
    workflowStep = workflowStep.action(new WorkflowStep.ActionMapper().action(action));
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression(">500")
                    .parameterName("amountEvaluation"));
    RuleLine ruleLineXOR =
        new RuleLine()
            .mappedActionKey(MAPPED_KEY_XOR)
            .rule(new RuleLine.Rule().conditionalExpression("true"));
    WorkflowStepCondition conditionXOR =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_XOR)).ruleLine(ruleLineXOR);
    WorkflowStepCondition conditionDmn =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn);
    workflowStep =
        workflowStep.workflowStepCondition(conditionXOR).workflowStepCondition(conditionDmn);
    return new Template()
        .name(TEMPLATE_NAME)
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowStep(workflowStep);
  }

  public static QueryHelper mockQueryHelperWithWorkflowStepsForDefintionQuery() {
    Query query = new Query();
    Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();
    workflowStepsQuery.type("/workflows/WorkflowStep");
    workflowStepsQuery.name("WorkflowSteps");
    workflowStepsQuery.setProperties(0, "id");
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Definition");
    preparedQuery.setName("definitions");
    preparedQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static QueryHelper mockQueryHelperWithoutWorkflowStepsForDefintionQuery() {
    Query query = new Query();
    Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();
    workflowStepsQuery.type("/workflows/WorkflowStep");
    workflowStepsQuery.name("Not WorkflowStep");
    workflowStepsQuery.setProperties(0, "id");
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Definition");
    preparedQuery.setName("definitions");
    preparedQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static QueryHelper mockQueryHelperWithWorkflowStepsForTemplateQuery() {
    Query query = new Query();
    Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();
    workflowStepsQuery.type("/workflows/workflowStep");
    workflowStepsQuery.name("workflowSteps");
    workflowStepsQuery.setProperties(0, "id");
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Template");
    preparedQuery.setName("templates");
    preparedQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static QueryHelper mockQueryHelperWithoutWorkflowStepsForTemplateQuery() {
    Query query = new Query();
    Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();
    workflowStepsQuery.type("/workflows/WorkflowStep");
    workflowStepsQuery.name("Not WorkflowStep");
    workflowStepsQuery.setProperties(0, "id");
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Template");
    preparedQuery.setName("templates");
    preparedQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static QueryHelper mockQueryHelperWithTemplateDataQuery() {
    Query query = new Query();
    List<Query.PreparedQuery> queryList = new ArrayList<>();
    Query.PreparedQuery workflowStepsQuery = new Query.PreparedQuery();
    Query.PreparedQuery templateDataQuery = new Query.PreparedQuery();
    templateDataQuery.setType("/workflows/templateData");
    templateDataQuery.setName("templateData");
    queryList.add(templateDataQuery);
    workflowStepsQuery.type("/workflows/WorkflowStep");
    workflowStepsQuery.name("WorkflowStep");
    workflowStepsQuery.setProperties(0, "id");
    queryList.add(workflowStepsQuery);
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Template");
    preparedQuery.setName("templates");
    preparedQuery.setSubQueries(queryList);
    query.setPreparedQuery(preparedQuery);
    templateDataQuery.setSubQueries(Collections.singletonList(workflowStepsQuery));
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static Definition mockDefinitionEntityWithoutWorkflowSteps(
      WorkflowStatusEnum workflowStatusEnum) {
    {
      return new Definition()
          .name(DefinitionTestConstants.DEFINITION_NAME)
          .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID)))
          .recordType(RecordType.INVOICE.getRecordType())
          .status(workflowStatusEnum);
    }
  }

  public static QueryHelper mockQueryHelperWithArgs() {
    Query query = new Query();
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Template");
    preparedQuery.setName("templates");
    Map<String, Object> by = new HashMap<>();
    Map<String, String> argsByValue = new HashMap<>();
    argsByValue.put("recordType", "Invoice");
    argsByValue.put("source", "QBO");
    by.put("by", argsByValue);
    preparedQuery.setArgs(by);
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static DefinitionDetails mockDefinitionDetailsObject(
      Long realmId, String definitionId, Status status, String definitionName) {
    return DefinitionDetails.builder()
        .ownerId(realmId)
        .definitionId(definitionId) // Definition id is different
        .status(status)
        .modelType(ModelType.BPMN)
        .definitionName(definitionName)
        .internalStatus(null)
        .build();
  }

  public static Template mockConfigTemplateEntity(String id, TemplateCategory templateCategory) {
    Template template = new Template();
    template.setDisplayName("Template DisplayName");
    template.setName(id);
    template.setId(GlobalId.create(REALM_ID, template.getTypeId(), id));
    template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
    return template;
  }

  public static Template mockDbTemplateEntity(String id, TemplateCategory templateCategory) {
    Template template = new Template();
    template.setDisplayName("Template DisplayName");
    template.setName(id);
    template.setCategory(TemplateCategory.valueOf(templateCategory.name()).name());
    template.setId(GlobalId.create(REALM_ID, template.getTypeId(), "1234"));
    return template;
  }

  public static QueryHelper mockQueryHelper(String... args) {
    Query query = new Query();
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    query.setPreparedQuery(preparedQuery);
    Query.PreparedQuery filterQuery = new Query.PreparedQuery();
    FilterExpression filterExpression = new FilterExpression();
    filterExpression.setProperty("category");
    // Adding Filters
    for (String s : args) {
      filterExpression.addArgs(s);
    }
    preparedQuery.setSubQueries(Collections.singletonList(filterQuery));
    preparedQuery.setWhere(filterExpression);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static Definition mockSingleStepMultiConditionDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");
    workflowSteps.add(firstActionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static Definition mockMultiConditionDefinitionEntity() {
    List<WorkflowStep> workflowSteps = new ArrayList<>();

    WorkflowStep firstActionStep = createMultiActionStep("actionStep-1");

    WorkflowStep secondActionStep = createMultiActionStep("actionStep-2");

    WorkflowStep fourthConditionStep = createMultiConditionStep("condition-4",
        secondActionStep.getId().toString(), firstActionStep.getId().toString(),
        "> 500","TxnAmount" );

    WorkflowStep thirdConditionStep = createMultiConditionStep("condition-3",
        fourthConditionStep.getId().toString(), null, "CONTAINS ALL_Customer",
        "Customer");

    WorkflowStep secondConditionStep = createMultiConditionStep("condition-2",
        null, thirdConditionStep.getId().toString(), "BTW 100,1000",
        "TxnAmount");

    WorkflowStep firstConditionStep = createMultiConditionStep("condition-1",
        secondConditionStep.getId().toString(), firstActionStep.getId().toString(), "> 500",
        "TxnAmount");

    workflowSteps.add(firstConditionStep);
    workflowSteps.add(firstActionStep);
    workflowSteps.add(secondActionStep);
    workflowSteps.add(secondConditionStep);
    workflowSteps.add(thirdConditionStep);
    workflowSteps.add(fourthConditionStep);

    return new Definition()
        .name(DefinitionTestConstants.DEFINITION_NAME)
        .template(new Template().id(getGlobalId(DefinitionTestConstants.TEMPLATE_ID))
            .name(CustomWorkflowType.APPROVAL.getTemplateName()))
        .recordType(RecordType.INVOICE.getRecordType())
        .workflowSteps(workflowSteps);
  }

  public static WorkflowStep createMultiActionStep(String stepId) {
    Action action =
        new Action()
            .id(getGlobalId("sendForApproval"))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));

    Action createTaskSubAction =
        new Action()
            .id(getGlobalId("createTask"))
            .parameter(new InputParameter().parameterName("To").fieldValue("ADMIN_TEST"));

    List<Action> subActions = new ArrayList<>();
    subActions.add(createTaskSubAction);
    action.setSubActions(subActions);

    ActionGroup actionGroup = new ActionGroup().actionKey("approval")
        .action(action);

    WorkflowStep actionStep =
        new WorkflowStep().id(getGlobalId(stepId))
            .actionGroup(actionGroup)
            .stepType(StepTypeEnum.ACTION);

    return actionStep;
  }

  public static WorkflowStep createMultiConditionStep(String stepId, String yesPathId,
      String noPathId, String conditionalExpression, String parameterName) {
    RuleLine ruleLineDmn =
        new RuleLine()
            .mappedActionKey(DefinitionTestConstants.MAPPED_KEY_DMN)
            .rule(
                new RuleLine.Rule()
                    .conditionalExpression(conditionalExpression)
                    .parameterName(parameterName));

    WorkflowStepCondition workflowStepCondition =
        new WorkflowStepCondition().id(getGlobalId(CONDITION_ID_DMN)).ruleLine(ruleLineDmn);

    List<WorkflowStep.StepNext> stepNexts = new ArrayList<>();

    if(ObjectUtils.isNotEmpty(yesPathId)) {

      WorkflowStep.StepNext yesPath = new WorkflowStep.StepNext()
          .workflowStepId(yesPathId)
          .label(NextLabelEnum.YES);

      stepNexts.add(yesPath);
    }

    if(ObjectUtils.isNotEmpty(noPathId)) {

      WorkflowStep.StepNext noPath = new WorkflowStep.StepNext()
          .workflowStepId(noPathId)
          .label(NextLabelEnum.NO);

      stepNexts.add(noPath);
    }

    WorkflowStep conditionStep =
        new WorkflowStep().id(getGlobalId(stepId))
            .workflowStepCondition(workflowStepCondition)
            .stepType(StepTypeEnum.CONDITION)
            .next(stepNexts);

    return conditionStep;
  }
}

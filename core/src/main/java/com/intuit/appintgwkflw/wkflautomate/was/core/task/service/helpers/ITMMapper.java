package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.helpers;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_REFERENCES_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_REFERENCES_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_COMPLETE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_STATUS_IN_PROGRESS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_TASK_BLOB_METADATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ITMConstants.ITM_TASK_EXTERNAL_BLOB_METADATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_ID;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.DateUtils.DateFormat;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.itm.entity.graphql.type.TaskManagement_CreateTaskInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceInput;
import com.intuit.itm.entity.graphql.type.TaskManagement_ReferenceType;
import com.intuit.itm.entity.graphql.type.TaskManagement_UpdateTaskInput;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONObject;

/** Maps different task requests to Project Service request pojo */
@UtilityClass
public class ITMMapper {

  public static final Map<String, String> statusMap = new HashMap<>();

  private Set<String> workflowVariables = new HashSet<>();

  static {
    for (WorkFlowVariables variable : WorkFlowVariables.values()) {
      workflowVariables.add(variable.getName());
    }
    statusMap.put(ActivityConstants.TASK_STATUS_CREATED, ITM_STATUS_IN_PROGRESS);
    statusMap.put(ActivityConstants.TASK_STATUS_COMPLETE, ITM_STATUS_COMPLETE);
  }

  /**
   * Converts the humanTask pojo to the ITM create task pojo
   *
   * @param humanTask HumanTask request
   * @return TaskManagement_CreateTaskInput
   */
  public TaskManagement_CreateTaskInput createITMCreateTaskInput(HumanTask humanTask) {

    return TaskManagement_CreateTaskInput.builder()
        .name(humanTask.getTaskName())
        .description(humanTask.getDescription())
        .status(getMappedStatus(humanTask))
        .dueDate(getDueDateFromHumanRequest(humanTask))
        .priority(1)
        .type(humanTask.getTaskType())
        .assignee(humanTask.getAssigneeId())
        .idempotenceId(!StringUtils.isBlank(humanTask.getId()) ? humanTask.getId() : null)
        .metadata(getMetadata(humanTask))
        .references(getExternalReferences(humanTask))
        .build();
  }

  /**
   * Converts the humanTask pojo to the ITM update task pojo
   *
   * @param humanTask HumanTask request
   * @return TaskManagement_UpdateTaskInput
   */
  public TaskManagement_UpdateTaskInput createITMUpdateTaskInput(HumanTask humanTask) {

    TaskManagement_UpdateTaskInput.Builder taskManagementUpdateTaskInputBuilder =
        TaskManagement_UpdateTaskInput.builder().id(humanTask.getTxnId());

    if (Objects.nonNull(humanTask.getTaskName())
        && !humanTask.getTaskName().equals(humanTask.getActivityName())) {
      taskManagementUpdateTaskInputBuilder.name(humanTask.getTaskName());
    }

    if (Objects.nonNull(humanTask.getDescription())) {
      taskManagementUpdateTaskInputBuilder.description(humanTask.getDescription());
    }

    if (Objects.nonNull(getDueDateFromHumanRequest(humanTask))) {
      taskManagementUpdateTaskInputBuilder.dueDate(getDueDateFromHumanRequest(humanTask));
    }

    if (Objects.nonNull(humanTask.getAssigneeId())) {
      taskManagementUpdateTaskInputBuilder.assignee(humanTask.getAssigneeId());
    }

    if (Objects.nonNull(humanTask.getStatus())) {
      taskManagementUpdateTaskInputBuilder.status(humanTask.getStatus());
    }

    return taskManagementUpdateTaskInputBuilder.build();
  }

  /**
   * Gets the due date for the ITM task from the human task pojo
   *
   * @param humanTask HumanTask request
   * @return ITM due date for the task
   */
  private DateTime getDueDateFromHumanRequest(HumanTask humanTask) {
    return !StringUtils.isBlank(humanTask.getDueDate())
        ? DateUtils.getDateWithFormat(humanTask.getDueDate(), DateFormat.YYYY_MM_DD)
        : null;
  }

  /**
   * Gets the ITM status for the ITM task from the human task pojo
   *
   * @param humanTask HumanTask request
   * @return ITM status for the task
   */
  private static String getMappedStatus(HumanTask humanTask) {
    return statusMap.getOrDefault(humanTask.getStatus(), humanTask.getStatus());
  }

  /**
   * Gets the metadata for a human task. The metadata would in the format as needed by the ITM team
   * (https://docs.google.com/document/d/1g9kpUXsFO0JKDVgCQo3D6JQxu9pdqCfEhUCzNxHL6_s/edit#heading=h.o5rheew8b0sn)
   *
   * <p>The metadata would contain two separate values : 
   * 1. wasReferences - for WAS specific variables 
   * 2. additionalTaskAttributes - additional values defined in the BPMN for the human task
   *
   * @param humanTask HumanTask request
   * @return list of TaskManagement_ReferenceInput
   */
  private static String getMetadata(HumanTask humanTask) {

    Map<String, Object> wasReferencesMap = new HashMap<>();
    wasReferencesMap.put(PROCESS_ID, humanTask.getProcessInstanceId());
    wasReferencesMap.put(ActivityConstants.EXTERNAL_TASK_ID, humanTask.getId());
    wasReferencesMap.put(ENTITY_ID, humanTask.getRecordId());

    JSONObject wasReferencesJSONObject =
        new JSONObject(
            Map.of(
                ActivityConstants.MANDATORY_EXTERNAL_REFERENCES,
                getITMTaskBlobMetadata(ObjectConverter.toJson(wasReferencesMap))));

    JSONObject additionalTaskAttributesJSONObject =
        new JSONObject(
            Map.of(
                ActivityConstants.ADDITIONAL_TASK_ATTRIBUTES,
                getITMTaskBlobMetadata(
                    ObjectConverter.toJson(getAdditionalAttributesForHumanTask(humanTask)))));

    JSONObject metadataJsonObject =
        new JSONObject(
            Map.of(
                ITM_TASK_EXTERNAL_BLOB_METADATA,
                new JSONArray(
                    List.of(wasReferencesJSONObject, additionalTaskAttributesJSONObject))));

    return metadataJsonObject.toString();
  }

  private JSONArray getITMTaskBlobMetadata(String blobMetadata) {
    return new JSONArray(List.of(new JSONObject(Map.of(ITM_TASK_BLOB_METADATA, blobMetadata))));
  }

  private Map<String, Object> getAdditionalAttributesForHumanTask(HumanTask humanTask) {
    TaskAttributes taskAttributes = humanTask.getTaskAttributes();
    if (MapUtils.isEmpty(taskAttributes.getVariables())) {
      return Collections.emptyMap();
    }
    Map<String, Object> taskMap =
        ObjectConverter.convertObject(humanTask, new TypeReference<>() {});

    Set<String> attributesKeySet = new HashSet<>(taskAttributes.getModelAttributes().keySet());
    attributesKeySet.addAll(taskAttributes.getRuntimeAttributes().keySet());
    attributesKeySet.removeAll(workflowVariables);
    attributesKeySet.removeAll(taskMap.keySet());

    /**
     * Fill additionalAttributes keys with values from taskAttribute.variables and
     * taskAttribute.modelAttributes
     */
    return attributesKeySet.stream()
        .collect(
            Collectors.toMap(
                attributeKey -> attributeKey,
                attributeKey ->
                    taskAttributes
                        .getVariables()
                        .getOrDefault(
                            attributeKey, taskAttributes.getModelAttributes().get(attributeKey))));
  }

  /**
   * Gets all the required external references for a human task
   *
   * @param humanTask HumanTask request
   * @return list of TaskManagement_ReferenceInput
   */
  private static List<TaskManagement_ReferenceInput> getExternalReferences(HumanTask humanTask) {
    List<TaskManagement_ReferenceInput> references = new ArrayList<>();
    if (Objects.nonNull(humanTask.getReferences()) && ObjectUtils.isNotEmpty(humanTask.getReferences())) {
      for (String referenceString : humanTask.getReferences()) {
        Map<String, String> referenceMap = ObjectConverter.fromJson(referenceString, Map.class);
        if (Objects.nonNull(referenceMap)
            && Objects.nonNull(referenceMap.get(ITM_REFERENCES_TYPE))
            && Objects.nonNull(referenceMap.get(ITM_REFERENCES_ID))
            && !TaskManagement_ReferenceType.safeValueOf(referenceMap.get(ITM_REFERENCES_TYPE))
                .equals(TaskManagement_ReferenceType.$UNKNOWN)) {
          references.add(
              TaskManagement_ReferenceInput.builder()
                  .referenceType(
                      TaskManagement_ReferenceType.safeValueOf(
                          referenceMap.get(ITM_REFERENCES_TYPE)))
                  .referenceId(referenceMap.get(ITM_REFERENCES_ID))
                  .build());
        }
      }
    }
    return references;
  }
}

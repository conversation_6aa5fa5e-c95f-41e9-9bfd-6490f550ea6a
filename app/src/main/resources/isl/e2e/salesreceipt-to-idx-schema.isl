fun transform() {
   $realmId : $headers | select( `entityId-accountId`)
    $t = {

         headers: {
        	...$headers,  // spread existing headers into the new headers
       	    entity_namespace: $eventNamespace,
        	entity_type: $eventName,
        	intuit_tid: $headers.intuitTid,
        	intuit_realmid: $realmId,
        	entity_version: $eventVersion,
        	provider_id: `was_$eventName` | lowerCase,
        	connection_id: "was-connectionId",
        	intuitUserId: if ($payload.meta.updatedByUser.id) $payload.meta.updatedByUser.id endif
    	},

    	payload: {
        	data: {
            	  sourceData: $payload,
       			  ownerId: $realmId,
       			  entityId: $headers | select( `entityId-entityId`),
        	      entityType: "SalesReceipt",
       	          offeringId: "default",
       	          entityData: @.This.getEntityData( $payload ),
        		}
   		 },

        transform: {
        	status: "VALID",
        	failureReasons: if ( !$realmId ) "Missing realmId due to which event cannot not be processed." endif | to.array
    	}
   }

    $triggerActions: []

    $workflowTriggerActionsNotification = @.This.getNotificationWorkflowActions( $payload , $headers )

    $triggerActions: if ( $workflowTriggerActionsNotification.entityChangeType ) $triggerActions|push( $workflowTriggerActionsNotification ) else $triggerActions endif

    $t.payload.data.triggerActions = $triggerActions
    @.Log.Info(`Transformation result $t`)
    return $t;

}

fun getEntityData( $payload ) {
  $t: {
    TxnId: $payload.id,
    TxnAmount: $payload.totalAmount.value,
    Customer: $payload.customerId,
    TxnPaymentStatus: 'PAID', #need to confirmation over this
    TxnDate: $payload.transactionDate,
    CustomerEmail: $payload.email.to[0].email,
    LastUpdatedDate: $payload.meta.updated|date.parse( "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'" )|to.string("yyyy-MM-dd"),
    DocNumber: $payload.referenceNumber,
    VendorName: $payload.vendor,
  }
  return $t
}

fun getNotificationWorkflowActions( $payload , $headers ) {
   $t : {}
   $t.workflowName = "notification"
   $t.targetApi = "EVALUATE_AND_TRIGGER_V2"
   $t.entityChangeType = @.This.getNotificationEntityChangeType($payload , $headers)
   $t.skip = @.This.skipEvent( $payload , $headers , $t.entityChangeType)
   return $t
}

fun skipEvent( $payload , $headers, $entityChangeType ) {
   if(!$entityChangeType)
      return true
   endif
   // Remove duplicate events coming from STS
   if($payload.meta and $payload.meta.createdByApp and $payload.meta.createdByApp.id and $payload.meta.createdByApp.id == '2740457965556624036')
      return true
   endif
   return false
}

fun getNotificationEntityChangeType($payload , $headers) {
  if($headers.entityChangeAction == "CREATE")
  	 return "Create"
  endif
  if ($headers.entityChangeAction == "UPDATE")
     return "Update"
  endif
  return null
}
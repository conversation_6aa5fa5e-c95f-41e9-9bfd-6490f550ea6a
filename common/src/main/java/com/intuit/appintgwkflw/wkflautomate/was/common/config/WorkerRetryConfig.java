package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration related to Worker Retry.
 * - defaultRetryCount : The default retry count for all external tasks
 * - defaultBackOffStepSize : The default back off time for any retry
 * - maximumBackOff : The maximum backoff time allowed for retry
 * - externalTaskConfig : Array of retry config for a particular external task. Default values are used in case not present.
 *
 * Example:
 * worker-retry:
 *   defaultRetryCount: 3
 *   defaultBackOffStepSize: 15000
 *   externalTaskConfig:
 *     - workflowName: sendForApprovalTest
 *       externalTaskName: createTask
 *       retryCount: 5
 *       backOffStepSize: 2000
 *     - workflowName: sendForApprovalTest
 *       externalTaskName: sendCompanyEmail
 *       retryCount: 5
 *       backOffStepSize: 2000
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "worker-retry")
public class WorkerRetryConfig {
    private Integer defaultRetryCount;
    private Long defaultBackOffStepSize;
    private Long maximumBackOff;
    private Integer exponentialBackoffMultiplier;
    private Integer defaultRandomStepSize;

    private List<ExternalTaskRetryConfig> externalTaskConfig;
}

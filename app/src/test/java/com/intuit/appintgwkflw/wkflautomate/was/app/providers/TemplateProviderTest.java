package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.helper.TestHelper;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TemplateProviderTest {
  @Mock private TemplateService templateService;

  @Mock private ProviderHelper providerHelper;

  @Mock private AuthDetailsService authDetailsService;

  @Mock private TemplateProvider templateProviderMock;

  @Mock private WASContextHandler wasContextHandler;

  @InjectMocks private TemplateProvider templateProvider;

  @InjectMocks private WASBaseProvider wasBaseProvider = new TemplateProvider(templateService);

  private Template template;
  private Authorization authorization;
  private List<Template> templateList;
  private QueryHelper queryHelper;

  private static final String REALM_ID = "991919191";

  @Before
  public void prepareMockData() {
    MockitoAnnotations.initMocks(this);
    template = TestHelper.mockTemplateEntity();
    authorization = TestHelper.mockAuthorization(TestHelper.REALM_ID);
    templateList = Collections.singletonList(template);
    queryHelper = TestHelper.mockQueryHelperWithWorkflowStepsForTemplateQuery();
  }

  @Test
  public void wasReadOneTemplateTest() throws IOException {
    String id = "123";
    GlobalId globalId = GlobalId.builder().setLocalId(id).build();
    Template template = Mockito.mock(Template.class);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithTemplateDataQuery();
    Mockito.when(templateService.fetchTemplate(eq(id), eq(globalId), eq(true))).thenReturn(template);
    SingleResult<Template> templateResult = wasBaseProvider.wasReadOne(context, globalId, queryHelper);
    Assert.assertEquals(template, templateResult.getResult());
  }

  @Test
  public void wasReadOneTemplateTestFail() throws IOException {
    String id = "123";
    GlobalId globalId = GlobalId.builder().setLocalId(id).build();
    Template template = Mockito.mock(Template.class);
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithTemplateDataQuery();
    Mockito.when(templateService.fetchTemplate(eq(id), eq(globalId), eq(true))).thenThrow(IOException.class);
    SingleResult result = wasBaseProvider.wasReadOne(context, globalId, queryHelper);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getResult());
    Assert.assertNotNull(result.getError());
    Assert.assertEquals("PROVIDER_PARSING_EXCEPTION", result.getError().getMessage());
  }

  @Test
  public void wasReadListTestWithoutWorkflowSteps() throws IOException {
    Template template = TestHelper.mockTemplateEntity();

    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithoutWorkflowStepsForTemplateQuery();
    Mockito.when(templateService.readAllTemplates(context.getAuthorization(), queryHelper))
        .thenReturn(new ListResult<>(Collections.singletonList(template)));
    ListResult<Template> templateResult = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertEquals(Collections.singletonList(template), templateResult.getResult());
  }

  @Test(expected = IOException.class)
  public void wasReadListTestWithWorkflowStepsFail() throws IOException {

    template.setId(TestHelper.getGlobalId("123"));
    RequestContext context = Mockito.mock(RequestContext.class);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithWorkflowStepsForTemplateQuery();
    Mockito.when(providerHelper.checkForWorkflowSteps(queryHelper)).thenReturn(true);
    Mockito.when(templateService.fetchAllTemplatesWithWorkflowSteps(queryHelper))
        .thenThrow(IOException.class);
    boolean isCheck = providerHelper.checkForWorkflowSteps(queryHelper);
    Assert.assertNotNull(isCheck);
    Assert.assertTrue(isCheck);
    templateService.fetchAllTemplatesWithWorkflowSteps(queryHelper);
    wasBaseProvider.wasReadList(context, queryHelper);
  }

  @Test
  public void testWasWriteOne() {
    RequestContext context = Mockito.mock(RequestContext.class);
    context.setAuthorization(authorization);
    SingleResult<Template> result = templateProvider.wasWriteOne(context, null);
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertNotNull(result.getError().getMessage());
    Assert.assertEquals("Write Method Not Supported.", result.getError().getMessage());
  }

  @Test
  public void wasReadTemplateMetadataTest() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Authorization auth = new Authorization();
    auth.putRealm("991919191");
    Mockito.when(context.getAuthorization()).thenReturn(auth);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithArgs();
    ListResult<Template> result = templateProvider.readTemplateMetadata(context, queryHelper);
    Assert.assertEquals(1, result.getResult().size());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void wasReadTemplateMetadataTestFail() {
    RequestContext context = Mockito.mock(RequestContext.class);
    Authorization auth = new Authorization();
    auth.putRealm("991919191");
    context.setAuthorization(auth);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithArgs();
    Mockito.when(templateProviderMock.readTemplateMetadata(context, queryHelper))
        .thenThrow(WorkflowGeneralException.class);
    templateProviderMock.readTemplateMetadata(context, queryHelper);
  }

  @Test
  public void wasReadListTestFail() throws IOException {
    RequestContext context = Mockito.mock(RequestContext.class);
    Authorization auth = new Authorization();
    auth.putRealm(REALM_ID);
    Mockito.when(context.getAuthorization()).thenReturn(auth);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithArgs();
    Mockito.doThrow(new IOException()).when(templateService).readAllTemplates(any(), any());
    ListResult<Error> result = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals("PROVIDER_PARSING_EXCEPTION", result.getError().getMessage());
  }

  @Test
  public void testReadAllConfigTemplateNoFilter() throws IOException {
    List<Template> dbTemplates = new ArrayList<>();
    List<Template> preCannedTemplates = new ArrayList<>();
    prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);

    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithoutWorkflowStepsForTemplateQuery();
    Mockito.when(templateService.readAllTemplates(context.getAuthorization(), queryHelper))
        .thenReturn(new ListResult<>(dbTemplates));

    ListResult<Template> templateResult = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(templateResult);
    Assert.assertEquals(2, templateResult.getResult().size());
  }

  @Test
  public void testReadAllConfigTemplateFilteringHubAndCustom() throws IOException {
    List<Template> dbTemplates = new ArrayList<>();
    List<Template> preCannedTemplates = new ArrayList<>();
    prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);
    List<Template> results = new ArrayList<>(preCannedTemplates);
    results.add(TestHelper.mockDbTemplateEntity("invoiceapproval", TemplateCategory.HUB));

    QueryHelper queryHelper = TestHelper.mockQueryHelper("HUB", "CUSTOM");

    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.when(templateService.readAllTemplates(context.getAuthorization(), queryHelper))
        .thenReturn(new ListResult<>(dbTemplates));

    ListResult<Template> templateResult = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(templateResult);
    Assert.assertEquals(2, templateResult.getResult().size());
  }

  @Test
  public void testReadAllConfigTemplateFilteringHubOnly() throws IOException {
    List<Template> dbTemplates = new ArrayList<>();
    List<Template> preCannedTemplates = new ArrayList<>();
    prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);

    QueryHelper queryHelper = TestHelper.mockQueryHelper("HUB");

    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);

    Mockito.when(templateService.readAllTemplates(context.getAuthorization(), queryHelper))
        .thenReturn(new ListResult<>(dbTemplates));

    ListResult<Template> templateResult = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(templateResult);
    Assert.assertEquals(2, templateResult.getResult().size());
  }

  @Test
  public void testReadAllConfigTemplateFilteringCustomOnly() throws IOException {
    List<Template> dbTemplates = new ArrayList<>();
    List<Template> preCannedTemplates = new ArrayList<>();
    prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);

    QueryHelper queryHelper = TestHelper.mockQueryHelper("CUSTOM");
    RequestContext context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);

    Mockito.when(templateService.readAllTemplates(context.getAuthorization(), queryHelper))
        .thenReturn(new ListResult<>(dbTemplates));

    ListResult<Template> templateResult = wasBaseProvider.wasReadList(context, queryHelper);
    Assert.assertNotNull(templateResult);
    Assert.assertEquals(2, templateResult.getResult().size());
  }

  @Test
  public void readOneTemplate_nonRealm(){
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    GlobalId id = Mockito.mock(GlobalId.class);
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.doNothing().when(wasContextHandler).addKey(any(), any());
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    SingleResult<Template> result = null;
    try {
      result = templateProvider.readOne(requestContext, id, queryHelper);
    }
    finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void readListTemplate_nonRealm(){
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    requestContext
        .getCustomHeaders()
        .put(
            WorkflowConstants.AUTHORIZATION_HEADER,
            "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=9130347714346286");
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.doNothing().when(wasContextHandler).addKey(any(), any());
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    ListResult<Template> result = null;
    try {
      result = templateProvider.readList(requestContext, queryHelper);
    }
    finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  @Test
  public void readTemplateMetadata_nonRealm(){
    RequestContext requestContext = Mockito.mock(RequestContext.class);
    Mockito.when(requestContext.getAuthorization()).thenReturn(authorization);
    WASContext.setNonRealmSystemUserContext(true);
    QueryHelper queryHelper = TestHelper.mockQueryHelperWithArgs();
    ListResult<Template> result = null;
    try {
      result = templateProvider.readTemplateMetadata(requestContext, queryHelper);
    }
    finally {
      WASContext.clear();
    }
    Assert.assertNotNull(result);
    Assert.assertNotNull(result.getError());
    Assert.assertEquals(WorkflowError.INVALID_REALM_ID.name(), result.getError().getMessage());
  }

  // Helper method to prepare mock data
  private void prepareMockDataForReadAllTemplates(
      List<Template> dbTemplates, List<Template> preCannedTemplates) {
    Template template =
        TestHelper.mockDbTemplateEntity("unsentInvoiceReminder", TemplateCategory.HUB);
    dbTemplates.add(template);
    template = TestHelper.mockDbTemplateEntity("invoiceapproval", TemplateCategory.HUB);
    dbTemplates.add(template);

    Template template1 =
        TestHelper.mockConfigTemplateEntity("unsentInvoiceReminder", TemplateCategory.CUSTOM);
    preCannedTemplates.add(template1);
    template1 =
        TestHelper.mockConfigTemplateEntity("invoiceoverduereminder", TemplateCategory.CUSTOM);
    preCannedTemplates.add(template1);
  }
}

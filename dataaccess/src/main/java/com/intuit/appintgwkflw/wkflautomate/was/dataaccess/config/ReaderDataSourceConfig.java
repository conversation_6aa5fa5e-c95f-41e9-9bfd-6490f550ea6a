package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "spring.reader")
public class ReaderDataSourceConfig {

    private boolean enabled;

    private String url;

}

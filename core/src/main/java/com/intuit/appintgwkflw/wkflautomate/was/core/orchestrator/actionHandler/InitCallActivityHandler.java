package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.INVALID_ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ON_DEMAND_APPROVAL;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Class used to populate sequence variables for the child process.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class InitCallActivityHandler extends WorkflowTaskHandler {

  private final ProcessDetailsRepoService processDetailsRepoService;
  private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  private final MultiStepConfig multiStepConfig;
  private final CustomWorkflowConfig customWorkflowConfig;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_INIT_CALL_ACTIVITY;
  }

  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {

    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    State inputState = new State();
    WorkflowVerfiy.verifyNull(workerActionRequest.getInputVariables(),
        WorkflowError.INVALID_PROCESS_DETAILS);

//    In case of on demand approval, fetching permissible subTask from config
    if(Boolean.TRUE.toString().equalsIgnoreCase(workerActionRequest.getInputVariables().getOrDefault(ON_DEMAND_APPROVAL, "false"))){
      String entityType = Optional.ofNullable(workerActionRequest.getInputVariables().get(ENTITY_TYPE)).map(type -> RecordType.fromTypeOrValue(type)).map(
          RecordType::getRecordType).orElseThrow(() -> new WorkflowNonRetriableException(INVALID_ENTITY_TYPE));
      String actionKey = CustomWorkflowType.APPROVAL.getActionKey();
      List<String> actionIds = CustomWorkflowUtil.getOnDemandConfiguredActions(customWorkflowConfig,entityType,actionKey);
      return actionIds.stream().collect(Collectors.toMap(actionId -> actionId, actionId -> true));
    }

    String rootActivityId = workerActionRequest.getInputVariables()
        .get(WorkflowConstants.ACTIVITY_ID);
    WorkflowVerfiy.verifyNull(rootActivityId, WorkflowError.INVALID_ACTIVITY_ID);
    inputState.addValue(WorkflowConstants.ACTIVITY_ID, rootActivityId);

    String rootProcessInstanceId = workerActionRequest.fetchParentProcessInstanceId();
    WorkflowVerfiy.verifyNull(rootProcessInstanceId,
        WorkflowError.INVALID_ROOT_PROCESS_INSTANCE_ID);
    inputState.addValue(WorkflowConstants.ROOT_PROCESS_INSTANCE_ID, rootProcessInstanceId);

    WorkflowLogger.logInfo(
        "step=WorkflowCustomTaskHandlerExecution status=Started externalTaskId=%s rootActivityId=%s rootProcessInstanceId=%s",
        workerActionRequest.getTaskId(),
        rootActivityId, rootProcessInstanceId);

    RxExecutionChain rxExecutionChain = new RxExecutionChain(inputState);
    State outputState = rxExecutionChain
        .next(new ActivitySequenceVariableExtractor(processDetailsRepoService,
            definitionActivityDetailsRepository, multiStepConfig))
        .execute();

    Map<String, Object> sequenceVariableMap = outputState.getValue(
        WorkflowConstants.ACTIVITY_MAPPED_SEQUENCE_VARIABLE_MAP);
    WorkflowLogger.logInfo(
        "step=WorkflowCustomTaskHandlerExecution status=Completed externalTaskId=%s rootActivityId=%s rootProcessInstanceId=%s sequenceVariableMap=%s",
        workerActionRequest.getTaskId(),
        rootActivityId, rootProcessInstanceId, sequenceVariableMap);

    return sequenceVariableMap;
  }

  @Override
  protected void logErrorMetric(Exception exception, WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.CALL_ACTIVITY_HANDLER, Type.APPLICATION_METRIC,
        exception);
  }


}

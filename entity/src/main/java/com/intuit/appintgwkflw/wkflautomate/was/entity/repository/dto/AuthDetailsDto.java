package com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class AuthDetailsDto {

    private String authDetailsId;

    private String subscriptionId;

    private Long createdByUserId;

    private Long ownerId;

    private String offlineTicket;

    private Timestamp expiryDate;

    private Timestamp createdDate;

    private Timestamp modifiedDate;

    private String migrationStatus;
}

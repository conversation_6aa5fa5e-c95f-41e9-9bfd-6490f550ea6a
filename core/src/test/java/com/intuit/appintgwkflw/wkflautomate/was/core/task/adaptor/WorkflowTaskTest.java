package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4GraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WASClientType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import java.util.HashMap;
import java.util.Map;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowTaskTest {

  @Mock
  private WorkflowTaskConfig workflowTaskConfig;

  @Mock
  private WASHttpClient wasHttpClient;

  @Mock
  private V4GraphqlClient v4GraphqlClient;

  @InjectMocks
  private WorkflowHumanTask workflowTask;

  @Before
  public void setUp() {
    ReflectionTestUtils.setField(workflowTask, "workflowTaskConfig", workflowTaskConfig);
    ReflectionTestUtils.setField(workflowTask, "v4GraphqlClient", v4GraphqlClient);
    ReflectionTestUtils.setField(workflowTask, "wasHttpClient", wasHttpClient);
  }

  @Test
  public void test_HumanTask_graphQL() {
    Map<TaskType, WorkflowTaskConfigDetails> configMap = new HashMap<>();
    WorkflowTaskConfigDetails humanTaskClientConfig = new WorkflowTaskConfigDetails();
    configMap.put(TaskType.HUMAN_TASK, humanTaskClientConfig);
    Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(configMap);
    WorkflowTaskConfigDetails returnedConfigMap = workflowTask.getTaskConfig();
    Assert.assertNotNull(returnedConfigMap);

  }

  @Test
  public void test_noConfig() {
    Map<TaskType, WorkflowTaskConfigDetails> configMap = new HashMap<>();
    WorkflowTaskConfigDetails humanTaskClientConfig = new WorkflowTaskConfigDetails();
    configMap.put(TaskType.SYSTEM_TASK, humanTaskClientConfig);
    Mockito.when(workflowTaskConfig.getTaskConfig()).thenReturn(configMap);
    WorkflowTaskConfigDetails returnedConfigMap = workflowTask.getTaskConfig();
    Assert.assertNull(returnedConfigMap);
  }

  @Test
  public void getClient_WASHTTP() {
    WASHttpClient httpClient = workflowTask.getClient(WASClientType.HTTP);
    Assert.assertEquals("Both should be same.", httpClient, wasHttpClient);
  }

  @Test
  public void getClient_GRAPHQL() {
    V4GraphqlClient object = workflowTask.getClient(WASClientType.GRAPHQL);
    Assert.assertEquals("Both should be same.", object, v4GraphqlClient);
  }

  @Test
  public void getClient_Null() {
    Object object = workflowTask.getClient(null);
    Assert.assertNull("Should be null.", object);
  }

}

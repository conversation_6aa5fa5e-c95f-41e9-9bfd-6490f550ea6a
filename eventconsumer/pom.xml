<?xml version="1.0"?>
<project
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
		xmlns="http://maven.apache.org/POM/4.0.0"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
		<artifactId>workflow-automation-service-aggregator</artifactId>
		<version>1.1.20</version>
	</parent>
	<artifactId>was-eventconsumer</artifactId>
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.xerial.snappy</groupId>
					<artifactId>snappy-java</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>was-core</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>org.camunda.bpm</groupId>
			<artifactId>camunda-external-task-client</artifactId>
		</dependency>
		<dependency>
			<groupId>com.intuit.data.process</groupId>
			<artifactId>eventbus-kafka</artifactId>
			<version>${eventbus.kafka.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.github.everit-org.json-schema</groupId>
					<artifactId>org.everit.json.schema</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>io.numaproj.numaflow</groupId>
			<artifactId>numaflow-java</artifactId>
			<version>${numaflow.version}</version>
		</dependency>
		<dependency>
			<groupId>com.intuit.app-foundations.guaranteed-consumer-sdk</groupId>
			<artifactId>event-handler</artifactId>
			<version>${gc-event-handler.version}</version>
		</dependency>
	</dependencies>
</project>

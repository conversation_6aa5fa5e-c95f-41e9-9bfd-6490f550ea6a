package com.intuit.appintgwkflw.wkflautomate.was.common.retry;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkerRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Map;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class WorkerRetryHelperTest {

    WorkerRetryHelper workerRetryHelper;

    WorkerRetryHelper workerRetryHelperWithNullRetryConfig;

    WorkerRetryHelper workerRetryHelperWithRetryConfigNullValues;

    @Mock FeatureFlagManager featureFlagManager;

    WorkerRetryConfig workerRetryConfig;

    public static String TEST_CONFIG_PATH = "schema/testData/worker_retry_config.yaml";
    public static String YAML_KEY = "worker-retry";

    @Before
    @SneakyThrows
    public void init() {
        workerRetryConfig =
                new ObjectMapper(new YAMLFactory())
                        .readValue(
                                TestHelper.readResourceAsString(TEST_CONFIG_PATH),
                                new TypeReference<Map<String, WorkerRetryConfig>>() {
                                })
                        .get(YAML_KEY);
        
        workerRetryHelper = new WorkerRetryHelper(workerRetryConfig, featureFlagManager);

        workerRetryHelperWithNullRetryConfig = new WorkerRetryHelper(null, featureFlagManager);
        
        workerRetryHelperWithRetryConfigNullValues = new WorkerRetryHelper(new WorkerRetryConfig(), featureFlagManager);
    }
    
    @Test
    public void testDefaultRetryCountValue() {
        Assertions.assertEquals(workerRetryHelper.getDefaultRetryCount(), 3);
    }

    @Test
    public void testDefaultRetryCountValueNullConfig() {
        Assertions.assertEquals(workerRetryHelperWithNullRetryConfig.getDefaultRetryCount(), 3);
    }

    @Test
    public void testDefaultRetryCountNullValueInConfig() {
        Assertions.assertEquals(workerRetryHelperWithRetryConfigNullValues.getDefaultRetryCount(), 3);
    }


    @Test
    public void testDefaultBackOffStepSize() {
        Assertions.assertEquals(workerRetryHelper.getDefaultBackOffStepSize(), 15000);
    }

    @Test
    public void testDefaultBackOffStepSizeNullConfig() {
        Assertions.assertEquals(workerRetryHelperWithNullRetryConfig.getDefaultBackOffStepSize(), 15000);
    }

    @Test
    public void testDefaultBackOffStepSizeNullValueInConfig() {
        Assertions.assertEquals(workerRetryHelperWithRetryConfigNullValues.getDefaultBackOffStepSize(), 15000);
    }

    @Test
    public void testMaximumBackOff() {
        Assertions.assertEquals(workerRetryHelper.getMaximumBackOff(), 14400000);
    }

    @Test
    public void testMaximumBackOffNullConfig() {
        Assertions.assertEquals(workerRetryHelperWithNullRetryConfig.getMaximumBackOff(), 14400000);
    }

    @Test
    public void testMaximumBackOffNullValueInConfig() {
        Assertions.assertEquals(workerRetryHelperWithRetryConfigNullValues.getMaximumBackOff(), 14400000);
    }

    @Test
    public void testGetRetryConfig_nullConfig() {
        String ownerId = "12345";
        String workflowName = "customReminder";
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelperWithNullRetryConfig.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_nullWorkflowName() {
        String ownerId = "12345";

        String workflowName = null;
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_nullExternalTaskName() {
        String ownerId = "12345";

        String workflowName = "customReminder";
        String externalTaskName = null;

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_nullOwnerId() {
        String ownerId = null;

        String workflowName = "customReminder";
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_emptyWorkflowName() {
        String ownerId = "12345";

        String workflowName = "";
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_emptyExternalTaskName() {
        String ownerId = "12345";

        String workflowName = "customReminder";
        String externalTaskName = "";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_emptyOwnerId() {
        String ownerId = "";

        String workflowName = "customReminder";
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    public void testGetRetryConfig_featureFlagDisabled() {
        String ownerId = "12345";
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId)).thenReturn(false);

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig("workflow", "externalTask", ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }

    @Test
    @SneakyThrows
    public void testGetRetryConfig_nullExternalTaskConfigList() {
        String ownerId = "12345";

        String workflowName = "customReminder";
        String externalTaskName = "sendPushNotification";
        
        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelperWithRetryConfigNullValues.getRetryConfig(workflowName, externalTaskName, ownerId);

        Assertions.assertNull(externalTaskRetryConfig);
    }
    
    @Test
    public void testGetRetryConfig_createTask_featureFlagEnabled() {
        String ownerId = "12345";
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId)).thenReturn(true);
        
        String workflowName = "customReminder";
        String externalTaskName = "createTask";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);
        
        Assertions.assertNotNull(externalTaskRetryConfig);
        Assertions.assertEquals(externalTaskRetryConfig.getRetryCount(), 5);
        Assertions.assertEquals(externalTaskRetryConfig.getBackOffStepSize(), 10000);
        Assertions.assertEquals(externalTaskRetryConfig.getRetryStrategyName(), RetryStrategyName.LINEAR_MOD_BACKOFF);
        Assertions.assertTrue(externalTaskRetryConfig.isFatalOnRetryExhaust());
    }
    
    @Test
    public void testGetRetryConfig_sendCompanyEmail_featureFlagEnabled() {
        String ownerId = "12345";
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId)).thenReturn(true);
        
        String workflowName = "customReminder";
        String externalTaskName = "sendCompanyEmail";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);
        
        Assertions.assertNotNull(externalTaskRetryConfig);
        Assertions.assertEquals(externalTaskRetryConfig.getRetryCount(), 5);
        Assertions.assertEquals(externalTaskRetryConfig.getBackOffStepSize(), 5000);
        Assertions.assertNull(externalTaskRetryConfig.getRetryStrategyName());
        Assertions.assertFalse(externalTaskRetryConfig.isFatalOnRetryExhaust());
    }
    
    @Test
    public void testGetRetryConfig_sendExternalEmail_featureFlagEnabled() {
        String ownerId = "12345";
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId)).thenReturn(true);
        
        String workflowName = "customReminder";
        String externalTaskName = "sendExternalEmail";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);
        
        Assertions.assertNotNull(externalTaskRetryConfig);
        Assertions.assertEquals(externalTaskRetryConfig.getRetryCount(), 5);
        Assertions.assertEquals(externalTaskRetryConfig.getBackOffStepSize(), 5000);
        Assertions.assertNull(externalTaskRetryConfig.getRetryStrategyName());
        Assertions.assertFalse(externalTaskRetryConfig.isFatalOnRetryExhaust());
    }
    
    @Test
    public void testGetRetryConfig_nonExistent_featureFlagEnabled() {
        String ownerId = "12345";
        Mockito.when(featureFlagManager.getBoolean(WorkflowConstants.WORKER_RETRY_CONFIG_ENABLED_FF, ownerId)).thenReturn(true);
        
        String workflowName = "customReminder";
        String externalTaskName = "sendPushNotification";

        ExternalTaskRetryConfig externalTaskRetryConfig =
                workerRetryHelper.getRetryConfig(workflowName, externalTaskName, ownerId);
        
        Assertions.assertNull(externalTaskRetryConfig);
    }
}

package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;

@Getter
/** <AUTHOR> */
public enum AppConnectDuzzits {
  WAS_CREATE_TASK("intuit-workflows/was-create-task", WorkflowConstants.PROJECT_SERVICE),
  WAS_UPDATE_INVOICE("intuit-workflows/was-update-invoice-status", WorkflowConstants.QBO),
  WAS_SEND_INVOICE_APPROVAL_NOTIFICATION(
      "intuit-workflows/was-send-invoice-approval-notification", WorkflowConstants.OINP),
  WAS_SEND_INVOICE_NOTIFICATION_TO_CREATOR(
      "intuit-workflows/was-send-invoice-notification-to-creator", WorkflowConstants.OINP),
  WAS_REMINDER_SEND_NOTIFICATION("intuit-workflows/was-reminder-send-notification", WorkflowConstants.OINP),
  UN_DEPOSITED_FUNDS_WAIT("intuit-workflows/undeposited-funds-wait", WorkflowConstants.QBO),
  BILL_DUE_WAIT("intuit-workflows/bill-due-wait", WorkflowConstants.QBO),
  RECEIPT_NOTIFICATION("intuit-workflows/receipt-notification", WorkflowConstants.OINP),
  OVERDUE_INVOICE_WAIT("intuit-workflows/overdue-invoice-wait", WorkflowConstants.QBO),
  DELETE_QBO_WEBHOOK("intuit-workflows/Delete-QBO-Webhook", WorkflowConstants.QBO),
  STAGE_DISCONNECT("intuit-workflows/stage-disconnect", WorkflowConstants.STAGE),
  EXCLUDE_STAGE_ENTITY("intuit-workflows/exclude-stage-entity", WorkflowConstants.STAGE),
  PROJECT_SERVICE("intuit-workflows/project-task-update", WorkflowConstants.PROJECT_SERVICE),
  UNSENT_INVOICE_REMINDER_WAIT("intuit-workflows/unsent-invoice-reminder-wait", WorkflowConstants.QBO),
  GET_STAGE_CONNECTION_ENTITIES("intuit-workflows/get-stage-connection-entities", WorkflowConstants.STAGE),
  WAS_UPDATE_TASK("intuit-workflows/was-update-task", WorkflowConstants.PROJECT_SERVICE);

  private static final Map<String, String> duzzitDependencyMap = getDuzzitDependencyValues();

  public static Map<String, String> getDuzzitDependencyMap() {
    return duzzitDependencyMap;
  }

  private final String duzzitName;
  private final String handlerId;

  /**
   * @param handlerId which carries the value of handler task id's from templates
   * @param duzzitName which specifies the duzzit name of the appropriate handler id's belongs to
   */
  AppConnectDuzzits(final String handlerId, final String duzzitName) {

    this.duzzitName = duzzitName;
    this.handlerId = handlerId;
  }

  /**
   * this map stores the duzzits and dependency respectively
   *
   * @return duzzitDependencyMap which have the handler id and dependency
   */
  private static Map<String, String> getDuzzitDependencyValues() {
    final Map<String, String> duzzitDependencyMap = new HashMap<>();
    for (final AppConnectDuzzits duzzitDependency : values()) {
      duzzitDependencyMap.put(duzzitDependency.handlerId, duzzitDependency.duzzitName);
    }
    return duzzitDependencyMap;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiWorkflowStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.CreateMultiActionStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.CreateMultiConditionStepProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepProcessorFactoryTest {

  @InjectMocks
  private MultiStepProcessorFactory multiStepProcessorFactory;

  @Mock
  private CreateMultiConditionStepProcessor createMultiConditionStepProcessor;

  @Mock
  private CreateMultiActionStepProcessor createMultiActionStepProcessor;

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
        TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

  WorkflowStep actionWorkflowStep = new WorkflowStep();
  WorkflowStep conditionWorkflowStep = new WorkflowStep();
  DefinitionInstance definitionInstance;
  DmnModelInstance dmnModelInstance;
  BpmnModelInstance multiConditionBpmnModelInstance;

  @Before
  public void setup() {
    actionWorkflowStep.setStepType(StepTypeEnum.ACTION);
    actionWorkflowStep.setActionGroup(new ActionGroup());
    conditionWorkflowStep.setStepType(StepTypeEnum.CONDITION);
    conditionWorkflowStep.setWorkflowStepCondition(new WorkflowStepCondition());
    dmnModelInstance = Dmn.readModelFromStream(
        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
      multiConditionBpmnModelInstance =
          Bpmn.readModelFromStream(
              IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    definitionInstance = new DefinitionInstance(null, multiConditionBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), null);
  }

  @Test
  public void testProcessStepWithConditionHandler_ForCreateOperation() {
    MultiWorkflowStepHandler actualOutput = multiStepProcessorFactory
        .getHandler(true, conditionWorkflowStep);
    Assert.assertTrue(actualOutput instanceof CreateMultiConditionStepProcessor);
    actualOutput = multiStepProcessorFactory
        .getHandler(false, conditionWorkflowStep);
    Assert.assertTrue(actualOutput instanceof CreateMultiConditionStepProcessor);
    actualOutput = multiStepProcessorFactory
        .getHandler(true, actionWorkflowStep);
    Assert.assertTrue(actualOutput instanceof CreateMultiConditionStepProcessor);
  }

  @Test
  public void testProcessStepWithActionHandler_ForCreateOperation() {
    MultiWorkflowStepHandler actualOutput = multiStepProcessorFactory
        .getHandler(false, actionWorkflowStep);
    Assert.assertTrue(actualOutput instanceof CreateMultiActionStepProcessor);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testProcessStepWithInvalidWorkflowStep() {
    actionWorkflowStep.setStepType(null);
    multiStepProcessorFactory.getHandler(false, actionWorkflowStep);
  }

  @Test
  public void testProcessWorkflowStep() {
    MultiWorkflowStepHandler multiWorkflowStepHandler = multiStepProcessorFactory
        .getHandler(true, conditionWorkflowStep);
    Mockito.when(multiWorkflowStepHandler.processWorkflowStep(
            anyString(), any(), any(), any(), anySet()))
        .thenReturn(new HashMap<>());
    Assert.assertNotNull(multiStepProcessorFactory.processWorkflowStep(
        conditionWorkflowStep, "decisionElement", definitionInstance,
        new LinkedList<>(), new HashSet<>()));
  }

}

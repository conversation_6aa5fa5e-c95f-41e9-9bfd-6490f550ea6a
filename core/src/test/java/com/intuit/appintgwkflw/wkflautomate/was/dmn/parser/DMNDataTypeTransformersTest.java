package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 *
 */
public class DMNDataTypeTransformersTest {

  @Mock private DaysTransformer daysTransformer;

  @Before
  public void init() {

    MockitoAnnotations.initMocks(this);
    Mockito.when(daysTransformer.getName()).thenReturn(DMNSupportedOperator.DAYS);
    DMNDataTypeTransformers.addTransformer(daysTransformer.getName(), daysTransformer);
  }

  @Test
  public void getTestNUll() {
    DMNDataTypeTransformer transformer = DMNDataTypeTransformers.getTransformer(null);
    Assert.assertNull(transformer);
  }

  @Test
  public void getTestRuleHandler() {
    DMNDataTypeTransformer transformer =
        DMNDataTypeTransformers.getTransformer(DMNSupportedOperator.DAYS);
    Assert.assertNotNull(transformer);
  }
}

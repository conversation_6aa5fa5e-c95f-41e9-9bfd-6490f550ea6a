package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepMergeUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.ActionGroup;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

/**
 * This class substitutes fieldValues with definition activities placeholder values
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepPlaceholderSubstitutor {

  private final CustomWorkflowConfig customWorkflowConfig;

  /**
   * This function substitutes the fieldValues with placeholder values for each call activity
   * element
   *
   * @param activityInstance activityInstance object
   * @param recordType       map of variables for a particular record type
   * @param workflowStep     action type workflowStep whose fieldValues are to be substituted
   */
  public void substitutePlaceholderValues(
      ActivityInstance activityInstance,
      String recordType,
      WorkflowStep workflowStep) {
    // get actionGroup from workflowStep
    ActionGroup stepActionGroup = workflowStep.getActionGroup();
    // get parent action and subActions from workflowStep actionGroup
    Action parentAction = stepActionGroup.getAction();
    List<Action> subActions = stepActionGroup.getAction().getSubActions();
    // substitute placeholder values for parent action
    substituteActionPlaceholders(
        activityInstance,
        recordType,
        parentAction);
    // get the map of associated child activity instances
    Map<String, ActivityInstance> childActivityInstances = activityInstance.getChildActivityInstances();

    subActions.stream().forEach(subAction -> {
      String subActionId = subAction.getId().getLocalId();
      ActivityInstance childActivityInstance = childActivityInstances.get(subActionId);
      if (Objects.nonNull(childActivityInstance)) {
        substituteActionPlaceholders(childActivityInstance, recordType, subAction);
      }
    });
  }

  /**
   * This function substitutes the parent action's fieldValues with placeholder values
   *
   * @param activityInstance activityInstance object
   * @param recordType       record type
   * @param action           workflowStep parent action object
   */
  private void substituteActionPlaceholders(
      ActivityInstance activityInstance,
      String recordType,
      Action action) {
    WorkflowLogger.logInfo("step=substituteActionPlaceholders recordType=%s actionId=%s",
        recordType, action.getId());
    JSONObject userAttributes = new JSONObject(activityInstance.getUserAttributes());
    action.setSelected(userAttributes.getBoolean(WorkflowConstants.SELECTED));
    JSONObject parameters = userAttributes.getJSONObject(WorkflowConstants.PARAMETERS);
    replaceFieldValuesAndHelpVariables(action, recordType, parameters);
  }

  /**
   * This function substitutes fieldValues with call activity placeholder values. The help variables
   * (i.e the process variable values) are also replaced with their corresponding display name
   * counterpart which is shown on the UI
   *
   * @param action action whose parameter fieldValues are being replaced
   * @param recordType record type
   * @param parameters JSON object of placeholder parameter values
   */
  private void replaceFieldValuesAndHelpVariables(
      Action action, String recordType, JSONObject parameters) {
    Record record = customWorkflowConfig.getRecordObjForType(recordType);
    action.getParameters().stream()
        .forEach(
            inputParameter -> {
              if (parameters.has(inputParameter.getParameterName())) {
                JSONObject inputParameterValues =
                    parameters.getJSONObject(inputParameter.getParameterName());
                inputParameter.setFieldValues(
                    (List<String>)
                        inputParameterValues
                            .toMap()
                            .getOrDefault(WorkflowConstants.FIELD_VALUE, new ArrayList<>()));

                //TODO: Refactor the code to avoid merging at every step in multiStep workflows
                List<String> helpVars =
                    MultiStepMergeUtil.mergeRecordParameters(
                        record, inputParameter, action.getId().getLocalId().toString());

                List<String> replacedFieldValues =
                    CustomWorkflowUtil.getReplacedFieldValuesWithDisplayNames(
                        inputParameter.getFieldValues(), helpVars);
                inputParameter.setFieldValues(replacedFieldValues);
              }
            });
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.DEFINITION_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.REALM_ID_KEY;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.RECURRENCE_START_PROCESS_EXCEPTION;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.RECURRENCE_START_PROCESS_TASK_FAILURE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;

import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.EventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.request.State;
import java.util.HashMap;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/** <AUTHOR> */
public class StartRecurringProcessTaskTest {

  private MetricLogger metricLogger = Mockito.mock(MetricLogger.class);

  private RunTimeService runTimeService = Mockito.mock(RunTimeService.class);

  public StartRecurringProcessTask getTask() {
    return new StartRecurringProcessTask(metricLogger, runTimeService);
  }

  @Test
  public void testErrorStarting() {
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_START_PROCESS_ERROR));
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    StartRecurringProcessTask startRecurringProcessTask = getTask();
    State response = startRecurringProcessTask.execute(getStateRequest());
    Assert.assertNotNull(response);
    Assert.assertEquals(Boolean.TRUE, response.getValue(RECURRENCE_START_PROCESS_TASK_FAILURE));
    WorkflowGeneralException exception = response.getValue(RECURRENCE_START_PROCESS_EXCEPTION);
    Assert.assertEquals(
        WorkflowError.TRIGGER_START_PROCESS_ERROR.name(), exception.getError().getMessage());
    WorkflowGenericResponse resp = response.getValue("defId");
    Assert.assertNull(resp);
  }

  @Test
  public void testStartingProcess() {
    Mockito.when(runTimeService.processTriggerMessageV2(Mockito.any()))
        .thenReturn(WorkflowGenericResponse.builder().build());
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    StartRecurringProcessTask startRecurringProcessTask = getTask();
    State response = startRecurringProcessTask.execute(getStateRequest());
    Assert.assertNotNull(response);
    WorkflowGenericResponse resp = response.getValue("defId");
    Assert.assertNotNull(resp);
  }

  @Test
  public void testInvalidDefinitionId() {
    StartRecurringProcessTask startRecurringProcessTask = getTask();
    State response = startRecurringProcessTask.execute(new State());
    Assert.assertNotNull(response);
    Assert.assertEquals(Boolean.TRUE, response.getValue(RECURRENCE_START_PROCESS_TASK_FAILURE));
    WorkflowGeneralException exception = response.getValue(RECURRENCE_START_PROCESS_EXCEPTION);
    Assert.assertEquals(
        WorkflowError.INVALID_DEFINITION_ID.name(), exception.getError().getMessage());
    WorkflowGenericResponse resp = response.getValue("defId");
    Assert.assertNull(resp);
  }

  /** @return */
  private State getStateRequest() {
    State inputRequest = new State();
    TransactionEntity transactionEntity = Mockito.mock(TransactionEntity.class);
    Mockito.when(transactionEntity.getEventHeaders()).thenReturn(new EventHeaders());
    Mockito.when(transactionEntity.getV3EntityPayload()).thenReturn(new HashMap<>());
    DefinitionInstance definitionInstance = new DefinitionInstance();
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    definitionInstance.setDefinitionDetails(definitionDetails);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);
    inputRequest.addValue(BPMN_ENGINE_DEPLOY_REQUEST_KEY, definitionInstance);
    inputRequest.addValue(DEFINITION_ID_KEY, "defId");
    inputRequest.addValue(REALM_ID_KEY, "12345");
    return inputRequest;
  }
}

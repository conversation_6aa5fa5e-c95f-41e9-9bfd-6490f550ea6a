package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import org.json.JSONObject;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.annotation.Import;

/** <AUTHOR> */
@Import(MockitoJUnitRunner.class)
public class RecurrenceParserUtilTest {

  private static final String testRecurrenceJson = "schema/testData/testJson";
  private static final String testRecurrenceJson2 = "schema/testData/testJson2";

  @Test
  public void testRecurrenceJson_partial() {
    String val = TestHelper.readResourceAsString(testRecurrenceJson);
    JSONObject jsonObject = ObjectConverter.convertObject(val, JSONObject.class);
    RecurrenceRule recurrenceRule = RecurrenceParserUtil.toRecurrenceRule(jsonObject);
    Assert.assertNotNull(recurrenceRule);
    Assert.assertEquals(RecurTypeEnum.MONTHLY, recurrenceRule.getRecurType());
    Assert.assertEquals(1, recurrenceRule.getInterval().intValue());
    Assert.assertNull(recurrenceRule.getDayOfMonth());
    Assert.assertEquals(Boolean.TRUE, recurrenceRule.isActive());
  }

  @Test
  public void testRecurrenceJson_full() {
    String val = TestHelper.readResourceAsString(testRecurrenceJson2);
    JSONObject jsonObject = ObjectConverter.convertObject(val, JSONObject.class);
    RecurrenceRule recurrenceRule = RecurrenceParserUtil.toRecurrenceRule(jsonObject);
    Assert.assertNotNull(recurrenceRule);
    Assert.assertEquals(RecurTypeEnum.MONTHLY, recurrenceRule.getRecurType());
    Assert.assertEquals(1, recurrenceRule.getInterval().intValue());
    Assert.assertNull(recurrenceRule.getDayOfMonth());
    Assert.assertEquals(Boolean.TRUE, recurrenceRule.isActive());

    Assert.assertEquals(recurrenceRule.getStartDate().toLocalDate().year().get(), 2021);
    Assert.assertEquals(recurrenceRule.getEndDate().toLocalDate().year().get(), 2025);
    Assert.assertEquals(2, recurrenceRule.getDaysOfMonth().size());
    Assert.assertNotNull(recurrenceRule.getDaysOfMonth());
    Assert.assertEquals(true, recurrenceRule.getDaysOfMonth().contains(1));
    Assert.assertEquals(true, recurrenceRule.getDaysOfMonth().contains(25));
    Assert.assertNotNull(recurrenceRule.getWeekOfMonth());
    Assert.assertEquals(WeekOfMonthEnum.FIRST, recurrenceRule.getWeekOfMonth());
    Assert.assertNotNull(recurrenceRule.getWeeksOfMonth());
    Assert.assertEquals(true, recurrenceRule.getWeeksOfMonth().contains(WeekOfMonthEnum.FIRST));
    Assert.assertEquals(true, recurrenceRule.getWeeksOfMonth().contains(WeekOfMonthEnum.SECOND));
    Assert.assertNotNull(recurrenceRule.getMonthOfYear());
    Assert.assertEquals(MonthsOfYearEnum.JANUARY, recurrenceRule.getMonthOfYear());
    Assert.assertNotNull(recurrenceRule.getMonthsOfYear());
    Assert.assertEquals(true, recurrenceRule.getMonthsOfYear().contains(MonthsOfYearEnum.JANUARY));
    Assert.assertEquals(true, recurrenceRule.getMonthsOfYear().contains(MonthsOfYearEnum.FEBRUARY));
    Assert.assertEquals(false, recurrenceRule.getMonthsOfYear().contains(MonthsOfYearEnum.MARCH));
    Assert.assertEquals(false, recurrenceRule.getMonthsOfYear().contains(MonthsOfYearEnum.APRIL));
    Assert.assertEquals(false, recurrenceRule.getMonthsOfYear().contains(MonthsOfYearEnum.MAY));
    Assert.assertEquals(9, recurrenceRule.getRecurrenceTime().getHours().intValue());
    Assert.assertEquals(30, recurrenceRule.getRecurrenceTime().getMinutes().intValue());
    Assert.assertEquals("UTC+05:30", recurrenceRule.getTimeZone());
  }
}

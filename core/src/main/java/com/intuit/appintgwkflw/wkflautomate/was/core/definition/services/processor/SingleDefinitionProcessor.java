package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeployDefinitionTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveSystemDefinitionInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveTemplateDetailsTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.DeployDefinitionRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTaskDetailRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback.SaveTemplateRollBackTask;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * Handles template processing for single definition
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class SingleDefinitionProcessor implements TemplateProcessor {

  private TemplateDetailsRepository templateDetailsRepository;
  private TriggerDetailsRepository triggerDetailsRepository;
  private ActivityDetailsRepository activityDetailsRepository;
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private  TemplateDomainEventHandler templateDomainEventHandler;
  private WorkflowTaskConfig workflowTaskConfig;
  private DefinitionServiceHelper definitionServiceHelper;

  @Override
  public State executeAction(State inputState, TemplateModelInstance model) {

    // Deploy in Camunda
    RxExecutionChain rxExecutionChain = new RxExecutionChain(inputState);

    // if failed, it will throw error
    return rxExecutionChain.next(new DeployDefinitionTask(bpmnEngineDefinitionServiceRest))
        .next(new SaveTemplateDetailsTask(templateDetailsRepository, triggerDetailsRepository, templateDomainEventHandler))
        .next(new SaveTaskDetails(activityDetailsRepository,
            workflowTaskConfig.isEnable()))
		.next(new SaveSystemDefinitionInDataStoreTask(definitionServiceHelper, model))
        .execute();
  }

	@Override
	public void checkAndRollBack(State inputState) {

		boolean saveTemplateTaskFail = BooleanUtils
				.isTrue(inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_TASK_FAILURE));

		boolean saveTaskDetailsFail = BooleanUtils
				.isTrue(inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_FAILURE));

		if (saveTemplateTaskFail || saveTaskDetailsFail) {
			// Delete deployment from Camunda
			new RxExecutionChain(inputState,
					new DeployDefinitionRollBackTask(bpmnEngineDefinitionServiceRest))
					.next(new SaveTaskDetailRollBackTask(activityDetailsRepository, workflowTaskConfig.isEnable()))
					.next(new SaveTemplateRollBackTask(templateDetailsRepository, triggerDetailsRepository))
					.executeAsync();
			if (saveTaskDetailsFail) {
				throw new WorkflowGeneralException(
						inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_ERROR_MESSAGE),
						(Exception) inputState.getValue(AsyncTaskConstants.SAVE_TASK_DETAILS_EXCEPTION));
			} else  {
				throw new WorkflowGeneralException(
						inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_ERROR_MESSAGE),
						(Exception) inputState.getValue(AsyncTaskConstants.SAVE_TEMPLATE_EXCEPTION));
			}
		}
	}

  @Override
  public DefinitionType getType() {
    return DefinitionType.SINGLE;
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.core.task.service.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.apollo.client.VariabilityGraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.variability.entity.graphql.GetAttributeQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Invokes Variability Serivce
 */
@AllArgsConstructor
@Component
public class VariabilityConnector {

  private VariabilityGraphqlClient variabilityGraphqlClient;
  
  private WASContextHandler wasContextHandler;

  /**
   * Calls Variability Service to get the list of Decisions.
   *
   * @param headers: Passing the custom headers, which would contain the mandatory parameters to
   *     uniquely identify in the variability service downstream
   * @return
   */
  public Map<String, Boolean> getDecisions(
      final List<String> decisions, Map<String, String> headers) {
    List<GetAttributeQuery.VariabilityEngine_getDecisionBatch> decisionsList =
        variabilityGraphqlClient.getDecisionBatch(decisions, headers);

    Map<String, Boolean> decisionMap = Optional.ofNullable(decisionsList).orElseGet(Collections::emptyList).stream()
			.filter(decision -> Objects.nonNull(decision.name()))
			.collect(Collectors.toMap(GetAttributeQuery.VariabilityEngine_getDecisionBatch::name,
					GetAttributeQuery.VariabilityEngine_getDecisionBatch::value));
	
    WorkflowLogger.logInfo(
            "Variability Service calls are successful. decisions=%s, targetRealm=%s operation=VARIABILITY_LOOKUP",
            decisionMap, wasContextHandler.get(WASContextEnums.OWNER_ID));
    
    return decisionMap;
}
}

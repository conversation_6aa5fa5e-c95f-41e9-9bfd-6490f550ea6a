package com.intuit.appintgwkflw.wkflautomate.was.observability;

import com.intuit.appintgwkflw.wkflautomate.was.common.observability.TagUtils;
import io.opentracing.Span;
import io.opentracing.contrib.web.servlet.filter.ServletFilterSpanDecorator;
import io.opentracing.tag.Tags;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;

/**
 * A custom span decorator to add business specific tags on spans.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public final class SpanDecorator implements ServletFilterSpanDecorator {

  private final TagUtils tagUtils;

  @Override
  public void onRequest(final HttpServletRequest httpServletRequest, final Span span) {
    // do nothing
  }

  @Override
  public void onResponse(final HttpServletRequest httpServletRequest,
      final HttpServletResponse httpServletResponse, final Span span) {

    tagUtils.addCommonTags(span);
    if (httpServletResponse.getStatus() >= 400) {
      Tags.ERROR.set(span, true);
    }
  }

  @Override
  public void onError(final HttpServletRequest httpServletRequest,
      final HttpServletResponse httpServletResponse, final Throwable throwable, final Span span) {
    // do nothing
  }

  @Override
  public void onTimeout(final HttpServletRequest httpServletRequest,
      final HttpServletResponse httpServletResponse, final long l, final Span span) {
    // do nothing
  }
}

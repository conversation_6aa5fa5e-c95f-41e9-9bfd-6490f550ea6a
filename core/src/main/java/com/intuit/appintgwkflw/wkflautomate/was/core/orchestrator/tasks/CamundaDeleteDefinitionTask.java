package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/** Author: Nitin Gupta Date: 28/01/20 Description: */
@AllArgsConstructor
public class CamundaDeleteDefinitionTask implements Task {

  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private String definitionId;
  private Boolean cascade;
  private Boolean skipListener;

  @Override
  public State execute(State inputRequest) {

    WorkflowVerfiy.verify(
        definitionId == null || cascade == null || skipListener == null,
        WorkflowError.INVALID_INPUT);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Deleting definition from camunda for definitionId=%s with cascade=%s and skipListener=%s",
                    definitionId, cascade, skipListener)
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_DELETE_DEFINITION));

    try {
      bpmnEngineDefinitionServiceRest.deleteDefinition(
          new DeleteDeploymentRequest(definitionId, cascade, skipListener));
    } catch (WorkflowGeneralException workflowGeneralException) {

      if (StringUtils.isNotEmpty(workflowGeneralException.getMessage())
          && workflowGeneralException.getMessage()
              .contains(WorkflowConstants.DEFINITION_NOT_FOUND)) {
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message("Details not found in camunda for given definitionId=%s", definitionId)
                    .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                    .downstreamServiceName(DownstreamServiceName.WAS_DELETE_DEFINITION));

      } else {
        throw workflowGeneralException;
      }
    }

    return inputRequest;
  }
}

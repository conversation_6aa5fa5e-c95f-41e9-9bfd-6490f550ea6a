package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ProcessVariableData;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.javatuples.Pair;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class MultiConditionPlaceholderExtractorTest {

  private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");
  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");
  @Mock
  private CustomWorkflowConfig customWorkflowConfig;
  private Definition multiConditionDefinition;
  private BpmnModelInstance bpmnModelInstance;
  private DefinitionInstance definitionInstance;
  private DmnModelInstance dmnModelInstance;
  @InjectMocks
  private MultiConditionPlaceholderExtractor multiConditionPlaceholderExtractor;

  @Before
  public void setUp() throws Exception {
    customWorkflowConfig = TestHelper.loadCustomConfig();
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    bpmnModelInstance =
        Bpmn.readModelFromStream(
            IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML,
                Charset.defaultCharset()));

    definitionInstance = new DefinitionInstance(multiConditionDefinition,
        bpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());
    definitionInstance.setPlaceholderValue(new HashMap<>());
    buildWorkflowStepMap(definitionInstance);
  }

  @Test
  public void testExtractPlaceholderValues() {
    String workflowStepId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();
    String workflowStepLocalId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().getLocalId();
    Pair<String, String> pathResults = new Pair<>("1", "2");
    multiConditionPlaceholderExtractor.extractPlaceholderValue(definitionInstance, workflowStepId,
        pathResults);
    Map<String, Object> dmnPlaceholderMap = (Map<String, Object>) definitionInstance.getPlaceholderValue()
        .get(DMN_PLACEHOLDER_VALUES);
    Assert.assertEquals(definitionInstance.getPlaceholderValue().size(), 1);
    Assert.assertNotNull(dmnPlaceholderMap);
    Assert.assertTrue(dmnPlaceholderMap.containsKey(workflowStepLocalId));
  }

  @Test
  public void testExtractPlaceholderValuesWithNullYesPath() {
    String workflowStepId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();
    String workflowStepLocalId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().getLocalId();
    definitionInstance.getWorkflowStepMap().get(workflowStepId).getNext().remove(0);

    Pair<String, String> pathResults = new Pair<>("false", "2");
    multiConditionPlaceholderExtractor.extractPlaceholderValue(definitionInstance, workflowStepId,
        pathResults);
    Map<String, Object> dmnPlaceholderMap = (Map<String, Object>) definitionInstance.getPlaceholderValue()
        .get(DMN_PLACEHOLDER_VALUES);
    Assert.assertEquals(definitionInstance.getPlaceholderValue().size(), 1);
    Assert.assertNotNull(dmnPlaceholderMap);
    Assert.assertTrue(dmnPlaceholderMap.containsKey(workflowStepLocalId));

    Map<String, Object> workflowStepMap = (Map<String, Object>) dmnPlaceholderMap.get(
        workflowStepLocalId);
    Assert.assertNotNull(workflowStepMap.get(WorkflowConstants.WORKFLOW_STEP_NEXTS));
    Map<String, String> nextMap = (Map<String, String>) workflowStepMap.get(
        WorkflowConstants.WORKFLOW_STEP_NEXTS);
    Assert.assertNotNull(nextMap.get("yes"));
    Assert.assertTrue(CustomWorkflowUtil.isFalse(nextMap.get("yes")));
    Assert.assertNotNull(nextMap.get("no"));
  }

  @Test
  public void testExtractPlaceholderValuesWithNullNoPath() {
    String workflowStepId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().toString();
    String workflowStepLocalId = multiConditionDefinition.getWorkflowSteps()
        .stream().findFirst().get().getId().getLocalId();
    definitionInstance.getWorkflowStepMap().get(workflowStepId).getNext().remove(1);

    Pair<String, String> pathResults = new Pair<>("1", "false");
    multiConditionPlaceholderExtractor.extractPlaceholderValue(definitionInstance, workflowStepId,
        pathResults);
    Map<String, Object> dmnPlaceholderMap = (Map<String, Object>) definitionInstance.getPlaceholderValue()
        .get(DMN_PLACEHOLDER_VALUES);
    Assert.assertEquals(definitionInstance.getPlaceholderValue().size(), 1);
    Assert.assertNotNull(dmnPlaceholderMap);
    Assert.assertTrue(dmnPlaceholderMap.containsKey(workflowStepLocalId));

    Map<String, Object> workflowStepMap = (Map<String, Object>) dmnPlaceholderMap.get(
        workflowStepLocalId);
    Assert.assertNotNull(workflowStepMap.get(WorkflowConstants.WORKFLOW_STEP_NEXTS));
    Map<String, String> nextMap = (Map<String, String>) workflowStepMap.get(
        WorkflowConstants.WORKFLOW_STEP_NEXTS);
    Assert.assertNotNull(nextMap.get("no"));
    Assert.assertTrue(CustomWorkflowUtil.isFalse(nextMap.get("no")));
    Assert.assertNotNull(nextMap.get("yes"));
  }

  @Test
  public void testCreateProcessVariables() {
    List<Attribute> attributes = new ArrayList<>();
    Attribute attribute = new Attribute();
    attribute.setName("Term");
    attribute.setType("String");
    attributes.add(attribute);
    definitionInstance.getPlaceholderValue().put(PROCESS_VARIABLES, new HashMap<>());

    multiConditionPlaceholderExtractor.createProcessVariables(definitionInstance, attributes);

    Assert.assertNotNull(definitionInstance.getPlaceholderValue().get(PROCESS_VARIABLES));
    Map<String, ProcessVariableData> processVariableDataMap = (Map<String, ProcessVariableData>) definitionInstance.getPlaceholderValue()
        .get(PROCESS_VARIABLES);
    Assert.assertTrue(processVariableDataMap.containsKey("Term"));
  }

  private void buildWorkflowStepMap(DefinitionInstance definitionInstance) {
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();

    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    definitionInstance.setWorkflowStepMap(workflowStepMap);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.common.exception;

import lombok.Getter;

/**
 * This class will be used for exceptions thrown in Event Publisher. For event publisher we have to
 * execute the executeFailure() flow in WorkerBaseExecutor for all the exceptions. Using this class
 * we won't have to mark each individual activity in the BPMN as fatal to ensure executeFailure() is
 * called for each exception.
 *
 * <AUTHOR> stripathy
 */
@Getter
public class WorkflowEventException extends WorkflowGeneralException {

  private static final long serialVersionUID = 1L;

  private WorkflowGeneralException workflowGeneralException;

  /**
   * convert {@link workflowError in String} to {@link workflowError enum} and wrap {@link
   * WorkflowRetriableException}
   *
   * @param workflowError workflow error
   */
  public WorkflowEventException(String workflowError) {
    this(new WorkflowRetriableException(WorkflowError.value(workflowError)));
  }

  public WorkflowEventException(WorkflowGeneralException workflowGeneralException) {
    super(workflowGeneralException.getWorkflowError());
    this.workflowGeneralException = workflowGeneralException;
  }
}

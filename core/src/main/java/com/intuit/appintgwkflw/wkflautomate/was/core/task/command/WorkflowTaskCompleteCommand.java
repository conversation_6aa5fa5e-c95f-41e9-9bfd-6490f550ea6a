package com.intuit.appintgwkflw.wkflautomate.was.core.task.command;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants.TASK_STATUS_COMPLETE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.WorkflowTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskDBOperationManager;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.Task;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Complete Command is responsible to mark task complete in Camunda and Downstream both. It also
 * updated DB to maintain state and push notification to make subscribers aware of the update.
 *
 * <AUTHOR>
 */

@Component
@AllArgsConstructor
public class WorkflowTaskCompleteCommand extends WorkflowTaskCommand {

  @Override
  public TaskCommand command() {
    return TaskCommand.COMPLETE;
  }

  private ActivityProgressDetailsRepository progressDetailRepo;

  private WorkflowTaskDBOperationManager taskDBOperationManager;

  @SuppressWarnings({"unchecked", "rawtypes"})
  @Override
  public WorkflowTaskResponse execute(WorkflowTaskRequest taskRequest) {

    ActivityProgressDetails activityProgressDetail = progressDetailRepo
        .findById(taskRequest.getId())
        .orElseThrow(
            () -> new WorkflowNonRetriableException(WorkflowError.ACTIVITY_PROGRESS_DETAIL_NOT_FOUND));
    
    /**
     * For the case of VEP, if DQL retry.
     */
    if (ActivityConstants.TASK_STATUS_COMPLETE.equals(activityProgressDetail.getStatus())) {
	  return WorkflowTaskResponse.builder().txnId(taskRequest.getTxnId())
			  .status(TASK_STATUS_COMPLETE).build();
    }
    
    WorkflowTaskResponse completeResponse = null;
    Task task = prepareTaskRequest(taskRequest);
    if (!taskRequest.isSkipCallback()) {
      /**
       * Scenarios like VEP, NotificationTask will call Downstream and mark task
       * Complete in downstream. AsyncMode QBO final ExternalTask complete will mark
       * complete in both DB and Downstream.
       */
      WorkflowTask workflowTask = WorkflowTasks.getWorkflowTask(task.getType());
      /**
       * For scenarios like,  
       *  - complete status save failed (DB Down).
       *  - Downstream returned 504 but actually processed the operation.
       *  We need to add handling of Get and Check.
       */
      WorkflowTaskResponse response = workflowTask.get(task);
      if (!TASK_STATUS_COMPLETE.equals(
    		  workflowTask.getWASActivityStatus(response.getStatus()))) {
        completeResponse = workflowTask.complete(task);
      }
      taskDBOperationManager
          .markTxnDetailAndActivityProgressCompleteInDB(task, activityProgressDetail,
        		  null != completeResponse? completeResponse : response);
    } else {
      /**
       * QBO Transaction Start External Task will be marked complete without calling
       * downstream adaptor.
       */
      taskDBOperationManager.markActivityProgressCompleteInDB(task, activityProgressDetail);
      completeResponse = WorkflowTaskResponse.builder()
          .status(TASK_STATUS_COMPLETE).txnId(taskRequest.getTxnId())
          .build();
    }

    // Publish event of task completion.
    checkAndPublish(taskRequest, activityProgressDetail.getProcessDetails());
    return completeResponse;
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowCircuitOpenException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.apache.kafka.common.record.TimestampType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.listener.adapter.RetryingMessageListenerAdapter;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.retry.context.RetryContextSupport;

@RunWith(MockitoJUnitRunner.class)
public class EventRetryHandlerTest {

  @InjectMocks private EventRetryHandler eventRetryHandler;

  @Mock private WASContextHandler contextHandler;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testIsRetryableException_true() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    retryContext.registerThrowable(
        new Throwable(new WorkflowRetriableException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED)));
    Assert.assertTrue(eventRetryHandler.isRetryableException(retryContext));
  }

  @Test
  public void testIsRetryableException_false() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    retryContext.registerThrowable(
        new Throwable(new WorkflowGeneralException(WorkflowError.CAMUNDA_TASK_NOT_FOUND)));
    Assert.assertFalse(eventRetryHandler.isRetryableException(retryContext));
  }

  @Test
  public void testIsWorkflowCircuitOpenException_true() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    CircuitBreaker circuitBreaker = CircuitBreaker.ofDefaults("test");
    CallNotPermittedException ex =
            CallNotPermittedException.createCallNotPermittedException(circuitBreaker);
    retryContext.registerThrowable(
            new Throwable(new WorkflowCircuitOpenException(WorkflowError.CIRCUIT_OPEN_ERROR, ex)));
    Assert.assertTrue(eventRetryHandler.isWorkflowCircuitOpenException(retryContext));
  }

  @Test
  public void testIsWorkflowCircuitOpenException_false() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    CircuitBreaker circuitBreaker = CircuitBreaker.ofDefaults("test");
    CallNotPermittedException ex =
            CallNotPermittedException.createCallNotPermittedException(circuitBreaker);
    retryContext.registerThrowable(
            new Throwable(new WorkflowGeneralException(ex)));
    Assert.assertFalse(eventRetryHandler.isWorkflowCircuitOpenException(retryContext));
    retryContext.registerThrowable(
            new Throwable(new WorkflowRetriableException(ex)));
    Assert.assertFalse(eventRetryHandler.isWorkflowCircuitOpenException(retryContext));
  }

  @Test
  public void testHandleRetryFailure_MissingRecord() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    retryContext.registerThrowable(
        new Throwable(new WorkflowRetriableException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED)));
    retryContext.setAttribute(RetryingMessageListenerAdapter.CONTEXT_RECORD, null);
    try {
      eventRetryHandler.handleRetryFailure(retryContext, null);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage()
              .contains(
                  WorkflowError.MISSING_CONSUMER_RECORD_DETAILS_RETRY_CONTEXT.getErrorMessage()));
    }
  }

  @Test
  public void testHandleRetryFailure_MissingAck() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    retryContext.registerThrowable(
        new Throwable(new WorkflowRetriableException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED)));
    retryContext.setAttribute(RetryingMessageListenerAdapter.CONTEXT_RECORD, getSuccessMessage());
    retryContext.setAttribute(RetryingMessageListenerAdapter.CONTEXT_ACKNOWLEDGMENT, null);
    try {
      eventRetryHandler.handleRetryFailure(retryContext, null);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(
          e.getMessage()
              .contains(
                  WorkflowError.MISSING_ACKNOWLEDGEMENT_DETAILS_RETRY_CONTEXT.getErrorMessage()));
    }
  }

  @Test
  public void testHandleRetryFailure_Complete() {
    RetryContextSupport retryContext = new RetryContextSupport(null);
    retryContext.registerThrowable(
        new Throwable(new WorkflowRetriableException(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED)));
    retryContext.setAttribute(RetryingMessageListenerAdapter.CONTEXT_RECORD, getSuccessMessage());
    retryContext.setAttribute(
        RetryingMessageListenerAdapter.CONTEXT_ACKNOWLEDGMENT,
        new Acknowledgment() {
          @Override
          public void acknowledge() {}
        });
    eventRetryHandler.handleRetryFailure(
        retryContext,
        ((consumerRecord, headers) -> {
          /** test recovery handler action* */
          contextHandler.addKey(WASContextEnums.OWNER_ID, "sample-owner-id");
        }));
    Mockito.verify(contextHandler, Mockito.atLeast(1)).addKey(Mockito.any(), Mockito.anyString());
    Mockito.verify(contextHandler, Mockito.times(1)).clear();
  }

  private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
    Result<FormatException, String> body = new Result.Success<FormatException, String>("value");
    RecordHeaders headers = new RecordHeaders();
    headers.add(EventHeaderConstants.INTUIT_TID, "sample-tid".getBytes());
    return new ConsumerRecord<>(
        "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body, headers);
  }
}

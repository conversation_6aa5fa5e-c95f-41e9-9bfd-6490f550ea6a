# Generate code from the spec by running `mvn compile`.
# To lint this API, get review or help: http://in/api-help.
openapi: 3.0.1
info:
  title: Documents API
  description: |
    The Documents API allows you to manage documents in a simple and efficient way. 
    It provides various operations such as listing, creating, updating, and deleting documents. 
    To use the API, you need to authenticate using either BearerAuth or OAuth2. 
    The API supports webhooks for document updates.
  version: 0.1.0
  contact: { }
servers:
  # servers are the environments that your API is deployed to
  - description: Local development
    url: https://localhost:8443
  - description: QAL
    url: https://service-qal.api.intuit.com
  - description: E2E
    url: https://service-e2e.api.intuit.com
  - description: Sandbox
    url: https://service-sandbox.api.intuit.com
  - description: Production
    url: https://service.api.intuit.com
tags:
  - name: documents
# the default security scheme for the API, may be overridden for specific paths
security:
  - BrowserAuth: [ ]
  - PrivateAuthPlus: [ ]
x-webhooks:
  # event types that are about the state-change of a resource
  # can be {namespace}.{resource}.{created|updated|deleted}.v1
  # event types must be lower-snake-case
  "namespace.document.updated.v1":
    post:
      requestBody:
        description: Document updated
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DocumentUpdatedEvent"
      responses:
        "200":
          description: OK
paths:
  # paths must be lower-kebab-case and start with /v1
  # do not put the version into servers field
  /v1/documents:
    get:
      summary: List documents
      description: Returns a list of documents with optional pagination.
      # operationId should be verb+noun
      # it must be be lower-camel-case
      operationId: listDocuments
      tags:
        - documents
      parameters:
        - name: cursor
          description: Pagination cursor
          schema:
            type: string
            maxLength: 64
          in: query
          example: abc123
        - name: limit
          description: The max number of paginated results.
          schema:
            type: integer
            minimum: 1
            maximum: 500
            default: 100
          example: 100
          in: query
      responses:
        "200":
          description: A list of documents
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ListDocumentsResponse"
              examples:
                ListExample:
                  value:
                    data:
                      - id: abc123
                        created_at: "2023-01-01T00:00:00.000Z"
                        username: whoami
                        content: Hello APIs!
                    metadata:
                      cursor: abc123
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              examples:
                ErrorExample:
                  value:
                    code: BAD_REQUEST
                    message: Bad request
    post:
      summary: Create document
      description: Create a document based on the payload.
      operationId: createDocument
      tags:
        - documents
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Document"
      responses:
        "201":
          description: Created
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              examples:
                ErrorExample:
                  value:
                    code: BAD_REQUEST
                    message: Bad request
  /v1/documents/{id}:
    get:
      summary: Get document
      description: Get a document by providing its unqique ID in the path parameter.
      operationId: getDocument
      tags:
        - documents
      parameters:
        - name: id
          required: true
          in: path
          schema:
            # IDs should be strings, not numbers
            type: string
            # 36 is the size of a RFC4122 UUID.
            maxLength: 36
          example: abc123
      responses:
        "200":
          description: The document
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Document"
              examples:
                GetExample:
                  value:
                    id: abc123
                    created_at: "2023-01-01T00:00:00.000Z"
                    username: whoami
                    content: Hello APIs!
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              examples:
                ErrorExample:
                  value:
                    code: BAD_REQUEST
                    message: Bad request
    put:
      summary: Update document
      description: Update an existing document by providing its unique ID in the path parameter and the updated information in the request body.
      operationId: updateDocument
      tags:
        - documents
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
            maxLength: 36
          example: abc123
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/Document"
            examples:
              PutExample:
                value:
                  id: abc123
                  created_at: "2023-01-01T00:00:00.000Z"
                  username: whoami
                  content: Hello APIs!
      responses:
        "204":
          description: Updated
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    delete:
      summary: Delete document
      description: Delete a specific document by providing its unique ID in the path parameter.
      operationId: deleteDocument
      tags:
        - documents
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
            maxLength: 36
          example: abc123
      responses:
        "204":
          description: Deleted
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
              examples:
                ErrorExample:
                  value:
                    code: BAD_REQUEST
                    message: Bad request
components:
  # valid security schemas, use these exact names (e.g. "BrowserAuth" not "browser-auth")
  # if you have a use case not covered here, contact API Governance Squad
  securitySchemes:
    # http://in/gateway-browser-auth
    BrowserAuth:
      type: apiKey
      name: Authorization
      in: header
    # http://in/gateway-oauth2
    OAuth2:
      type: oauth2
      flows:
        clientCredentials:
          tokenUrl: https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
          scopes:
            "namespace.documents": "Access and change documents"
        authorizationCode:
          authorizationUrl: https://appcenter.intuit.com/connect/oauth2
          tokenUrl: https://oauth.platform.intuit.com/oauth2/v1/tokens/bearer
          scopes:
            "namespace.documents": "Access and change documents"
    # http://in/gateway-private-auth
    PrivateAuth:
      type: apiKey
      name: Authorization
      in: header
    # http://in/gateway-private-auth-plus
    PrivateAuthPlus:
      type: apiKey
      name: Authorization
      in: header
    # http://in/gateway-simple-auth
    SimpleAuth:
      type: apiKey
      name: Authorization
      in: header
    # some services may use these schemes, but this is very rare
    # if you're using them, are you doing something wrong?
    BearerAuth:
      type: http
      scheme: bearer
    BasicAuth:
      type: http
      scheme: basic
  schemas:
    # core schema
    Error:
      description: An error response which may be shown to the end user
      type: object
      required:
        - code
      properties:
        code:
          description: Machine-readable code
          type: string
          maxLength: 64
          # values must be upper-snake-case
          pattern: ^[A-Z][A-Z0-9_]*$
          example: BAD_REQUEST
        message:
          description: Human-readable message
          type: string
          maxLength: 256
          example: Bad request
        # in pre-production the detail may include diagnostic information, such as the stack trace
        detail:
          description: Detailed human-readable information
          type: string
          maxLength: 4096
          example: The request body was malformed.
      example:
        code: BAD_REQUEST
        message: Bad request
    ListMetaData:
      type: object
      required:
        - cursor
      properties:
        cursor:
          type: string
          maxLength: 64
          example: abc123
      example:
        cursor: abc123
    # models
    Document:
      type: object
      required:
        - content
      properties:
        id:
          type: string
          example: abc123
          maxLength: 36
        # fields should be lower-snake-case
        created_at:
          type: string
          format: date-time
          example: 2023-01-01T00:00:00.000Z
        username:
          type: string
          maxLength: 64
          example: whoami
        content:
          type: string
          maxLength: 256
          example: Hello APIs!
      example:
        id: abc123
        created_at: "2023-01-01T00:00:00.000Z"
        username: whoami
        content: Hello APIs!
    # requests and responses
    ListDocumentsResponse:
      required:
        - data
        - metadata
      properties:
        data:
          type: array
          items:
            $ref: "#/components/schemas/Document"
        metadata:
          $ref: "#/components/schemas/ListMetaData"
      example:
        data:
          - id: abc123
            created_at: "2023-01-01T00:00:00.000Z"
            username: whoami
            content: Hello APIs!
        metadata:
          cursor: abc123
    # events
    DocumentUpdatedEvent:
      required:
        - specversion
        - source
        - id
        - type
        - datacontenttype
        - data
      properties:
        specversion:
          type: string
          example: "1.0"
        source:
          type: string
          example: intuit
        id:
          type: string
          example: abc123
        type:
          type: string
          example: namespace.document.updated.v1
        datacontenttype:
          type: string
          example: application/json
        data:
          $ref: "#/components/schemas/Document"
      example:
        specversion: "1.0"
        source: intuit
        id: abc123
        type: my-namespace.document.updated.v1
        datacontenttype: application/json
        data:
          id: abc123
          created_at: "2023-01-01T00:00:00.000Z"
          username: whoami
          content: Hello APIs!

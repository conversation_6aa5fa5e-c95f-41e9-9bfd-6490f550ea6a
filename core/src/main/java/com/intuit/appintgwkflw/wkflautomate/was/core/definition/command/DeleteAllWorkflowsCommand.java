package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName.DELETE_ALL_WORKFLOWS_ASYNC_PROCESS;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.DeleteAuthDetailsTasks;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UnsubscribeAppConnectTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CommandUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *     <p>Initiate a downgrade command for the given company.
 */
@Component
@AllArgsConstructor
public class DeleteAllWorkflowsCommand implements DefinitionCommand {

  private final DefinitionDetailsRepository definitionDetailsRepository;

  private final ProcessDetailsRepository processDetailsRepository;

  private final AuthDetailsRepository authDetailsRepository;

  private final AppConnectService appConnectService;

  private final AuthDetailsService authDetailsService;

  private final CommandUtil commandUtil;

  private final MetricLogger metricLogger;

  /**
   * initiate Downgrade in WAS.
   *
   * <p>1. Delete Definition in Camunda for all definitions . 2. Cleanup all ProcessDetails and
   * definition details and Delete. 3. Delete auth details if all definition and process details are
   * deleted.
   *
   * @param definitionInstance input definition detail
   * @param ownerId input owner id of company for which deletion is to be initiated
   */
  public void execute(final DefinitionInstance definitionInstance, final String ownerId) {

    logInfo(
        "Initiating Delete All Workflow command for cleanup in Datastore, camunda and app-connect");

    final State inputRequest = new State();

    // fetch camunda delete task and data store delete task
    final Pair<List<Task>, List<Task>> camundaAndDataStoreTaskPair =
        commandUtil.prepareCamundaAndDatastoreTasks(
            definitionInstance.getDefinitionDetailsList(), inputRequest);

    inputRequest.addValue(AsyncTaskConstants.REALM_ID_KEY, ownerId);

    final RxExecutionChain chain = new RxExecutionChain(inputRequest);

    if (!CollectionUtils.isEmpty(camundaAndDataStoreTaskPair.getLeft())) {
      chain.next(
          camundaAndDataStoreTaskPair
              .getLeft()
              .toArray(new Task[camundaAndDataStoreTaskPair.getLeft().size()]));
    }
    if (!CollectionUtils.isEmpty(camundaAndDataStoreTaskPair.getRight())) {
      chain.next(
          camundaAndDataStoreTaskPair
              .getRight()
              .toArray(new Task[camundaAndDataStoreTaskPair.getRight().size()]));
    }

    chain
        .next(new UnsubscribeAppConnectTask(appConnectService, authDetailsService))
        .next(
            new DeleteAuthDetailsTasks(
                authDetailsRepository, definitionDetailsRepository, processDetailsRepository))
        .executeAsyncWithObv()
        .subscribe(
            next -> {
              // done with execution
            },
            error -> {
              logError(
                  error,
                  "Exception occurred while performing Delete all worlkflow operation for ownerId=%s",
                  ownerId);
              metricLogger.logErrorMetric(
                  DELETE_ALL_WORKFLOWS_ASYNC_PROCESS, Type.APPLICATION_METRIC, error);
            },
            () -> {
              logInfo("Delete all workflow command completed successfully for ownerId=%s", ownerId);
            });
  }
  
  @Override
  public String getName() {
    return CrudOperation.DELETE_ALL.name();
  }

  private void logInfo(final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
  }

  private void logError(final Throwable e, final String message, final Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .stackTrace(e)
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
  }
}

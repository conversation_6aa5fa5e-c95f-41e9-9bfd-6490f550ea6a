package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.google.common.base.CharMatcher;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.hibernate.exception.ConstraintViolationException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.Collection;

/**
 * Class with utility functions used in multi-condition/multi-step workflow
 *
 * <AUTHOR>
 */
@UtilityClass
public class MultiStepUtil {

  public final int numberOfChildren = 2;
  public final int leftChildSuffix = 1;
  public final int rightChildSuffix = 2;

  /**
   * Helper method to check if templateData should be returned as part of ReadOne template response.
   * If the config for a given record and action type contains actionIdMapper (i.e. call activity
   * action blocks) then those will be returned in templateData
   *
   * @param record    record
   * @param actionKey action key
   * @return boolean
   */
  public boolean isTemplateDataForReadOneTemplate(Record record, String actionKey) {
    return Objects.nonNull(getTemplateDataForReadOneTemplate(record, actionKey));
  }

  public String getTemplateDataForReadOneTemplate(Record record, String actionKey) {
    List<ActionGroup> recordActionGroups = record.getActionGroups();
    Optional<ActionGroup> actionGroup = Optional.ofNullable(recordActionGroups.stream()
        .filter(recordActionGroup -> recordActionGroup.getId().equalsIgnoreCase(actionKey))
        .collect(Collectors.toList()).stream().findFirst().orElse(null));
    if(actionGroup.isPresent() &&
        Objects.nonNull(actionGroup.get().getActionIdMapper()) &&
        Objects.nonNull(actionGroup.get().getActionIdMapper().getActionId())) {
      return actionGroup.get().getActionIdMapper().getActionId();
    }
    return null;
  }

  /**
   * This function creates and returns a map built using the config steps defined in custom workflow
   * config
   *
   * @param customWorkflowConfig custom workflow config
   * @param record               record type object
   * @param actionKey            action key
   * @param isPreCannedTemplate  whether template is precanned/custom
   * @return map of config steps
   */
  public Map<Integer, Steps> buildConfigStepIdMap(
      CustomWorkflowConfig customWorkflowConfig,
      Record record,
      String actionKey,
      boolean isPreCannedTemplate) {
    Map<Integer, Steps> configStepMap = new LinkedHashMap<>();
    List<Steps> steps;
    if (isPreCannedTemplate) {
      steps = record.getSteps();
    } else {
      Optional<ActionGroup> recordActionGroup = record.getFilteredActiongroup(actionKey);
      Optional<ConfigTemplate> template = Optional.ofNullable(
          customWorkflowConfig.getConfigTemplates()
              .stream().filter(configTemplate -> configTemplate.getId()
                  .equalsIgnoreCase(recordActionGroup.get().getPrecannedTemplateId()))
              .collect(Collectors.toList()).stream().findFirst().orElse(null));
      steps = template.isPresent() ? template.get().getSteps() : Collections.emptyList();
    }
      if (CollectionUtils.isEmpty(steps)) {
          return Collections.emptyMap();
      }
    steps.stream().forEach(stepObject -> {
      configStepMap.put(stepObject.getStepId(), stepObject);
    });
    return configStepMap;
  }

  /**
   * TODO - clean this up when we migrate all approval workflows to multi-step
   * <p>
   * This function checks if templateData is sent as part of readOne
   * graphql query or not. Based on boolean result we send templateData
   * back in the response otherwise not
   *
   * @param subQueries graphql subqueries sent as part of readOne
   * @return boolean true/false
   */
  public boolean isTemplateDataPassed(List<Query.PreparedQuery> subQueries) {
    if (CollectionUtils.isEmpty(subQueries)) {
      return false;
    }
    boolean isTemplateDataPresent = subQueries.stream().anyMatch(preparedQuery ->
        preparedQuery.getName().equalsIgnoreCase(WorkflowConstants.TEMPLATE_DATA));
    if (isTemplateDataPresent) {
      return true;
    }
    Query.PreparedQuery templateQuery = subQueries.stream().filter(
            preparedQuery -> preparedQuery.getName().equalsIgnoreCase(WorkflowConstants.TEMPLATE_QUERY))
        .collect(Collectors.toList()).stream().findFirst().orElse(null);
    return Objects.nonNull(templateQuery) && templateQuery.getSubQueries().stream()
        .anyMatch(preparedQuery ->
            preparedQuery.getName().equalsIgnoreCase(WorkflowConstants.TEMPLATE_DATA));
  }

    /**
     * This function transforms the evaluate api response for multi-condition workflow
     * from string value(activityId/false) to a boolean value(true/false).
     * For example:
     * <p>1. If input = "decisionResult": "action-2", then output = "decisionResult": "true"</p>
     * <p>2. If input = "decisionResult": "true", then output = "decisionResult": "true"</p>
     * <p>3. If input = "decisionResult": "false", then output = "decisionResult": "false"</p>
     *
     * @param response List of Map<String, Object>
     * @return List of Map<String, Object>
     */
    public List<Map<String, Object>> multiConditionResponseTransformer(
        List<Map<String, Object>> response) {
        if (Objects.isNull(response) || response.isEmpty()) {
            return response;
        }
        Map<String, Object> dmnResponseMap = response.stream().findFirst().get();
        Object dmnResultValue = dmnResponseMap.values().stream().findFirst().get();
        dmnResultValue = CustomWorkflowUtil.isFalse(String.valueOf(dmnResultValue)) ? Boolean.FALSE
            : Boolean.TRUE;
        dmnResponseMap.put(WorkflowConstants.CUSTOM_DECISION_RESULT, dmnResultValue);
        List<Map<String, Object>> outputResponse = new ArrayList<>();
        outputResponse.add(dmnResponseMap);
        return outputResponse;
    }

    /**
     * This function checks if the given next label is of yes type
     *
     * @param nextLabel
     * @return boolean
     */
    public boolean isYesLabel(NextLabelEnum nextLabel) {
        return !isNoLabel(nextLabel);
    }

    /**
     * This function checks if the given next label is of no type
     *
     * @param nextLabel
     * @return boolean
     */
    public boolean isNoLabel(NextLabelEnum nextLabel) {
        return nextLabel == NextLabelEnum.NO;
    }

    /**
     * This function checks if the workflow step is of action type or not
     * and has an actionGroup block
     * @param workflowStep
     * @return boolean
     */
    public boolean isActionStep(WorkflowStep workflowStep) {
        return workflowStep.getStepType() == StepTypeEnum.ACTION &&
            Objects.nonNull(workflowStep.getActionGroup());
    }

    /**
     * This function checks if the workflow step is of condition type or not
     * and has a condition block
     * @param workflowStep
     * @return boolean
     */
    public boolean isConditionStep(WorkflowStep workflowStep) {
        return workflowStep.getStepType() == StepTypeEnum.CONDITION &&
            Objects.nonNull(workflowStep.getWorkflowStepCondition());
    }

  /**
   * This function checks if the workflow step is of WORKFLOWSTEP type or not
   * and has both condition and actionGroup blocks
   * @param workflowStep
   * @return boolean
   */
  public boolean isCompositeStep(WorkflowStep workflowStep) {
    return workflowStep.getStepType() == StepTypeEnum.WORFKLOWSTEP &&
        Objects.nonNull(workflowStep.getWorkflowStepCondition()) &&
        Objects.nonNull(workflowStep.getActionGroup());
  }

    /**
     * This function computes the output of left child index by using the current node index.
     * <p>For example, if number of children = 2, then left Child Index formula becomes = 2 * i + 1</p>
     *
     * @param currentNodeIndex
     * @return left child index
     */
    public int computeLeftChildIndex(Integer currentNodeIndex) {
        return numberOfChildren * currentNodeIndex + leftChildSuffix;
    }

    /**
     * This function computes the output of right child index by using the current node index.
     * <p>For example, if number of children = 2, then right Child Index formula becomes = 2 * i + 2</p>
     *
     * @param currentNodeIndex
     * @return right child index
     */
    public int computeRightChildIndex(Integer currentNodeIndex) {
        return numberOfChildren * currentNodeIndex + rightChildSuffix;
    }

    /**
     * This function checks if the given definition is of multi-condition type or not by checking
     * <p>1. if next array exists in the create/update definition payload</p>
     * <p>2. if the workflow has only one action step</p>
     *
     * @param definition
     * @return boolean
     */
    public boolean isMultiCondition(Definition definition) {
        return
            ObjectUtils.isNotEmpty(findFirstWorkflowStep(definition.getWorkflowSteps()).getNext())
                || isSingleStepMultiCondition(definition);
    }

  /**
   * This function checks if the given definition is of multi-condition type or not by checking if
   * the definition data (BPMN XML) is null or not in DefinitionDetails object.
   * @param definitionDetails
   * @return boolean
   */
  public boolean isMultiConditionWorkflow(DefinitionDetails definitionDetails) {
    if (Objects.isNull(definitionDetails)) {
      return false;
    }
    return Objects.isNull(definitionDetails.getDefinitionData()) &&
        Objects.nonNull(definitionDetails.getTemplateDetails()) &&
        TemplateCategory.CUSTOM.toString().equals(definitionDetails.getTemplateDetails().getTemplateCategory()) &&
            DefinitionType.SINGLE.equals(definitionDetails.getTemplateDetails().getDefinitionType());
  }

    /**
     * This function is used to check if the definition has only one action step present in the workflow
     * without any condition step.
     *
     * @param definition
     * @return boolean
     */
    public boolean isSingleStepMultiCondition(Definition definition) {
        WorkflowStep firstWorkflowStep = findFirstWorkflowStep(definition.getWorkflowSteps());
        return isActionStep(firstWorkflowStep);
    }

  /**
   * This function generates workflowStepId based on the stepType and localId passed
   *
   * @param stepType          workflowStep type
   * @param localId           local id
   * @param wasContextHandler context object
   * @return workflowStepId (GlobalId)
   */
  public GlobalId generateWorkflowStepId(
      String stepType,
      String localId,
      WASContextHandler wasContextHandler) {
    return GlobalId.builder()
        .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
        .setTypeId(stepType)
        .setLocalId(localId
            .concat(WorkflowConstants.HYPHEN)
            .concat(UUID.randomUUID().toString()))
        .build();
  }

  /**
   * This function gets the key from dmnHeadersMap basis value passed
   *
   * @param dmnHeadersMap  map of dmn input columns
   * @param dmnHeaderValue value
   * @return corresponding key from the map
   */
  public String getDmnHeaderKey(Map<String, Map<String, DmnHeader>> dmnHeadersMap,
      DmnHeader dmnHeaderValue) {
    return dmnHeadersMap.get(WorkflowConstants.INPUT).entrySet().stream()
        .filter(entry -> dmnHeaderValue.equals(entry.getValue()))
        .map(Map.Entry::getKey).findFirst().get();
  }

  /**
   * This function gets the value from dmnHeadersMap basis key passed
   *
   * @param dmnHeadersMap map of dmn input columns
   * @param indexValue    key
   * @return corresponding value from the map
   */
  public DmnHeader getDmnHeaderValue(Map<String, Map<String, DmnHeader>> dmnHeadersMap,
      int indexValue) {
    return dmnHeadersMap.get(WorkflowConstants.INPUT).values()
        .stream().filter(dmnHeader -> dmnHeader.getColumnIndex()
            .equals(indexValue)).findFirst().get();
  }

  /**
   * This function filters the actionGroup from the config record object basis the action key value
   * passed
   *
   * @param record    custom workflow config record object
   * @param actionKey action key
   * @return actionGroup filtered by action key
   */
  public ActionGroup getFilteredRecordActionGroup(Record record, String actionKey) {
    return record.getActionGroups().stream().filter(actionGroup ->
        Objects.nonNull(actionKey) && actionGroup.getId().equals(actionKey)
            && Objects.nonNull(actionGroup.getActionIdMapper())
            && Objects.nonNull(actionGroup.getActionIdMapper()
            .getActionId())).findFirst().orElse(null);
  }

    /**
     * This function is used to check if the current element id passed is a Business Rule Task or not
     *
     * @param elementId
     * @param definitionInstance
     * @return boolean
     */
    public boolean isDmnStep(String elementId, DefinitionInstance definitionInstance) {
        BaseElement baseElement = definitionInstance.getBpmnModelInstance()
            .getModelElementById(elementId);
        return baseElement instanceof BusinessRuleTask;
    }

    /**
     * It finds the first workflow step from the payload (root workflow step of the tree). Building a
     * set of nexts of all workflow steps and then checking if the stepId is present in the nextSet or
     * not
     *
     * @param workflowSteps
     * @return firstWorkflowStep, which has no incoming nexts
     */
    public WorkflowStep findFirstWorkflowStep(List<WorkflowStep> workflowSteps) {
        Set<String> allWorkflowStepNextIds = workflowSteps.stream().map(WorkflowStep::getNext)
            .flatMap(Collection::stream).collect(Collectors.toList()).stream()
            .map(WorkflowStep.StepNext::getWorkflowStepId).collect(Collectors.toSet());
        return workflowSteps.stream().filter(workflowStep ->
            !allWorkflowStepNextIds.contains(workflowStep.getId().toString())).findFirst().get();
    }

  public List<WorkflowStep> findRootSiblings(List<WorkflowStep> workflowSteps) {
    Set<String> allWorkflowStepNextIds = workflowSteps.stream().map(WorkflowStep::getNext)
        .flatMap(Collection::stream).collect(Collectors.toList()).stream()
        .map(WorkflowStep.StepNext::getWorkflowStepId).collect(Collectors.toSet());
    return workflowSteps.stream().filter(workflowStep ->
        !allWorkflowStepNextIds.contains(workflowStep.getId().toString())).collect(Collectors.toList());
  }

  /**
   * Its an util method to find the root workflows
   *
   * @param configStepIdMap Map of stepId and step details
   * @return List<Steps> list of all root workflow steps. In case of Multi-split, its a list.
   */
  public List<Steps> findFirstWorkflowSteps(Map<Integer, Steps> configStepIdMap) {
    List<Steps> rootSteps = new ArrayList<>();
    Set<Integer> nextSteps = new HashSet<>();
//  traverse the map and convert to a set with next stepids
    for (Map.Entry<Integer, Steps> stepEntry : configStepIdMap.entrySet()) {
      if (Objects.nonNull(stepEntry.getValue().getNexts())) {
        stepEntry.getValue().getNexts().stream().
            forEach(next -> nextSteps.add(Integer.parseInt(next.getStepId())));
      }
    }

    for (Map.Entry<Integer, Steps> stepEntry : configStepIdMap.entrySet()) {
      if (!nextSteps.contains(stepEntry.getKey())) {
        rootSteps.add(stepEntry.getValue());
      }
    }

    rootSteps.sort(Comparator.comparing(Steps::getStepId));
    return rootSteps;
  }

  /**
     * This function checks if decisionResult is of type condition or action ex - true and 1, 2 will
     * return true "action-2" will return false
     *
     * @param decisionResult dmn decisionResult value
     * @return boolean
     */
    public boolean isDecisionResultTypeCondition(String decisionResult) {
        return StringUtils.isNumeric(decisionResult) || CustomWorkflowUtil.isFalse(decisionResult.replace("\"", ""));
    }

    /**
     * This function detects and returns the typeId based on the dmn's decisionResult value that is to
     * be used while generating the stepId for a particular workflowStep
     *
     * @param decisionResult dmn decision result value
     * @return step type id
     */
    public String getWorkflowStepTypeId(String decisionResult) {
        if (StringUtils.isNumeric(decisionResult) || Boolean.parseBoolean(decisionResult)) {
            return WorkflowConstants.CUSTOM_DECISION_ELEMENT;
        } else {
            return String.valueOf(CharMatcher.is('\'').trimFrom(decisionResult));
        }
    }

  /**
   * This function checks if the list of inputEntries for a particular rule are all
   * empty or not. Except the index column, all other column values must be empty
   * for our function to return false, otherwise we return true.
   *
   * ex:
   * 1. The following type of rule will return true as a result -
   * Index        |      TxnAmount   | decisionResult
   * Index == 0   |         -        | "action-3"
   *
   * 2. The following type of rule will return false as a result -
   * Index        | TxnAmount        | decisionResult
   * Index == 0   | TxnAmount > 100  | "action-3"
   *
   * @param ruleInputEntries list of inputEntries from a rule
   * @return boolean true/false
   */
  public boolean isNotEmptyRuleLine(List<InputEntry> ruleInputEntries) {
    Optional<InputEntry> emptyRule = IntStream.range(1, ruleInputEntries.size())
        .mapToObj(index -> ruleInputEntries.get(index)).filter(inputEntry ->
            Objects.nonNull(inputEntry.getText()) && StringUtils.isNotEmpty(
                inputEntry.getText().getTextContent())).findAny();
    return emptyRule.isPresent();
  }

  /**
   * Fetch the parent ProcessDetails. If its empty then fetch the current process details
   * @param processDetails
   * @return
   */
  public ProcessDetails fetchParentProcessDetails(ProcessDetails processDetails) {
    return Objects.nonNull(processDetails.getParentProcessDetails())
        ? processDetails.getParentProcessDetails() : processDetails;
  }

  /**
   * Util function to know,whether a given Process id is a child process or not
   * @param processDetails
   * @return
   */
  public boolean isChildProcess(ProcessDetails processDetails) {
    return Objects.nonNull(processDetails.getParentId());
  }

  /**
   * Fills the processdetails with parent process details.
   *
   * @param processDetails
   * @param processDetailsRepository
   * @throws WorkflowGeneralException Parallel activities can lead to same processIds saved to
   *                                      DB resulting in primary key violation error. Gracefully
   *                                      rollback the transaction and doesn't publish to
   *                                      outbox(Since it will be already be published)
   */
  public void fillParentProcessDetails(ProcessDetails processDetails,
      ProcessDetailsRepository processDetailsRepository) {
    try {
      if (isChildProcess(processDetails)) {
        ProcessDetails parentProcessDetails = processDetailsRepository.findByProcessId(
                processDetails.getParentId())
            .orElse(null);
        processDetails.setParentProcessDetails(parentProcessDetails);
      }
    } catch (ConstraintViolationException e) {
      //SpringBoot uses Default isolation level defined in RDBMS.
      //For Postgres it is "Read-committed", hence it throws unique constraint violation error even for read calls.
      throw new WorkflowGeneralException(WorkflowError.PROCESS_DETAILS_CONSTRAINT_VIOLATION_ERROR, e);
    }
  }

  /**
   * Get the recordType from the definitionDetails . RecordType can be empty for called processes.
   * In those cases, fetch the entityType from parent definition. If still parent
   * @param processDetails
   * @return
   */
  public String getEntityType(ProcessDetails processDetails) {
    RecordType recordType = processDetails.getDefinitionDetails().getRecordType();
    if (Objects.nonNull(recordType)) {
      return recordType.getRecordType();
    } else if (isChildProcess(processDetails) && Objects.nonNull(
        processDetails.getParentProcessDetails().getDefinitionDetails().getRecordType())) {
      return processDetails.getParentProcessDetails().getDefinitionDetails().getRecordType().getRecordType();
    } else {
      return null;
    }
  }

  /**
   * This function tells if the given activityInstance is of dmn(condition) type
   *
   * @param activityInstance
   * @return
   */
  public Boolean isDmnTypeActivityInstance(ActivityInstance activityInstance) {
    return ObjectUtils.isEmpty(activityInstance.getChildActivityInstances()) &&
        Objects.nonNull(activityInstance.getDmnModelInstance());
  }

  /**
   * This function tells if the given activityInstance is of call activity type
   *
   * @param activityInstance
   * @return
   */
  public Boolean isCallActivityTypeActivityInstance(ActivityInstance activityInstance) {
    return !isDmnTypeActivityInstance(activityInstance);
  }

  public int computeChildIndex(Integer currentNodeIndex, Integer maxNumberOfChildren, Integer childCounter) {
    return maxNumberOfChildren * currentNodeIndex + childCounter;
  }


  /**
   * This function checks if the current rule is of type DAYS for multi-condition reminder
   *
   * @param rule
   * @return
   */
//  Will always have a single rule since it is the last condition before action for composite type
  public boolean isRuleTypeComposite(RuleLine.Rule rule) {
    return (Objects.nonNull(rule)
        && FieldTypeEnum.DAYS.equals(rule.getParameterType()));
  }


  /**
   * This function checks if the current step is of type DAYS for multi-condition reminder
   * @param  currentConfigStep
   * @return
   */

  public boolean isStepTypeComposite(Steps currentConfigStep) {
    if (ObjectUtils.isEmpty(currentConfigStep.getAttributes())) {
      return false;
    }
    Optional<Attribute> dayAttribute = currentConfigStep.getAttributes().stream()
        .filter(
            attribute -> ObjectUtils.isNotEmpty(attribute.getType()) && FieldTypeEnum.DAYS.name()
                .equals(attribute.getType().toUpperCase()))
        .findFirst();
    return dayAttribute.isPresent();
  }

  /**
   * This function returns the parent workflow step of a given child workflow step.
   *
   * @param workflowStepList
   * @param childWorkflowStepId
   * @return
   */
  public WorkflowStep getParentWorkflowStep(List<WorkflowStep> workflowStepList,
      GlobalId childWorkflowStepId) {
    return workflowStepList.stream()
        .filter(workflowStep -> Objects.nonNull(workflowStep.getNext()) &&
            workflowStep.getNext().stream()
                .filter(stepNext -> stepNext != null && stepNext.getWorkflowStepId() != null)
                .anyMatch(stepNext ->
                    stepNext.getWorkflowStepId().equals(childWorkflowStepId.toString())))
        .findFirst().orElse(null);
  }

  /**
   * This function checks that the current template step is a leaf node or not
   * @param configStep
   * @return
   */
  public boolean isLeafNode(Steps configStep){
    return (CollectionUtils.isEmpty(configStep.getNexts()) || isStepTypeComposite(configStep));
  }

  /**
   * This function returns the path of all the workflow step conditions for a particular call activity
   * @param currentStep
   * @param leafNodeId
   * @param workflowStepMap
   * @return
   */
  // current step will never be null
  public List<WorkflowStep> fetchListOfNodesInPath(WorkflowStep currentStep, String leafNodeId, Map<String, WorkflowStep> workflowStepMap){
    List<WorkflowStep> stepsInPath = new ArrayList<>();
    // Base case
    if (leafNodeId.equalsIgnoreCase(String.valueOf(currentStep.getId()))) {
      stepsInPath.add(currentStep);
      return stepsInPath;
    }

    if (CollectionUtils.isEmpty(currentStep.getNext())) {
      return stepsInPath;
    }

    // add to list
    for(WorkflowStep.StepNext next : currentStep.getNext()){
      List<WorkflowStep> children = fetchListOfNodesInPath(workflowStepMap.get(next.getWorkflowStepId()), leafNodeId, workflowStepMap);
      if(!children.isEmpty()){
        stepsInPath.add(currentStep);
        stepsInPath.addAll(children);
        break;
      }
    }
    return stepsInPath;
  }

}

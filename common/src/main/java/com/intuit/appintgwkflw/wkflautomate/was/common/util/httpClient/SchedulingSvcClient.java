package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.SCHEDULING_SVC;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;

/** <AUTHOR> */
@AllArgsConstructor
@Retry(name = ResiliencyConstants.HTTP_CLIENT_RETRY)
@Component
public class SchedulingSvcClient {

    private WASHttpClient client;

    @ServiceMetric(serviceName = SCHEDULING_SVC, type = HTTP)
    public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
            final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {
        return client.httpResponse(wasHttpRequest);
    }
}

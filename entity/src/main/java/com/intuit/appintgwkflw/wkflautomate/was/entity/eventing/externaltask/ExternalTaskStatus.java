package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Getter
@AllArgsConstructor
@Slf4j
public enum ExternalTaskStatus {
  SUCCESS("success"),
  FAILED("failed"),
  BLOCKED("blocked"),
  IN_PROCESS("in-process"),
  PAUSED("paused"),
  EXTEND_LOCK("extend-lock"),
  FAIL_WITH_RETRY("fail-with-retry");
	

  private String status;

  public static ExternalTaskStatus fromStatus(String status) {
    for (ExternalTaskStatus externalTaskStatus : ExternalTaskStatus.values()) {
      if (externalTaskStatus.status.equalsIgnoreCase(status)) {
        return externalTaskStatus;
      }
    }
    log.info("ExternalTaskStatus :: {} not qualified as enum.", status);
    return null;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TriggerType;

import javax.persistence.AttributeConverter;

/** <AUTHOR> */
public class TriggerDetailsConverter implements AttributeConverter<TriggerType, String> {

  @Override
  public String convertToDatabaseColumn(TriggerType attribute) {
    return attribute != null ? attribute.getTriggerType() : null;
  }

  @Override
  public TriggerType convertToEntityAttribute(String dbData) {
    return dbData != null ? TriggerType.fromType(dbData) : null;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.camunda.bpm.dmn.xlsx.AdvancedSpreadsheetAdapter;
import org.camunda.bpm.dmn.xlsx.XlsxConverter;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionRule;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.ws.rs.Consumes;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import org.apache.commons.io.FilenameUtils;

/**
 * <AUTHOR> <br>
 *     </>This controller converts Excel into a DMN file and returns xml as response
 */
@RestController
@RequestMapping("/v1")
@AllArgsConstructor
public class ExcelToDmnController {

  private static final String REGEX_STR = "<text>\"\"(.*)\"\"<\\/text>";
  private static final String REGEX_DASH = "<text>\"(-)\"<\\/text>";
  private static final String DATE_MODIFIER = "date";
  private static final String OUTPUT_FILENAME = "output.dmn";
  private static final String JUEL_EXPRESSION = "juel";

  @Metric(name = MetricName.CREATE_DMN_DEFINITION, type = Type.API_METRIC)
  @PostMapping(value = "/createDmn")
  @Consumes(MediaType.MULTIPART_FORM_DATA)
  @Produces(MediaType.APPLICATION_OCTET_STREAM)
  public ResponseEntity<Object> createDmn(
      @RequestParam final MultipartFile excel,
      @RequestHeader(value = "download_path", required = false, defaultValue = "")
          final String downloadPath)
      throws IOException {
    final String fileName = FilenameUtils.getName(downloadPath) + OUTPUT_FILENAME;
    this.validate(excel, fileName);
    // upload to s3 or can be mailed to provided email, can be done later on.
    return this.prepareResponse(fileName);
  }

  /**
   * Verify if DMN and Excel are both provided
   *
   * @param excel
   * @param fileName
   */
  private void validate(MultipartFile excel, String fileName) throws IOException {

    if (!excel.getOriginalFilename().endsWith(WorkflowConstants.XLSX)) {
      throw new WorkflowGeneralException(WorkflowError.INVALID_FILE_FORMAT);
    }
    // Parse excel for rules
    InputStream xlsxInputStream = new BufferedInputStream(excel.getInputStream());

    // Convert using Camunda' DMN Excel convertor
    XlsxConverter converter = new XlsxConverter();
    converter.setIoDetectionStrategy(new AdvancedSpreadsheetAdapter());
    DmnModelInstance dmnModelInstance = converter.convert(xlsxInputStream);
    dmnModelInstance = setExpressionLanguageToJuel(dmnModelInstance);

    // write
    OutputStream dmnOutputStream =
        new BufferedOutputStream(new FileOutputStream(fileName)); // open output stream to file here

    String dmnXmlString =
        Dmn.convertToString(dmnModelInstance)
            .replaceAll(REGEX_STR, "<text>\"$1\"<\\/text>")
            .replaceAll(REGEX_DASH, "<text><\\/text>");

    final DmnModelInstance dmnModelInstanceNew =
        Dmn.readModelFromStream(
            new ByteArrayInputStream(dmnXmlString.getBytes(StandardCharsets.UTF_8)));

    handleDateTypes(dmnModelInstanceNew);
    Dmn.writeModelToStream(dmnOutputStream, dmnModelInstanceNew);
  }

  /**
   * This method sets default expression language to Juel
   *
   * @param dmnModelInstance
   * @return
   */
  private DmnModelInstance setExpressionLanguageToJuel(DmnModelInstance dmnModelInstance) {
    dmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
        .map(rule -> rule.getRules())
        .flatMap(rules -> rules.stream().map(rule -> rule.getInputEntries()))
        .flatMap(
            inputEntries ->
                inputEntries.stream()
                    .map(
                        inputEntry -> {
                          inputEntry.setExpressionLanguage(JUEL_EXPRESSION);
                          return inputEntry;
                        }))
        .collect(Collectors.toList());
    dmnModelInstance.getDefinitions().setExpressionLanguage(JUEL_EXPRESSION);
    return dmnModelInstance;
  }

  /**
   * Return Workflow DMN response
   *
   * @param fileName
   * @return
   */
  private ResponseEntity<Object> prepareResponse(String fileName) throws FileNotFoundException {
    final File file = new File(fileName);
    InputStreamResource inputStreamResource = new InputStreamResource(new FileInputStream(file));
    HttpHeaders headers = prepareHeaders(fileName);
    ResponseEntity<Object> responseEntity =
        ResponseEntity.ok()
            .headers(headers)
            .contentLength(file.length())
            .contentType(org.springframework.http.MediaType.parseMediaType("application/xml"))
            .body(inputStreamResource);
    return responseEntity;
  }

  /**
   * Prepare Response Headers
   *
   * @param fileName
   * @return
   */
  private HttpHeaders prepareHeaders(String fileName) {
    HttpHeaders headers = new HttpHeaders();
    headers.add("Content-Disposition", "attachment; filename=" + fileName);
    headers.add("Content-Type", "application/xml");
    headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
    headers.add("Pragma", "no-cache");
    headers.add("Expires", "0");
    return headers;
  }

  private void handleDateTypes(final DmnModelInstance newDmnModelInstance) {
    Set<Integer> expSet = new HashSet<>();
    DecisionTable originalDecisionTable =
        newDmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
            .findFirst()
            .orElse(null);

    AtomicReference<AtomicInteger> counter = new AtomicReference<>(new AtomicInteger(0));
    originalDecisionTable
        .getInputs()
        .forEach(
            input -> {
              String typeRef = input.getInputExpression().getTypeRef();
              if (Objects.equals(typeRef, DATE_MODIFIER)) {
                expSet.add(counter.get().intValue());
                counter.get().incrementAndGet();
              } else {
                counter.get().incrementAndGet();
              }
            });

    // There exists one or more attributes of type date
    if (!expSet.isEmpty()) {

      Collection<Rule> rules =
          newDmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
              .findFirst()
              .map(DecisionTable::getRules)
              .orElse(null);

      rules.stream()
          .map(DecisionRule::getInputEntries)
          .forEach(
              rule -> {
                AtomicReference<AtomicInteger> count = new AtomicReference<>(new AtomicInteger(0));
                rule.stream()
                    .forEach(
                        inputEntry -> {
                          if (expSet.contains(count.get().intValue())) {
                            if (ObjectUtils.isNotEmpty(inputEntry.getTextContent())) {
                              inputEntry
                                  .getText()
                                  .setTextContent(
                                      StringEscapeUtils.escapeXml11(
                                          inputEntry
                                              .getText()
                                              .getTextContent()
                                              .replaceAll("^\"|\"$", WorkflowConstants.BLANK)));
                            }
                            count.get().incrementAndGet();
                          } else {
                            count.get().incrementAndGet();
                          }
                        });
              });
    }
  }
}

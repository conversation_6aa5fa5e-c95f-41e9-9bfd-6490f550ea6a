package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
public class WorkflowTasksTest {

  @Mock
  private WorkflowHumanTask workflowHumanTask;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(workflowHumanTask.type()).thenReturn(TaskType.HUMAN_TASK);
    WorkflowTasks.addWorkflowTask(TaskType.HUMAN_TASK, workflowHumanTask);
  }

  @Test
  public void getTestNUll() {
    WorkflowTask workflowTask = WorkflowTasks.getWorkflowTask(null);
    Assert.assertNull(workflowTask);
  }

  @Test
  public void getTestAction() {
    WorkflowTask workflowTask = WorkflowTasks.getWorkflowTask(TaskType.HUMAN_TASK);
    Assert.assertNotNull(workflowTask);
  }

  @Test
  public void containsFalse() {
    Assert.assertFalse(WorkflowTasks.contains(null));
  }

  @Test
  public void containsTrue() {
    Assert.assertTrue(WorkflowTasks.contains(TaskType.HUMAN_TASK));
  }
}

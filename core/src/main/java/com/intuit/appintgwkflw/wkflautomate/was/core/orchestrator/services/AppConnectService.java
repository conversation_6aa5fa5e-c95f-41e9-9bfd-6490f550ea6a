package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectSaveWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectUnsubscribeResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import lombok.NonNull;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;

import java.util.List;

public interface AppConnectService {

  /**
   * Get the subscription details from App connect
   *
   * @param realmId ownerID
   * @return SubscriptionId
   */
  String getSubscriptionForApp(String realmId);

  /**
   * Create the subscription for App connect
   *
   * @param realmId ownerID
   * @return SubscriptionId
   */
  String createSubscriptionForApp(String realmId);

  /**
   * Creates the workflow in AppConnect
   *
   * @param subscriptionId app subscription id for the company
   * @param definitionId id of the definition which is deployed in workflow engine
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param definitionName {@link String} : User Provided Definition Name
   * @return {@link AppConnectSaveWorkflowResponse}
   */
  AppConnectSaveWorkflowResponse createWorkflow(
      String subscriptionId,
      String definitionId,
      BpmnModelInstance bpmnModelInstance,
      String definitionName);

  /**
   * Activate the workflow in app-connect
   *
   * @param workflowId for which activation needs to be done
   * @param subscriptionId for that company
   * @param activate If need to activate set to true, if deactivate set to false
   * @return {@link AppConnectSaveWorkflowResponse}
   */
  AppConnectSaveWorkflowResponse activateDeactivateActionWorkflow(
      String workflowId, String subscriptionId, boolean activate);

  /**
   * Disable the workflow in app-connect using offlineTicket in Auth Header
   *
   * @param workflowId for which activation needs to be done
   * @param authDetails Realm scoped AuthDetails should be non-null
   * @return {@link AppConnectSaveWorkflowResponse}
   */
  AppConnectSaveWorkflowResponse disableAppConnectWorkflow(
      String workflowId, @NonNull AuthDetails authDetails);

  /**
   * update the workflow in AppConnect
   *
   * @param workflowId workflowId
   * @param subscriptionId app subscription id for the company
   * @param definitionId id of the definition which is deployed in workflow engine known as
   *     externalId
   * @param bpmnModelInstance {@link BpmnModelInstance}
   * @param definitionName {@link String} : User Provided Definition Name
   */
  void updateWorkflow(
      @NonNull String workflowId,
      @NonNull String subscriptionId,
      @NonNull String definitionId,
      @NonNull BpmnModelInstance bpmnModelInstance,
      @NonNull String definitionName);

  /**
   * Delete the workflow in AppConnect
   *
   * @param workflowId workflowId
   * @param subscriptionId app subscription id for the company
   */
  void deleteWorkflow(@NonNull String workflowId, @NonNull String subscriptionId);

  /**
   * Delete the workflow in AppConnect with offlineTicket value for Auth Header
   *
   * @param workflowId workflowId
   * @param subscriptionId app subscription id for the company
   * @param authDetails authDetails sent so as to pick/update the offline ticket from authDetails,
   *     send null if not to be used
   */
  void deleteWorkflow(
      @NonNull String workflowId, @NonNull String subscriptionId, AuthDetails authDetails);

  /**
   * unsubscribe the workflow in app-connect. Delete all subscription and workflow records from *
   * AppConnect and invalidate any existing tokens / credentials, as appropriate
   *
   * @param authDetails : Authorization details
   * @param useOfflineTicket : boolean flag that will indicate if to use user offline ticket.
   * @return
   */
  AppConnectUnsubscribeResponse unsubscribe(AuthDetails authDetails, boolean useOfflineTicket);

  List<AppConnectWorkflowResponse> getAppConnectWorkflows(String realmId, AuthDetails authDetails);

  /**
   * This method calls the AppConnect to delete workflow(s). Multiple calls are being made to delete
   * workflow. The tasks are run in parallel.
   *
   * @param workflowIds : List of Workflow ids
   * @param authDetails : auth details of the company
   * @return
   */
  void deleteWorkflows(List<String> workflowIds, @NonNull AuthDetails authDetails);

  /**
   * This method calls the AppConnect to register Token with entityType and entityOperation
   *
   * @param realmId: the companyId for which the tokens need to be registered
   * @param entityType : the entity type of the definition
   * @param entityOperation : the operations to be registered
   * @return
   */
  void registerToken(String realmId, String entityType, String entityOperation);
}

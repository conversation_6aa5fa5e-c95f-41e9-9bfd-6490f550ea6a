
mutation addPostalAddress($request: AddPostalAddressInput!){
    addPostalAddress(request: $request) {
        id
        accountId
        accountRelationships
        personInfo {
            name {
                familyName
                givenName
            }
            contactInfo {
                addresses {
                    id
                    displayAddress {
                        address1
                        address2
                        address3
                        locality
                        region
                        postalCode
                        postalExt
                        subRegion
                        country
                    }
                    variation {
                        usage
                        purposes {
                            purpose
                            defaultPurpose
                        }
                    }
                    active
                }
            }
        }
        metadata {
            lastUpdated
        }
    }
}
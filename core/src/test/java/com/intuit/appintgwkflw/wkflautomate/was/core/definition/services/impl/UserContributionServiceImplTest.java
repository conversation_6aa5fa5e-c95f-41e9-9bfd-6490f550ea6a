package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.UCSHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.UCSConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeleteUcsTemplateResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.UcsVerifyAccessResponse;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Definition;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.mockDefinitionEntity;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.mockDefinitionEntityWithActionGroup;
import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 */
public class UserContributionServiceImplTest {
  @InjectMocks private UserContributionServiceImpl userContributionService;
  @Mock private OfflineTicketClient offlineTicketClient;
  @Mock private UCSHttpClient ucsHttpClient;
  @Mock private UCSConfig ucsConfig;
  @Mock private CustomWorkflowConfig customWorkflowConfig;
  @Mock private WASContextHandler wasContextHandler;
  @Mock private TranslationService translationService;

  @Before
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void crossAccountTestConfigDisabled() {
    final UcsVerifyAccessRequest ucsVerifyAccessRequest =
        UcsVerifyAccessRequest.builder()
            .requesterOwnerId("resId")
            .requesterUserId("requesterUserId")
            .resourceId("defId")
            .build();
    Mockito.doReturn(false).when(ucsConfig).isEnabled();
    userContributionService.verifyAccess("resId", ucsVerifyAccessRequest);
  }

  @Test
  public void crossAccountTestSameRealm() {
    final UcsVerifyAccessRequest ucsVerifyAccessRequest =
        UcsVerifyAccessRequest.builder()
            .requesterOwnerId("resId")
            .requesterUserId("requesterUserId")
            .resourceId("defId")
            .build();
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    userContributionService.verifyAccess("resId", ucsVerifyAccessRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void crossAccountTestDifferentRealmSuccessNoAccess() {
    final UcsVerifyAccessRequest ucsVerifyAccessRequest =
        UcsVerifyAccessRequest.builder()
            .requesterOwnerId("resId")
            .requesterUserId("requesterUserId")
            .resourceId("defId")
            .build();
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("resId"))
        .thenReturn("offline-header");
    WASHttpResponse<UcsVerifyAccessResponse> response =
        WASHttpResponse.<UcsVerifyAccessResponse>builder()
            .response(UcsVerifyAccessResponse.builder().permit(false).build())
            .build();
    Mockito.when(ucsConfig.getVerifyAccess()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.verifyAccess("ucsAllowId", ucsVerifyAccessRequest);
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void crossAccountTestDifferentRealmSuccessHasAccess() {
    final UcsVerifyAccessRequest ucsVerifyAccessRequest =
        UcsVerifyAccessRequest.builder()
            .requesterOwnerId("resId")
            .requesterUserId("requesterUserId")
            .resourceId("defId")
            .build();
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("resId"))
        .thenReturn("offline-header");
    WASHttpResponse<UcsVerifyAccessResponse> response =
        WASHttpResponse.<UcsVerifyAccessResponse>builder()
            .response(UcsVerifyAccessResponse.builder().permit(true).build())
            .build();
    Mockito.when(ucsConfig.getVerifyAccess()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.verifyAccess("ucsAllowId", ucsVerifyAccessRequest);
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void testObfuscateReadDefinitionConfigDisabled() {
    Definition definition = mockDefinitionEntityWithActionGroup();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().build();
    Mockito.when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(new Record());
    userContributionService.obfuscateDefinitionDetails(definition, "invoice", true);
  }

  @Test
  public void testObfuscateReadDefinitionConfigEnabled() {
    Definition definition = mockDefinitionEntityWithActionGroup();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder().recordType(RecordType.INVOICE).build();
    Mockito.when(
            customWorkflowConfig.getRecordObjForType(
                definitionDetails.getRecordType().getRecordType()))
        .thenReturn(new Record());
    userContributionService.obfuscateDefinitionDetails(definition, "invoice", true);
  }

  @Test
  public void testObfuscateReadDefinitionConfigDisabledNonMultiStep() {
    Definition definition = mockDefinitionEntityWithActionGroup();
    Mockito.when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(new Record());
    userContributionService.obfuscateDefinitionDetails(definition, "invoice", false);
  }

  @Test
  public void testObfuscateReadDefinitionConfigEnabledNonMultiStep() {
    Definition definition = mockDefinitionEntityWithActionGroup();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder().recordType(RecordType.INVOICE).build();
    Mockito.when(
            customWorkflowConfig.getRecordObjForType(
                definitionDetails.getRecordType().getRecordType()))
        .thenReturn(new Record());
    userContributionService.obfuscateDefinitionDetails(definition, "invoice", false);
  }

  @Test
  public void testDeleteDefinition() {
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(wasContextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("realm-id");
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.anyString()))
        .thenReturn("offline-header");

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
        WASHttpResponse.<List<DeleteUcsTemplateResponse>>builder()
            .response(
                List.of(
                    DeleteUcsTemplateResponse.builder()
                        .success(true)
                        .id("1")
                        .message("message")
                        .statusCode("0001")
                        .build()))
            .build();
    Mockito.when(ucsConfig.getDelete()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.deletePublishedTemplate("1");
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void testDeleteDefinitionUcsConfigOff() {
    Mockito.doReturn(false).when(ucsConfig).isEnabled();
    userContributionService.deletePublishedTemplate("1");
    Mockito.verify(ucsHttpClient, Mockito.times(0)).httpResponse(any());
  }

  @Test
  public void testDeleteAllPublishedTemplates() {
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.anyString()))
        .thenReturn("offline-header");

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
        WASHttpResponse.<List<DeleteUcsTemplateResponse>>builder()
            .response(
                List.of(
                    DeleteUcsTemplateResponse.builder()
                        .success(true)
                        .id("1")
                        .message("message")
                        .statusCode("0001")
                        .build()))
            .build();
    Mockito.when(ucsConfig.getDelete()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.deleteAllPublishedTemplates("realm");
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void testDeleteAllPublishedTemplatesUcsConfigOff() {
    Mockito.doReturn(false).when(ucsConfig).isEnabled();
    userContributionService.deleteAllPublishedTemplates("1234");
    Mockito.verify(ucsHttpClient, Mockito.times(0)).httpResponse(any());
  }

  @Test
  public void testDeleteAllPublishedTemplatesCustomList() {
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.anyString()))
            .thenReturn("offline-header");

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
            WASHttpResponse.<List<DeleteUcsTemplateResponse>>builder()
                    .response(
                            List.of(
                                    DeleteUcsTemplateResponse.builder()
                                            .success(true)
                                            .id("1")
                                            .message("message")
                                            .statusCode("0001")
                                            .build()))
                    .build();
    Mockito.when(ucsConfig.getDelete()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.deleteAllPublishedTemplates("realmId",List.of("def-key1"));
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void testDeleteAllPublishedTemplatesCustomListEmpty() {
    Mockito.doReturn(true).when(ucsConfig).isEnabled();
    Mockito.when(offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(Mockito.anyString()))
            .thenReturn("offline-header");

    WASHttpResponse<List<DeleteUcsTemplateResponse>> response =
            WASHttpResponse.<List<DeleteUcsTemplateResponse>>builder()
                    .response(
                            List.of(
                                    DeleteUcsTemplateResponse.builder()
                                            .success(true)
                                            .id("1")
                                            .message("message")
                                            .statusCode("0001")
                                            .build()))
                    .build();
    Mockito.when(ucsConfig.getDelete()).thenReturn("url");
    Mockito.doReturn(response).when(ucsHttpClient).httpResponse(any());
    userContributionService.deleteAllPublishedTemplates("realmId",Collections.EMPTY_LIST);
    Mockito.verify(ucsHttpClient, Mockito.times(1)).httpResponse(any());
  }

  @Test
  public void testDeleteAllPublishedTemplatesUcsConfigOffCustomList() {
    Mockito.doReturn(false).when(ucsConfig).isEnabled();
    userContributionService.deleteAllPublishedTemplates("realmId",List.of("def-key1"));
    Mockito.verify(ucsHttpClient, Mockito.times(0)).httpResponse(any());
  }
}

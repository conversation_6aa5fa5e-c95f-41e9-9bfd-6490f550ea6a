package com.intuit.appintgwkflw.wkflautomate.was.app.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.AsyncTaskManager;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import com.intuit.appintgwkflw.wkflautomate.was.worker.config.WorkerGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.cloud.context.scope.refresh.RefreshScopeRefreshedEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RegionConfigTest {

    @Mock
    private WorkerGenerator generator;

    @Mock
    private KafkaConsumerStarter consumerStarter;

    private List<AsyncTaskManager> asyncTaskManagers;

    @Mock
    private AsyncTaskManager asyncTaskManager;

    @InjectMocks
    private RegionConfig regionConfig;

    @BeforeEach
    public void setUp() {
        regionConfig.setActive(true);
        regionConfig.setConsumerActive(true);
        asyncTaskManagers = List.of(asyncTaskManager);
        ReflectionTestUtils.setField(regionConfig, "asyncTaskManager", asyncTaskManagers);
    }

    @Test
    public void testOnRefreshScopeRefreshed_Active() {
        RefreshScopeRefreshedEvent event = mock(RefreshScopeRefreshedEvent.class);

        regionConfig.onRefreshScopeRefreshed(event);

        verify(generator).startWorkers();
        verify(consumerStarter).startAllConsumers();
        verify(asyncTaskManager).start();
    }

    @Test
    public void testOnRefreshScopeRefreshed_Inactive() {
        regionConfig.setActive(false);
        regionConfig.setConsumerActive(false);
        RefreshScopeRefreshedEvent event = mock(RefreshScopeRefreshedEvent.class);

        regionConfig.onRefreshScopeRefreshed(event);

        verify(generator).stopWorkers();
        verify(consumerStarter).stopAllConsumers();
        verify(asyncTaskManager).stop();
    }

    @Test
    public void testStopPolling() {
        ContextClosedEvent event = mock(ContextClosedEvent.class);

        regionConfig.stopPolling(event);

        verify(generator).stopWorkers();
        verify(asyncTaskManager).stop();
    }

    @Test
    public void testOnAppStarted_Active() {
        ApplicationStartedEvent event = mock(ApplicationStartedEvent.class);

        regionConfig.onAppStarted(event);

        verify(generator).startWorkers();
        verify(consumerStarter).startAllConsumers();
        verify(asyncTaskManager).start();
    }

    @Test
    public void testOnAppStarted_Inactive() {
        regionConfig.setActive(false);
        regionConfig.setConsumerActive(false);
        ApplicationStartedEvent event = mock(ApplicationStartedEvent.class);

        regionConfig.onAppStarted(event);

        verify(generator, never()).startWorkers();
        verify(consumerStarter, never()).startAllConsumers();
        verify(asyncTaskManager, never()).start();
    }
}
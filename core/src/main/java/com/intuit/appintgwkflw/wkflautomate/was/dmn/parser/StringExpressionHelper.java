package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/** This class contains methods for string expression conversion for DMN and UI. */
@UtilityClass
public class StringExpressionHelper {

  private final Pattern exprValPattern = Pattern.compile("\\\"(.*?)\\\"");

  /**
   * Select ALL check for Contains Use Case. Returns true if it is case of Select All
   *
   * @param parameterName : Name of the DMN Parameter [ex, Customer,Department]
   * @param expression : input expression/rule to be set.
   * @return
   */
  public boolean isSelectAllRule(String parameterName, String expression) {
    String selectAllExpressionValue =
        MessageFormat.format("{0}_{1}", WorkflowConstants.KEYWORD_ALL, parameterName);
    String[] stringTokens = expression.split(WorkflowConstants.SPACE);
    WorkflowVerfiy.verify(stringTokens.length < 2, WorkflowError.INVALID_INPUT_FOR_CREATING_RULES);
    String value = stringTokens[1].trim();
    return selectAllExpressionValue.equalsIgnoreCase(value);
  }

  /**
   * Generates FEEL expression with contains or not_contains operation
   * <pre>
   *   ex.
   *   containsAnyElement(["1","5","4"], Customer)
   *   not(containsAnyElement(["2","3","4"], Term))
   * </pre>
   * @param parameterName
   * @param operatorKeyword
   * @param values
   * @return
   */
  public String prepareContainsAnyElementExpressionForList(
      String parameterName, String operatorKeyword, List<String> values) {
    String dmnExpression = "";
    switch (operatorKeyword) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        dmnExpression = createContainsExpressionForList(parameterName, values);
        break;
      case WorkflowConstants.NOT_CONTAINS:
        dmnExpression = createNotContainsExpressionForList(parameterName, values);
        break;
      default:
        throw new WorkflowGeneralException(WorkflowError.UNSUPPORTED_OPERATION);
    }
    return dmnExpression;
  }

  /**
   *
   * @param expression
   * @return
   */
  public String transformEqualsUserFriendlyExpression(String expression) {
    return createUserFriendlyRuleExpression(expression, WorkflowConstants.EQUALS_OPERATOR);
  }

  public String transformListContainsUserFriendlyExpression(String expression) {
    boolean containsNegationOperator = expression.startsWith(WorkflowConstants.NOT_OP);
    // String expression = "containsAnyElement([\"1\",\"11\"], ItemList)";
    Matcher matcher = exprValPattern.matcher(expression);
    List<String> idList =
        matcher.results().map(result -> result.group(1)).collect(Collectors.toList());
    return MessageFormat.format(
        "{0} {1}",
        (containsNegationOperator
            ? WorkflowConstants.NOT_CONTAINS
            : WorkflowConstants.CONTAINS_OPERATOR),
        StringUtils.join(idList, ","));
  }

  public String transformContainsUserFriendlyExpression(String expression) {
    return createUserFriendlyRuleExpression(expression, WorkflowConstants.CONTAINS_OPERATOR);
  }

  /**
   * This method is responsible for converting DMN friendly expression to a UI expression.
   *
   * <pre>
   * Ex.
   * expression: Customer.equals("1") || Customer.equals("2") parameterName: Customer operation:
   * CONTAINS
   * <p>returns "CONTAINS 1,2"
   * </pre>
   */
  private String createUserFriendlyRuleExpression(String expression, String operation) {
    String result = "";
    boolean containsNegationOperator = expression.contains(WorkflowConstants.NOT_OPERATOR);
    // Result now is ("1") ("2"). Remove all the extra characters to get 1,2
    String values = evaluateRegexExpression(expression);

    switch (operation) {
      case WorkflowConstants.CONTAINS_OPERATOR:
        result =
            getExpressionWithOp(
                WorkflowConstants.CONTAINS_OPERATOR, containsNegationOperator, values);
        break;
      case WorkflowConstants.EQUALS_OPERATOR:
        result =
            getExpressionWithOp(
                WorkflowConstants.EQUALS_OPERATOR, containsNegationOperator, values);
        break;
      default:
        throw new WorkflowGeneralException((WorkflowError.UNSUPPORTED_OPERATION));
    }

    return result;
  }

  /**
   * generate user friendly expression in case of feel language
   * Expression="not("abc","def","ghi")" return NOT_CONTAINS abc,def,ghi
   */
  public String createUserFriendlyRuleExpressionForFeelExp(String expression) {
    boolean containsNegationOperator = expression.contains(WorkflowConstants.NOT_OP);
    String values = evaluateRegexExpression(expression);
    return getExpressionWithOp(
            WorkflowConstants.CONTAINS_OPERATOR,
            WorkflowConstants.NOT_CONTAINS,
            containsNegationOperator,
            values);
  }

  // input expression juel: DisplayName.equals("abc") return abc
  // input expression feel: "abc" return abc
  // matches against regex to find values in between double quotes and return a list
  private String evaluateRegexExpression(String expression){
    return exprValPattern
            .matcher(expression)
            .results()
            .map(matchResult -> matchResult.group(1).trim())
            .collect(Collectors.joining(","));
  }

  private String getExpressionWithOp(
      String operator, boolean containsNegationOperator, String values) {
    if (operator.equalsIgnoreCase(WorkflowConstants.CONTAINS_OPERATOR)) {
      return getExpressionWithOp(
          WorkflowConstants.ANY_MATCH,
          WorkflowConstants.NO_MATCH,
          containsNegationOperator,
          values);
    } else {
      return getExpressionWithOp(
          WorkflowConstants.CONTAINS_OPERATOR,
          WorkflowConstants.NOT_CONTAINS,
          containsNegationOperator,
          values);
    }
  }

  private String getExpressionWithOp(
      String yesOp, String NoOp, boolean containsNegationOperator, String values) {
    return MessageFormat.format(
            WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE,
            containsNegationOperator ? NoOp : yesOp,
            values)
        .trim();
  }

  /**
   *
   *
   * <pre>
   * dmnFriendlyExpression="", parameterName="Customer", values=["123"]
   * returns Expression Customer.equals("123")
   * dmnFriendlyExpression="Customer.equals("123")", parameterName="Customer", values=["456"]
   * returns Expression Customer.equals("123") || Customer.equals("456")
   * </pre>
   */
  public String createContainsLegacyDmnExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    for (String token : values) {
      dmnFriendlyExpression =
          legacyDmnExpressionForContainsAndAnyMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.EQUALS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  public String createContainsDmnExpression(List<String> values) {
    return values.stream()
        .map(value -> StringUtils.wrap(value, "\""))
        .collect(Collectors.joining(WorkflowConstants.COMMA));
  }

  /**
   * <pre>
   * dmnFriendlyExpression="", parameterName="Customer", values=["123"]
   * returns Expression !Customer.equals("123")
   * dmnFriendlyExpression="!Customer.equals("123")", parameterName="Customer", values=["456"]
   * returns Expression !Customer.equals("123") && !Customer.equals("456")
   * </pre>
   */
  public String createNotContainsLegacyDmnExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    for (String token : values) {
      dmnFriendlyExpression =
          legacyDmnExpressionForNotContainsAndNoMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.EQUALS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  /**
   *
   *
   * <pre>
   * dmnFriendlyExpression="", parameterName="Customer", values=["123"]
   * returns Expression Customer.contains("123")
   * dmnFriendlyExpression="Customer.contains("123")", parameterName="Customer", values=["456"]
   * returns Expression Customer.contains("123") || Customer.contains("456")
   * </pre>
   */
  public String createAnyMatchLegacyDmnExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    for (String token : values) {
      dmnFriendlyExpression =
          legacyDmnExpressionForContainsAndAnyMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.CONTAINS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  /**
   *
   *
   * <pre>
   * dmnFriendlyExpression="", parameterName="Customer", values=["123"]
   * returns Expression !Customer.contains("123")
   * dmnFriendlyExpression="!Customer.contains("123")", parameterName="Customer", values=["456"]
   * returns Expression !Customer.contains("123") && !Customer.contains("456")
   * </pre>
   */
  public String createNoMatchLegacyDmnExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    for (String token : values) {
      dmnFriendlyExpression =
          legacyDmnExpressionForNotContainsAndNoMatch(
              token,
              dmnFriendlyExpression,
              parameterName,
              WorkflowConstants.CONTAINS_OPERATOR.toLowerCase());
    }
    return dmnFriendlyExpression;
  }

  private String legacyDmnExpressionForContainsAndAnyMatch(
      String token, String dmnFriendlyExpression, String parameterName, String operation) {
    String result = "";
    if (StringUtils.isNotEmpty(dmnFriendlyExpression)) {
      result =
          MessageFormat.format("{0} {1} ", dmnFriendlyExpression, WorkflowConstants.OR_MODIFIER);
    }

    result +=
        MessageFormat.format(
            "{0}{1}{2}(\"{3}\")", parameterName, WorkflowConstants.DOT_OPERATOR, operation, token);
    return result;
  }

  private String legacyDmnExpressionForNotContainsAndNoMatch(
      String token, String dmnFriendlyExpression, String parameterName, String operation) {
    String result = "";
    if (StringUtils.isNotEmpty(dmnFriendlyExpression)) {
      result =
          MessageFormat.format("{0} {1} ", dmnFriendlyExpression, WorkflowConstants.AND_MODIFIER);
    }
    result +=
        MessageFormat.format(
            "{0}{1}{2}{3}(\"{4}\")",
            WorkflowConstants.NOT_OPERATOR,
            parameterName,
            WorkflowConstants.DOT_OPERATOR,
            operation,
            token);
    return result;
  }

  public String createContainsExpressionForList(String parameterName, List<String> values) {
    String valueStr = ObjectConverter.toJson(values);
    return String.format("containsAnyElement(%s, %s)", valueStr, parameterName);
  }

  public String createNotContainsExpressionForList(String parameterName, List<String> values) {
    String valueStr = ObjectConverter.toJson(values);
    return String.format("not(containsAnyElement(%s, %s))", valueStr, parameterName);
  }

  public static String createNotContainsDmnExpression(List<String> values) {
    return MessageFormat.format(
        "{0}({1})", WorkflowConstants.NOT_OP, createContainsDmnExpression(values));
  }

  public static String createAnyMatchDmnExpression(String parameterName, List<String> values) {
    return values.stream()
        .map(value -> MessageFormat.format("contains({0}, \"{1}\")", parameterName, value))
        .collect(Collectors.joining(" or "));
  }

  public static String createNoMatchDmnExpression(
      String dmnFriendlyExpression, String parameterName, List<String> values) {
    String anyMatchDmnExpression = createAnyMatchDmnExpression(parameterName, values);
    return MessageFormat.format("{0}({1})", WorkflowConstants.NOT_OP, anyMatchDmnExpression);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor;

import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Map;

/** <AUTHOR> */
public interface RecoveryCallbackHandler {

  /**
   * functional interface to handle multiple actions for kafka recovery callback post retries
   *
   * @param consumerRecord kafka record
   * @param headers kafka record headers
   */
  void retryFailureHandler(
      ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
      Map<String, String> headers);
}

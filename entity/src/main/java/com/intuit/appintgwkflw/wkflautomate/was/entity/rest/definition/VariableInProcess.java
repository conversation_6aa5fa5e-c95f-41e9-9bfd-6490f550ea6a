package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import lombok.Data;

import java.util.Map;

@Data
public class VariableInProcess extends RestDefinitionCommon {
    private String processInstanceId;

    public VariableInProcess(String processInstanceId, Map<String, String> headers, Class responseType) {
        super(headers, responseType);
        this.processInstanceId = processInstanceId;
    }
}

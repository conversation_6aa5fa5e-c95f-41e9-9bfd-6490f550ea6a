package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import javax.persistence.AttributeConverter;


public class ModelTypeConverter implements AttributeConverter<ModelType, String> {

  @Override
  public String convertToDatabaseColumn(ModelType attribute) {
    return attribute != null ? attribute.getModelType() : null;
  }

  @Override
  public ModelType convertToEntityAttribute(String dbData) {
    return dbData != null ? ModelType.fromType(dbData) : null;
  }
}
[IPF - was-event-in - PRD - Salesreceipt Pod Memory Utilization]
alert_metric_name = ts("heapster.pod.memory.usage", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd and label.app=was-event-in-salesreceipt) / ts("heapster.pod.memory.limit", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prd and label.app=was-event-in-salesreceipt) * 100
severity = SEVERE
threshold = 80
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =
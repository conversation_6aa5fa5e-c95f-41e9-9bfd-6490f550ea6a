package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum WeekOfMonth {
  FIRST("FIRST", 1),
  SECOND("SECOND", 2),
  THIRD("THIRD", 3),
  FOURTH("FOURTH", 4);

  private final String name;
  private final int index;
  private static Map<String, Integer> weekOfMonthIndexMap;

  private static Map<String, Integer> weekOfMonthNameIndexMapping() {
    if (ObjectUtils.isEmpty(weekOfMonthIndexMap)) {
      weekOfMonthIndexMap = new HashMap<>();
      for (WeekOfMonth s : values()) {
        weekOfMonthIndexMap.put(s.name, s.index);
      }
    }
    return weekOfMonthIndexMap;
  }

  public static int getWeekOfMonthIndex(String weekOfMonth) {
    return weekOfMonthNameIndexMapping().get(weekOfMonth);
  }
}

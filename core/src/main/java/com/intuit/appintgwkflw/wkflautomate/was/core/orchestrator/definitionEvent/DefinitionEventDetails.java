package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

/**
 * Definition event details for processing the events.
 *
 * <AUTHOR>
 */
@Builder
@Getter
public class DefinitionEventDetails {

  private DefinitionDetails definitionDetails;
  private Map<String, String> metaData;
}

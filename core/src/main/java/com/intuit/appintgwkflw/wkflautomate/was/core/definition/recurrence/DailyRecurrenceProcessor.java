package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import static com.cronutils.model.field.expression.FieldExpressionFactory.always;
import static com.cronutils.model.field.expression.FieldExpressionFactory.every;
import static com.cronutils.model.field.expression.FieldExpressionFactory.questionMark;

import com.cronutils.model.field.expression.FieldExpression;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import org.springframework.stereotype.Component;

/*
 * handle recurrence for recurType = DAILY
 */
@Component
public class DailyRecurrenceProcessor implements RecurrenceProcessor {

  /**
   * given a recurrence rule of type DAILY
   * "recurrence": {
   *    "interval": 2,
   *    "recurType": "DAILY",
   *    "monthsOfYear": ["JANUARY", "APRIL"],
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   * the corresponding parameters are populated
   * daysOfMonth => every(2) = * /2
   * monthsOfYear => and(on(1), on(4)) = 1,4
   * daysOfWeek => ? (any)
   * year => * (always)
   *
   */
  // resultant cron expression 0 0 0 */2 1,4 ? *

  /**
   * given a recurrence rule of type DAILY
   * "recurrence": {
   *    "interval": 2,
   *    "recurType": "DAILY",
   *    "startDate": "2021-08-02",
   *    "active": true
   * }
   *
   * the corresponding parameters are populated
   * daysOfMonth => every(2) = * /2
   * monthsOfYear => always() = since the value is null
   * daysOfWeek => ? (any)
   * year => * (always)
   *
   */
  // resultant cron expression 0 0 0 */2 * ? *

  @Override
  public String getRecurrence(RecurrenceRule recurrenceRule) {
    FieldExpression daysOfMonth = every(recurrenceRule.getInterval());
    FieldExpression monthsOfYear = populateMonthsOfYearParameter(recurrenceRule);
    FieldExpression daysOfWeek = questionMark();
    FieldExpression year = always();
    return buildCronExpression(
        year, daysOfMonth, monthsOfYear, daysOfWeek, recurrenceRule.getRecurrenceTime());
  }

  @Override
  public RecurrencePattern buildESSRecurrencePattern(RecurrenceRule recurrenceRule) {
    return new RecurrencePattern()
        .interval(recurrenceRule.getInterval())
        .type(RecurrencePatternType.fromValue(getName().value()));
  }

  public RecurTypeEnum getName() {
    return RecurTypeEnum.DAILY;
  }
}

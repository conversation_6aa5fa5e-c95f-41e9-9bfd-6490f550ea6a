mutation Custom_Create_Workflows_Definition($input_0: CreateWorkflows_DefinitionInput!) {
                createWorkflows_Definition(input: $input_0) {
   workflowsDefinitionEdge {
       node {
           id
           status
           description
           name
           displayName
           meta {
               created
               updated
               createdBy {
                   id
               }
           }
           recordType
           recurrence {
               interval
               recurType
               startDate
               daysOfMonth
               daysOfWeek
               monthsOfYear
               weekOfMonth
               active
           }
           workflowSteps {
               edges {
                   node {
                       trigger {
                           id
                           parameters {
                               parameterName
                               fieldValues
                               required
                               possibleFieldValues
                           }
                       }
                       workflowStepCondition {
                           id
                           ruleLines {
                               edges {
                                   node {
                                       mappedActionKeys
                                       rules {
                                           conditionalExpression
                                           parameterName
                                           selectedlogicalGroupingOperator
                                       }
                                   }
                               }
                           }
                       }
                       actions {
                           actionKey
                           action {
                               selected
                               name
                               parameters {
                                   parameterName
                                   parameterType
                                   helpVariables
                                   getOptionsForFieldValue
                                   fieldValues
                                   configurable
                                   multiSelect
                               }
                           }
                       }
                   }
               }
           }
       }
   }
} }
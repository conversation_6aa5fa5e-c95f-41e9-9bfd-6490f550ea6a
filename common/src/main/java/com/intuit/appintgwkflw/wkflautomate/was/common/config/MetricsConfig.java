package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "metrics")
@Getter
@Setter
public class MetricsConfig {

  private boolean emitOwnerId = false;
  private boolean emitServiceMetrics = true;
  private boolean emitHikariMetrics = true;
  private boolean emitWorkflowName = false;
}

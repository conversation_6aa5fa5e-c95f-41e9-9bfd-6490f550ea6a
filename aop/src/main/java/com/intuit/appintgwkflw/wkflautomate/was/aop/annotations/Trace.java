package com.intuit.appintgwkflw.wkflautomate.was.aop.annotations;

import com.intuit.appintgwkflw.wkflautomate.was.aop.aspects.TraceAspect;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Author: <PERSON><PERSON>
 * Date: 18/12/19
 * Description: A tag annotation.
 * Utilize this annotation in order to catch trace ids .
 * {@link TraceAspect} will be Aspect to cache ids to MDC map
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME) //Spring AOP would not be able to see the annotation if not runtime
public @interface Trace {
}

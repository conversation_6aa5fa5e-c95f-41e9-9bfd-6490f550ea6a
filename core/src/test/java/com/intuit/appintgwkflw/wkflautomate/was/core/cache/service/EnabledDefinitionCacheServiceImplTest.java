package com.intuit.appintgwkflw.wkflautomate.was.core.cache.service;

import com.intuit.appintgwkflw.wkflautomate.was.core.cache.clientImplementations.CacheClientOperation;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.CacheConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

@RunWith(MockitoJUnitRunner.class)
public class EnabledDefinitionCacheServiceImplTest {

    @Mock
    private DefinitionDetailsRepository definitionDetailsRepository;
    @Mock
    private CacheClientOperation cacheClientOperation;
    @Mock
    private CustomWorkflowQueryCapability customWorkflowQueryCapability;
    @InjectMocks
    private EnabledDefinitionCacheServiceImpl enabledDefinitionCacheService;

    @Test
    public void testUpdateCacheWithValidCustomWorkflow() {

        // return empty redisson map
        Mockito.when(cacheClientOperation.getMap(Mockito.anyString())).thenReturn(new HashMap<>());
        Set<String> hs = new HashSet<>();
        hs.add("testKey");
        Mockito.when(definitionDetailsRepository.findDefinitionKeysOfAllEnabledDefinitionsForOwnerIdModelRecordTypeAndTemplateName(
                Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(Optional.of(hs));
        Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(
                Mockito.any(), Mockito.any())).thenReturn(true);

        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(createEnabledDefinitionDetails(true));

        Mockito.verify(cacheClientOperation, Mockito.times(1)).put(
                Mockito.any(),
                Mockito.any(),
                Mockito.any());
        Mockito.verify(definitionDetailsRepository, Mockito.times(1)).findDefinitionKeysOfAllEnabledDefinitionsForOwnerIdModelRecordTypeAndTemplateName(Mockito.anyLong(), Mockito.any(), Mockito.any(), Mockito.anyString());
    }

    @Test
    public void testUpdateCacheWithNullDefinitionDetails() {
        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(null);
        Mockito.verify(cacheClientOperation, Mockito.never()).getMap(Mockito.anyString());
    }

    // test with non custom workflow
    @Test
    public void testNonCustomWorkflow() {
        Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(
                Mockito.any(), Mockito.any()
        )).thenReturn(false);
        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(createEnabledDefinitionDetails(false));
        Mockito.verify(cacheClientOperation, Mockito.times(0)).getMap(Mockito.anyString());
    }

    // test that workflow is not checked when the 'workflows' filter
    // config is not present
    @Test
    public void testNoFilterCalledWhenNoFilterWorkflowsConfig() {
        Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(
                Mockito.any(), Mockito.any()
        )).thenReturn(true);
        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(createEnabledDefinitionDetails(true));
        Mockito.verify(cacheClientOperation, Mockito.times(1)).getMap(Mockito.anyString());
    }

    // test that workflow is filtered out when the 'workflows' filter
    // config does not have that workflow
    @Test
    public void testWorkflowIsFilteredWhenNotInWorkflowsConfig() throws NoSuchFieldException, IllegalAccessException {
        Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(
                Mockito.any(), Mockito.any()
        )).thenReturn(true);
        Field enabledWorkflows =
                EnabledDefinitionCacheServiceImpl.class
                        .getDeclaredField("enabledWorkflowsSet");
        enabledWorkflows.setAccessible(true);
        Set<String> ew = new HashSet<>();
        ew.add("customNotification");
        enabledWorkflows.set(enabledDefinitionCacheService, ew);
        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(createEnabledDefinitionDetails(true));
        Mockito.verify(cacheClientOperation, Mockito.times(0)).getMap(Mockito.anyString());
    }

    // test that workflow is not filtered out when the 'workflows' filter
    // config has that workflow
    @Test
    public void testWorkflowNotFilteredWhenInWorkflowsConfig() throws NoSuchFieldException, IllegalAccessException {
        Mockito.when(customWorkflowQueryCapability.isCustomWorkflow(
                Mockito.any(), Mockito.any()
        )).thenReturn(true);
        Field enabledWorkflows =
                EnabledDefinitionCacheServiceImpl.class
                        .getDeclaredField("enabledWorkflowsSet");
        enabledWorkflows.setAccessible(true);
        Set<String> ew = new HashSet<>();
        ew.add("customNotification");

        DefinitionDetails dd = createEnabledDefinitionDetails(true);
        dd.getTemplateDetails().setTemplateName("customNotification");
        enabledWorkflows.set(enabledDefinitionCacheService, ew);
        enabledDefinitionCacheService.updateCacheWithDefinitionDetails(dd);
        Mockito.verify(cacheClientOperation, Mockito.times(1)).getMap(Mockito.anyString());
    }

    private DefinitionDetails createEnabledDefinitionDetails(Boolean isCustomWorkflow) {
        TemplateDetails templateDetails;
        if (isCustomWorkflow)
            templateDetails = TemplateDetails.builder()
                .templateName("customReminder")
                .build();
        else
            templateDetails = TemplateDetails.builder()
                .templateName("userReminder")
                .build();
        DefinitionDetails definitionDetails = DefinitionDetails.builder()
                .ownerId(1L)
                .recordType(RecordType.ACCOUNT)
                .modelType(ModelType.BPMN)
                .definitionKey("testKey")
                .templateDetails(templateDetails)
                .build();
        return definitionDetails;
    }
}

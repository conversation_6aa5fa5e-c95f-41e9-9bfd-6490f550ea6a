package com.intuit.appintgwkflw.wkflautomate.was.common.experimentation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ExperienceDecisionConstants {
    public static final String ENTITY_ID_REALM_ID = "realmOrCompanyId";
    public static final String ENTITY_ID_CAN_ID = "canID";
    public static final String ENTITY_ID_NS = "ns";
    public static final String ENTITY_ID_ACCOUNT_ID ="accountId";
    public static final String ENTITY_ID_IVID ="ivid";
    public static final String ENTITY_ID_HASHED_PHONE_NUMBER_ID ="hashedPhoneNumberId";
    public static final String ENTITY_ID_VERSION ="version";
    public static final String ENTITY_ID_IXP_IVID ="ixpIvid";
    public static final String ENTITY_ID_HASHED_TURBOTAX_DESKTOP_ID ="hashedTurbotaxDesktopId";
    public static final String ENTITY_ID_HASHED_EMAIL_ID ="hashedEmailId";
    public static final String ENTITY_ID_PSEUDONYM_ID ="pseudonymId";
    public static final String ENTITY_ID_AUTH_ID ="authId";
    public static final String ENTITY_ID_MOBILE_DEVICE_ID ="mobileDeviceId";
    public static final String ENTITY_ID_MOBILE_ADVERTISING_ID ="mobileAdvertisingId";
    public static final String DECISION_DETAILS = "decisionDetails";
    public static final String BOOLEAN = "Boolean";
}

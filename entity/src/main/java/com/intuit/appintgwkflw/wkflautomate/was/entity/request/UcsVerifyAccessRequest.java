package com.intuit.appintgwkflw.wkflautomate.was.entity.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Builder
@Getter
@Setter
@RequiredArgsConstructor
@AllArgsConstructor
public class UcsVerifyAccessRequest {
  String resourceId;
  String requesterOwnerId;
  String requesterUserId;
}
[IPF - was-event-in - PRD - Runtime Throttle Limit Reached]
alert_metric_name = sum(align(1m, sum, ratediff(ts("iks.intuit.dataexchange.pipelineframework.runtime.exit.duration.seconds.count" and namespace=appintgwkflw-wkflautomate-waseventin-usw2-prd))))
severity = SEVERE
threshold = 100000
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 2
additional_info =
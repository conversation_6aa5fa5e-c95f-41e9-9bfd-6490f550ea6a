package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.helper;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ActivityRuntimeDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.ProcessDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.system.interfaces.BaseEntity;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 */
public class DomainEventServiceTest {
  @InjectMocks private DomainEventService domainEventService;

  @Mock private WASContextHandler wasContextHandler;
  @Mock private DomainEventConfig domainEventTopiConfig;
  @Mock private ProcessDetailsRepoService processDetailsRepoService;
  @Mock private ProcessDomainEventHandler processDomainEventHandler;
  @Mock private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;
  @Mock private ActivityProgressDetailsRepository activityProgressDetailsRepository;
  @Mock private ActivityDetailsRepository activityDetailsRepository;

  private Map<DomainEventName, TopicDetails> map;
  private static final String ACTIVITY_RUNTIME_TOPIC = "qal.foundation.workflow.workflowautomation.activityruntime.v1";
  private static final String PROCESS_RUNTIME_TOPIC = "qal.foundation.workflow.workflowautomation.process.v1";
  private static final long ACTIVITY_DEF_ID = 1l;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("testTopic");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    map.put(DomainEventName.ACTIVITY, topicDetails);
    map.put(DomainEventName.DEFINITION, topicDetails);
    map.put(DomainEventName.TEMPLATE, topicDetails);
  }

  @Test
  public void successPrepareEntityHeaders() {
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tId");

    EventHeaderEntity eventHeaderEntity =
        domainEventService.prepareEntityHeaders(
            WorkerActionRequest.builder()
                .taskId("tId")
                .activityId("aId")
                .workerId("wId")
                .processInstanceId("pId")
                .processDefinitionId("dId")
                .build());

    Assert.assertNotNull(eventHeaderEntity);
    Assert.assertEquals("tId", eventHeaderEntity.getTid());
    Assert.assertEquals("tId:pId", eventHeaderEntity.getIdempotencyKey());
    Assert.assertEquals("oId", eventHeaderEntity.getOfferingId());
  }

  @Test
  public void successPrepareEntityHeadersOfferingIdNull() {
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn(null);
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tId");

    EventHeaderEntity eventHeaderEntity =
        domainEventService.prepareEntityHeaders(
            WorkerActionRequest.builder()
                .taskId("tId")
                .activityId("aId")
                .workerId("wId")
                .processInstanceId("pId")
                .processDefinitionId("dId")
                .build());

    Assert.assertNotNull(eventHeaderEntity);
    Assert.assertEquals("tId", eventHeaderEntity.getTid());
    Assert.assertEquals("tId:pId", eventHeaderEntity.getIdempotencyKey());
    Assert.assertNull(eventHeaderEntity.getOfferingId());
  }

  @Test
  public void testDomainEventConfigTrue() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    boolean response = domainEventService.isDomainEventPublishEnabled();
    Assert.assertTrue(response);
  }

  @Test
  public void testDomainEventConfigFalse() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);
    boolean response = domainEventService.isDomainEventPublishEnabled();
    Assert.assertFalse(response);
  }

  @Test
  public void testUpdateStatusWithEntityChangeAction() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);
    Mockito.when(processDetailsRepoService.updateStatus("pId", ProcessStatus.ENDED)).thenReturn(1);
    int val =
        domainEventService.updateStatus(
            ProcessDetails.builder()
                .processId("pId")
                .entityVersion(0)
                .processStatus(ProcessStatus.ACTIVE)
                .build(),
            ProcessStatus.ENDED,
            EventHeaderEntity.builder().build(),
            EntityChangeAction.DELETE,
            Collections.emptyMap());
    Assert.assertNotNull(val);
  }

  @Test
  public void testUpdateStatusWithDomainEvents() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(PROCESS_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    String obj = ObjectConverter.toJson(new Process());
    DomainEvent<? extends BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(processDomainEventHandler.transform(any(DomainEntityRequest.class)))
            .thenReturn(val);
    Mockito.when(
            processDetailsRepoService.updateStatusAndPublishDomainEvent(
                ProcessDetails.builder()
                    .processId("pId")
                    .processStatus(ProcessStatus.ENDED)
                    .entityVersion(0)
                    .build(),
                DomainEvent.builder().build()))
        .thenReturn(1);
    domainEventService.updateStatus(
        ProcessDetails.builder()
            .processStatus(ProcessStatus.ACTIVE)
            .entityVersion(0)
            .processId("pId")
            .build(),
        ProcessStatus.ENDED,
        EventHeaderEntity.builder().build(),
        EntityChangeAction.UPDATE,
        Collections.emptyMap());
  }

  @Test
  public void testMigrarionEventWithDomainEventsWithOfferingIdInContext() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(PROCESS_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(processDomainEventHandler.publish(any(DomainEntityRequest.class))).thenReturn(any());
    domainEventService.publishMigrationEvent(
        ProcessDetails.builder()
            .definitionDetails(
                DefinitionDetails.builder()
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .build());
  }

  @Test
  public void testMigrarionEventWithDomainEventsWithoutOfferingIdInContext() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(PROCESS_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    // domainEventTopiConfig.getTopic().get(domainEventName).isEnabled()
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn(null);
    domainEventService.publishMigrationEvent(
        ProcessDetails.builder()
            .definitionDetails(
                DefinitionDetails.builder()
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .build());
  }

  @Test
  public void testMigrarionEventWithoutDomainEvents() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(PROCESS_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.PROCESS, topicDetails);
    domainEventService.publishMigrationEvent(
        ProcessDetails.builder()
            .definitionDetails(
                DefinitionDetails.builder()
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .build());
  }

  @Test
  public void testPublishActivityRuntimeEventConfigDisabled() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(false);
    Mockito.when(activityDetailsRepository.findByTemplateDetailsAndActivityId(any(), any()))
        .thenReturn(Optional.ofNullable(ActivityDetail.builder().build()));
    ActivityProgressDetails activityProgressDetails = ActivityProgressDetails.builder().build();
    Mockito.when(activityProgressDetailsRepository.saveAndFlush(any()))
        .thenReturn(activityProgressDetails);
    domainEventService.publishActivityRuntimeEvent(
        getTransitionEventDetails("start"), getHeaderDetails(), getMockProcessDetails());
    Mockito.verify(activityProgressDetailsRepository, Mockito.times(1))
        .saveAndFlush(any(ActivityProgressDetails.class));
  }

  @Test
  public void testPublishActivityRuntimeEventConfigEnabledButTopicDisabled() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(false);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(false);
    domainEventService.publishActivityRuntimeEvent(
        getTransitionEventDetails("start"), getHeaderDetails(), getMockProcessDetails());
  }

  @Test
  public void testPublishActivityRuntimeEventConfigFullyEnabledStartEvent() {
    WorkflowStateTransitionEvents workflowStateTransitionEvents =
        getTransitionEventDetails("start");
    EventHeaderEntity eventHeaderEntity = getHeaderDetails();
    ProcessDetails processDetails = getMockProcessDetails();

    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(true);

    DomainEntityRequest<ActivityProgressDetails> request =
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .request(getActivityProgressDetailsWithoutActivityDefinitionDetails())
            .eventHeaderEntity(eventHeaderEntity)
            .entityChangeAction(EntityChangeAction.CREATE)
            .build();

    DomainEvent<BaseEntity> response = new DomainEvent<>();
    response.setVersion(getActivityProgressDetailsWithActivityDefinitionDetails().getVersion());
    Mockito.when(activityRuntimeDomainEventHandler.publish(any(DomainEntityRequest.class)))
        .thenReturn(response);

    domainEventService.publishActivityRuntimeEvent(
        workflowStateTransitionEvents, eventHeaderEntity, processDetails);
  }

  @Test
  public void testPublishActivityRuntimeEventConfigFullyEnabledEndEvent() {
    WorkflowStateTransitionEvents workflowStateTransitionEvents = getTransitionEventDetails("end");
    EventHeaderEntity eventHeaderEntity = getHeaderDetails();
    ProcessDetails processDetails = getMockProcessDetails();

    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(true);

    DomainEntityRequest<ActivityProgressDetails> request =
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .request(getActivityProgressDetailsWithoutActivityDefinitionDetails())
            .eventHeaderEntity(eventHeaderEntity)
            .entityChangeAction(EntityChangeAction.UPDATE)
            .build();
    DomainEvent<? extends BaseEntity> response = new DomainEvent<>();
    response.setVersion(getActivityProgressDetailsWithActivityDefinitionDetails().getVersion());
    Mockito.when(activityRuntimeDomainEventHandler.publish(any(DomainEntityRequest.class)))
        .thenReturn(response);
    domainEventService.publishActivityRuntimeEvent(
        workflowStateTransitionEvents, eventHeaderEntity, processDetails);
  }

  @Test
  public void testCaseProcessDetails() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ProcessDetails.builder().build()).build();
    boolean result = domainEventService.isDomainEventEnabled(request);
    Assert.assertTrue(result);
  }

  @Test
  public void testCaseDefinitionDetails() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(DefinitionDetails.builder().build()).build();
    boolean result = domainEventService.isDomainEventEnabled(request);
    Assert.assertTrue(result);
  }

  @Test
  public void testCaseTemplateDetails() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(TemplateDetails.builder().build()).build();
    boolean result = domainEventService.isDomainEventEnabled(request);
    Assert.assertTrue(result);
  }

  @Test
  public void testCaseActivityProgressDetails() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ActivityProgressDetails.builder().build()).build();
    boolean result = domainEventService.isDomainEventEnabled(request);
    Assert.assertTrue(result);
  }

  @Test
  public void testCaseActivityDetails() {
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    DomainEntityRequest request =
        DomainEntityRequest.builder().request(ActivityDetail.builder().build()).build();
    boolean result = domainEventService.isDomainEventEnabled(request);
    Assert.assertTrue(result);
  }

  @Test
  public void testStartEventPublishDbSave() {
    ActivityDetail activityDetail = getMockActivityDefinitionDetails(TaskType.MILESTONE);
    ActivityProgressDetails activityProgressDetails =
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE);
    EventHeaderEntity eventHeaderEntity = getHeaderDetails();
    DomainEntityRequest<ActivityProgressDetails> request =
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .request(activityProgressDetails)
            .eventHeaderEntity(eventHeaderEntity)
            .entityChangeAction(EntityChangeAction.CREATE)
            .build();
    WorkflowStateTransitionEvents workflowStateTransitionEvents =
        getTransitionEventDetails("start");

    ProcessDetails processDetails = getMockProcessDetails();

    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(true);

    DomainEvent<? extends BaseEntity> response = new DomainEvent<>();
    response.setVersion(
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE).getVersion());

    Mockito.when(activityDetailsRepository.findByTemplateDetailsAndActivityId(any(), any()))
        .thenReturn(Optional.ofNullable(activityDetail));

    Mockito.when(activityProgressDetailsRepository.saveAndFlush(any()))
        .thenReturn(activityProgressDetails);
    Mockito.when(activityRuntimeDomainEventHandler.publish(any(DomainEntityRequest.class)))
        .thenReturn(response);

    domainEventService.publishActivityRuntimeEvent(
        workflowStateTransitionEvents, eventHeaderEntity, processDetails);

    Assert.assertNotNull(response);
  }

  @Test
  public void testStartEventPublishDbSaveEndOnly() {
    ActivityDetail activityDetail = getMockActivityDefinitionDetails(TaskType.MILESTONE);
    ActivityProgressDetails activityProgressDetails =
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE);
    EventHeaderEntity eventHeaderEntity = getHeaderDetails();
    DomainEntityRequest<ActivityProgressDetails> request =
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .request(activityProgressDetails)
            .eventHeaderEntity(eventHeaderEntity)
            .entityChangeAction(EntityChangeAction.CREATE)
            .build();
    WorkflowStateTransitionEvents workflowStateTransitionEvents = getTransitionEventDetails("end");

    ProcessDetails processDetails = getMockProcessDetails();

    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(true);

    DomainEvent<BaseEntity> response = new DomainEvent<>();
    response.setVersion(
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE).getVersion());

    Mockito.when(activityDetailsRepository.findByTemplateDetailsAndActivityId(any(), any()))
        .thenReturn(Optional.ofNullable(activityDetail));

    Mockito.when(activityProgressDetailsRepository.findById(any()))
        .thenReturn(Optional.ofNullable(null));

    Mockito.when(activityProgressDetailsRepository.saveAndFlush(any()))
        .thenReturn(activityProgressDetails);

    domainEventService.publishActivityRuntimeEvent(
        workflowStateTransitionEvents, eventHeaderEntity, processDetails);

    Assert.assertNotNull(response);
  }

  @Test
  public void testStartEventPublishDbSaveEndWithBothEventsEnabled() {
    ActivityDetail activityDetail = getMockActivityDefinitionDetails(TaskType.MILESTONE);
    ActivityProgressDetails activityProgressDetails =
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE);
    EventHeaderEntity eventHeaderEntity = getHeaderDetails();
    DomainEntityRequest<ActivityProgressDetails> request =
        DomainEntityRequest.<ActivityProgressDetails>builder()
            .request(activityProgressDetails)
            .eventHeaderEntity(eventHeaderEntity)
            .entityChangeAction(EntityChangeAction.CREATE)
            .build();
    WorkflowStateTransitionEvents workflowStateTransitionEvents = getTransitionEventDetails("end");

    ProcessDetails processDetails = getMockProcessDetails();

    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(wasContextHandler.get(WASContextEnums.OFFERING_ID)).thenReturn("oId");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic(ACTIVITY_RUNTIME_TOPIC);
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(activityRuntimeDomainEventHandler.isTopicEnabled(DomainEventName.ACTIVITY_RUNTIME))
        .thenReturn(true);

    DomainEvent<BaseEntity> response = new DomainEvent<>();
    response.setVersion(
        getActivityProgressDetailsWithActivityDefinitionDetails(TaskType.MILESTONE).getVersion());

    Mockito.when(activityDetailsRepository.findByTemplateDetailsAndActivityId(any(), any()))
        .thenReturn(Optional.ofNullable(activityDetail));

    Mockito.when(activityProgressDetailsRepository.findById(any()))
        .thenReturn(Optional.ofNullable(activityProgressDetails));

    Mockito.when(activityProgressDetailsRepository.saveAndFlush(any()))
        .thenReturn(activityProgressDetails);
    Mockito.when(activityRuntimeDomainEventHandler.publish(any(DomainEntityRequest.class)))
        .thenReturn(response);

    domainEventService.publishActivityRuntimeEvent(
        workflowStateTransitionEvents, eventHeaderEntity, processDetails);

    Assert.assertNotNull(response);
  }

  private ActivityProgressDetails getActivityProgressDetailsWithoutActivityDefinitionDetails() {
    return ActivityProgressDetails.builder()
        .version(0)
        .id("idempotenceKey")
        .processDetails(getMockProcessDetails())
        .name("aName")
        .build();
  }

  private ActivityProgressDetails getActivityProgressDetailsWithActivityDefinitionDetails(
      TaskType... taskTypes) {
    return ActivityProgressDetails.builder()
        .version(0)
        .id("idempotenceKey")
        .processDetails(getMockProcessDetails())
        .activityDefinitionDetail(
            getMockActivityDefinitionDetails(
                ObjectUtils.isEmpty(taskTypes) ? TaskType.SYSTEM_TASK : taskTypes[0]))
        .name("aName")
        .txnDetails(TransactionDetails.builder().build())
        .attributes("attributes")
        .build();
  }

  private ActivityDetail getMockActivityDefinitionDetails(TaskType taskType) {
    return ActivityDetail.builder()
        .activityName("activityName")
        .activityType("activityType")
        .type(taskType)
        .id(ACTIVITY_DEF_ID)
        .templateDetails(
            TemplateDetails.builder()
                .id("templateId")
                .templateName("templateName")
                .version(0)
                .build())
        .build();
  }

  private WorkflowStateTransitionEvents getTransitionEventDetails(String eventType) {
    return WorkflowStateTransitionEvents.builder()
        .eventType(eventType)
        .activityType("aType")
        .status("success")
        .workflowMetadata(WorkflowMetaData.builder().build())
        .activityMetadata(
            ActivityMetaData.builder()
                .activityId("activityId")
                .activityName("activityName")
                .externalTaskId("externalTaskId")
                .build())
        .build();
  }

  private ProcessDetails getMockProcessDetails() {
    return ProcessDetails.builder()
        .processId("pId")
        .processStatus(ProcessStatus.ACTIVE)
        .ownerId(123l)
        .recordId("rId")
        .internalStatus(null)
        .definitionDetails(
            DefinitionDetails.builder()
                .definitionId("definitionId")
                .definitionName("definitionName")
                .templateDetails(
                    TemplateDetails.builder()
                        .id("templateId")
                        .templateName("templateName")
                        .version(0)
                        .build())
                .build())
        .build();
  }

  private EventHeaderEntity getHeaderDetails() {
    return EventHeaderEntity.builder()
        .entityId("eId")
        .offeringId("ttlive")
        .tid("tid")
        .idempotencyKey("executionId")
        .build();
  }
}

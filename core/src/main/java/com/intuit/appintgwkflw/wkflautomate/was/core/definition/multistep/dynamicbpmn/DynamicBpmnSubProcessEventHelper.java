package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.DynamicBpmnFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import java.util.Collection;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> This class helps in adding subprocess and its child elements from the
 * baseTemplate to bpmnModelInstance.
 */
@Component
@RequiredArgsConstructor
public class DynamicBpmnSubProcessEventHelper {

  private final StartEventFlowNodeProcessor startEventFlowNodeProcessor;
  private final DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  public void updateProcessElementAndAddSubprocess(
      BpmnModelInstance bpmnModelInstance, BpmnModelInstance precannedDynamicBpmnModelInstance) {

    // There should be only one process at this moment
    Optional<Process> processOptional = DynamicBpmnUtil.getProcessFromBpmnModelInstance(
        bpmnModelInstance);

    if (processOptional.isEmpty()) {
      return;
    }

    Optional<Process> precannedProcess =
        precannedDynamicBpmnModelInstance.getModelElementsByType(Process.class).stream()
            .findFirst();

    if (precannedProcess.isEmpty()) {
      WorkflowLogger.logError(
          "Pre-canned process is empty for the baseTemplate = %s",
          precannedDynamicBpmnModelInstance);
      return;
    }

    Collection<SubProcess> precannedSubprocesses =
        precannedDynamicBpmnModelInstance.getModelElementsByType(SubProcess.class);

    Process process = processOptional.get();

    // this id will be the templateName in TemplateDetails table for this dynamic Bpmn
    process.setId(precannedProcess.get().getId());
    process.setName(precannedProcess.get().getName());
    process.setExecutable(true);
    process.setCamundaHistoryTimeToLiveString(
        precannedProcess.get().getCamundaHistoryTimeToLiveString());
    process.setProcessType(precannedProcess.get().getProcessType());
    process.setClosed(precannedProcess.get().isClosed());
    precannedSubprocesses.forEach(
        precannedSubprocess ->
            process.builder().eventSubProcess(precannedSubprocess.getId()).done());
  }

  public void updateBpmnProcessId(
      BpmnModelInstance bpmnModelInstance,
      final BpmnModelInstance precannedDynamicBpmnModelInstance,
      final String hashSum) {

    // There should be only one process at this moment
    Optional<Process> processOptional = DynamicBpmnUtil.getProcessFromBpmnModelInstance(
        bpmnModelInstance);

    if (processOptional.isEmpty()) {
      return;
    }

    Optional<Process> precannedProcess =
        precannedDynamicBpmnModelInstance.getModelElementsByType(Process.class).stream()
            .findFirst();

    if (precannedProcess.isEmpty()) {
      WorkflowLogger.logError(
          "Pre-canned process is empty for the baseTemplate = %s",
          precannedDynamicBpmnModelInstance);
      return;
    }

    // this id will be the templateName in TemplateDetails table for this dynamic Bpmn
    processOptional.get().setId(precannedProcess.get().getId() + UNDERSCORE + hashSum);
  }

  public void updateSubprocessWithChildEvents(
      BpmnModelInstance bpmnModelInstance, BpmnModelInstance baseTemplateBpmnModelInstance) {

    Collection<SubProcess> subProcesses =
        bpmnModelInstance.getModelElementsByType(SubProcess.class);

    // There could be multiple subProcesses
    for (SubProcess subProcess : subProcesses) {

      SubProcess baseTemplateSubprocess =
          baseTemplateBpmnModelInstance.getModelElementById(subProcess.getId());
      subProcess.setName(baseTemplateSubprocess.getName());

      Optional<StartEvent> baseTemplateStartEvent =
          baseTemplateSubprocess.getChildElementsByType(StartEvent.class).stream().findFirst();

      if (baseTemplateStartEvent.isEmpty()) {
        WorkflowLogger.logInfo("StartEvent not found for subProcess= %s", subProcess.getId());
        continue;
      }

      addSubProcessElementsForOutgoingNodes(
          baseTemplateStartEvent.get(),
          subProcess,
          baseTemplateSubprocess,
          bpmnModelInstance,
          baseTemplateBpmnModelInstance);
    }
  }

  private void addSubProcessElementsForOutgoingNodes(
      FlowNode baseTemplateRootNode,
      SubProcess subProcess,
      SubProcess baseTemplateSubprocess,
      BpmnModelInstance bpmnModelInstance,
      BpmnModelInstance baseTemplateBpmnModelInstance) {

    // add startEvent to the subProcess
    if (baseTemplateRootNode instanceof StartEvent) {
      startEventFlowNodeProcessor.addEventToSubProcess(
          null, subProcess, baseTemplateSubprocess, bpmnModelInstance);
    }

    Collection<SequenceFlow> outgoingSequenceFlows = baseTemplateRootNode.getOutgoing();
    for (SequenceFlow outgoingSequenceFlow : outgoingSequenceFlows) {

      FlowNode targetNode = outgoingSequenceFlow.getTarget();

      // add other outgoing events to the subProcess
      DynamicBpmnFlowNodeProcessor flowNodeProcessor =
          dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(targetNode);
      flowNodeProcessor.addEventToSubProcess(
          baseTemplateRootNode, subProcess, baseTemplateSubprocess, bpmnModelInstance);
      flowNodeProcessor.addExtensionElements(
          null, targetNode, bpmnModelInstance, baseTemplateBpmnModelInstance);

      addSubProcessElementsForOutgoingNodes(
          targetNode,
          subProcess,
          baseTemplateSubprocess,
          bpmnModelInstance,
          baseTemplateBpmnModelInstance);
    }
  }
}

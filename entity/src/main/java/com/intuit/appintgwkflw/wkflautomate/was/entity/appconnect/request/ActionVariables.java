package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.request;

import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class ActionVariables {

  /** rest end point */
  private String actionUrl;

  /** action id that is to be called (example: appconnect duzzit id) */
  private String actionId;

  /** input json request payload */
  private String requestBody;

  /** Query Params Required */
  private Map<String, String> queryParams;

  /** output params */
  private Set<String> outputParams;
}

package com.intuit.appintgwkflw.wkflautomate.was.app.utils;

import com.intuit.appintgwkflw.wkflautomate.was.app.utils.UrlPatternMatcher;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UrlPatternMatcherTest {

  @InjectMocks
  private UrlPatternMatcher urlPatternMatcher;

  @Mock
  private OfferingConfig offeringConfig;

  @Before
  public void init(){
    List<String> list = new ArrayList<>();
    list.add("/v1/template/**");
    list.add("/v1/history/process/*");
    list.add("/v2/trigger");
    Mockito.when(offeringConfig.getAllowedSystemUserUrls()).thenReturn(list);
  }

  @Test
  public void test1(){
    String path = "/v1/template/save";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test2(){
    String path = "/v1/template/update";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test3(){
    String path = "/v1/template/migrationPlan";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test4(){
    String path = "/v1/template/migrate/version/1";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test5(){
    String path = "/v1/evaluate-rules";
    Assert.assertFalse(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test6(){
    String path = "/v1/trigger";
    Assert.assertFalse(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test7(){
    String path = "/v1/ticket/update";
    Assert.assertFalse(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test8(){
    String path = "/v2/trigger";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test9(){
    String path = "/v2/evaluate-and-trigger";
    Assert.assertFalse(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test10(){
    String path = "/v1/sendEvent";
    Assert.assertFalse(urlPatternMatcher.isMatching(path));
  }

  @Test
  public void test11(){
    String path = "/v1/template/save?templateMetadata=testMetdadata&intuit_tid=xyz";
    Assert.assertTrue(urlPatternMatcher.isMatching(path));
  }
}

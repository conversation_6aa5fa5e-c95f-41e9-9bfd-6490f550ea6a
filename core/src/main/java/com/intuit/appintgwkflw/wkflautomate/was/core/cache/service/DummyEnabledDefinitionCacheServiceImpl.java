package com.intuit.appintgwkflw.wkflautomate.was.core.cache.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "cache.enabled", havingValue = "false", matchIfMissing = true)
public class DummyEnabledDefinitionCacheServiceImpl implements EnabledDefinitionCacheService {

    @Override
    public void updateCacheWithDefinitionDetails(DefinitionDetails definitionDetails) {
        WorkflowLogger.logWarn("Caching disabled. No cache operations performed.");
    }

    @Override
    public void populateEnabledDefinitionKeysForRealmId(Long realmId) {
        WorkflowLogger.logWarn("Caching disabled. No cache operations performed.");
    }
}

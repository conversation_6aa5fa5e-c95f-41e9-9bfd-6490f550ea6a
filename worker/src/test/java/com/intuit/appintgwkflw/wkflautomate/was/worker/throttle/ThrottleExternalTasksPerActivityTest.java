package com.intuit.appintgwkflw.wkflautomate.was.worker.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service.CamundaHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleServiceHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors.WorkerExecutorHelper;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleExternalTasksPerActivityTest {

    @Mock private ProceedingJoinPoint proceedingJoinPoint;
    @Mock private ThrottleConfigs throttleConfigs;
    @Mock private ExternalTask externalTask;
    @Mock private ExternalTaskService externalTaskService;
    @Mock private ThrottleHelper throttleHelper;
    @Mock private WorkerExecutor workerExecutor;
    @Mock private ActivityProgressDetailsRepository dbRepository;
    @Mock private CamundaHistoryServiceRest historyServiceRest;
    @InjectMocks private ThrottleExternalTasksPerActivity throttleHandler;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(throttleHandler, "throttleHelper", throttleHelper);
        ReflectionTestUtils.setField(throttleHandler, "throttleConfigs", throttleConfigs);
    }

    @Test
    public void testGetAttribute() {
        Assert.assertEquals(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, throttleHandler.getAttribute());
    }

    // When throttling config not present
    @Test
    public void testCheckAndExecuteWhenThrottlingConfigNotPresent() throws Throwable {
        ThrottleServiceHandlers.addHandler(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, throttleHandler);
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        boolean response = throttleHandler.canContinueExecution(proceedingJoinPoint);
        Assert.assertTrue(response);
        Mockito.verify(externalTask, Mockito.times(0)).getActivityId();
    }

    // When throttling disabled
    @Test
    public void testIsThrottlingEnabled_WhenThrottlingDisabledForActivity() {
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(false);
        Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    // When throttling is enabled for activity in config; but no threshold configs present for workflow (disabled for workflow/neither configs present for workflow nor default configs present)
    @Test
    public void testIsThrottlingEnabled_WhenThrottlingEnabledForActivityInConfigButBypassedLater() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(false);
        Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    // When throttling is disabled through bpmn extension property
    @Test
    public void testIsThrottlingEnabled_WhenExtensionPropertyDisabled() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, String> extProp = new HashMap<>();
        extProp.put("disableThrottlePerActivity", "true");
        when(externalTask.getExtensionProperties()).thenReturn(extProp);
        Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    // When throttling is not disabled through bpmn extension property
    @Test
    public void testIsThrottlingEnabled_WhenExtensionPropertyNotDisabled() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, String> extProp = new HashMap<>();
        extProp.put("disableThrottlePerActivity", "false");
        when(externalTask.getExtensionProperties()).thenReturn(extProp);
        Assert.assertTrue(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    // When throttling is not disabled through bpmn extension property
    @Test
    public void testIsThrottlingEnabled_WhenExtensionPropertyNotDisabled_2() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(true);
        Map<String, String> extProp = new HashMap<>();
        extProp.put("disableThrottlePerActivityWronglySpelled", "true");
        when(externalTask.getExtensionProperties()).thenReturn(extProp);
        Assert.assertTrue(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }


    @Test
    public void testIsThrottlingEnabled_WhenManualRetry() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(true);
        when(externalTask.getErrorMessage()).thenReturn(WorkflowError.EXTERNAL_TASK_THRESHOLD_BREACHED.toString());
        Assert.assertFalse(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    @Test
    public void testIsThrottlingEnabled_WhenNotManualRetry() {
        Mockito.when(throttleConfigs.isExternalTasksPerActivity()).thenReturn(true);
        when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        when(throttleHelper.isThrottlingEnabledForWorkflow(Mockito.any(), Mockito.any())).thenReturn(true);
        when(externalTask.getErrorMessage()).thenReturn(WorkflowError.PROCESS_DETAILS_ERROR.toString());
        Assert.assertTrue(throttleHandler.isThrottlingEnabled(proceedingJoinPoint));
    }

    // Also checks correct extraction of definition key for SLB use cases
    @Test
    public void getExternalTaskCount_FromDB() {
        try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
            when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
            when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey");
            when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
            workerExecutorHelperMockedStatic.when(()-> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler");
            when(externalTask.getProcessInstanceId()).thenReturn("processId");
            when(externalTask.getActivityId()).thenReturn("activityId");
            Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
            Mockito.verify(dbRepository).getCountOfExternalTasksPerActivityPerProcess("activityId", "processId");
            Mockito.verify(historyServiceRest, Mockito.times(0)).getExternalTaskCount(Mockito.any());
            Assert.assertEquals("processDefnKey", response.getRight());
        }

    }

    // Also checks correct extraction of definition key for SLA use cases
    @Test
    public void getExternalTaskCount_FromCamunda() {
        try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
            workerExecutorHelperMockedStatic.when(() -> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler1");
            when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey2_9999");
            when(externalTask.getBusinessKey()).thenReturn("9999");
            when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
            Map<String, Integer> resp = new HashMap<>();
            resp.put("count", 5);
            WASHttpResponse<Map<String, Integer>> httpResponse = WASHttpResponse.<Map<String, Integer>>builder()
                    .response(resp)
                    .build();
            when(historyServiceRest.getExternalTaskCount(Mockito.any())).thenReturn(httpResponse);
            Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
            Assert.assertEquals((Integer) 5, response.getLeft());
            Mockito.verify(dbRepository, Mockito.times(0)).getCountOfExternalTasksPerActivityPerProcess(Mockito.any(), Mockito.any());
            Assert.assertEquals("processDefnKey2", response.getRight());
        }
    }

    @Test
    public void testIgnoreThrottlingCheckIfRetriableException() {
        try {
            try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
                workerExecutorHelperMockedStatic.when(() -> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler1");
                when(externalTask.getProcessDefinitionKey()).thenReturn("processDefnKey2");
                when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
                when(historyServiceRest.getExternalTaskCount(Mockito.any())).thenThrow(WorkflowRetriableException.class);
                Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
                Assert.assertEquals((Integer) 0, response.getLeft());
                Assert.assertEquals("processDefnKey2", response.getRight());
            }
        }
        catch (Exception e) {
            Assert.fail("Method should not throw exception");
        }
    }

    @Test(expected = WorkflowGeneralException.class)
    public void exceptionThrownIfNotRetriable() {
        try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
            workerExecutorHelperMockedStatic.when(() -> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler1");
            when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
            when(historyServiceRest.getExternalTaskCount(Mockito.any())).thenThrow(WorkflowGeneralException.class);
            Pair<Integer, String> response = throttleHandler.getExecutionCountAndWorkflow(proceedingJoinPoint);
        }
    }

    @Test
    public void testCheckAndExecuteWhenThrottlingEnabled_ErrorBreached() throws Throwable {
        try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
            workerExecutorHelperMockedStatic.when(() -> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler1");
            when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
            when(externalTask.getProcessDefinitionKey()).thenReturn("processKey");

            Map<String, Integer> resp = new HashMap<>();
            resp.put("count", 5);
            WASHttpResponse<Map<String, Integer>> httpResponse = WASHttpResponse.<Map<String, Integer>>builder()
                    .response(resp)
                    .build();
            when(historyServiceRest.getExternalTaskCount(Mockito.any())).thenReturn(httpResponse);

            when(throttleHelper.getThreshold("processKey", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY)).thenReturn(3);

            throttleHandler.execute(proceedingJoinPoint);

            Mockito.verify(workerExecutor).executeFailure(Mockito.any(), Mockito.any(), Mockito.any());
            Mockito.verify(proceedingJoinPoint, Mockito.times(0)).proceed();
        }
    }

    @Test
    public void testCheckAndExecuteWhenThrottlingEnabled_WarnBreached() throws Throwable {
        try (MockedStatic<WorkerExecutorHelper> workerExecutorHelperMockedStatic  = Mockito.mockStatic(WorkerExecutorHelper.class)) {
            workerExecutorHelperMockedStatic.when(()-> WorkerExecutorHelper.prepareHandlerName(Mockito.any())).thenReturn("was_workflowCustomTaskHandler1");
            when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
            when(externalTask.getProcessDefinitionKey()).thenReturn("processKey");

            Map<String, Integer> resp = new HashMap<>();
            resp.put("count", 5);
            WASHttpResponse<Map<String, Integer>> httpResponse = WASHttpResponse.<Map<String, Integer>>builder()
                    .response(resp)
                    .build();
            when(historyServiceRest.getExternalTaskCount(Mockito.any())).thenReturn(httpResponse);

            when(throttleHelper.getThreshold("processKey", ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY)).thenReturn(7);

            Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
            throttleWarnDiffMap.put(ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME, 10);

            when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);

            boolean response = throttleHandler.execute(proceedingJoinPoint);

            Mockito.verify(workerExecutor, Mockito.times(0)).executeFailure(Mockito.any(), Mockito.any(), Mockito.any());
            Assert.assertTrue(response);
        }
    }

    @Test
    public void testGetWarnDiff_WarnConfigPresent() {
        Map<ThrottleAttribute, Integer> throttleWarnDiffMap = new HashMap<>();
        throttleWarnDiffMap.put(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, 10);

        when(throttleConfigs.getWarnDiffCount()).thenReturn(throttleWarnDiffMap);
        Assert.assertEquals((Integer) 10, throttleHandler.getWarnDiff());
    }

    @Test
    public void testGetWarnDiff_WarnConfigNotPresent() {
        Assert.assertEquals((Integer) 0, throttleHandler.getWarnDiff());
    }

    @Test
    public void testExecuteWarn() {
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        throttleHandler.executeWarn(proceedingJoinPoint, 15);
        Mockito.verify(workerExecutor, Mockito.times(0)).executeFailure(Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testExecuteFailure() {
        when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
        throttleHandler.executeFailure(proceedingJoinPoint, 15);
        Mockito.verify(workerExecutor).executeFailure(Mockito.any(), Mockito.any(), Mockito.any());
    }

    private Object[] joinPointArgs() {
        Object[] args = new Object[3];
        args[0] = externalTask;
        args[1] = externalTaskService;
        return args;
    }
}
package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BACKSLASH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DOT_OPERATOR;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DYNAMIC_BPMN_BASE_TEMPLATE_RESOURCE_PATH;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DYNAMIC_BPMN_MIN_NUMBER_OF_APPROVERS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ELEMENT_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BaseTemplateElementType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DynamicBpmnBaseTemplateType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.text.MessageFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.FlowNodeImpl;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.ConditionExpression;
import org.camunda.bpm.model.bpmn.instance.EscalationEventDefinition;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.MessageEventDefinition;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;

/**
 * Utility class for creating Dynamic BPMN workflows.
 *
 * <AUTHOR>
 */
@UtilityClass
public class DynamicBpmnUtil {

  /**
   * This method checks if the bpmn model should be generated dynamically or not
   * on the basis of number of actions present in the bpmn model.
   * @param definition Definition
   * @return boolean
   */
  public boolean canGenerateBpmnDynamically(Definition definition) {

    List<WorkflowStep> workflowStepActions =
        definition.getWorkflowSteps().stream()
            .filter(workflowStep -> Objects.nonNull(workflowStep.getActionGroup()))
            .collect(Collectors.toList());

    String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definition);

    // Condition on number of call activities in the bpmn model
    return workflowStepActions.size() > getMinNumberOfActionsToBuildDynamicBpmn(actionKey);
  }

  private int getMinNumberOfActionsToBuildDynamicBpmn(String actionKey) {

    // Can move these values to MultiStepConfig in future as well
    CustomWorkflowType customWorkflowType = CustomWorkflowType.get(actionKey);
    if (CustomWorkflowType.APPROVAL.equals(customWorkflowType)) {
      return DYNAMIC_BPMN_MIN_NUMBER_OF_APPROVERS;
    }
    return 0;
  }

  public WorkflowStep getWorkflowStepFromId(
      String workflowId, Map<String, WorkflowStep> workflowStepMap) {
    return workflowStepMap.getOrDefault(workflowId, null);
  }

  public String getBaseTemplateResourcePath(String actionKey, ModelType modelType) {

    if (Objects.isNull(modelType)) {
      WorkflowLogger.logError("Model Type is found to be null.");
      throw new WorkflowGeneralException("Invalid Model Type");
    }

    return new StringBuilder(DYNAMIC_BPMN_BASE_TEMPLATE_RESOURCE_PATH)
        .append(modelType)
        .append(BACKSLASH)
        .append(DynamicBpmnBaseTemplateType.getTemplateResourceName(actionKey))
        .append(DOT_OPERATOR)
        .append(modelType)
        .toString();
  }

  public String getMessageNameFromFlowNode(FlowNode flowNode) {
    Optional<MessageEventDefinition> endEventMessageDefinition =
        flowNode.getChildElementsByType(MessageEventDefinition.class).stream().findFirst();

    return endEventMessageDefinition.isEmpty()
        ? null
        : endEventMessageDefinition.get().getMessage().getName();
  }

  public String getEscalationCodeFromFlowNode(FlowNode flowNode) {
    Optional<EscalationEventDefinition> startEventEscalationDefinition =
        flowNode.getChildElementsByType(EscalationEventDefinition.class).stream().findFirst();

    return startEventEscalationDefinition.isEmpty()
        ? null
        : startEventEscalationDefinition.get().getEscalation().getEscalationCode();
  }

  public boolean isNodeAnImplicitElement(
      FlowNode targetNode, BpmnModelInstance baseTemplateBpmnModelInstance) {
    FlowNodeImpl baseTemplateFlowNodeImpl =
        baseTemplateBpmnModelInstance.getModelElementById(targetNode.getId());
    Map<String, String> baseTemplateNodeExtensionProperties =
        BpmnProcessorUtil.getMapOfCamundaProperties(
            baseTemplateFlowNodeImpl.getExtensionElements());

    return Objects.nonNull(baseTemplateNodeExtensionProperties)
        && BaseTemplateElementType.IMPLICIT
        .getName()
        .equals(baseTemplateNodeExtensionProperties.get(ELEMENT_TYPE).toLowerCase());
  }

  public void addConditionToSequenceFlows(
      SequenceFlow sequenceFlow, FlowNode childNode, BpmnModelInstance bpmnModelInstance) {

    ConditionExpression conditionExpression =
        Objects.nonNull(sequenceFlow.getConditionExpression())
            ? sequenceFlow.getConditionExpression()
            : bpmnModelInstance.newInstance(ConditionExpression.class);

    conditionExpression.setTextContent(
        MessageFormat.format(
            "{0}{1}=={2}{3}",
            WorkflowConstants.DMN_VAR_OPEN_BRACE,
            new StringBuilder("decisionResult"),
            new StringBuilder("'").append(childNode.getId()).append("'"),
            WorkflowConstants.DMN_VAR_CLOSE_BRACE));

    sequenceFlow.setConditionExpression(conditionExpression);
  }

  public boolean isSourceAndTargetNodesConnected(FlowNode sourceNode, FlowNode targetNode) {
    Collection<SequenceFlow> outgoingSequenceFlows = sourceNode.getOutgoing();
    Optional<SequenceFlow> sequenceFlow =
        outgoingSequenceFlows.stream()
            .filter(
                outgoingSequenceFlow ->
                    targetNode.getId().equals(outgoingSequenceFlow.getTarget().getId()))
            .findFirst();
    return sequenceFlow.isPresent();
  }

  public Optional<Process> getProcessFromBpmnModelInstance(BpmnModelInstance bpmnModelInstance) {
    Collection<Process> processes = bpmnModelInstance.getModelElementsByType(Process.class);

    Optional<Process> processOptional = processes.stream().findFirst();

    return processOptional;
  }

  public void updateBusinessRuleTaskActivityIds(final BpmnModelInstance bpmnModelInstance,
      final String hashSum) {
    final Collection<BusinessRuleTask> businessRuleTaskList =
        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class);
    for (final BusinessRuleTask businessRuleTask : businessRuleTaskList) {
      String activityId = businessRuleTask.getId();
      activityId = new StringBuilder(activityId).append(UNDERSCORE).append(hashSum).toString();
      businessRuleTask.setId(activityId);
    }
  }

  public boolean isElementExplicit(String elementType) {
    return BaseTemplateElementType.EXPLICIT.getName().equals(elementType);
  }

}

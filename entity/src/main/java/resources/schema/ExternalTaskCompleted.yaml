description: "Entity for Workflow External Task as Complete"
properties:
  status:
    type: "string"
    description: "Status of the external task. SUCCESS/FAILED"
    intuitDataClassification: 'RESTRICTED'
  errorMessage:
    type: "string"
    description: "Reason for Error in case an external task failed."
    intuitDataClassification: 'RESTRICTED'
  errorDetails:
    type: "string"
    description: "Detailed Description for the reason for failure"
    intuitDataClassification: 'RESTRICTED'
  variables:
    type: "object"
    document: true
    description: "**context variables** to perform external task. These will be passed back IN to Camunda. Value will be a json object"
    intuitDataClassification: 'RESTRICTED'
  offeringId:
    type: "string"
    description: "identifier for the offering. For Eg: qblive/ttlive"
    intuitDataClassification: 'RESTRICTED'    
additionalProperties: false
required:
  - "status"
  - "offeringId"
# Mandatory Attribute for Topic schema creation
customerOwned: false

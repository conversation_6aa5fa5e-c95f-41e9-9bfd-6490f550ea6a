package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.helper.DefinitionDetailsFilter;
import com.intuit.v4.query.CompoundExpression;
import com.intuit.v4.query.FilterExpression;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FilterQueryPredicateProcesserTest {
  @Mock
  private DefinitionDetailsFilter definitionDetailsFilter;

  private CriteriaBuilder criteriaBuilder;

  @Rule
  public ExpectedException expectedEx = ExpectedException.none();

  @InjectMocks
  private FilterQueryPredicateProcessor filterQueryPredicateProcessor;

  @Before
  public void setUp() {

    prepareMocks();
  }

  private void prepareMocks() {
    criteriaBuilder = spy(CriteriaBuilder.class);
    CriteriaQuery<?> criteriaQuery = mock(CriteriaQuery.class);
    Root<?> root = mock(Root.class);
    doReturn(criteriaQuery).when(criteriaBuilder).createQuery(any());
    doReturn(root).when(criteriaQuery).from(DefinitionDetails.class);
  }

  @Test
  public void getPredicateFromQueryExpressionWithFilterExpressionTest(){
    FilterExpression filterExpression = prepareFilterExpression("recordType", "invoice");
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    Predicate predicate = mock(Predicate.class);
    when(definitionDetailsFilter.validateFilter(any())).thenReturn(true);
    when(definitionDetailsFilter.getFilter(filterExpression, detailsRoot)).thenReturn(predicate);
    Predicate predicate1 = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, filterExpression);
    Assert.assertNotNull(predicate1);
    Assert.assertEquals(predicate1, predicate);
  }

  @Test
  public void getPredicateFromQueryExpressionWithCompoundExpressionTest(){
    CompoundExpression compoundExpression = prepareCompoundExpressionWithTemplateName();
    Predicate predicate = mock(Predicate.class);
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    when(definitionDetailsFilter.validateFilter(any())).thenReturn(true);
    when(definitionDetailsFilter.getFilter(any(), any())).thenReturn(predicate);
    when(criteriaBuilder.or(any())).thenReturn(predicate);
    Predicate resultPredicate = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, compoundExpression);
    Assert.assertNotNull(resultPredicate);
    Assert.assertEquals(resultPredicate, predicate);
  }

  @Test
  public void getPredicateFromQueryExpressionWithCompoundLookupExpressionTestSuccess(){
    CompoundExpression compoundExpression = prepareCompoundExpressionWithLookupKeys("&&");
    Predicate predicate = mock(Predicate.class);
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    when(definitionDetailsFilter.getPredicateForLookupExpression(any(), any(), any(), any())).thenReturn(predicate);
    when(criteriaBuilder.and(any())).thenReturn(predicate);
    Predicate resultPredicate = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, compoundExpression);
    Assert.assertNotNull(resultPredicate);
    Assert.assertEquals(resultPredicate, predicate);
  }

  @Test
  public void getPredicateFromQueryExpressionWithIncorrectLookupExpressionOrderTest() throws Exception{
    expectedEx.expect(WorkflowGeneralException.class);
    expectedEx.expectMessage("Unsupported filter expression");
    CompoundExpression compoundExpression = prepareCompoundExpressionWithLookupKeysIncorrectOrder();
    Predicate predicate = mock(Predicate.class);
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    Predicate resultPredicate = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, compoundExpression);
  }

  @Test
  public void getPredicateFromQueryExpressionWithIncorrectLookupExpressionWithoutValueTest() throws Exception{
    expectedEx.expect(WorkflowGeneralException.class);
    expectedEx.expectMessage("Incorrect lookup filter expression");
    CompoundExpression compoundExpression = prepareCompoundExpressionWithLookupKeysWithoutValue();
    Predicate predicate = mock(Predicate.class);
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    Predicate resultPredicate = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, compoundExpression);
  }

  @Test
  public void getPredicateFromQueryExpressionWithIncorrectLookupExpressionOperationTest() throws Exception{
    expectedEx.expect(WorkflowGeneralException.class);
    expectedEx.expectMessage("Incorrect lookup filter expression");
    CompoundExpression compoundExpression = prepareCompoundExpressionWithLookupKeys("||");
    Predicate predicate = mock(Predicate.class);
    Root<DefinitionDetails> detailsRoot = criteriaBuilder.createQuery(DefinitionDetails.class).from(DefinitionDetails.class);
    Predicate resultPredicate = filterQueryPredicateProcessor.getPredicateFromQueryExpression(criteriaBuilder, detailsRoot, compoundExpression);
  }



  private FilterExpression prepareFilterExpression(String filterExpressionKey, String filterExpressionValue){
    if( Objects.nonNull(filterExpressionKey) && Objects.nonNull(filterExpressionValue)) {
      FilterExpression filterExpression = new FilterExpression();
      filterExpression.setProperty(filterExpressionKey);
      filterExpression.addArgs(filterExpressionValue);
      return filterExpression;
    }
    return null;
  }

  private CompoundExpression prepareCompoundExpressionWithTemplateName(){
    CompoundExpression compoundExpression = new CompoundExpression();
    compoundExpression.setOp("||");
    compoundExpression.addArgs(prepareFilterExpression("template.name", "invoiceapproval"));
    compoundExpression.addArgs(prepareFilterExpression("template.name", "customreminder"));
    return compoundExpression;
  }

  private CompoundExpression prepareCompoundExpressionWithLookupKeys(String operation){
    CompoundExpression compoundExpression = new CompoundExpression();
    compoundExpression.setOp(operation);
    compoundExpression.addArgs(prepareFilterExpression("lookupKeys.key", "id"));
    compoundExpression.addArgs(prepareFilterExpression("lookupKeys.value", "1234"));
    return compoundExpression;
  }

  private CompoundExpression prepareCompoundExpressionWithLookupKeysIncorrectOrder() {
    CompoundExpression compoundExpression = new CompoundExpression();
    compoundExpression.setOp("&&");
    compoundExpression.addArgs(prepareFilterExpression("lookupKeys.value", "1234"));
    compoundExpression.addArgs(prepareFilterExpression("lookupKeys.key", "id"));
    return compoundExpression;
  }

  private CompoundExpression prepareCompoundExpressionWithLookupKeysWithoutValue() {
    CompoundExpression compoundExpression = new CompoundExpression();
    compoundExpression.setOp("&&");
    compoundExpression.addArgs(prepareFilterExpression("lookupKeys.key", "id"));
    compoundExpression.addArgs(prepareFilterExpression("recordType", "invoiceapproval"));
    return compoundExpression;

  }



}

package com.intuit.appintgwkflw.wkflautomate.was.batch.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.DEFINITION_TYPE_USER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.MIGRATION_QUERY_ALL_VERSIONS;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.MIGRATION_QUERY_BY_VERSION;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_MIGRATION_READER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_MIGRATION_STEP;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_MIGRATION_WRITER;
import static com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobConstants.USER_TO_SINGLE_DEFINITION_STEP_NAME;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchJobType;
import com.intuit.appintgwkflw.wkflautomate.was.batch.BatchService;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfig;
import com.intuit.appintgwkflw.wkflautomate.was.batch.config.BatchJobConfigDetails;
import com.intuit.appintgwkflw.wkflautomate.was.batch.offline.BatchJobInitContext;
import com.intuit.appintgwkflw.wkflautomate.was.batch.util.MigrationTask;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.repository.dto.AuthDetailsDto;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import javax.persistence.EntityManagerFactory;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemStreamReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.database.JpaItemWriter;
import org.springframework.batch.item.database.JpaPagingItemReader;
import org.springframework.batch.item.database.orm.JpaNativeQueryProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;

@Service
@ConditionalOnExpression("${batch-job.stepConfig.userToSingleDefinitionMigration.enabled:false}")
public class UserToSingleDefinitionMigrationImpl extends BatchService<DefinitionDetails> {

  private EntityManagerFactory entityManagerFactory;
  private TemplateDetailsRepository templateDetailsRepository;
  private BatchJobConfig batchjobConfig;
  private BatchJobInitContext batchJobInitContext;
  private ThreadPoolTaskExecutor batchThreadPoolTaskExecutor;
  private MigrationServiceHelper migrationServiceHelper;
  private final StepBuilderFactory stepBuilderFactory;
  private final MetricLogger metricLogger;

  private final DefinitionDetailsRepository definitionDetailsRepository;

  @Qualifier(DataAccessConstants.OFFERING_AWARE_TM)
  private final PlatformTransactionManager transactionManager;
//  Used to store latest single definition templateId
  private static String singleTemplateId = null;

  public UserToSingleDefinitionMigrationImpl(EntityManagerFactory entityManagerFactory,
                                             TemplateDetailsRepository templateDetailsRepository, BatchJobConfig batchjobConfig,
                                             BatchJobInitContext batchJobInitContext, ThreadPoolTaskExecutor batchThreadPoolTaskExecutor,
                                             MigrationServiceHelper migrationServiceHelper, StepBuilderFactory stepBuilderFactory,
                                             PlatformTransactionManager transactionManager,
                                             DefinitionDetailsRepository definitionDetailsRepository, ApplicationContext applicationContext,
                                             MetricLogger metricLogger) {
    super(batchjobConfig, batchThreadPoolTaskExecutor, applicationContext);
    this.entityManagerFactory = entityManagerFactory;
    this.templateDetailsRepository = templateDetailsRepository;
    this.batchjobConfig = batchjobConfig;
    this.batchJobInitContext = batchJobInitContext;
    this.batchThreadPoolTaskExecutor = getThreadPoolTaskExecutor();
    this.migrationServiceHelper = migrationServiceHelper;
    this.stepBuilderFactory = stepBuilderFactory;
    this.transactionManager = transactionManager;
    this.definitionDetailsRepository = definitionDetailsRepository;
    this.metricLogger = metricLogger;
  }


  @Override
  @Bean(name=USER_TO_SINGLE_DEFINITION_MIGRATION_READER)
  @StepScope
  public ItemStreamReader<DefinitionDetails> reader() {
    JpaPagingItemReader<DefinitionDetails> reader = new JpaPagingItemReader<>() {
      @Override
      public int getPage() {
        return 0;
      }
    };
    BatchJobConfigDetails batchJobConfigDetails = getBatchConfig();
    try{

      Timestamp baseTimeStamp = Timestamp.valueOf(
          LocalDateTime.now().minusMinutes(batchJobConfigDetails.getBaseTimeJobStart()));

      Timestamp endTimeStamp = Timestamp.valueOf(
          LocalDateTime.now().plusMinutes(batchJobConfigDetails.getDurationInMinutes()));

      reader.setPageSize(getBatchSize());
      reader.setEntityManagerFactory(entityManagerFactory);

      JpaNativeQueryProvider<DefinitionDetails> queryProvider = new JpaNativeQueryProvider<>();

      String definitionType =
          StringUtils.isEmpty(batchJobConfigDetails.getDefinitionType())
              ? DEFINITION_TYPE_USER
              : batchJobConfigDetails.getDefinitionType();
      String migrationQuery =
          StringUtils.isEmpty(batchJobConfigDetails.getTemplateVersion())
              ? String.format(
                  MIGRATION_QUERY_ALL_VERSIONS,
                  batchJobConfigDetails.getTemplateName(),
                  definitionType,
                  batchJobConfigDetails.getRecordTypes(),
                  baseTimeStamp,
                  endTimeStamp)
              : String.format(
                  MIGRATION_QUERY_BY_VERSION,
                  batchJobConfigDetails.getTemplateName(),
                  definitionType,
                  batchJobConfigDetails.getRecordTypes(),
                  baseTimeStamp,
                  endTimeStamp,
                  batchJobConfigDetails.getTemplateVersion());

      queryProvider.setSqlQuery(migrationQuery);
      queryProvider.setEntityClass(DefinitionDetails.class);

      queryProvider.afterPropertiesSet();
      reader.setQueryProvider(queryProvider);
      reader.afterPropertiesSet();
      WorkflowLogger.logInfo("step=MigrationReaderComplete, status=success, MigrationQuery="+migrationQuery);
    } catch (Exception e) {
      WorkflowLogger.logError("step=MigrationReaderInit, status=failed, error=" + e);
      metricLogger.logErrorMetric(MetricName.USER_TO_SINGLE_DEFINITION_JOB, Type.APPLICATION_METRIC, e);
    }
    return reader;
  }

  @Override
  @Bean(name=USER_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR)
  public ItemProcessor<DefinitionDetails, DefinitionDetails> processor() {
    return definitionDetails -> {
      try{
        /*
         * Here we are fetching the latest single template id ,
         * Once we get it from db storing it in static variable, so it can be reuse by further
         * and no need to make extra db calls
         */
        if(null == singleTemplateId){
          singleTemplateId = MigrationServiceHelper.getSingleTemplateId(definitionDetails, templateDetailsRepository, getBatchConfig().getTemplateName());
        }
        batchJobInitContext.refreshTicketToContext(definitionDetails);

        //Executing migration in a new thread to avoid issues with batch transaction boundary
        //refer: https://docs.google.com/document/d/1-Xb8eB8JEn55bSuBIDOMiS3ay9QytTkvw3v0NRS-e-A/edit#
        State state = new State();
        state.addValue(WorkflowConstants.TEMPLATE_ID, singleTemplateId);
        new RxExecutionChain(state).next(new MigrationTask(migrationServiceHelper, definitionDetails, batchJobInitContext)).execute();

        //Since we are executing the update definition in another thread, batch job overwrites the old definition object
        //We will get the definition from DB and update the params of the existing definition object
        definitionDetailsRepository.findByDefinitionId(definitionDetails.getDefinitionId()).ifPresent(definitionDetailsUpdated -> copyDefinitionAttributes(definitionDetails, definitionDetailsUpdated));
        return definitionDetails;
      }
      catch (WorkflowGeneralException e) {
          definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
          WorkflowLogger.logError("step=MigrationProcessInitFailure, status=WorkflowGeneralException, error=" + e);
          metricLogger.logErrorMetric(MetricName.USER_TO_SINGLE_DEFINITION_JOB, Type.APPLICATION_METRIC, e);
      }
      catch (Exception e) {
        // Won't fetch in the current batch
        definitionDetails.setModifiedDate(Timestamp.valueOf(LocalDateTime.now()));
        WorkflowLogger.logError("step=MigrationProcessInitFailure, status=failed, error=" + e);
        metricLogger.logErrorMetric(MetricName.USER_TO_SINGLE_DEFINITION_JOB, Type.APPLICATION_METRIC, e);
      }
      return null;
    };
  }

  @Override
  @Bean(name=USER_TO_SINGLE_DEFINITION_MIGRATION_WRITER)
  public ItemWriter<DefinitionDetails> writer() {
    JpaItemWriter<DefinitionDetails> writer = new JpaItemWriter<>();
    writer.setEntityManagerFactory(entityManagerFactory);
    return writer;
  }

  @Override
  @Bean(name = USER_TO_SINGLE_DEFINITION_MIGRATION_STEP)
  public Step createStep(@Qualifier(USER_TO_SINGLE_DEFINITION_MIGRATION_READER) ItemReader<DefinitionDetails> reader,
                         @Qualifier(USER_TO_SINGLE_DEFINITION_MIGRATION_PROCESSOR) ItemProcessor<DefinitionDetails, DefinitionDetails> processor,
                         @Qualifier(USER_TO_SINGLE_DEFINITION_MIGRATION_WRITER) ItemWriter<DefinitionDetails> writer) {
    return stepBuilderFactory
        .get(USER_TO_SINGLE_DEFINITION_STEP_NAME)
        .transactionManager(transactionManager)
        .<DefinitionDetails, DefinitionDetails>chunk(getChunkSize())
        .reader(reader)
        .processor(processor)
        .writer(writer)
        .taskExecutor(batchThreadPoolTaskExecutor)
        .build();
  }

  @Override
  public BatchJobType getName() {
    return BatchJobType.USER_TO_SINGLE_DEFINITION_MIGRATION;
  }

  /**
   * We will get the updated definition from DB and update the params of the existing definition object
   * Spring batch stores the definition object reference, so when we do the migration in a separate thread, the changes in this object are not reflected
   * Only internal status changes when we update the definition so copying that attribute
   * TODO: Create a generic class that can copy attributes of one object to another
   */
  private void copyDefinitionAttributes(DefinitionDetails definitionDetailsOld, DefinitionDetails definitionDetailsNew){
      definitionDetailsOld.setInternalStatus(definitionDetailsNew.getInternalStatus());
  }
}

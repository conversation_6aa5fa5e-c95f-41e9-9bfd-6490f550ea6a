package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilderTestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import java.util.HashMap;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
public class MultiStepActionBuilderTest {
    public static String APPROVAL_ACTION_KEY = "approval";
    public static String REMINDER_ACTION_KEY = "reminder";
    public static String NOTIFICATION_ACTION_KEY = "notification";
    public static String RECORD_TYPE_INVOICE = "invoice";
    public static String CALL_ACTIVITY_ID = "sendForApproval";
    @Mock
    private WASContextHandler wasContextHandler;
    @Mock
    private TemplateActionBuilder actionBuilder;
    private CustomWorkflowConfig customWorkflowConfig;
    @InjectMocks
    private MultiStepActionBuilder multiStepActionBuilder;

    @Before
    @SneakyThrows
    public void setUp() {
        customWorkflowConfig = TemplateBuilderTestHelper.getConfig();
        multiStepActionBuilder = new MultiStepActionBuilder(wasContextHandler, actionBuilder);
    }

    @Test
    public void testProcessWorkflowStep() {
        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(NextTypeEnum.ACTION.value());
        configStep.setAction(CALL_ACTIVITY_ID);
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(StepTypeEnum.CONDITION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        WorkflowStep result = multiStepActionBuilder.processWorkflowStep(
                record,
                APPROVAL_ACTION_KEY,
                false,
                configStep,
                null,
                new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.ACTION, result.getStepType());
        Assert.assertNotNull(result.getActionGroup());
        Assert.assertEquals(APPROVAL_ACTION_KEY, result.getActionGroup().getActionKey());

        WorkflowStep.StepNext nextPath = new WorkflowStep.StepNext();
        nextPath.setWorkflowStepId(Integer.toString(2));
        result = multiStepActionBuilder.processWorkflowStep(
                record,
                APPROVAL_ACTION_KEY,
                false,
                configStep,
                nextPath,
                new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.ACTION, result.getStepType());
        Assert.assertNotNull(result.getActionGroup());
        Assert.assertEquals(APPROVAL_ACTION_KEY, result.getActionGroup().getActionKey());
        Assert.assertNotNull(result.getId());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testProcessWorkflowStepForNotification() {
        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(NextTypeEnum.ACTION.value());
        configStep.setAction(CALL_ACTIVITY_ID);
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(StepTypeEnum.CONDITION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        try {
            multiStepActionBuilder.processWorkflowStep(
                    record,
                    NOTIFICATION_ACTION_KEY,
                    false,
                    configStep,
                    null,
                new HashMap<>());
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException error) {
            Assert.assertEquals(WorkflowError.ACTION_ID_MAPPER_NOT_FOUND_IN_TEMPLATE_CONFIG, error.getWorkflowError());
            throw error;
        }
    }

    @Test
    public void testProcessReminderWorkflowStep() {
        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(NextTypeEnum.ACTION.value());
        configStep.setAction(CALL_ACTIVITY_ID);
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(StepTypeEnum.CONDITION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
        Record record = customWorkflowConfig.getRecordObjForType(RECORD_TYPE_INVOICE);
        WorkflowStep result = multiStepActionBuilder.processWorkflowStep(
            record,
            REMINDER_ACTION_KEY,
            false,
            configStep,
            null,
            new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.ACTION, result.getStepType());
        Assert.assertNotNull(result.getActionGroup());
        Assert.assertEquals(REMINDER_ACTION_KEY, result.getActionGroup().getActionKey());

        WorkflowStep.StepNext nextPath = new WorkflowStep.StepNext();
        nextPath.setWorkflowStepId(Integer.toString(2));
        result = multiStepActionBuilder.processWorkflowStep(
            record,
            REMINDER_ACTION_KEY,
            false,
            configStep,
            nextPath,
            new HashMap<>());
        Assert.assertNotNull(result);
        Assert.assertEquals(StepTypeEnum.ACTION, result.getStepType());
        Assert.assertNotNull(result.getActionGroup());
        Assert.assertEquals(REMINDER_ACTION_KEY, result.getActionGroup().getActionKey());
        Assert.assertNotNull(result.getId());
    }
}

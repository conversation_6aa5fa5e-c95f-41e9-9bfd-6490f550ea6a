package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep;

import com.intuit.v4.workflows.WorkflowStep;
import java.util.Collection;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.camunda.bpm.model.dmn.instance.Rule;

/**
 * This class is used to represent all the variables that are used to create dmn rule inside dmn
 *
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
public class RuleLineInstance {
  //value of index column for the dmn rule lines that we will add
  private int currentIndexColumnValue;

  //step id of current workflow step
  private String workflowStepId;

  //collection of dmn rules in which we will add more rules(dmn rows)
  private Collection<Rule> rules;

  private List<WorkflowStep> siblingSteps;

  public void addRules(Rule rule) {
    rules.add(rule);
  }

}

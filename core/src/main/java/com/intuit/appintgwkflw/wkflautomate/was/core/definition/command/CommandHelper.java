package com.intuit.appintgwkflw.wkflautomate.was.core.definition.command;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessage;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.CorrelateAllMessageAsync;
import com.intuit.async.execution.request.State;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.experimental.UtilityClass;

import org.springframework.util.CollectionUtils;

/** <AUTHOR> */
@UtilityClass
public class CommandHelper {

/**
   * validate the definition id
   *
   * @param definitionInstance input definition details
   */
  public void validateDefinitionInstance(DefinitionInstance definitionInstance) {
    WorkflowVerfiy.verifyNull(definitionInstance, WorkflowError.INVALID_DEFINITION_DETAILS);
    validateDefinition(definitionInstance.getDefinition());
  }

  /**
   * validate the definition id
   *
   * @param definition input definition details
   */
  public void validateDefinition(Definition definition) {
    WorkflowVerfiy.verify(
        !Optional.ofNullable(definition)
            .map(Definition::getId)
            .map(GlobalId::getLocalId)
            .isPresent(),
        WorkflowError.INVALID_DEFINITION_DETAILS);
  }

  /**
   * @param definitionInstance input definition details
   * @param ownerId input company id
   * @return State request object for enabled
   */
  public State prepareStateRequestForEnabled(
      DefinitionInstance definitionInstance, String ownerId) {
    State inputRequestForDeploy = new State();
    inputRequestForDeploy.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, definitionInstance);

    inputRequestForDeploy.addValue(AsyncTaskConstants.REALM_ID_KEY, ownerId);

    if(Objects.nonNull(definitionInstance.getDefinitionDetails().getWorkflowId())) {
      inputRequestForDeploy.addValue(
              AsyncTaskConstants.WORKFLOW_ID_KEY,
              definitionInstance.getDefinitionDetails().getWorkflowId());
    }
    inputRequestForDeploy.addValue(
        AsyncTaskConstants.DEFINITION_ID_KEY,
        definitionInstance.getDefinition().getId().getLocalId());

    inputRequestForDeploy.addValue(
        AsyncTaskConstants.ACTIVATE_ACTION_KEY,
        WorkflowStatusEnum.ENABLED.equals(definitionInstance.getDefinition().getStatus()));
    return inputRequestForDeploy;
  }
  /**
   * @param definitionInstance input definition details
   * @param ownerId input company id
   * @return State request object for disabled
   */
  public State prepareStateRequestForDisabledAndDelete(
      final DefinitionInstance definitionInstance,
      final String ownerId,
      final boolean isCorrelateAsync) {

    State inputRequestForDisabled = new State();
    inputRequestForDisabled.addValue(AsyncTaskConstants.REALM_ID_KEY, ownerId);
    final DefinitionDetails definitionDetails = definitionInstance.getDefinitionDetails();

    if (!isCorrelateAsync) {
      final Map<String, CorrelateAllMessage.CorrelateKey> correlateKeysMap = new HashMap<>();
      correlateKeysMap.put(
          CorrelationKeysEnum.DEFINITION_KEY.getName(),
          new CorrelateAllMessage.CorrelateKey(
              definitionDetails.getDefinitionKey(),
              CorrelationKeysEnum.DEFINITION_KEY.getDataType()));
      inputRequestForDisabled.addValue(AsyncTaskConstants.CORRELATE_KEYS, correlateKeysMap);
    } else {
      /** adding required filters for correlate-async */
      List<CorrelateAllMessageAsync.ProcessVariable> variables =
          Collections.singletonList(
              CorrelateAllMessageAsync.ProcessVariable.builder()
                  .name(CorrelationKeysEnum.DEFINITION_KEY.getName())
                  .value(definitionDetails.getDefinitionKey())
                  .build());
      CorrelateAllMessageAsync.ProcessInstanceQuery processInstanceQuery =
          CorrelateAllMessageAsync.ProcessInstanceQuery.builder()
              .businessKey(ownerId)
              .variables(variables)
              .build();

      inputRequestForDisabled.addValue(
          AsyncTaskConstants.PROCESS_INSTANCE_QUERY, processInstanceQuery);
    }

    inputRequestForDisabled.addValue(AsyncTaskConstants.IS_CORRELATE_ASYNC, isCorrelateAsync);
    return inputRequestForDisabled;
  }

  /**
   * validate the definition object and template object [IDs]
   *
   * @param definitionInstance input definition details
   */
  public void validate(DefinitionInstance definitionInstance) {
    WorkflowVerfiy.verifyNull(definitionInstance, WorkflowError.INVALID_INPUT);
    validateTemplateDetail(definitionInstance.getDefinition());
    validateDefinition(definitionInstance.getDefinition());
  }

  /**
   * validate Template details object
   *
   * @param definition input definition details
   */
  private void validateTemplateDetail(Definition definition) {
    WorkflowVerfiy.verify(
        !Optional.ofNullable(definition)
            .map(template -> definition.getTemplate())
            .map(Template::getId)
            .map(GlobalId::getLocalId)
            .isPresent(),
        WorkflowError.INVALID_TEMPLATE_DETAILS);
  }
  /**
   * @param definitionDetails input definition detail list
   * @return list of definition id
   */
  public List<String> fetchDefinitionIdList(List<DefinitionDetails> definitionDetails) {
    if (CollectionUtils.isEmpty(definitionDetails)) {
      return null;
    }
    return definitionDetails
        .stream()
        .map(DefinitionDetails::getDefinitionId)
        .collect(Collectors.toList());
  }

  /**
   * @param messageName signal to be sent to trigger process
   * @param triggerDetailsList {@link TriggerDetails}
   * @return message to be sent
   */
  public String getTriggerNameFromMessageName(
      final String messageName, final List<TriggerDetails> triggerDetailsList) {

    final Optional<TriggerDetails> triggerDetailsOptional =
        triggerDetailsList
            .stream()
            .filter(
                triggerDetailsFiltered ->
                    triggerDetailsFiltered.getTriggerName().contains(messageName.toLowerCase()))
            .findFirst();
    return triggerDetailsOptional.isPresent()
        ? triggerDetailsOptional.get().getTriggerName()
        : null;
  }
  public boolean isCorrelateAsyncEnabled(FeatureManager featureManager, String ownerId) {
    return featureManager.getBoolean(AsyncTaskConstants.CORRELATE_ASYNC_ENABLED_FF, ownerId);
  }
}

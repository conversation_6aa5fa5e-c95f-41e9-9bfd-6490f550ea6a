package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;

import javax.persistence.AttributeConverter;

/** <AUTHOR> */
public class CreatorTypeConverter implements AttributeConverter<CreatorType, String> {

  @Override
  public String convertToDatabaseColumn(CreatorType attribute) {
    return attribute != null ? attribute.getCreatorType() : null;
  }

  @Override
  public CreatorType convertToEntityAttribute(String dbData) {
    return dbData != null ? CreatorType.fromType(dbData) : null;
  }
}

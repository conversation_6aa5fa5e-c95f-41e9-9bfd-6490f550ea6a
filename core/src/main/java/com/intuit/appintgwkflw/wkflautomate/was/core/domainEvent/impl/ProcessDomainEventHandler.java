package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.DomainEventPublisherCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.DomainEventMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.system.interfaces.BaseEntity;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Handler for handling Process Domain Events
 *
 * <AUTHOR>
 */
@Component(WorkflowBeansConstants.PROCESS_HANDLER)
@AllArgsConstructor
public class ProcessDomainEventHandler
    extends DomainEventPublisherCapability<ProcessDetails, BaseEntity> {
  private final WASContextHandler contextHandler;
  private final DomainEventRepository domainEventRepository;
  private final ProcessDetailsRepository processDetailsRepository;

  @Override
  public DomainEventName getName() {
    return DomainEventName.PROCESS;
  }

  @Override
  public DomainEvent<? extends BaseEntity> transform(
      final DomainEntityRequest<ProcessDetails> domainEntityRequest) {

    // In case of Start Process call via SLA, we're setting this as null (For non-eventing calls.
    // This will be internally constructed from WASContext)
    if (Objects.isNull(domainEntityRequest.getEventHeaderEntity())) {
      EventHeaderEntity eventHeaderEntity =
          EventHeaderEntity.builder()
              .tid(
                  Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                      .orElse(UUID.randomUUID().toString()))
              .offeringId(contextHandler.get(WASContextEnums.OFFERING_ID))
              .build();
      domainEntityRequest.setEventHeaderEntity(eventHeaderEntity);
    }

    final DomainEventHeaders domainEventHeaders =
        prepareProcessDomainEventHeaders(domainEntityRequest);
//  Only fills Parent process details when parentId is present.
    MultiStepUtil.fillParentProcessDetails(domainEntityRequest.getRequest(), processDetailsRepository);
    Process process =
        DomainEventMapper.mapEntityToProcessDomainEvent(domainEntityRequest);

    DomainEvent<BaseEntity> domainEvent =
        DomainEvent.builder()
            .topic(getTopicDetails(DomainEventName.PROCESS))
            .partitionKey(process.getId())
            .headers(domainEventHeaders)
            .region(getRegionDetails())
            .payload(ObjectConverter.toJson(process))
            .version(domainEventHeaders.getEntityversion())
            .build();

    return domainEvent;
  }

  private DomainEventHeaders prepareProcessDomainEventHeaders(
      final DomainEntityRequest<ProcessDetails> domainEntityRequest) {
    final ProcessDetails processDetails = domainEntityRequest.getRequest();
    final EntityChangeAction entityChangeAction = domainEntityRequest.getEntityChangeAction();
    final EventHeaderEntity headerEntity = domainEntityRequest.getEventHeaderEntity();

    int version = processDetails.getEntityVersion();
    // End Events will be published using Transition Events
    String accountId =
        ObjectUtils.isNotEmpty(contextHandler.get(WASContextEnums.OWNER_ID))
            ? contextHandler.get(WASContextEnums.OWNER_ID)
            : String.valueOf(processDetails.getOwnerId());
    DomainEventHeaders.DomainEventHeadersBuilder headersBuilder = DomainEventHeaders.builder()
        .intuitTid(
            Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                .orElse(UUID.randomUUID().toString()))
        .entityId(processDetails.getProcessId()) // Setting process id
        .entityType(Process.class.getName())
        .accountId(accountId)
        .offeringId(
            StringUtils.isNotBlank(headerEntity.getOfferingId())
                ? headerEntity.getOfferingId()
                : processDetails.getDefinitionDetails().getTemplateDetails().getOfferingId())
        .entitychangeaction(entityChangeAction)
        .entityversion(version);

    addDomainEventHeaders(headersBuilder, Process.SCHEMA_VERSION, Process.URN, entityChangeAction, version);

    return headersBuilder.build();
  }
}

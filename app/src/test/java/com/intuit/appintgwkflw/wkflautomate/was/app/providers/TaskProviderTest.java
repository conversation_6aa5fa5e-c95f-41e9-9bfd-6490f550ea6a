package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.providerservice.WorkflowTaskService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.tasks.Task;
import com.intuit.v4.workflows.tasks.TaskAttribute;
import com.intuit.v4.workflows.tasks.TaskTypeEnum;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TaskProviderTest {

	@InjectMocks
	private TaskProvider taskProvider;

	@Mock
	private WASContextHandler wasContextHandler;

	@Mock
	private WorkflowTaskService workflowTaskService;

	private RequestContext context;
	private QueryHelper queryHelper;

  @Before
  public void prepareMockData() {
    MockitoAnnotations.openMocks(this);
    Authorization authorization = new Authorization().realm("12345");
    context = Mockito.mock(RequestContext.class);
    Mockito.when(context.getAuthorization()).thenReturn(authorization);
    Mockito.doNothing().when(wasContextHandler).addKey(Mockito.any(), Mockito.anyString());
    Mockito.when(workflowTaskService.getWorkflowTasks(Mockito.any()))
        .thenReturn(Collections.singletonList(populateTask()));
    queryHelper = Mockito.mock(QueryHelper.class);
    Map<String, String> input = new HashMap<>();
    input.put("workflowId", "wId");
    Mockito.when(queryHelper.getArg(WorkflowConstants.BY)).thenReturn(Optional.ofNullable(input));
  }

	@Test
	public void taskReadListTest() throws IOException {
		ListResult<Task> taskResult = taskProvider.wasReadList(context, queryHelper);
		Assert.assertNotNull(taskResult);
		Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, taskResult.getResult().stream().findFirst().orElse(null).getType());
	}

	@SuppressWarnings("rawtypes")
	@Test
	public void taskReadOneTest() throws IOException {
		GlobalId globalId = GlobalId.builder().setLocalId("test").build();
		SingleResult<Task> taskResult = taskProvider.wasReadOne(context, globalId, queryHelper);
		Assert.assertNotNull(taskResult);
		Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, taskResult.getResult().getType());
	}

	@Test
	public void taskWorkflowTaskTest() throws IOException {
		ListResult<Task> taskResult = taskProvider.workflowTask(context, queryHelper);
		Assert.assertNotNull(taskResult);
		Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, taskResult.getResult().stream().findFirst().orElse(null).getType());
	}


	@Test
	public void taskWorkflowTaskTest_ServiceException() throws IOException {
		Mockito.when(workflowTaskService.getWorkflowTasks(Mockito.any())).thenThrow(new WorkflowGeneralException(
				WorkflowError.UNKNOWN_ERROR));
		ListResult<Task> taskResult = taskProvider.workflowTask(context, queryHelper);
		Assert.assertNotNull(taskResult);

		Assert.assertEquals(WorkflowError.UNKNOWN_ERROR.name(), taskResult.getError().getMessage());
	}


	@Test
	public void taskWorkflowTaskTest_SystemError() throws IOException {
		Mockito.when(workflowTaskService.getWorkflowTasks(Mockito.any())).thenThrow(new RuntimeException());
		ListResult<Task> taskResult = taskProvider.workflowTask(context, queryHelper);
		Assert.assertNotNull(taskResult);
		Assert.assertEquals(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage(), taskResult.getError().getMessage());
	}

  @Test
  public void taskWriteOneTest() throws IOException {
    MockitoAnnotations.openMocks(this);
    Authorization authorization = new Authorization().realm("12345");
    Mockito.when(workflowTaskService.updateWorkflowTasks(Mockito.any())).thenReturn(populateTask());

    Task task = Mockito.mock(Task.class);
    SingleResult<Task> taskResult = taskProvider.wasWriteOne(context, task);
    Assert.assertNotNull(taskResult);
    Assert.assertEquals(TaskTypeEnum.SYSTEM_TASK, taskResult.getResult().getType());
  }

	private Task populateTask() {
		Task task = new Task();
		task.setName("Test Task");
		task.setType(TaskTypeEnum.SYSTEM_TASK);
		task.setActivityId("testActivityId");
		task.setActivityType("serviceTask");
		task.setActivityInstanceId("testActivityInstanceId");
		task.setExternalRefId("testExternalRefId");
		task.setStatus("created");
		task.setCreatedDate(DateTime.now());
		task.setUpdatedDate(DateTime.now());
		task.setCompletedDate(DateTime.now());
		TaskAttribute taskAttributes = new TaskAttribute();
		taskAttributes.setName("key1");
		taskAttributes.setValue("value1");
		task.setAttributes(Arrays.asList(taskAttributes));
		return task;
	}
}

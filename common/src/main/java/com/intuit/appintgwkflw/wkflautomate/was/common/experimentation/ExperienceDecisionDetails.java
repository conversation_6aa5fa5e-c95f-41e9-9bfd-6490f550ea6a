package com.intuit.appintgwkflw.wkflautomate.was.common.experimentation;

import lombok.Data;
import lombok.Getter;

/**
 * Decision Details class
 * This class is responsible for holding the decision details passed from the BPMN for evaluating the experiment details.
 * <AUTHOR>
 */
@Getter
@Data
public class ExperienceDecisionDetails {
    /**
     * Feature Flag name.
     * This is a mandatory field required for FF Evaluation used in AB Experiment Task.
     */
    String featureFlag;
    /**
     * Default value for the feature flag evaluation.
     * This is a mandatory field required for FF Evaluation used in AB Experiment Task.
     */
    String defaultValue;
    /**
     * Variation Type of the feature flag
     * This is an Optional field defaults to String type.
     *  Possible values are Boolean or String.
     */
    String variationType;
    /**
     * Context Map (in Json string) for the feature flag evaluation
     * This is an Optional field.
     */
    String contextMap;
    /**
     * Entity ID (in Json string) for the feature flag evaluation.
     * This is an Optional field.
     */
    String entityId;
}

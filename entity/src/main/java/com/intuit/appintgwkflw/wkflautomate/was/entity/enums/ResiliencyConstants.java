package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.experimental.UtilityClass;

@UtilityClass
public class ResiliencyConstants {
  public final String WAS_DB = "wasDB";
  public final String APP_CONNECT = "appConnect";
  public final String CAMUNDA = "camunda";
  public final String UCS = "ucs";
  public final String CAMUNDA_SIGNAL = "camunda_signal";
  public final String APP_CONNECT_WORKFLOW_TASK_HANDLER = "appConnect-workflow-task-handler";
  public final String HTTP_CLIENT_RETRY = "http_client";
  public final String V4_GRAPHQL_CLIENT = "v4_graphql_client";
  public final String APOLLO_GRAPHQL_CLIENT = "apollo_graphql_client";
  public final String EVENT_HANDLER = "event_handler";
  public final String CIRCUIT_BREAKER_PREFIX = "handler=CircuitBreaker ";
  public final String CIRCUIT_BREAKER_STATE_METRIC = "resilience4j.circuitbreaker.state";
  public final String THROTTLE_ACTIVITY_PREFIX = "throttle=Activity ";
  public final String THROTTLE_DEFINITION_PER_WORKFLOW_PER_REALM_PREFIX = "throttle=definitionsPerWorkflowPerRealm ";
  public final String THROTTLE_DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME_PREFIX = "throttle=definitionsPerWorkflowInTimeframe ";

  public final String EVENT_PUBLISH = "event_publish";
}

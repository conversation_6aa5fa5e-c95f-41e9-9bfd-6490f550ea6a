package com.intuit.appintgwkflw.wkflautomate.was.core.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttle;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttles;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ThrottleAspectTest {
    @Mock private ProceedingJoinPoint proceedingJoinPoint;
    @Mock private Throttle throttle;
    @Mock private Throttles throttles;
    @Mock private ThrottleService throttleActivityService = new ThrottleService() {
        public ThrottleAttribute getAttribute() { return null; }
        public Pair<Integer, String> getExecutionCountAndWorkflow(ProceedingJoinPoint joinPoint) { return null; }

        @Override
        public Integer getWarnDiff() {
            return null;
        }

        public boolean isThrottlingEnabled(ProceedingJoinPoint joinPoint) { return false; }
        public void executeFailure(final ProceedingJoinPoint joinPoint, Integer count) {}
        public void executeWarn(final ProceedingJoinPoint joinPoint, Integer count) {}
    };

    @Mock private ThrottleService throttleDefinitionService = new ThrottleService() {
        public ThrottleAttribute getAttribute() { return null; }
        public Pair<Integer, String> getExecutionCountAndWorkflow(ProceedingJoinPoint joinPoint) { return null; }

        @Override
        public Integer getWarnDiff() {
            return null;
        }

        public boolean isThrottlingEnabled(ProceedingJoinPoint joinPoint) { return false; }
        public void executeFailure(final ProceedingJoinPoint joinPoint, Integer count) {}
        public void executeWarn(final ProceedingJoinPoint joinPoint, Integer count) {}
    };

    @InjectMocks private ThrottleAspect throttleAspect;

    @Test
    public void testExecute() throws Throwable {
        ThrottleServiceHandlers.addHandler(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, throttleActivityService);
        Mockito.when(throttle.attribute()).thenReturn(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY);
        throttleAspect.execute(proceedingJoinPoint, throttle);
        Mockito.verify(throttleActivityService).canContinueExecution(Mockito.any());
        Mockito.verify(throttleDefinitionService, Mockito.times(0)).canContinueExecution(Mockito.any());
    }

    @Test
    public void testExecute_repeatingAnnotation() throws Throwable {
        ThrottleServiceHandlers.addHandler(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY, throttleActivityService);
        Mockito.when(throttle.attribute()).thenReturn(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY);
        Mockito.when(throttles.value()).thenReturn(new Throttle[]{throttle});

        throttleAspect.execute(proceedingJoinPoint, throttles);
        Mockito.verify(throttleActivityService).canContinueExecution(Mockito.any());
        Mockito.verify(throttleDefinitionService, Mockito.times(0)).canContinueExecution(Mockito.any());
    }
}

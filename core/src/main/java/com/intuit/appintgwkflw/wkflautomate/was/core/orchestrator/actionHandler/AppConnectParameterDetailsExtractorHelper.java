package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ON_DEMAND_APPROVAL;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.v4.workflows.Template;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AppConnectParameterDetailsExtractorHelper {
  private final CustomWorkflowUserConfiguredParameterDetailsExtractor customWorkflowUserConfiguredParameterDetailsExtractor;
  private final MultiStepParameterDetailsExtractor multiStepParameterDetailsExtractor;
  private final PrecannedWorkflowParameterDetailsExtractor
      precannedWorkflowParameterDetailsExtractor;
  private final DefaultParameterDetailsExtractor
      defaultParameterDetailsExtractor;
  private final CustomWorkflowNonUserConfiguredParameterDetailsExtractor
      customWorkflowNonUserConfiguredParameterDetailsExtractor;
  private final CustomWorkflowConfig customWorkflowConfig;
  private final OnDemandParameterDetailExtractor onDemandParameterDetailExtractor;

  /**
   * As part of single definition, parameter details are filled using the user placeholder values.
   * Returns the corresponding parameter detail extractor based on workflow type If the flag is off,
   * or if the handler-details are prefilled, the old/default parameter detail extractor would be
   * returned. TODO: Introduce template type single condition on migration single template.
   *
   * @param workflowTemplate
   * @param definitionDetails
   *  @param handlerDetails
   * @param workerActionRequest
   * @return AppConnectParameterDetailExtractor
   */
  public AppConnectParameterDetailExtractor getAppConnectParameterDetailExtractor(
      Template workflowTemplate, DefinitionDetails definitionDetails,String handlerDetails, WorkerActionRequest workerActionRequest) {

    String taskDetails = workerActionRequest.getInputVariables().get(WorkflowConstants.TASK_DETAILS);
    boolean isCalledChildProcess = workerActionRequest.isCalledProcess();

    if(isRequestForOnDemandWorkflowTask(workerActionRequest)){
      return onDemandParameterDetailExtractor;
    }

    if (SingleDefinitionUtil.isSingleDefPrecannedTriggerFeasible(workflowTemplate, definitionDetails)) {
      return precannedWorkflowParameterDetailsExtractor;
    }

    HandlerDetails actionHandlerDetails =
        ObjectConverter.fromJson(handlerDetails, HandlerDetails.class);
    // If handler details are present in the custom template, then corresponding
    // parameter details would also be
    // extracted out from the template using default parameter detail extractor.
    // This is particularly done for custom workflows where some actions have handler id and
    // parameter details defined in the template itself.
    // TODO: Move handler details and parameter details to the config for every action.

    boolean singleDefCustomTriggerFeasible =
        SingleDefinitionUtil.isSingleDefCustomTriggerFeasible(workflowTemplate, definitionDetails);

    if (singleDefCustomTriggerFeasible && Objects.isNull(actionHandlerDetails.getTaskHandler())) {
      // in case the process is a called process, the parameter extraction is done using the multi extractor
      if (isCalledChildProcess) {
        return multiStepParameterDetailsExtractor;
      }
      return customWorkflowUserConfiguredParameterDetailsExtractor;
    }

    // This for non user configured blocks
    HandlerDetails.TaskDetails handlerTaskDetails =
        ObjectConverter.fromJson(taskDetails, HandlerDetails.TaskDetails.class);
    if (singleDefCustomTriggerFeasible
        && !Objects.isNull(handlerTaskDetails)
        && !Objects.isNull(handlerTaskDetails.getRequired())
        && !handlerTaskDetails.getRequired()) {
      return customWorkflowNonUserConfiguredParameterDetailsExtractor;
    }

    return defaultParameterDetailsExtractor;
  }

  /**
   * The method returns true for all On demand approval sub actions from config .
   * Eg: Create task, send company email etc (Parameters fetched from config instead of database)
   * For tasks with a handler eg: Close task, parameters are fetched in the same way as the normal flow
   * Create an incident in case of an invalid/null entityType
   *
   * @param workerActionRequest
   * @return
   */
  private boolean isRequestForOnDemandWorkflowTask(WorkerActionRequest workerActionRequest){
    Optional<String> entityType = Optional.ofNullable(workerActionRequest.getInputVariables().get(ENTITY_TYPE)).map(type -> RecordType.fromTypeOrValue(type)).map(
        RecordType::getRecordType);
    // entityType is null in case of old approval templates like invoiceApproval
    if(entityType.isEmpty()){
      return false;
    }
    String actionKey = CustomWorkflowType.APPROVAL.getActionKey();
    return (Boolean.TRUE.toString().equalsIgnoreCase(workerActionRequest.getInputVariables().getOrDefault(ON_DEMAND_APPROVAL, Boolean.FALSE.toString())))
     && CustomWorkflowUtil.getOnDemandConfiguredActions(customWorkflowConfig,entityType.get(),actionKey).contains(workerActionRequest.getActivityId());
  }
}

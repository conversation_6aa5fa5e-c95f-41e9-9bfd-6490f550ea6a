package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.RuleLineProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.RuleLineInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.RuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;

/**
 * This class parses the create workflow payload and creates the dmn for multi-condition workflow.
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiConditionBusinessRuleTaskHandler {

  private final RuleLineProcessorFactory ruleLineProcessorFactory;

  /**
   * This method does the processing of Dmn task Adding ruleLines in the dmn table based on the
   * conditions present in the subtree of the root Workflow Step (subTree from rootWorkflowStep till
   * an action step is encountered for each branch) 1) Create DMN Headers 2) Popuate DMN Headers Map
   * 3) Create DMN tree
   *
   * @param dmnModelInstance   {@Link DmnModelInstance}
   * @param workflowStep       {@link WorkflowStep}
   * @param definitionInstance {@link DefinitionInstance}
   * @param activityIds        list of activity Ids of the next actions{@link List}
   */
  public Map<String, String> workflowDecisionHandler(DmnModelInstance dmnModelInstance,
      WorkflowStep workflowStep,
      DefinitionInstance definitionInstance,
      List<String> activityIds) {

    WorkflowStepCondition firstWorkflowStepCondition = workflowStep.getWorkflowStepCondition();
    boolean isSingleStepMultiCondition = MultiStepUtil.isSingleStepMultiCondition(
        definitionInstance.getDefinition());

    List<WorkflowStep> siblings = MultiStepUtil.findRootSiblings(
        definitionInstance.getDefinition().getWorkflowSteps());
    RuleLineProcessor ruleLineProcessor = ruleLineProcessorFactory.getHandler(
        isSingleStepMultiCondition, siblings);

    WorkflowVerfiy.verifyNullForList(
        WorkflowError.INVALID_INPUT, definitionInstance.getBpmnModelInstance(),
        definitionInstance.getDmnModelInstanceList());

    if (!isSingleStepMultiCondition) {
      WorkflowVerfiy.verifyNull(firstWorkflowStepCondition, WorkflowError.INVALID_INPUT);
    }

    DecisionTable decisionTable =
        ruleLineProcessor.createDmnHeaders(dmnModelInstance, workflowStep, definitionInstance);

    // input and output column header map for the Dmn
    Map<String, Map<String, DmnHeader>> attributeToHeaderMap =
        ruleLineProcessor.buildDmnHeadersMap(decisionTable);

    WorkflowLogger.logInfo(
        "step=createDmnHeaders attributeToDmnColumnHeaderMap=%s", attributeToHeaderMap);

    Collection<Rule> rules = decisionTable.getRules();

    Map<String, String> stepIdToActivityIdMap = new HashMap<>();

    ruleLineProcessor.buildDmn(definitionInstance,
        buildRuleEntity(workflowStep, rules, siblings),
        activityIds,
        attributeToHeaderMap,
        stepIdToActivityIdMap,
        dmnModelInstance);

    WorkflowLogger.logInfo(
        "step=createDmnTree status=Completed");

    return stepIdToActivityIdMap;
  }

  private RuleLineInstance buildRuleEntity(WorkflowStep workflowStep, Collection<Rule> rules,
      List<WorkflowStep> siblings) {

    return RuleLineInstance.builder().currentIndexColumnValue(WorkflowConstants.START_INDEX_VALUE)
        .workflowStepId(String.valueOf(workflowStep.getId()))
        .siblingSteps(siblings)
        .rules(rules)
        .build();
  }
}

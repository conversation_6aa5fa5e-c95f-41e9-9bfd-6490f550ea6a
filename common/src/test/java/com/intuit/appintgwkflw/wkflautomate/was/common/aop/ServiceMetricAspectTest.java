package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest.WorkflowLoggerRequestBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.V4GraphqlClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql.WASV4GraphqlResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.v4.Error;
import com.intuit.v4.interaction.InteractionException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;


@RunWith(MockitoJUnitRunner.class)
public class ServiceMetricAspectTest {

  @Mock
  private ServiceMetricLogger serviceMetricLogger;

  @InjectMocks
  private ServiceMetricAspect serviceMetricAspect;

  @Test
  public void testSuccess() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);
    setupMocks(joinPoint, serviceMetric, HttpStatus.OK);
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }

  @Test
  public void testAccepted() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);
    setupMocks(joinPoint, serviceMetric, HttpStatus.ACCEPTED);
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }

  @Test(expected = HttpClientErrorException.class)
  public void testNotFound() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);
    setupMocks(joinPoint, serviceMetric, HttpStatus.NOT_FOUND);
    serviceMetricAspect.execute(joinPoint, serviceMetric);
  }

  private void setupMocks(final ProceedingJoinPoint joinPoint, final ServiceMetric serviceMetric,
      HttpStatus status) throws Throwable {

    WASHttpResponse<String> wasHttpResponse = WASHttpResponse.<String>builder()
        .status(status)
        .isSuccess2xx(status.is2xxSuccessful()).build();
    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);

    Parameter[] parameters = {};

    doReturn(ServiceName.APP_CONNECT).when(serviceMetric).serviceName();
    doReturn(ServiceType.HTTP).when(serviceMetric).type();
    doReturn(parameters).when(method).getParameters();
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    if (status.is2xxSuccessful()) {
      doReturn(wasHttpResponse).when(joinPoint).proceed();
    } else {
      doThrow(new HttpClientErrorException(status)).when(joinPoint).proceed();
    }
  }

  @Test
  public void testSuccess_GraphQL() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);

    doReturn(ServiceName.OTHER).when(serviceMetric).serviceName();
    doReturn(ServiceType.V4_GRAPHQL).when(serviceMetric).type();
    doReturn(V4GraphqlClient.GRAPHQL_MUTATE_METHOD).when(serviceMetric).methodName();

    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);
    Mockito.when(method.getName()).thenReturn("write");
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    Mockito.when(joinPoint.proceed()).thenReturn(WASV4GraphqlResponse.builder().build());

    WASV4GraphqlRequest request = WASV4GraphqlRequest.builder().serviceName(ServiceName.HUMAN_TASK)
        .build();

    Mockito.when(joinPoint.getArgs()).thenReturn(new Object[]{request});
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }

  @Test
  public void testSuccess_Read_GraphQL() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);

    doReturn(ServiceName.OTHER).when(serviceMetric).serviceName();
    doReturn(ServiceType.V4_GRAPHQL).when(serviceMetric).type();
    doReturn(V4GraphqlClient.GRAPHQL_READ_METHOD).when(serviceMetric).methodName();

    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);
    Mockito.when(method.getName()).thenReturn("read");
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    Mockito.when(joinPoint.proceed()).thenReturn(WASV4GraphqlResponse.builder().build());

    WASV4GraphqlRequest request = WASV4GraphqlRequest.builder().serviceName(ServiceName.HUMAN_TASK)
        .build();

    Mockito.when(joinPoint.getArgs()).thenReturn(new Object[]{request});
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }

  @SuppressWarnings("rawtypes")
  @Test
  public void testSuccess_ErrorCode_GraphQL() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);

    doReturn(ServiceName.OTHER).when(serviceMetric).serviceName();
    doReturn(ServiceType.V4_GRAPHQL).when(serviceMetric).type();
    doReturn(V4GraphqlClient.GRAPHQL_MUTATE_METHOD).when(serviceMetric).methodName();

    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);
    Mockito.when(method.getName()).thenReturn("write");
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    List<Error> errors = new ArrayList<>();
    Error error1 = new Error();
    error1.setCode("DST-1");
    errors.add(error1);
    WASV4GraphqlResponse graphqlResponse = WASV4GraphqlResponse.builder().error(true).errors(errors)
        .build();

    Mockito.when(joinPoint.proceed()).thenReturn(graphqlResponse);

    WASV4GraphqlRequest request = WASV4GraphqlRequest.builder()
        .serviceName(ServiceName.HUMAN_TASK)
        .build();

    Mockito.when(joinPoint.getArgs()).thenReturn(new Object[]{request});
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }


  @SuppressWarnings("rawtypes")
  @Test
  public void testSuccess_ErrorCode_GraphQL_HttpStatus() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);

    doReturn(ServiceName.OTHER).when(serviceMetric).serviceName();
    doReturn(ServiceType.V4_GRAPHQL).when(serviceMetric).type();
    doReturn(V4GraphqlClient.GRAPHQL_MUTATE_METHOD).when(serviceMetric).methodName();

    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);
    Mockito.when(method.getName()).thenReturn("write");
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    List<Error> errors = new ArrayList<>();
    Error error1 = new Error();
    error1.setCode("DST-1");
    error1.setMessage("Service Unavailable");
    errors.add(error1);
    WASV4GraphqlResponse graphqlResponse = WASV4GraphqlResponse.builder().error(true).errors(errors)
        .build();

    Mockito.when(joinPoint.proceed()).thenReturn(graphqlResponse);

    WASV4GraphqlRequest request = WASV4GraphqlRequest.builder()
        .serviceName(ServiceName.HUMAN_TASK)
        .build();

    Mockito.when(joinPoint.getArgs()).thenReturn(new Object[]{request});
    Object response = serviceMetricAspect.execute(joinPoint, serviceMetric);
    assertNotNull(response);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void testError_GraphQL() throws Throwable {

    final ProceedingJoinPoint joinPoint = mock(ProceedingJoinPoint.class);
    final ServiceMetric serviceMetric = mock(ServiceMetric.class);

    doReturn(ServiceName.OTHER).when(serviceMetric).serviceName();
    doReturn(ServiceType.V4_GRAPHQL).when(serviceMetric).type();
    doReturn(V4GraphqlClient.GRAPHQL_MUTATE_METHOD).when(serviceMetric).methodName();

    MethodSignature signature = mock(MethodSignature.class);
    Method method = mock(Method.class);
    Mockito.when(method.getName()).thenReturn("write");
    doReturn(method).when(signature).getMethod();
    doReturn(signature).when(joinPoint).getSignature();

    Mockito.when(joinPoint.proceed())
        .thenThrow(new WorkflowRetriableException(new InteractionException("Service Unavailable")));

    WASV4GraphqlRequest request = WASV4GraphqlRequest.builder().serviceName(ServiceName.HUMAN_TASK)
        .build();

    Mockito.when(joinPoint.getArgs()).thenReturn(new Object[]{request});
    serviceMetricAspect.execute(joinPoint, serviceMetric);
  }


  @Test
  public void serviceName_test_NoParamPresent() {
    WorkflowLoggerRequestBuilder requestBuilder = WorkflowLoggerRequest.builder();
    ServiceMetric serviceMetric = new ServiceMetric() {

      @Override
      public Class<? extends Annotation> annotationType() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      public ServiceType type() {
        // TODO Auto-generated method stub
        return null;
      }

      @Override
      public ServiceName serviceName() {
        // TODO Auto-generated method stub
        return ServiceName.HUMAN_TASK;
      }

      @Override
      public String methodName() {
        // TODO Auto-generated method stub
        return null;
      }
    };
    String serviceName = ReflectionTestUtils
        .invokeMethod(serviceMetricAspect, "serviceName", requestBuilder, serviceMetric);
    Assert.assertEquals(ServiceName.HUMAN_TASK.name(), serviceName);
  }
  
  @Test
  public void statusCode_SetForHttpErrorMsg() {
	  WorkflowLoggerRequestBuilder requestBuilder = WorkflowLoggerRequest.builder();
	  Exception ex = new HttpServerErrorException(HttpStatus.SERVICE_UNAVAILABLE,
			  HttpStatus.SERVICE_UNAVAILABLE.getReasonPhrase());
	  ReflectionTestUtils.invokeMethod(serviceMetricAspect, "setStatusCode", ex, requestBuilder);
	  WorkflowLoggerRequest request = requestBuilder.build();
	  Assert.assertEquals(Integer.toString(HttpStatus.SERVICE_UNAVAILABLE.value()), 
			  request.getLoggingParams().get("status"));
  }
  
  @Test
  public void statusCode_NotSet() {
	  WorkflowLoggerRequestBuilder requestBuilder = WorkflowLoggerRequest.builder();
	  Exception ex = new RuntimeException("Some error");
	  ReflectionTestUtils.invokeMethod(serviceMetricAspect, "setStatusCode", ex, requestBuilder);
	  WorkflowLoggerRequest request = requestBuilder.build();
	  Assert.assertNull(request.getLoggingParams().get("status"));
  }

}
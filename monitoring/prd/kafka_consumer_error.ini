[IPF - was-event-in - PRD - Kafka Consumer Error]
alert_metric_name = rawsum(align(60s, sum, ratediff(ts("iks.intuit.dataexchange.pipelineframework.kafka.consume.entry.count.total" and status="FAILURE" and  namespace=appintgwkflw-wkflautomate-waseventin-usw2-prd))))
severity = SEVERE
threshold = 5
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =



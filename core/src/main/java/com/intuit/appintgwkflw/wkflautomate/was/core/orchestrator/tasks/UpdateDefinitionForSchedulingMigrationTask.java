package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SchedulingServiceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

import static com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger.logInfo;


/**
 * Task implementation for updating the definition for scheduling migration.
 * This class handles the update of definition data and placeholder values with specified time,
 * and deletes the scheduler details for the given definition.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public class UpdateDefinitionForSchedulingMigrationTask implements Task {
    private DefinitionDetails definitionDetails;
    private EventScheduleHelper eventScheduleHelper;

    /**
     * Executes the task to update the definition for scheduling migration.
     * This method updates the definition data and placeholder values with random hours and minutes,
     * deletes the scheduler details, and logs the operations. If an error occurs, it updates the state
     * with the failure information.
     *
     * @param state The state object containing the current execution state.
     * @return The updated state object after executing the task.
     */
    @Override
    public State execute(State state) {
        WorkflowVerfiy.verifyNull(definitionDetails, WorkflowError.INVALID_INPUT);
        String definitionId = definitionDetails.getDefinitionId();

        byte[] updatedDefinitionData = ObjectUtils.isNotEmpty(definitionDetails.getDefinitionData()) ? SchedulingServiceUtil.getUpdateDefinitionDataWithTime(definitionDetails.getDefinitionData(), state.getValue(AsyncTaskConstants.REALM_ID_KEY)) : null;
        String updatedPlaceholderValues = SchedulingServiceUtil.getUpdatedPlaceholderValuesWithTime(definitionDetails.getPlaceholderValue(), state.getValue(AsyncTaskConstants.REALM_ID_KEY));
        logInfo("Updating the definition for scheduling migration. definitionId=%s", definitionId);
        eventScheduleHelper.updateDefinitionForSchedulingMigration(definitionId, definitionDetails, updatedDefinitionData, updatedPlaceholderValues);
        logInfo("Definition updated for scheduling migration. definitionId=%s", definitionId);
        return state;
    }

    @Override
    public State onError(State state){
        state.addValue(AsyncTaskConstants.UPDATE_DEFINITION_TASK_FAILURE,true);
        return state;
    }
}
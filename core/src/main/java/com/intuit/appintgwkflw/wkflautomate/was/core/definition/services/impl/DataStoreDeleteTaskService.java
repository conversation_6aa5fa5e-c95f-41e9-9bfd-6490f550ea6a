package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TransactionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TransactionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * <p>Performs Data Store CleanUp during Downgrade for Templates in WAS.
 */

@Service
@AllArgsConstructor
public class DataStoreDeleteTaskService {


  private ProcessDetailsRepository processDetailsRepository;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private ActivityProgressDetailsRepository activityProgressDetailsRepository;
  private TransactionDetailsRepository txnDetailsRepository;
  private SchedulerDetailsRepository schedulerDetailsRepository;
  private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;

  /**
   * DB clean up for the given realmId for downgrade.
   *
   * @param workerActionRequest
   * @return
   */
  @Transactional
  public Map<String, Object> execute(WorkerActionRequest workerActionRequest) {

    String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);

    List<DefinitionDetails> definitionDetailsList =
        definitionDetailsRepository.findByOwnerId(Long.valueOf(realmId));

    // No Workflow Definition Exists for that company then log then simply logging the message
    if (CollectionUtils.isEmpty(definitionDetailsList)) {
      logInfo("No Workflow Definition Exists for the Company");
    } else {
      deleteDefinitions(definitionDetailsList);
      logInfo("Process Definitions deleted for RealmId=%s Successfully", realmId);
    }

    return Map.of(
        new StringBuilder(workerActionRequest.getActivityId())
            .append(UNDERSCORE)
            .append(RESPONSE.getName())
            .toString(),
        Boolean.TRUE.toString());
  }

    /**
     * Deleting the process from the DB
     * @param processDetailsList
     */
    @Transactional
    public void deleteProcess(List<String> processDetailsList) {

      Optional<List<ProcessDetails>> childProcessDetails =
              processDetailsRepository.findByParentIdIn(processDetailsList);

      processDetailsList.addAll(
      childProcessDetails.orElse(Collections.emptyList())
          .stream().map(ProcessDetails::getProcessId)
          .collect(Collectors.toList())
  );

      /**
       * Fetches Activity id and txn_id only to save n/w bandwidth using processInstanceId.
       * Activity and Transaction needs to be purged.
       */
      Optional.ofNullable(activityProgressDetailsRepository.findByProcessDetailsIn(processDetailsList))
          .ifPresent(activityProgressDetails -> {
            Set<Long> txnIds = activityProgressDetails.stream()
                .map(ActivityProgressDetails::getTxnDetails)
                .filter(Objects::nonNull)
                .map(TransactionDetails::getId)
                .collect(Collectors.toSet());
            Set<String> activityDetails = activityProgressDetails.stream()
                .map(ActivityProgressDetails::getId)
                .collect(Collectors.toSet());
            /**
             * Delete activity based on Ids fetched from above step.
             */
            Optional.ofNullable(activityDetails).ifPresent(activityDetailsList ->
                activityProgressDetailsRepository.deleteByIdIn(activityDetailsList));
            /**
             * Deletes all transaction instances based on txnIds fetched from above step.
             */
            Optional.ofNullable(txnIds).ifPresent(txnIdList ->
                txnDetailsRepository.deleteByIdIn(txnIdList));
            logInfo("Activity and Transactions records purged.");
          });

      /** This will ensure that Downgrade Process doesn't get deleted. */
      processDetailsRepository.deleteByProcessIdIn(processDetailsList);
  }

  @Transactional
  public void deleteDefinitions(List<DefinitionDetails> definitionDetailsList) {
	List<String> processDetails = processDetailsRepository
          .findByDefinitionDetailsIn(definitionDetailsList);

    deleteProcess(processDetails);

      /** Delete all the schedulerDetails **/
      schedulerDetailsRepository.deleteByDefinitionDetailsIn(definitionDetailsList);

      List<DefinitionDetails> childDefinitionDetails =
              definitionDetailsRepository.findByParentIdIn(
                      definitionDetailsList.stream()
                              .map(DefinitionDetails::getDefinitionId)
                              .collect(Collectors.toList())
              ).orElse(Collections.emptyList());
      
      /** Delete all the definition activity details **/
      definitionActivityDetailsRepository.deleteByDefinitionDetailsIn(
              ListUtils.union(
                      definitionDetailsList, 
                      childDefinitionDetails
              )
      );

      /** Deleting all the definitions except for System Definition for that realmId */
      definitionDetailsRepository.deleteAllByDefinitionIdIn(
          definitionDetailsList.stream()
              .map(DefinitionDetails::getDefinitionId)
              .collect(Collectors.toList()));
}

  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
  }

}

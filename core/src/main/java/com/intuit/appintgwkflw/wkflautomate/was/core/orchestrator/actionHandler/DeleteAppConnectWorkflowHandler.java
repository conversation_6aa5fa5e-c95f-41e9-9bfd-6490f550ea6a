package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables.RESPONSE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response.AppConnectWorkflowResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This class is implementation for executing an external task of BPMN to delete the Non-WAS
 * AppConnect workflow for the provided realmId. It leverage deleteAppConnect method of
 * AppConnectServiceImpl which needs offlineTicket to make the call successfully
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class DeleteAppConnectWorkflowHandler extends WorkflowTaskHandler {

  private AuthDetailsService authDetailsService;
  private AppConnectService appConnectService;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.DELETE_APPCONNECT_WORKFLOW_HANDLER;
  }

  @Override
  protected <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;

    String activityId = workerActionRequest.getActivityId();
    if (WorkflowTaskUtil.isWorkflowFilterPresent(workerActionRequest)) {
      return ImmutableMap.of(
          new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
          Boolean.TRUE.toString());
    }
    final String realmId = workerActionRequest.getInputVariables().get(INTUIT_REALMID);
    final AuthDetails authDetails = authDetailsService.getAuthDetailsFromRealmId(realmId);
    List<AppConnectWorkflowResponse> appConnectWorkflows =
        appConnectService.getAppConnectWorkflows(realmId, authDetails);
    List<String> workflowIds = fetchNonWasWorkflowIds(appConnectWorkflows);

    /**
     * In case no active non-WAS workflow exists, log the same and move forward. No Action is
     * required at the AppConnect end. The method calls all the active workflow in parallel manner
     */
    appConnectService.deleteWorkflows(workflowIds, authDetails);
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Total Number of Workflows Deleted=%s", workflowIds.size())
                .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
                .downstreamServiceName(DownstreamServiceName.DELETE_WORKFLOW_APPCONNECT)
                .className(this.getClass().getName()));

    return ImmutableMap.of(
        new StringBuilder(activityId).append(UNDERSCORE).append(RESPONSE.getName()).toString(),
        Boolean.TRUE.toString());
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    metricLogger.logErrorMetric(MetricName.DELETE_APPCONNECT_WORKFLOW, Type.APP_CONNECT_METRIC, exception);
  }

  /**
   * Return list of Non-WAS Workflows workflow ids
   *
   * @param appConnectWorkflows
   * @return
   */
  private List<String> fetchNonWasWorkflowIds(
      List<AppConnectWorkflowResponse> appConnectWorkflows) {
    return Objects.isNull(appConnectWorkflows)
        ? Collections.emptyList()
        : appConnectWorkflows.stream()
            .filter(Objects::nonNull)
            .filter(
                workflow ->
                    WorkflowConstants.NON_WAS_WORKFLOW_TYPE.equalsIgnoreCase(
                        workflow.getWorkflowType())
                        && WorkflowConstants.ACTIVE_WORKFLOW.equalsIgnoreCase(
                        workflow.getStatus().getStatus()))
            .map(workflow -> workflow.getStatus().getWorkflowId())
            .collect(Collectors.toList());
  }
}

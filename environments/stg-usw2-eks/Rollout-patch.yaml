kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  labels:
    env: stg-usw2-eks
  name: rollout
spec:
  strategy:
    canary:
      canaryMetadata:
        annotations:
          role: canary
        labels:
          role: canary
      stableMetadata:
        annotations:
          role: stable
        labels:
          role: stable
      steps:
      - setWeight: 10
      - pause: {duration: 5m}
      - setWeight: 30
      - pause: {duration: 5m}
      - setWeight: 50
      - pause: {duration: 5m}
      - setWeight: 80
      - pause: {duration: 5m}
  # Set replicas to null when enabling HPA, sample: 'replicas: null'
  replicas: null
  template:
    metadata:
      labels:
        env: stg-usw2-eks
        splunk-index: messagemesh_stg
      annotations:
        iam.amazonaws.com/role: k8s-appintgwkflw-wkflautomate-waseventin-usw2-stg
    spec:
      containers:
      - name: app
        env:
        - name: APP_ENV
          value: stg
        - name: APP_CONFIG_NAME
          value: messagemeshconfig
        - name: RUNTIME_ID
          value: <<RUNTIME_ID>>
        - name: MESSAGE_MESH_SRC_TOPICS
          value: <<MESSAGE_MESH_SRC_TOPICS>>
        - name: EVENT_BUS_IDPS_POLICY_ID
          value: <<EVENT_BUS_IDPS_POLICY_ID>>
        - name: MESSAGE_MESH_SRC_CONSUMER_GROUP
          value: ipf-sample-processor
        - name: MESSAGE_MESH_PIPELINE_FILENAME
          value: sample-pipeline.yml
        - name: SPRING_CONFIG_IDPS_POLICY_ID
          value: <<SPRING_CONFIG_IDPS_POLICY_ID>>
        - name: ASSET_ID
          value: '4110678153014047831'
        - name: APP_ID
          value: Intuit.appintgwkflw.wkflautomate.waseventin
        - name: APP_SECRET
          value: '{secret}idps:/messageMesh/appSecret'
        - name: HOSTED_CONFIG_BRANCH
          value: stg
      initContainers:
      - name: segment-app-init
        env:
        - name: APP_ENV
          value: stg
        - name: SEGMENT_CLUSTER_ROLE_ARN
          value: arn:aws:iam::536910636111:role/shared.sbg-qbo-prod-usw2-k8s
        - name: SEGMENT_IDPS_APPLIANCE
          value: MSaaSBU02-PRODUCTION-RJPH0E.pd.idps.a.intuit.com
        - name: SEGMENT_IDPS_POLICY_ID
          value: p-vdpuprtfpj5i
        - name: AWS_SDK_LOAD_CONFIG
          value: "1"
        - name: AWS_ROLE_SESSION_NAME
          value: kiam-kiam

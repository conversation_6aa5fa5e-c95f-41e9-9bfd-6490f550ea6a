package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.BPMN_START_EVENTS;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants.TRIGGER_TRANSACTION_ENTITY;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3RunTimeHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.V3StartProcess;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.TriggerStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTriggerResponse;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import com.intuit.async.execution.request.State;
import java.util.Collection;
import java.util.Collections;
import java.util.Map;

import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 */
public class StartUserDefinitionTaskTest {

  private V3StartProcess startProcess = Mockito.mock(V3StartProcess.class);

  private WASContextHandler contextHandler = Mockito.mock(WASContextHandler.class);

  private V3RunTimeHelper runTimeHelper = Mockito.mock(V3RunTimeHelper.class);

  private final MetricLogger metricLogger = Mockito.mock(MetricLogger.class);

  public StartUserDefinitionTask getTask(DefinitionDetails definitionDetails) {

    return new StartUserDefinitionTask(startProcess, contextHandler, runTimeHelper,
        definitionDetails, metricLogger);
  }

  @Test
  public void testErrorStarting() {

    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=1234";
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(header);
    Mockito.when(startProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_START_PROCESS_ERROR));
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    StartUserDefinitionTask startUserDefinitionTask = getTask(definitionDetails);
    State response = startUserDefinitionTask.execute(getStateRequest());
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("defId");
    Assert.assertNotNull(resp);
    Assert.assertEquals(TriggerStatus.ERROR_STARTING_PROCESS, resp.getStatus());
  }

  @Test
  public void testStartingProcess() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=1234";
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(header);
    Mockito.when(startProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", "processId"));
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    StartUserDefinitionTask startUserDefinitionTask = getTask(definitionDetails);
    State response = startUserDefinitionTask.execute(getStateRequest());
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("defId");
    Assert.assertNotNull(resp);
    Assert.assertEquals(TriggerStatus.PROCESS_STARTED, resp.getStatus());
  }

  @Test
  public void testStartingProcessNoAction() {
    String header = "Intuit_IAM_Authentication intuit_token=345 ,intuit_realmid=1234";
    Mockito.when(contextHandler.get(Mockito.any())).thenReturn(header);
    Mockito.when(startProcess.startProcess(Mockito.any(), Mockito.any(), Mockito.any()))
        .thenReturn(Collections.singletonMap("id", ""));
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    StartUserDefinitionTask startUserDefinitionTask = getTask(definitionDetails);
    State response = startUserDefinitionTask.execute(getStateRequest());
    Assert.assertNotNull(response);
    WorkflowTriggerResponse resp = response.getValue("defId");
    Assert.assertNotNull(resp);
    Assert.assertEquals(TriggerStatus.NO_ACTION, resp.getStatus());
  }

  /** @return */
  private State getStateRequest() {
    State inputRequest = new State();
    TransactionEntity transactionEntity = Mockito.mock(TransactionEntity.class);
    @SuppressWarnings("unchecked")
    Map<String, Object> startEvents = Mockito.mock(Map.class);
    inputRequest.addValue(BPMN_START_EVENTS, startEvents);
    inputRequest.addValue(TRIGGER_TRANSACTION_ENTITY, transactionEntity);
    return inputRequest;
  }
}

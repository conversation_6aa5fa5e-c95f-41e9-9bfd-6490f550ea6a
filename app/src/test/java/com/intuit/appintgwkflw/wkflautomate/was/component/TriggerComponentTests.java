package com.intuit.appintgwkflw.wkflautomate.was.component;

import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import com.intuit.appintgwkflw.wkflautomate.was.Application;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.reactive.function.client.WebClient;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;


@SpringBootTest(classes = Application.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("component-test")
@WireMockTest
@Tag("componentTest")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class TriggerComponentTests {

    @LocalServerPort
    private int port;

    private WebClient webClient;

    @Autowired
    private ProcessDetailsRepository processDetailsRepository;

    private static final String TRIGGER_PROCESS_JSON = "src/test/resources/payload/trigger-process.json";

    private static final String SIGNAL_PROCESS_JSON = "src/test/resources/payload/signal-process.json";


    @BeforeEach
    public void setup() {

        webClient = WebClient.builder()
                .baseUrl("http://localhost:" + port)
                .defaultHeader(HttpHeaders.AUTHORIZATION,
                        "Intuit_IAM_Authentication intuit_token_type=IAM-Ticket,intuit_token=token," +
                                "intuit_appid=appId,intuit_app_secret=secret," +
                                "intuit_userid=9130353521960306,intuit_realmid=9130353747084236")
                .build();

        ComponentTest.startWireMockServer();
    }

    @Test
    @Order(1)
    @DisplayName("Trigger Start Process")
    public void testTriggerStartProcess() throws IOException {

        String triggerProcessJson = Files.readString(Paths.get(TRIGGER_PROCESS_JSON));

        List<String> mappings = Arrays.asList("mappings/startProcess.json", "mappings/appConnectSubscription.json", "mappings/deploymentCreate.json",
                "mappings/appConnectWorkflow.json", "mappings/appConnectActivate.json", "mappings/appConnectDelete.json", "mappings/createSchedule.json");

        ComponentTest.createMockedMappings(mappings);
        TemplateTestUtil.createCustomTemplate(webClient);
        DefinitionTestUtil.createCustomReminder(webClient);

        String triggerCustomDefinition = webClient.post()
                .uri("/v2/trigger")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(triggerProcessJson)
                .header("intuit_tid", "trigger-process")
                .retrieve()
                .bodyToMono(String.class)
                .block();

        Assertions.assertNotNull(triggerCustomDefinition);

        ProcessDetails processDetails = processDetailsRepository.findByRecordIdAndOwnerId(String.valueOf(3), 9130353747084236L).get();

        Assertions.assertEquals(ProcessStatus.ACTIVE, processDetails.getProcessStatus());
        Assertions.assertEquals("3", processDetails.getRecordId());
    }


    @Test
    @Order(2)
    @DisplayName("Signal Waiting Process")
    public void testSignalProcess() throws IOException {

        List<String> mappings = Arrays.asList("mappings/signalProcess.json");

        ComponentTest.createMockedMappings(mappings);

        String signalProcessJson = Files.readString(Paths.get(SIGNAL_PROCESS_JSON));

        String signalProcess = webClient.post()
                .uri("/v2/trigger")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(signalProcessJson)
                .header("intuit_tid", "signal-testing")
                .retrieve()
                .bodyToMono(String.class)
                .block();

        Assertions.assertNotNull(signalProcess);
        JSONObject response = new JSONObject(signalProcess);
        Assertions.assertEquals("SUCCESS", response.getString("status"));

        JSONArray signalResponse = response.getJSONObject("response").getJSONArray("triggers");

        JSONObject trigger = signalResponse.getJSONObject(0);
        Assertions.assertEquals( "invoice custom swati", trigger.getString("definitionName"));
        Assertions.assertEquals( "c92ab8db-7259-11ee-a4dc-acde48001122", trigger.getString("processId"));
        Assertions.assertEquals("PROCESS_SIGNALLED", trigger.getString("status"));    }

}
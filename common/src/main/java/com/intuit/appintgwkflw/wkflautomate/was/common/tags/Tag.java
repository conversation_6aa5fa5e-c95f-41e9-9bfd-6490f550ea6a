package com.intuit.appintgwkflw.wkflautomate.was.common.tags;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
/*
* The internal object to be used in the System Tag contains version_key
* */
public class Tag implements Serializable {
  public String version;
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;


import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MigrationServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.migration.BulkSingleDefinitionMigrationService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.migration.SingleDefInputMigration;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service(WorkflowConstants.BULK_DEF_MIGRATION_SERVICE)
@RequiredArgsConstructor
public class BulkDefinitionMigrationServiceImpl implements
    BulkSingleDefinitionMigrationService {

  private final DefinitionServiceHelper definitionServiceHelper;
  private final WASContextHandler contextHandler;
  private final MigrationServiceHelper migrationServiceHelper;
  private final OfflineTicketClient offlineTicketClient;

  @Override
  public void migrateDefinition(
      SingleDefInputMigration singleDefInputMigration) {
    singleDefInputMigration.getBpmnDefinitionIds().stream().forEach(definitionId -> {
      logInfo(String.format("step=sdefMigrationInit definitionId=%s", definitionId));
      try {
//      Fetch Definition Details
        DefinitionDetails definitionDetails = definitionServiceHelper.findByDefinitionId(
            definitionId);

//      Set Auth in the context
        Authorization authorization = setContext(definitionDetails);
//      Helper class which does the migration for the definitionId
        Definition updatedDefinition = migrationServiceHelper.migrateDefinition(definitionDetails,
            authorization, singleDefInputMigration.getUpdatedTemplateId());

        logInfo(String.format(
            "step=sdefMigrationStarted oldDefinitionId=%s newDefinitionId=%s, workflowId=%s", definitionId,
            updatedDefinition.getId().getLocalId(), updatedDefinition.getConnectorWorkflowId()));
      } catch (WorkflowGeneralException e) {
//      For Any Exceptions log it and move forward to next defnid. It should be retried from developer again, after checking from splunk
        logError(String.format(
            "step=sdefMigrationError oldDefinitionId=%s error=%s", definitionId,
            e));
      }
    });
  }

  /**
   * @param definitionDetails
   * @return Generates the offline Auth header Ticket from offlineTicket. These details are also set
   * to context, since current user context is not valid. Also setRealmSystemUserContext since, it
   * will be used later in skipping generation of additional OfflineTktRefresh
   */
  private Authorization setContext(DefinitionDetails definitionDetails) {
    String offlineTicketHeader = offlineTicketClient
            .getSystemOfflineHeaderWithContextRealmForOfflineJob(Long.toString(definitionDetails.getOwnerId()));
    Authorization authorization = new Authorization(offlineTicketHeader);
    contextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
    contextHandler.addKey(WASContextEnums.INTUIT_REALMID, authorization.getRealm());
    contextHandler.addKey(WASContextEnums.OWNER_ID, authorization.getRealm());
    contextHandler.addKey(WASContextEnums.INTUIT_WAS_LOCALE,
        SingleDefinitionUtil.getLocaleFromPlaceHolderValues(definitionDetails));
    WASContext.setMigrationContext(true);
    WASContext.setAuthContext(authorization);
    return authorization;
  }

  private void logInfo(String msg) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(msg)
                .className(this.getClass().getName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_MIGRATE_DEFINITION));
  }

  private void logError(String msg) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .message(msg)
                .className(this.getClass().getName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_MIGRATE_DEFINITION));
  }

}

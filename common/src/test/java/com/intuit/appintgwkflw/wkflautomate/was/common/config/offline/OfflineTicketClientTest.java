package com.intuit.appintgwkflw.wkflautomate.was.common.config.offline;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthnClientConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClientException;
import com.intuit.identity.authn.offline.sdk.client.AuthNClient;
import com.intuit.identity.authn.offline.sdk.model.GetHeadersForAccountRequest;
import com.intuit.identity.authn.offline.sdk.model.GetHeadersForOfflineJobRequest;
import com.intuit.identity.authn.offline.sdk.model.OfflineJobRequestHeaders;
import com.intuit.identity.authn.offline.sdk.utils.Constants;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

@Import(OfflineTicketClient.class)
@RunWith(SpringRunner.class)
public class OfflineTicketClientTest {

  @Autowired private OfflineTicketClient offlineTicketClient;

  @MockBean private WASContextHandler wasContextHandler;

  @MockBean private AuthNClient authNClient;

  @MockBean private AuthnClientConfig authnClientConfig;

  @Before
  public void setUp() throws Exception {
    Mockito.when(authnClientConfig.getSystemOfflineJobId()).thenReturn("jobId");
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void getSystemOfflineHeadersForOfflineJob() throws Exception {

    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    ArgumentCaptor<GetHeadersForOfflineJobRequest> argumentCaptor = ArgumentCaptor.forClass(GetHeadersForOfflineJobRequest.class);
    OfflineJobRequestHeaders offlineJobRequestHeaders = Mockito.mock(OfflineJobRequestHeaders.class);
    Mockito.when(offlineJobRequestHeaders.getOfflineHeadersMap()).thenReturn(Map.of(Constants.AUTHORIZATION_HEADER, "value"));
    Mockito.when(authNClient.getHeadersForOfflineJob(argumentCaptor.capture())).thenReturn(offlineJobRequestHeaders);
    Assert.assertEquals("value", offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    Assert.assertEquals("tid", argumentCaptor.getValue().getTransactionId());
    Assert.assertEquals("jobId", argumentCaptor.getValue().getOfflineJobId());
  }

  @Test(expected = OfflineTicketClientException.class)
  public void testGetSystemOfflineHeadersForOfflineJob_Error(){

    Mockito.when(authNClient.getHeadersForOfflineJob(Mockito.any())).thenThrow(RuntimeException.class);
    offlineTicketClient.getSystemOfflineHeadersForOfflineJob();
  }

  @Test
  public void testGetSystemOfflineHeaderWithContextRealmForOfflineJob() {
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    ArgumentCaptor<GetHeadersForAccountRequest> argumentCaptor = ArgumentCaptor.forClass(GetHeadersForAccountRequest.class);
    OfflineJobRequestHeaders offlineJobRequestHeaders = Mockito.mock(OfflineJobRequestHeaders.class);
    Mockito.when(offlineJobRequestHeaders.getOfflineHeadersMap()).thenReturn(Map.of(Constants.AUTHORIZATION_HEADER, "value"));
    Mockito.when(authNClient.getHeadersForAccount(argumentCaptor.capture())).thenReturn(offlineJobRequestHeaders);
    Assert.assertEquals("value", offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12345"));
    Assert.assertEquals("12345", argumentCaptor.getValue().getImpersonatedAccountId());
    Assert.assertEquals("tid", argumentCaptor.getValue().getTransactionId());
    Assert.assertEquals("jobId", argumentCaptor.getValue().getOfflineJobId());
  }

  @Test(expected = OfflineTicketClientException.class)
  public void testGetSystemOfflineHeaderWithContextRealmForOfflineJob_Error() {
    Mockito.when(authNClient.getHeadersForAccount(Mockito.any())).thenThrow(RuntimeException.class);
    offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob("12345");
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetSystemOfflineHeaderWithContextRealmForOfflineJob_Error_NullRealm() {
    offlineTicketClient.getSystemOfflineHeaderWithContextRealmForOfflineJob(null);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.INTUIT_REALMID;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CorrelationKeysEnum;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.util.ExternalTaskUtil;

/**
 * Executes external tasks asynchronously
 *
 * <AUTHOR>
 */
public class ExecuteActionAsync implements Runnable {

  private ExternalTask externalTask;
  private ExternalTaskService externalTaskService;
  private WorkerBaseExecutor workerBaseExecutor;
  private WASContextHandler contextHandler;
  private Worker worker;

  public ExecuteActionAsync(ExternalTask externalTask,
      ExternalTaskService externalTaskService, WorkerBaseExecutor workerBaseExecutor,
      WASContextHandler logHandler, Worker worker) {
    this.externalTaskService = externalTaskService;
    this.externalTask = externalTask;
    this.workerBaseExecutor = workerBaseExecutor;
    this.contextHandler = logHandler;
    this.worker = worker;
  }

  @Override
  public void run() {
    populateOfferingId();
    populateMDCParams();
    contextHandler.addKey(WASContextEnums.INTUIT_TID, externalTask.getId());
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Running external task asynchronously for processId=%s taskId=%s entityId=%s activityId=%s topic=%s",
                        externalTask.getProcessInstanceId(),
                        externalTask.getId(),
                        ExternalTaskUtil.getEntityId(externalTask),
                        externalTask.getActivityId(),
                        externalTask.getTopicName())
                .className(this.getClass().getSimpleName()));
    workerBaseExecutor.execute(externalTask, externalTaskService, worker);
  }

  private void populateOfferingId() {
    WASContext.clear();
    if (ObjectUtils.isNotEmpty(worker.getOfferingId())) {
      WASContext.setOfferingId(worker.getOfferingId());
    }
  }

  /** populates the MDC params for worker */
  private void populateMDCParams() {
    /**
     * Clearing the MDC since threads are getting reused.It is a short term fix.Will be revisited in
     * next release.
     */
    contextHandler.clear();

    // add tid
    contextHandler.addKey(WASContextEnums.INTUIT_TID, UUID.randomUUID().toString());

    // add process id
    contextHandler.addKey(WASContextEnums.PROCESS_INSTANCE_ID, externalTask.getProcessInstanceId());

    // add owner id
    getFromExternalTaskVariables(INTUIT_REALMID)
        .ifPresent(owner -> contextHandler.addKey(WASContextEnums.OWNER_ID, String.valueOf(owner)));

    // add workflow name
    getFromExternalTaskVariables(CorrelationKeysEnum.TEMPLATE_NAME.getName())
        .ifPresentOrElse(
            workflow -> contextHandler.addKey(WASContextEnums.WORKFLOW, String.valueOf(workflow)),
            () -> contextHandler.addKey(WASContextEnums.WORKFLOW,
            		WasUtils.unWrapId(externalTask.getProcessDefinitionKey(), externalTask.getBusinessKey())));

    // add offering id
    contextHandler.addKey(WASContextEnums.OFFERING_ID, worker.getOfferingId());
  }

  /**
   * fetches value of given process variable.
   *
   * @param processVariableName process variable name
   * @return {@link Optional}
   */
  private Optional<Object> getFromExternalTaskVariables(String processVariableName) {
    return Optional.ofNullable(externalTask.getAllVariables())
        .filter(Objects::nonNull)
        .map(processVariable -> processVariable.get(processVariableName));
  }
}

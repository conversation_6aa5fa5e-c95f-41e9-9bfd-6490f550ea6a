package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/** Enum Stores the configuration which is returned to the front end for list related parameters. */
@Getter
@RequiredArgsConstructor
public enum ListOperator {
  /**
   * CONTAINS: absolute check to see if a given attribute is equal to a given value
   * NOT_CONTAINS: absolute check to see if a given attribute is not equal to a given value
   *
   * <p>CONTAINS internally uses the java String.equals() method
   */
  CONTAINS("CONTAINS", "Contains"),
  NOT_CONTAINS("NOT_CONTAINS", "Not Contains");

  private final String symbol;
  private final String description;

  public static Map<String, String> possibleOperatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (ListOperator s : values()) {
      possibleOperatorValueMap.put(s.symbol, s.description);
    }
    return possibleOperatorValueMap;
  }
}

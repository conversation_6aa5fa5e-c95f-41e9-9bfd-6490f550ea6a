package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
/**
 * DefinitionEvent will be part of all publishers who are publishing to Event bus.
 *
 * <AUTHOR>
 */
public class DefinitionEvent {

  private String definitionId;
  private String handlerType;
  private Map<String, String> metaData;
}

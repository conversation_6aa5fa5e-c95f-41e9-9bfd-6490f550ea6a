package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handler;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.RuleLineProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.MultiConditionBusinessRuleTaskHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.EmptyConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiSplitRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

/**
 * author btolani
 */
@RunWith(MockitoJUnitRunner.class)
public class MultiConditionBusinessRuleTaskHandlerTest {

    @InjectMocks
    private MultiConditionBusinessRuleTaskHandler multiConditionBusinessRuleTaskHandler;

    @Mock
    private MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;

    @Mock
    private MultiSplitRuleLineProcessor multiSplitRuleLineProcessor;

    @InjectMocks
    private RuleLineProcessorFactory ruleLineProcessorFactory;

    @Mock
    private EmptyConditionRuleLineProcessor emptyConditionRuleLineProcessor;

    DefinitionInstance definitionInstance;
    Definition multiConditionDefinition;
    DmnModelInstance dmnModelInstance;
    DmnModelInstance invalidDmnModelInstance;
    BpmnModelInstance multiConditionBpmnModelInstance;
    Definition singleStepMultiConditionDefinition;
    DefinitionInstance singleStepDefinitionInstance;

    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML =
            TestHelper.readResourceAsString("bpmn/customApproval_multiCondition.bpmn");

    private static final String CUSTOM_WORKFLOW_DMN_XML =
            TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

    private static final String CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML =
            TestHelper.readResourceAsString("dmn/customWorkflow_withNoDecisionTable.dmn");

    @Before
    public void init() {
        multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
        singleStepMultiConditionDefinition = TestHelper.mockSingleStepMultiConditionDefinitionEntity();
        ruleLineProcessorFactory = new RuleLineProcessorFactory(multiConditionRuleLineProcessor,
            emptyConditionRuleLineProcessor, multiSplitRuleLineProcessor);
        multiConditionBusinessRuleTaskHandler = new MultiConditionBusinessRuleTaskHandler(
            ruleLineProcessorFactory);
        multiConditionBpmnModelInstance =
            Bpmn.readModelFromStream(
                IOUtils.toInputStream(MULTI_CONDITION_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
        dmnModelInstance =
                Dmn.readModelFromStream(
                        IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));

        invalidDmnModelInstance =
                Dmn.readModelFromStream(
                        IOUtils.toInputStream(CUSTOM_WORKFLOW_WITHOUT_DECISION_TABLE_DMN_XML, Charset.defaultCharset()));

        definitionInstance = new DefinitionInstance(multiConditionDefinition, multiConditionBpmnModelInstance,
                Collections.singletonList(dmnModelInstance), new TemplateDetails());

        singleStepDefinitionInstance = new DefinitionInstance(singleStepMultiConditionDefinition,
            multiConditionBpmnModelInstance, Collections.singletonList(dmnModelInstance), new TemplateDetails());
    }

    @Test
    public void test_workflowDecisionHandler_Success() {
        String dmnElementId = ((StartEvent) CustomWorkflowUtil.findStartEventElement(multiConditionBpmnModelInstance))
                .getOutgoing().stream().findFirst().get().getTarget().getId();
        DmnModelInstance dmnModelInstance = CustomWorkflowUtil.getDmnModelInstance(definitionInstance, dmnElementId);
        DecisionTable decisionTable = dmnModelInstance.getModelElementsByType(DecisionTable.class)
                .stream().findFirst().get();
        WorkflowStep workflowStep = multiConditionDefinition.getWorkflowSteps().stream().findFirst().get();
        Mockito.when(multiConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance, workflowStep, definitionInstance))
                .thenReturn(decisionTable);
        Mockito.when(multiConditionRuleLineProcessor.buildDmnHeadersMap(any()))
                .thenReturn(new HashMap<>());
        Assert.notNull(multiConditionBusinessRuleTaskHandler.workflowDecisionHandler(dmnModelInstance,
                workflowStep, definitionInstance, new LinkedList<>()));
    }

    @Test
    public void test_workflowDecisionHandlerForSingleStepMultiConditionDefinition_Success() {
        String dmnElementId = ((StartEvent) CustomWorkflowUtil.findStartEventElement(multiConditionBpmnModelInstance))
            .getOutgoing().stream().findFirst().get().getTarget().getId();
        DmnModelInstance dmnModelInstance = CustomWorkflowUtil.getDmnModelInstance(singleStepDefinitionInstance, dmnElementId);
        DecisionTable decisionTable = dmnModelInstance.getModelElementsByType(DecisionTable.class)
            .stream().findFirst().get();
        WorkflowStep workflowStep = singleStepMultiConditionDefinition.getWorkflowSteps().stream().findFirst().get();
        Mockito.when(emptyConditionRuleLineProcessor.createDmnHeaders(dmnModelInstance, workflowStep, singleStepDefinitionInstance))
            .thenReturn(decisionTable);
        Mockito.when(emptyConditionRuleLineProcessor.buildDmnHeadersMap(any()))
            .thenReturn(new HashMap<>());
        Assert.notNull(multiConditionBusinessRuleTaskHandler.workflowDecisionHandler(dmnModelInstance,
            workflowStep, singleStepDefinitionInstance, new LinkedList<>()));
    }

    @Test(expected = WorkflowGeneralException.class)
    public void test_workflowDecisionHandler_InvalidInput_Exception() {
        multiConditionBusinessRuleTaskHandler.workflowDecisionHandler(null,
                new WorkflowStep(),
                definitionInstance, new LinkedList<>());
    }

}

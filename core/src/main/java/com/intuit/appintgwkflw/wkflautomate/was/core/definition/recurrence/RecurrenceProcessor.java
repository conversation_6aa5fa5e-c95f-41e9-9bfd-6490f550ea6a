package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import static com.cronutils.model.field.expression.FieldExpressionFactory.always;
import static com.cronutils.model.field.expression.FieldExpressionFactory.and;
import static com.cronutils.model.field.expression.FieldExpressionFactory.on;

import com.cronutils.builder.CronBuilder;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.field.expression.FieldExpression;
import com.cronutils.model.field.expression.FieldExpressionFactory;
import com.cronutils.model.field.value.SpecialChar;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DayOfWeek;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.MonthOfYear;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WeekOfMonth;
import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.WeekIndexType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

/**
 * Tranforms a given RecurrenceRule object to different formats
 * Currently supports conversion to Quartz cron expression and ESS event recurrence schedule
 */
public interface RecurrenceProcessor {

  Integer MAX_LAST_DAY_OF_MONTH = 31;

  /*
   * Used to convert given recurrenceRule object to its corresponding cron expression Makes use of the
   * https://github.com/jmrozanec/cron-utils framework
   * QUARTZ cron syntax: [SECONDS] [MINUTES] [HOURS] [DAYSOFMONTH] [MONTHS] [DAYSOFWEEK] [YEARS]
   */
  default String buildCronExpression(
      FieldExpression year,
      FieldExpression daysOfMonth,
      FieldExpression monthsOfYear,
      FieldExpression daysOfWeek,
      TimeDuration timeDuration) {
    try {
      WorkflowLogger.logInfo(
          "cron expression fields: year=%s | daysOfMonth=%s | monthsOfYear=%s | daysOfWeek=%s",
          year.asString(), daysOfMonth.asString(), monthsOfYear.asString(), daysOfWeek.asString());
      Cron cronExpression =
          /**
           * Harcoding Quartz as camunda makes use of Quartz cron
           * https://docs.camunda.org/manual/7.15/reference/bpmn20/events/timer-events/#time-cycle,
           */
          CronBuilder.cron(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ))
              .withYear(year)
              .withDoM(daysOfMonth)
              .withMonth(monthsOfYear)
              .withDoW(daysOfWeek)
              .withHour(populateHours(timeDuration))
              .withMinute(populateMinutes(timeDuration))
              .withSecond(on(0))
              .instance();

      WorkflowLogger.logInfo("Created recurrence cron expression=%s", cronExpression.asString());
      // return the cron expression as a string
      return cronExpression.asString();
    } catch (Exception e) {
      WorkflowLogger.logError(
          e, "Error creating cron expression for recurrence | Error=%s", e.getMessage());

      throw new WorkflowGeneralException(WorkflowError.INVALID_RECURRENCE_PARAMETER, e);
    }
  }

  /*
   * If recurrence time is set, retrieve the hours
   * to create expressions for time in cron
   *
   * Example: recurrence time: 23:35
   * hours = on(23) = 23
   *
   * If recurrence time is not provided, the local instant time is taken
   * to populate the hours
   */
  private FieldExpression populateHours(TimeDuration timeDuration) {
    return on(
        Objects.nonNull(timeDuration) ? timeDuration.getHours() : LocalDateTime.now().getHour());
  }

  /*
   * If recurrence time is set, retrieve the hours
   * to create expressions for time in cron
   *
   * Example: recurrence time: 23:35
   * minutes = on(35) = 35
   *
   * If recurrence time is not provided, the local instant time is taken
   * to populate the minutes
   */
  private FieldExpression populateMinutes(TimeDuration timeDuration) {
    return on(
        Objects.nonNull(timeDuration)
            ? timeDuration.getMinutes()
            : LocalDateTime.now().getMinute());
  }

  /*
   * syntax for day of week for Last week -> <DayOfWeekIndex>L, eg: 4L = last wednesday
   *
   * <p>syntax for day of week in 1st - 4th week -> <DayOfWeekIndex>#<WeekOfMonth>, eg: 4#1 =
   * First Wednesday
   *
   * <p>Given week of the month, cron does not support providing multiple relative days example:
   * First Wednesday and Last Thursday so the first value in DaysOfWeek list is utilised
   * to populate the relative day such as First Wednesday
   * and remaining absolute day of week values are added
   *
   * If DaysOfWeek is empty, default to 1st day of week - Sunday
   */
  default FieldExpression populateWeeksOfMonthParameter(RecurrenceRule recurrenceRule) {
    List<FieldExpression> daysOfWeek = new ArrayList<>();
    int dayOfWeekIndex =
        CollectionUtils.isEmpty(recurrenceRule.getDaysOfWeek())
            ? DayOfWeek.SUNDAY.getIndex()
            : DayOfWeek.getDayOfWeekIndex(recurrenceRule.getDaysOfWeek(0).value());
    if (WeekOfMonthEnum.LAST.equals(recurrenceRule.getWeekOfMonth())) {
      daysOfWeek.add(on(dayOfWeekIndex, SpecialChar.L));
      WorkflowLogger.logInfo(
          "DayOfWeek populated based Last WeekOfMonth=%s", daysOfWeek.get(0).asString());
    } else {
      daysOfWeek.add(
          on(
              dayOfWeekIndex,
              SpecialChar.HASH,
              WeekOfMonth.getWeekOfMonthIndex(recurrenceRule.getWeekOfMonth().value())));
      WorkflowLogger.logInfo(
          "DayOfWeek populated based weekOfMonth (1-4)=%s", daysOfWeek.get(0).asString());
    }
    /*
     * post population of relative day of week using the week of month parameter,
     * remaining list of days are added.
     */
    if (recurrenceRule.getDaysOfWeek().size() > 1) {
      daysOfWeek.addAll(
          recurrenceRule.getDaysOfWeek().subList(1, recurrenceRule.getDaysOfWeek().size()).stream()
              .map(
                  dayOfWeek -> {
                    int dowIndex = DayOfWeek.getDayOfWeekIndex(dayOfWeek.value());
                    return on(dowIndex);
                  })
              .collect(Collectors.toList()));
    }

    /*
     * this collates the list of field expressions
     * example:
     * daysOfWeek: ["MONDAY", "TUESDAY", "FRIDAY"]
     * WeekOfMonth: "FIRST"
     *
     * returns 2#1,3,6 (On First Monday, all tuesdays and fridays)
     */
    return and(daysOfWeek);
  }

  /*
   * if monthsOfYear is empty, expression always() = *
   * is returned to denote all months
   *
   * else, a list of all applicable months are formed
   * Example: monthsOfYear = [MARCH, APRIL, DECEMBER]
   * returns and(on(3), on(4), on(12)) = 1,3,12
   *
   * throws exception for values other than 1-12
   */
  default FieldExpression populateMonthsOfYearParameter(RecurrenceRule recurrenceRule) {
    if (CollectionUtils.isEmpty(recurrenceRule.getMonthsOfYear())) {
      return always();
    }
    return and(
        recurrenceRule.getMonthsOfYear().stream()
            .map(
                month -> {
                  int monthIndex = MonthOfYear.getMonthOfYearIndex(month.value());
                  return on(monthIndex);
                })
            .collect(Collectors.toList()));
  }

  /*
   * if daysOfMonth is empty, expression always() = *
   * is returned to denote all days of the month
   *
   * else, a list of all applicable days are formed
   * Example: daysOfMonth = [1, 3, 24]
   * returns and(on(1), on(3), on(24)) = 1,3,24
   *
   * throws exception for values other than 1-31
   */
  default FieldExpression populateDaysOfMonthParameter(RecurrenceRule recurrenceRule) {
    if (CollectionUtils.isEmpty(recurrenceRule.getDaysOfMonth())) {
      return always();
    }
    List<Integer> daysOfMonth = recurrenceRule.getDaysOfMonth();
    /*
     * if provided a recurrence schedule to run on 31st of every month, it will correctly execute
     * only for January, March, May, July, August, October and December as only these months have 31st
     * and cron is stateless. Considering the recurrence is set of every month, the cron expression
     * should be able to accomodate the same across all months.
     * This check is to relatively set the day of month to L when 31st is chosen as dayOfMonth
     *
     * TODO: handle cases for all dayOfMonth > 28 as Feb month will not be handled with this fix
     *
     * For other days, list of applicable days are returned
     */
    if (daysOfMonth.contains(MAX_LAST_DAY_OF_MONTH) && daysOfMonth.size() == 1) {
      return on(SpecialChar.L);
    }
    return and(
        daysOfMonth.stream()
            .map(FieldExpressionFactory::on)
            .collect(Collectors.toList()));
  }

  /*
   * if daysOfWeek is empty, expression always() = *
   * is returned to denote all days of the week
   *
   * else, a list of all applicable days are formed
   * Example: daysOfMonth = [MONDAY, TUESDAY, SATURDAY]
   * returns and(on(2), on(3), on(7)) = 2,3,7
   *
   * throws exception for values other than 1-7
   */
  default FieldExpression populateDaysOfWeekParameter(RecurrenceRule recurrenceRule) {
    if (CollectionUtils.isEmpty(recurrenceRule.getDaysOfWeek())) {
      return always();
    }
    return and(
        recurrenceRule.getDaysOfWeek().stream()
            .map(
                day -> {
                  int dayOfWeekIndex = DayOfWeek.getDayOfWeekIndex(day.value());
                  return on(dayOfWeekIndex);
                })
            .collect(Collectors.toList()));
  }

  default List<DayOfWeekType> mapDaysOfWeekTypeForESSSchedule(
      List<DayOfWeekEnum> recurrenceDaysOfWeek) {
    if (ObjectUtils.isNotEmpty(recurrenceDaysOfWeek)) {
      List<DayOfWeekType> essDaysOfWeekTypeList =
          recurrenceDaysOfWeek.stream()
              .map(dayOfWeek -> DayOfWeekType.fromValue(dayOfWeek.value().substring(0, 3)))
              .collect(Collectors.toList());
      return essDaysOfWeekTypeList;
    }
    return null;
  }

  default List<Integer> mapMonthIndexForESSSchedule(List<MonthsOfYearEnum> recurrenceMonthsOfYear) {
    if (ObjectUtils.isNotEmpty(recurrenceMonthsOfYear)) {
      List<Integer> essMonthIndexList =
          recurrenceMonthsOfYear.stream()
              .map(monthsOfYear -> monthsOfYear.ordinal()+1)
              .collect(Collectors.toList());
      return essMonthIndexList;
    }
    return null;
  }

  private WeekIndexType mapWeekOfMonthForESSSchedule(
      WeekOfMonthEnum recurrenceWeekOfMonth) {
    if (ObjectUtils.isNotEmpty(recurrenceWeekOfMonth)) {
      return WeekIndexType.fromValue(recurrenceWeekOfMonth.value());
    }
    return null;
  }

  default void mapRelativeAndAbsoluteParametersForESSSchedule(
      RecurrenceRule recurrenceRule, RecurrencePattern pattern) {
    if (RecurrencePatternType.RELATIVEMONTHLY.equals(pattern.getType())
        || RecurrencePatternType.RELATIVEYEARLY.equals(pattern.getType())) {
      List<DayOfWeekType> daysOfWeek =
          mapDaysOfWeekTypeForESSSchedule(recurrenceRule.getDaysOfWeek());
      WeekIndexType weekOfMonth = mapWeekOfMonthForESSSchedule(recurrenceRule.getWeekOfMonth());
      pattern.daysOfWeek(daysOfWeek).weekIndex(weekOfMonth);
    } else {
      pattern.daysOfMonth(recurrenceRule.getDaysOfMonth());
    }
  }

  /**
   * With input as WAS recurrence rule, returns a transformed RecurrencePattern based on recurrence
   * type that is compatible for storing event schedules in ESS
   *
   * @param recurrenceRule
   * @return RecurrencePattern
   */
  RecurrencePattern buildESSRecurrencePattern(RecurrenceRule recurrenceRule);

  String getRecurrence(RecurrenceRule recurrenceRule);

  RecurTypeEnum getName();
}

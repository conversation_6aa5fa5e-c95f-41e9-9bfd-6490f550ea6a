package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;

/**
 * This class orchestrates
 * 1. Creates/renews offline ticket
 * 2. Creates appconnect subscription if not already present
 * 3. Saves the info in authdetails table
 */
@AllArgsConstructor
public class PopulateAuthDetailsTask implements Task {

  private WASContextHandler contextHandler;
  private AuthDetailsServiceHelper authDetailsServiceHelper;

  @Override
  public State execute(State inputRequest) {
    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder().message("Populate AuthDetails Task"));
    Authorization authorization = new Authorization(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER));
    authDetailsServiceHelper.populateAuthDetailsSync(authorization, true);
    return inputRequest;
  }
}

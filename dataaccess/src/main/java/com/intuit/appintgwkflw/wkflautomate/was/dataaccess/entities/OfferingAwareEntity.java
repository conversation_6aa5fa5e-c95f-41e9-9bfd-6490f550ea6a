package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@Data
@AllArgsConstructor
@NoArgsConstructor
@FilterDef(
    name = DataAccessConstants.OFFERING_FILTER,
    parameters = {@ParamDef(name = DataAccessConstants.OWNER_ID, type = "big_integer")})
@Filter(name = DataAccessConstants.OFFERING_FILTER, condition = "owner_id = :ownerId")
@Access(AccessType.PROPERTY)
public class OfferingAwareEntity extends WASBaseEntity {

  private Long ownerId;
}

package com.intuit.appintgwkflw.wkflautomate.was.common.aop;

import com.intuit.appintgwkflw.wkflautomate.was.common.observability.TagUtils;
import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import io.opentracing.tag.Tags;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Aspect
@Component
@AllArgsConstructor
public final class JpaTraceAspect {

  private static final String COMPONENT_NAME = "spring-data-jpa";

  private final Tracer tracer;

  private final TagUtils tagUtils;

  @Around("execution(* org.springframework.data.jpa.repository.JpaRepository+.*(..))")
  public Object aroundJpaCalls(final ProceedingJoinPoint joinPoint) throws Throwable {

    final String operation = joinPoint.getSignature().getName();
    final Span span = tracer.buildSpan(operation).start();
    try (final Scope ignored = tracer.activateSpan(span)) {
      return joinPoint.proceed();
    } catch (final Exception e) {
      Tags.ERROR.set(span, true);
      throw e.getCause();
    } finally {
      Tags.SPAN_KIND.set(span, Tags.SPAN_KIND_CLIENT);
      Tags.COMPONENT.set(span, COMPONENT_NAME);
      tagUtils.addCommonTags(span);
      span.finish();
    }
  }
}

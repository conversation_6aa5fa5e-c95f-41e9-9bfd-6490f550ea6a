package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.command.WorkflowTaskCreateCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.orm.ObjectOptimisticLockingFailureException;

@RunWith(MockitoJUnitRunner.class)
public class WorkflowExternalTaskManagerTest {

  @InjectMocks
  private WorkflowExternalTaskManager taskManager;

  @Mock
  private WorkflowTaskCreateCommand createCommand;

  @Mock
  private WASContextHandler contextHandler;

  WorkflowTaskRequest prepareTaskRequest() {
    Map<String, String> modelAttributes = new HashMap<>();
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    modelAttributes.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME, "actName");

    Map<String, Object> runtimeAttributes = new HashMap<>();
    runtimeAttributes.put("assigneeId", "assignee1");

    return WorkflowTaskRequest.builder().id("ext1").processInstanceId("proc1")
        .command(TaskCommand.CREATE).status("created")
        .taskAttributes(TaskAttributes.builder().modelAttributes(modelAttributes)
        		.runtimeAttributes(runtimeAttributes).variables(runtimeAttributes).build())
        .taskType(TaskType.HUMAN_TASK)
        .build();
  }

  /**
   * Successful execution.
   */
  @Test
  public void executeSuccess() {
	Mockito.doNothing().when(contextHandler)
		.addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());  
    Mockito.when(createCommand.execute(Mockito.any()))
        .thenReturn(WorkflowTaskResponse.builder().build());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
    Mockito.verify(createCommand, Mockito.times(1)).execute(Mockito.any());
  }

  /**
   * Downstream failure.
   */
  @Test(expected = WorkflowGeneralException.class)
  public void execute_downstreamFailure() {
	Mockito.doNothing().when(contextHandler)
		.addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_WORKFLOW_TASK))
        .when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void execute_Not_WorkflowGeneralException() {
	Mockito.doNothing().when(contextHandler)
	  .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());  
    Mockito.doThrow(new RuntimeException("Random error"))
        .when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

  /**
   * Unknown Exception occurred at runtime.
   */
  @Test(expected = WorkflowRetriableException.class)
  public void execute_unkownException() {
	Mockito.doNothing().when(contextHandler)
	  .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());  
    Mockito.doThrow(WorkflowRetriableException.class).when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }


  @Test(expected = WorkflowGeneralException.class)
  public void execute_DBException() {
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    Mockito.doThrow(DataIntegrityViolationException.class).when(createCommand)
        .execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void execute_DBException_retry() {
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    Mockito.doThrow(ObjectOptimisticLockingFailureException.class).when(createCommand)
        .execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }
  

  /**
   * Unknown Exception occurred at runtime.
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_NoHandlerFound() {
	Mockito.doNothing().when(contextHandler)
	  .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());  
    Mockito.doThrow(new WorkflowNonRetriableException(WorkflowError.ERROR_NO_TASK_HANDLER_REGISTERED))
        .when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

  /**
   * Unknown Exception occurred at runtime.
   */
  @Test(expected = WorkflowNonRetriableException.class)
  public void execute_NoProcessFound() {
	Mockito.doNothing().when(contextHandler)
	  .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());  
    Mockito.doThrow(new WorkflowNonRetriableException(WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR))
        .when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

  @Test(expected = WorkflowRetriableException.class)
  public void  execute_Retriable_Exception_For_Optimistic_Locking_Failure() {
    Mockito.doNothing().when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
    Mockito.doThrow(new ObjectOptimisticLockingFailureException("Optimistic Locking Failure",
            new Object()))
        .when(createCommand).execute(Mockito.any());
    WorkflowTaskCommands.addCommand(TaskCommand.CREATE, createCommand);
    WorkflowTaskRequest taskRequest = prepareTaskRequest();
    taskManager.execute(taskRequest);
  }

}

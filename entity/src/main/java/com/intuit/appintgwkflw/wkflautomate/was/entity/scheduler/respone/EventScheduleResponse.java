package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
/** This class is used to store the response after calling the ESS */
public class EventScheduleResponse {

  private List<EventScheduleError> errors;
  private List<EventScheduleData> data;
}

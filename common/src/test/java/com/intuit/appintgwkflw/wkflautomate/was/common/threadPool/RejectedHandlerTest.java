package com.intuit.appintgwkflw.wkflautomate.was.common.threadPool;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class RejectedHandlerTest {

  @Spy
  private final BlockingQueue<Runnable> blockingQueue = new LinkedBlockingDeque<>();

  @Mock
  private Runnable runnable;

  @Mock
  private ThreadPoolExecutor executor;

  @Before
  public void setUp() {

    doReturn(blockingQueue).when(executor).getQueue();
  }

  @Test
  public void whenRejected_andExecutorShutDown_thenDoNothing() {

    doReturn(true).when(executor).isShutdown();

    new RejectedHandler().rejectedExecution(runnable, executor);

    assertTrue(executor.getQueue().isEmpty());
  }

  @Test
  public void whenRejected_andExecutorIsRunning_thenAddBack() {

    doReturn(false).when(executor).isShutdown();

    new RejectedHandler().rejectedExecution(runnable, executor);

    assertEquals(1, executor.getQueue().size());
  }

  @Test
  public void whenHandlerThrowsException_thenDoNothing() throws InterruptedException {

    doReturn(false).when(executor).isShutdown();
    doThrow(InterruptedException.class).when(blockingQueue).put(runnable);

    new RejectedHandler().rejectedExecution(runnable, executor);

    assertTrue(executor.getQueue().isEmpty());
  }
}

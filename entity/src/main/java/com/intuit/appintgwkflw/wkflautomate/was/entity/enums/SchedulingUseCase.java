package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

/**
 * Enum representing different scheduling use cases.
 *
 * <AUTHOR>
 */
public enum SchedulingUseCase {
    WORKFLOW_SCHEDULING_USECASE("workflow-scheduling"),
    WORKFLOW_REMINDER_USECASE("workflow-reminder");

    private String name;

    SchedulingUseCase(String name) {
        this.name = name;
    }

    /**
     * Gets the name of the scheduling use case.
     *
     * @return the name of the scheduling use case
     */
    public String getName() {
        return name;
    }
}
package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.listener;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.processor.ESSMessageProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.cloud.aws.messaging.listener.Acknowledgment;
import org.springframework.cloud.aws.messaging.listener.SqsMessageDeletionPolicy;
import org.springframework.cloud.aws.messaging.listener.annotation.SqsListener;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> mmahapatra
 *     <p>This class is responsible for listening all the messages from ESS SQS Queue.
 */
@Component
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class ESSMessageListener {
  private final ESSMessageProcessor essMessageProcessor;

  private final ThreadPoolExecutor threadPoolQueue;
  private final WASContextHandler wasContextHandler;

  public ESSMessageListener(
      ESSMessageProcessor essMessageProcessor,
      @Qualifier(WorkflowConstants.SQS_EXECUTOR_THREAD_BEAN) ThreadPoolExecutor threadPoolQueue,
      WASContextHandler wasContextHandler) {
    this.essMessageProcessor = essMessageProcessor;
    this.threadPoolQueue = threadPoolQueue;
    this.wasContextHandler = wasContextHandler;
  }

  /**
   * Read the message from sqs and manually acknowledge it.
   *
   * @param message
   * @param acknowledgment
   * @param headers
   */
  @SqsListener(value = "${ess-sqs.sqsQueueUrl}", deletionPolicy = SqsMessageDeletionPolicy.NEVER)
  // Reason for SqsListener -  we used here long polling - the costs of SQS, since they are
  // calculated by API calls
  public void processMessage(
      @Payload final String message,
      final Acknowledgment acknowledgment,
      @Headers final Map<String, String> headers) {
    EventingLoggerUtil.logInfo(
        "Reading message in ESS Listener  message=%s", this.getClass().getSimpleName(), message);
    threadPoolQueue.submit(() -> process(message, acknowledgment, headers));
  }

  /**
   * This method process and clear the was context.
   *
   * @param message
   * @param acknowledgment
   * @param headers
   */
  void process(
      final String message,
      final Acknowledgment acknowledgment,
      final Map<String, String> headers) {
    try {
      essMessageProcessor.process(message, acknowledgment, headers);
    } finally {
      EventingLoggerUtil.logInfo("Clearing WAS Context", this.getClass().getSimpleName(), message);
      wasContextHandler.clear();
    }
  }
}

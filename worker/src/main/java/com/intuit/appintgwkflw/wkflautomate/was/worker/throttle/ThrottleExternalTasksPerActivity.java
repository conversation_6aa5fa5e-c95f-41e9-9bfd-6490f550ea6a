package com.intuit.appintgwkflw.wkflautomate.was.worker.throttle;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ThrottleConfigs;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineHistoryServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.throttle.ThrottleService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityProgressDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.ExternalTaskLogRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors.WorkerExecutorHelper;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;


@Component
@AllArgsConstructor
public class ThrottleExternalTasksPerActivity extends ThrottleService {
    private final WorkerExecutor workerExecutor;
    private final ActivityProgressDetailsRepository activityProgressDetailsRepository;
    private final BPMNEngineHistoryServiceRest camundaHistoryRest;
    private final static String DISABLE_THROTTLE = "disableThrottlePerActivity";
    private static final String COUNT = "count";

    public ThrottleAttribute getAttribute() {
        return ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY;
    }

    /**
     * Returns number of times an activity has been executed for a workflow
     * and the workflow name
     *
     * @param joinPoint
     * @throws Throwable
     */
    public Pair<Integer, String> getExecutionCountAndWorkflow(final ProceedingJoinPoint joinPoint) {

        ExternalTask externalTask = ((ExternalTask) joinPoint.getArgs()[0]);

        WorkflowLogger.logInfo(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX + "Starting throttling check for activityId=%s processId=%s", externalTask.getActivityId(), externalTask.getProcessInstanceId());

        // TODO: IS INTEGER enough for the three configs below???
        return new ImmutablePair<>(
                getExternalTaskCount(externalTask),
                WasUtils.unWrapId(externalTask.getProcessDefinitionKey(), externalTask.getBusinessKey()));
    }

    @Override
    public Integer getWarnDiff() {
        return Optional.ofNullable(throttleConfigs).map(ThrottleConfigs::getWarnDiffCount)
            .map(y -> y.get(ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY)).orElse(0);
    }

    /**
     * Get number of external tasks that have been created for the activity
     *
     * Method of retrieving this count varies depending on the handler details
     *
     * If the activity has WAS_WORKFLOW_CUSTOM_TASK_HANDLER in its handler details (which
     * means that the external task details are available in the DB), the DB is queried
     * to find the number of external tasks that have been created for the activity.
     * Else a request is made to Camunda to get this detail.
     *
     * @param externalTask
     * @return
     */
    private Integer getExternalTaskCount(ExternalTask externalTask) {
        HandlerDetails handlerDetails = workerExecutor.extractHandlerDetails(
                WorkerExecutorHelper.createInputVariablesMap(externalTask.getAllVariables()),
                externalTask
        );
        TaskHandlerName handlerName = TaskHandlerName.getActionFromName(WorkerExecutorHelper.prepareHandlerName(handlerDetails));


        // Fetch count from DB
        if (TaskHandlerName.WAS_WORKFLOW_CUSTOM_TASK_HANDLER.equals(handlerName)) {
            WorkflowLogger.logInfo(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX + "Fetching external task count from database");
            // Add 1 to count as DB has one less creation record than Camunda
            return activityProgressDetailsRepository.getCountOfExternalTasksPerActivityPerProcess(externalTask.getActivityId(), externalTask.getProcessInstanceId()) + 1;
        }

        // Fetch count from Camunda
        return getExternalTaskCountFromCamunda(externalTask);
    }

    /**
     * Fetch external task history count from Camunda
     * If Camunda is down, ignore throttling check
     *
     * @param externalTask
     * @return
     */
    private Integer getExternalTaskCountFromCamunda(ExternalTask externalTask) {
        try {
            WorkflowLogger.logInfo(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX + "Fetching external task count from Camunda");
            WASHttpResponse<Map<String, Integer>> wasResponse = camundaHistoryRest
                    .getExternalTaskCount(
                            ExternalTaskLogRequest.builder()
                                    .processInstanceId(externalTask.getProcessInstanceId())
                                    .activityIdIn(Arrays.asList(externalTask.getActivityId()))
                                    .creationLog(true)
                                    .build()
                    );

            return Optional.ofNullable(wasResponse.getResponse())
                    .map(response -> response.get(COUNT))
                    .orElse(0);
        }
        catch (WorkflowRetriableException e) {
            WorkflowLogger.logWarn(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX + "Skipped throttling check for activityId=%s processId=%s due to RetriableException from Camunda", externalTask.getActivityId(), externalTask.getProcessInstanceId());
            return 0;
        }
    }

    /**
     * Checks scenarios in which throttling is not performed
     *
     * Throttling must be enabled for activity at root config level for any further processing.
     * If it has been disabled in the root config (throttle.externalTaskPerActivity.enabled = false), all other
     * configs are ignored and no further throttling check is performed.
     *
     * If throttle is enabled for activity, but throttling is disabled for a specific workflow
     * in the config, that will take precedence over the other configurations.
     *
     * Even if throttling has been enabled for activity at root level, further throttling is performed only if
     * a threshold value has been set for the workflow, or a default threshold value has been configured.
     *
     * @param joinPoint
     * @return
     */
    public boolean isThrottlingEnabled(final ProceedingJoinPoint joinPoint) {
        ExternalTask externalTask = ((ExternalTask) joinPoint.getArgs()[0]);
        return throttleConfigs.isExternalTasksPerActivity() &&
            throttleHelper.isThrottlingEnabledForWorkflow(
                WasUtils.unWrapId(externalTask.getProcessDefinitionKey(),
                    externalTask.getBusinessKey()),
                ThrottleAttribute.EXTERNAL_TASKS_PER_ACTIVITY
            ) &&
            !isThrottleBypassSet(externalTask) &&
            !isManualRetry(externalTask);
    }

    /**
     * Throttling check is bypassed if the extension property 'disableThrottlePerActivity' is set to true, in case the activity genuinely needs to be executed several times
     *
     * @param externalTask
     * @return
     */
    private boolean isThrottleBypassSet(ExternalTask externalTask) {
        return externalTask.getExtensionProperties().containsKey(DISABLE_THROTTLE) &&
                Boolean.TRUE.toString().equals(externalTask.getExtensionProperties().get(DISABLE_THROTTLE));
    }

    /**
     * Do not throttle when external task which failed due to threshold breach is being manually retried
     *
     * @param externalTask
     * @return
     */
    private boolean isManualRetry(ExternalTask externalTask) {
        return externalTask.getErrorMessage()!=null &&
                WorkflowError.EXTERNAL_TASK_THRESHOLD_BREACHED.toString().equals(externalTask.getErrorMessage());
    }

    /**
     * On breach of threshold, an incident is created
     *
     * @param joinPoint
     */
    public void executeFailure(final ProceedingJoinPoint joinPoint, Integer executionCount) {
        ExternalTask externalTask = ((ExternalTask) joinPoint.getArgs()[0]);
        ExternalTaskService externalTaskService = ((ExternalTaskService) joinPoint.getArgs()[1]);
        WorkflowLogger.logError(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX +
                        "External task threshold has been breached for activityId=%s having numExternalTasks=%s. Creating incident.",
                externalTask.getActivityId(),
                executionCount);
        workerExecutor.executeFailure(externalTask, externalTaskService,
                new WorkflowNonRetriableException(WorkflowError.EXTERNAL_TASK_THRESHOLD_BREACHED, externalTask.getActivityId()));
    }

    public void executeWarn(final ProceedingJoinPoint joinPoint, Integer executionCount) {
        ExternalTask externalTask = ((ExternalTask) joinPoint.getArgs()[0]);
        WorkflowLogger.logWarn(ResiliencyConstants.THROTTLE_ACTIVITY_PREFIX +
                        "External task threshold about to be breached for activityId=%s having numExternalTasks=%s.",
                externalTask.getActivityId(),
                executionCount);
    }
}

package com.intuit.appintgwkflw.wkflautomate.was.common.annotations;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR> smenon4
 *     <p>This annotation is used to validate/log the headers and populate the WAS context info.It
 *     will also Acknowledge the packet that is received by consumer & logs the error and latency
 *     metrics
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface EventListener {

  MetricName name();

  Type type();
}

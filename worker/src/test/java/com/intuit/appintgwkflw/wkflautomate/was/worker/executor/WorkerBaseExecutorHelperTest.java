package com.intuit.appintgwkflw.wkflautomate.was.worker.executor;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExternalTaskRetryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy.RetryStrategyName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ExternalTaskConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Client;
import junit.framework.Assert;
import org.camunda.bpm.client.task.ExternalTask;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

public class WorkerBaseExecutorHelperTest {
    @Mock private ExternalTask externalTask;
    @Mock private ExternalTaskConfiguration externalTaskConfig;
    @Mock private WorkerRetryHelper workerRetryHelper;

    @InjectMocks private WorkerBaseExecutorHelper workerBaseExecutorHelper;
    @InjectMocks private HandlerDetails.TaskDetails taskDetails;
    @InjectMocks private Client client;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getTaskDetailsFromExternalTaskTestNull() {
        Mockito.when(externalTask.getVariable(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn(null);
        Mockito.when(externalTask.getExtensionProperty(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn(null);
        Optional<HandlerDetails.TaskDetails> actualValue = workerBaseExecutorHelper.getTaskDetailsFromExternalTask(externalTask);
        Assert.assertEquals(Optional.empty(), actualValue);
    }

    @Test
    public void getTaskDetailsFromExternalTaskTestValues() {
        Mockito.when(externalTask.getVariable(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn("{ \"required\": true, \"fatal\": true, \"retryCount\": 10, \"retryStrategyName\": \"EXPONENTIAL_MOD_BACKOFF\"}");
        Optional<HandlerDetails.TaskDetails> actualValue = workerBaseExecutorHelper.getTaskDetailsFromExternalTask(externalTask);
        taskDetails.setRetryCount(10);
        taskDetails.setFatal(true);
        taskDetails.setRequired(true);
        taskDetails.setRetryStrategyName("EXPONENTIAL_MOD_BACKOFF");
        Optional<HandlerDetails.TaskDetails> expectedVal = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Assert.assertEquals(expectedVal, actualValue);
    }

    @Test
    public void getTaskDetailsFromExternalTaskTestValuesFromExtensionAttributes() {
        Mockito.when(externalTask.getVariable(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn(null);
        Mockito.when(externalTask.getExtensionProperty(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn("{ \"required\": true, \"fatal\": true, \"retryCount\": 10, \"retryStrategyName\": \"EXPONENTIAL_MOD_BACKOFF\"}");
        Optional<HandlerDetails.TaskDetails> actualValue = workerBaseExecutorHelper.getTaskDetailsFromExternalTask(externalTask);
        taskDetails.setRetryCount(10);
        taskDetails.setFatal(true);
        taskDetails.setRequired(true);
        taskDetails.setRetryStrategyName("EXPONENTIAL_MOD_BACKOFF");
        Optional<HandlerDetails.TaskDetails> expectedVal = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Assert.assertEquals(expectedVal, actualValue);
    }

    @Test
    public void getTaskDetailsFromExternalTaskTestMissingValues() {
        Mockito.when(externalTask.getVariable(WorkFlowVariables.TASK_DETAILS_KEY.getName())).thenReturn("{ \"required\": true, \"fatal\": true, \"retryCount\": 10}");
        Optional<HandlerDetails.TaskDetails> actualValue = workerBaseExecutorHelper.getTaskDetailsFromExternalTask(externalTask);
        taskDetails.setRetryCount(10);
        taskDetails.setFatal(true);
        taskDetails.setRequired(true);
        Optional<HandlerDetails.TaskDetails> expectedVal = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Assert.assertEquals(expectedVal, actualValue);
    }

    @Test
    public void getRetryStrategyNameDefault() {
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);

        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(Optional.empty(), externalTask);

        Assert.assertEquals(RetryStrategyName.DEFAULT, actualValue);
    }

    @Test
    public void getRetryStrategyNameFromExternalTaskRetryConfig_RetryStrategyNotPresent() {
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        3,
                        null,
                        null,
                        false
                )
        );

        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(Optional.empty(), externalTask);

        Assert.assertEquals(RetryStrategyName.DEFAULT, actualValue);
    }

    @Test
    public void getRetryStrategyNameFromExternalTaskRetryConfig_RetryStrategyPresent() {
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        3,
                        null,
                        RetryStrategyName.EXPONENTIAL_MOD_BACKOFF,
                        false
                )
        );

        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(Optional.empty(), externalTask);

        Assert.assertEquals(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF, actualValue);
    }

    @Test
    public void getRetryStrategyNameFromTaskDetails() {
        taskDetails.setRetryStrategyName(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF.toString());
        Optional<HandlerDetails.TaskDetails> mockTaskDetails = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(mockTaskDetails, externalTask);

        Assert.assertEquals(RetryStrategyName.EXPONENTIAL_MOD_BACKOFF, actualValue);
    }

    @Test
    public void getRetryStrategyNameTestDefault() {
        Optional<HandlerDetails.TaskDetails> mockTaskDetails = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(mockTaskDetails, externalTask);

        Assert.assertEquals(RetryStrategyName.DEFAULT, actualValue);
    }

    @Test
    public void getRetryStrategyNameTestIllegalValue() {
        taskDetails.setRetryStrategyName("RANDOM_STRING");
        Optional<HandlerDetails.TaskDetails> mockTaskDetails = Optional.of((HandlerDetails.TaskDetails) taskDetails);
        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
        RetryStrategyName actualValue = workerBaseExecutorHelper.getRetryStrategyName(mockTaskDetails, externalTask);

        Assert.assertEquals(RetryStrategyName.DEFAULT, actualValue);
    }

    @Test
    public void getNumberOfRetriesTest() {
        Mockito.when(externalTask.getRetries()).thenReturn(5);
        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetries(externalTask), 4);
    }

  @Test
  public void getRetryTimerTest() {
    Client client1 = new Client();
    client1.setRetryCount(0);
    Mockito.when(externalTask.getRetries()).thenReturn(null);
    Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);
    Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);
    Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetries(externalTask), 0);
  }

    @Test
    public void getNumberOfRetriesTestWhenNull() {
        WorkerBaseExecutorHelper workerBaseExecutorHelperMock = Mockito.spy(workerBaseExecutorHelper);
        Mockito.when(externalTask.getRetries()).thenReturn(null);
        Mockito.doReturn(5).when(workerBaseExecutorHelperMock).getNumberOfRetriesFromTemplateOrConfig(externalTask);
        Assert.assertEquals(workerBaseExecutorHelperMock.getNumberOfRetries(externalTask), 5);
    }

    @Test
    public void getNumberOfRetriesTestWhenDefault() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.empty(), externalTask), 3);
    }

    @Test
    public void getNumberOfRetriesTestWhenExternalRetryConfigNotPresent() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(null);

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.empty(), externalTask), 3);
    }

    @Test
    public void getNumberOfRetriesTestFromExternalRetryConfig_RetryCountNotSet() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        3,
                        null,
                        RetryStrategyName.DEFAULT,
                        false
                )
        );

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.empty(), externalTask), 3);
    }

    @Test
    public void getNumberOfRetriesTestFromExternalRetryConfig_RetryCountSet() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        5,
                        null,
                        null,
                        false
                )
        );

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.empty(), externalTask), 5);
    }

    @Test
    public void getNumberOfRetriesTestFromTaskDetails() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        5,
                        null,
                        null,
                        false
                )
        );

        taskDetails.setRetryCount(7);

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.of(taskDetails), externalTask), 7);
    }

    @Test
    public void checkRetryCountMoreThanMaxRetriesAllowed() {
        Client client1 = new Client();
        client1.setRetryCount(3);
        Mockito.when(externalTaskConfig.getClient()).thenReturn(client1);

        Mockito.when(workerRetryHelper.getRetryConfig(any(), any(), any())).thenReturn(
                getMockExternalTaskRetryConfig(
                        "workflowName",
                        "externalTaskName",
                        5,
                        null,
                        null,
                        false
                )
        );

        taskDetails.setRetryCount(25);

        Assert.assertEquals(workerBaseExecutorHelper.getNumberOfRetriesFromTemplateOrConfigOrTask(Optional.of(taskDetails), externalTask), 20);
    }

    private ExternalTaskRetryConfig getMockExternalTaskRetryConfig(String workflowName, String externalTaskName, Integer retryCount, Long backOffStepSize, RetryStrategyName retryStrategyName, boolean fatalOnRetryExhaust) {
        ExternalTaskRetryConfig externalTaskRetryConfig = new ExternalTaskRetryConfig();
        externalTaskRetryConfig.setWorkflowName(workflowName);
        externalTaskRetryConfig.setExternalTaskName(externalTaskName);
        externalTaskRetryConfig.setRetryCount(retryCount);
        externalTaskRetryConfig.setBackOffStepSize(backOffStepSize);
        externalTaskRetryConfig.setRetryStrategyName(retryStrategyName);
        externalTaskRetryConfig.setFatalOnRetryExhaust(fatalOnRetryExhaust);
        return externalTaskRetryConfig;
    }
}

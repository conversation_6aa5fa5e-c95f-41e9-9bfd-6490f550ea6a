kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:************:certificate/e3ae5880-ce3d-493f-a35f-0aad3aa65e29
    alb.ingress.kubernetes.io/security-groups: iks-intuit-cidr-ingress-tcp-443,iks-intuit-api-gw-ingress-prod-tcp-443,iks-intuit-app-alb-custom-ingress,iks-intuit-ibp-ingress-tcp-443
    external-dns.alpha.kubernetes.io/hostname: appintgwkflw-prd-was-event-in-account.sbgqboprodusw2.iks2.a.intuit.com,appintgwkflw-prd-was-event-in-account-desired.sbgqboprodusw2.iks2.a.intuit.com,appintgwkflw-prd-was-event-in-account-stable.sbgqboprodusw2.iks2.a.intuit.com
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '15'
  name: was-event-in-ingress
spec:
  rules:
  - host: appintgwkflw-prd-was-event-in-account-desired.sbgqboprodusw2.iks2.a.intuit.com
    http:
      paths:
      - backend:
          service:
            name: was-event-in-desired-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
  - host: appintgwkflw-prd-was-event-in-account-stable.sbgqboprodusw2.iks2.a.intuit.com
    http:
      paths:
      - backend:
          service:
            name: was-event-in-stable-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
  - http:
      paths:
      - backend:
          service:
            name: was-event-in-root-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
status:
  loadBalancer: {}

# Same as sandbox.yaml. 
# 1. Replace 'default: "default"'  with 'default: "sandbox"'
# 2. Add para in the decription section 
# 3. Add tid so that local calls donot fail

openapi: 3.0.1
info:
  description: WAS(Workflow Automation Service) is an orchestrator for Business Process Management. Workflow is a sequence of steps that define a process. The power of WAS is in defining and executing stateful workflows that have a series of triggers, evaluations and actions.
    </br></br>
    This swagger point to our sandbox environment (using the value specified in the intuit_was_offeringid header). <b>The data in the sandbox environment gets purged every 12 hours (01:00 and 13:00 GMT everyday)</b>
    
    <p><b>Due to security issues, the API's in the <i>template-controller</i> section cannot be accessed via swagger. We recommend you to use our postman collection to try it out</b></p>
    
    <a href= "https://github.intuit.com/appintgwkflw-wkflautomate/workflow-automation-service">Workflow Automation Service source code</a> <br/>
    <a href= " https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/">User Documentation</a> <br/>
    
  version: 1.0.0
  title: Workflow Automation Service
servers:
  - url: https://workflowautomation-e2eb.api.intuit.com
    description: sandbox
  - url: https://localhost.intuit.com:8443
    description: local
security:
  - Authorization: []
paths:
  /v1/template/{templateName}:
    get:
      summary: Fetches the template using template name
      tags:
        - template-controller
      operationId: fetchTemplate
      description: This API fetches the template from Camunda DB using template name and download in the desired directory </br> <i>Where,</i> <br><b> templateName ( Required )</b> <i> </br> -  Template name(as defined in the <bpmn:process>) with `.bpmn` extension <br><b> download_path</b> <i> </br> - download directory
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: templateName
          in: path
          required: true
          description: Template name with bpmn extension. Example - file.bpmn
          schema:
            type: string
        - name: download_path
          in: header
          required: false
          description: Enter the download path. Example - /usr/xyz/..
          schema:
            type: string
            default: ""

      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  context:
                    type: object
                    properties:
                      entityTag:
                        type: object
                        properties:
                          value:
                            type: string
                          weak:
                            type: boolean
                      stringHeaders:
                        type: object
                        properties:
                          header:
                            type: object
                            properties:
                              Content-Disposition:
                                type: array
                                items:
                                  type: string
                      entity:
                        type: string
                      mediaType:
                        type: object
                        properties:
                          type:
                            type: string
                          subtype:
                            type: string
                          parameters:
                            type: object
                            additionalProperties:
                              type: string
                          wildcardType:
                            type: boolean
                          wildcardSubtype:
                            type: boolean
                      allowedMethods:
                        uniqueItems: true
                        type: array
                        items:
                          type: string
                      length:
                        type: integer
                        format: int32
                      location:
                        type: string
                        format: uri
                      language:
                        type: object
                        properties:
                          language:
                            type: string
                          script:
                            type: string
                          country:
                            type: string
                          variant:
                            type: string
                          extensionKeys:
                            uniqueItems: true
                            type: array
                            items:
                              type: string
                          unicodeLocaleAttributes:
                            uniqueItems: true
                            type: array
                            items:
                              type: string
                          unicodeLocaleKeys:
                            uniqueItems: true
                            type: array
                            items:
                              type: string
                          iso3Language:
                            type: string
                          iso3Country:
                            type: string
                          displayLanguage:
                            type: string
                          displayScript:
                            type: string
                          displayCountry:
                            type: string
                          displayVariant:
                            type: string
                          displayName:
                            type: string
                      date:
                        type: string
                        format: date-time
                      lastModified:
                        type: string
                        format: date-time
                      headers:
                        type: object
                        properties:
                          header:
                            type: object
                            properties:
                              Content-Disposition:
                                type: array
                                items:
                                  type: string
                      links:
                        uniqueItems: true
                        type: array
                        items:
                          type: object
                          properties:
                            uriBuilder:
                              type: object
                            rels:
                              type: array
                              items:
                                type: string
                            type:
                              type: string
                            params:
                              type: object
                              additionalProperties:
                                type: string
                            uri:
                              type: string
                              format: uri
                            rel:
                              type: string
                            title:
                              type: string
                      configuration:
                        type: object
                      entityType:
                        type: string
                      entityAnnotations:
                        type: array
                        items:
                          type: string
                      entityStream:
                        type: object
                        properties:
                          committed:
                            type: boolean
                          closed:
                            type: boolean
                      acceptableLanguages:
                        type: array
                        items:
                          type: string
                      requestCookies:
                        type: object
                      lengthLong:
                        type: integer
                      entityClass:
                        type: string
                      acceptableMediaTypes:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                            subtype:
                              type: string
                            parameters:
                              type: object
                            quality:
                              type: integer
                            wildcardType:
                              type: boolean
                            wildcardSubtype:
                              type: boolean
                      responseCookies:
                        type: object
                      committed:
                        type: boolean
                  status:
                    type: integer
                    format: int32
                  statusInfo:
                    type: string
                  entityTag:
                    type: object
                    properties:
                      value:
                        type: string
                      weak:
                        type: boolean
                  stringHeaders:
                    type: object
                    properties:
                      header:
                        type: object
                        properties:
                          Content-Disposition:
                            type: array
                            items:
                              type: string
                  allowedMethods:
                    uniqueItems: true
                    type: array
                    items:
                      type: string
                  links:
                    uniqueItems: true
                    type: array
                    items:
                      type: object
                      properties:
                        uriBuilder:
                          type: object
                        rels:
                          type: array
                          items:
                            type: string
                        type:
                          type: string
                        params:
                          type: object
                          additionalProperties:
                            type: string
                        uri:
                          type: string
                          format: uri
                        rel:
                          type: string
                        title:
                          type: string
                  cookies:
                    type: object
                  mediaType:
                    type: object
                    properties:
                      type:
                        type: string
                      subtype:
                        type: string
                      parameters:
                        type: object
                        additionalProperties:
                          type: string
                      wildcardType:
                        type: boolean
                      wildcardSubtype:
                        type: boolean
                  metadata:
                    type: object
                    properties:
                      header:
                        type: object
                        properties:
                          Content-Disposition:
                            type: array
                            items:
                              type: string
                  lastModified:
                    type: string
                    format: date-time
                  entity:
                    type: string
                  length:
                    type: integer
                    format: int32
                  location:
                    type: string
                    format: uri
                  language:
                    type: object
                    properties:
                      language:
                        type: string
                      script:
                        type: string
                      country:
                        type: string
                      variant:
                        type: string
                      extensionKeys:
                        uniqueItems: true
                        type: array
                        items:
                          type: string
                      unicodeLocaleAttributes:
                        uniqueItems: true
                        type: array
                        items:
                          type: string
                      unicodeLocaleKeys:
                        uniqueItems: true
                        type: array
                        items:
                          type: string
                      iso3Language:
                        type: string
                      iso3Country:
                        type: string
                      displayLanguage:
                        type: string
                      displayScript:
                        type: string
                      displayCountry:
                        type: string
                      displayVariant:
                        type: string
                      displayName:
                        type: string
                  date:
                    type: string
                    format: date-time
                  headers:
                    type: object
                    properties:
                      header:
                        type: object
                        properties:
                          Content-Disposition:
                            type: array
                            items:
                              type: string
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
    put:
      tags:
        - template-controller
      operationId: updateTemplateStatus
      description: This API used to enable or disable the template </br> <i>Where,</i> <br><b> templateName ( Required )</b> <i> </br> - Template name(as defined in the <bpmn:process>) with `.bpmn` extension </br><b>status ( Required )</b>  </br>- ENABLED</br>- DISABLED
      summary: Enable or Disable the template
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: templateName
          in: path
          required: true
          description: Template name with bpmn extension. Example - file.bpmn
          schema:
            type: string
        - name: status
          in: query
          required: true
          description: Specify whether to enable or disable template
          schema:
            type: string
            description: It will enable or disable the template
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
                  response:
                    type: object
                    properties:
                      response:
                        type: string
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v2/trigger:
    post:
      tags:
        - run-time-controller-v2
      operationId: trigger
      summary: This API either starts the process or signals the waiting process - v2
      externalDocs:
        description: About Trigger API - v2
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/trigger_process_v2
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              $ref: '#/components/schemas/TriggerPayload'
              additionalProperties:
                type: object
        required: true
        description: Payload for trigger v2
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ProcessTriggerResponse'
        "400":
          description: Invalid input or Input cannot be empty
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v2/evaluate-and-trigger:
    post:
      tags:
        - run-time-controller-v2
      operationId: evaluateAndTrigger
      summary: Evaluate the definition and start/signal the process
      externalDocs:
        description: About Evaluate and Trigger API - v2
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/evaluate_and_trigger
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              $ref: '#/components/schemas/TriggerPayload'
              additionalProperties:
                type: object
        required: true
        description: Payload for Evaluate and trigger v2
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ProcessTriggerResponse'
        "400":
          description: Invalid input or Input cannot be empty
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/trigger:
    post:
      tags:
        - run-time-controller
      operationId: trigger_1
      summary: This API either starts the process or signals the waiting process - v1
      externalDocs:
        description: Trigger API - v1
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/trigger_process
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
              $ref: '#/components/schemas/TriggerPayload'
        required: true
        description: Payload for trigger v1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
                      - FAILURE
                  response:
                    type: object
                    properties:
                      processId:
                        type: string
                        description: eed76284-b18d-11eb-8bdd-ea53bf7acc9b
                      status:
                        type: string
                        enum:
                          - PROCESS_STARTED
                          - PROCESS_SIGNALLED

        "400":
          description: Invalid input or Input cannot be empty
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/ticket/update:
    post:
      tags:
        - offline-ticket-controller
      operationId: updateOfflineTicket
      summary: Updates the offline ticket
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/template/validate:
    post:
      tags:
        - template-controller
      operationId: validateTemplate
      summary: Validates template files
      description: This API will validate the template by uploading the template files( BPMN and DMN files ) </br> <i>Where,</i> <br><b> files ( Required )</b> <i> </br> - BPMN and DMN files </br>Examples - invoiceapproval.bpmn, decision_invoiceapproval.dmn
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
        required: true
        description: BPMN and DMN files
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: boolean
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/template/save:
    post:
      tags:
        - template-controller
      operationId: saveTemplate
      summary: Save the template
      description: This API used to save the template by uploading the template files (BPMN and DMN files ) </br> <i>Where,</i> <br><b> files ( Required )</b> <i> </br> - BPMN and DMN files </br>Examples - invoiceapproval.bpmn, decision_invoiceapproval.dmn
      externalDocs:
        description: Save Template API
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/save_template
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                template_metadata:
                  type: object
                  $ref: '#/components/schemas/TemplateMetaData'
                  default:
                    status: enabled
                    creatorType: system
                    allowMultipleDefs: false
                    definitionType: USER
                    templateCategory: HIDDEN
                    validateTriggerNames: true
                    validateHistoryTTL: true
        required: true
        description: BPMN and DMN files
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TemplateSaveUpdateDetails'
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Template Save Exception
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/template/update:
    post:
      tags:
        - template-controller
      operationId: updateTemplate
      summary: Update the template
      description: This API used to update the template by uploading the template files( BPMN and DMN files ) </br> <i>Where,</i> <br><b> files ( Required )</b> <i> </br> - BPMN and DMN files </br>Examples - invoiceapproval.bpmn, decision_invoiceapproval.dmn
      externalDocs:
        description: Update Template API
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/update_template
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                files:
                  type: array
                  items:
                    type: string
                    format: binary
                template_metadata:
                  type: object
                  $ref: '#/components/schemas/TemplateMetaData'
                  default:
                    status: enabled
                    creatorType: system
                    allowMultipleDefs: false
                    definitionType: USER
                    templateCategory: HIDDEN
                    validateTriggerNames: true
                    validateHistoryTTL: true
        required: true
        description: BPMN and DMN files
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TemplateSaveUpdateDetails'
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Template Save Exception
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/evaluate-rules:
    post:
      tags:
        - run-time-controller
      operationId: evaluateRules
      summary: Evaluate the rules of definition and start/signal the process
      externalDocs:
        description: About Evaluate Rules API
        url: https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/api/evaluate_rules
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: object
              $ref: '#/components/schemas/TriggerPayload'
        required: true
        description: Payload for Evaluate rules
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
                  response:
                    type: object
                    properties:
                      evaluationResult:
                        type: object
                        properties:
                          name:
                            type: string
                          result:
                            type: array
                            items:
                              type: object
                              properties:
                                details:
                                  type: array
                                  items:
                                    type: object
                                id:
                                  type: string
                          inputParameters:
                            type: array
                            items:
                              type: string
        "400":
          description: Invalid input or Input cannot be empty
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/template/{templateName}/version/{templateVersion}:
    get:
      tags:
        - template-controller
      operationId: fetchTemplateByVersion
      summary: Fetches the template by its version
      description: This API fetches the template from Camunda DB using template name and its version. </br> <i>Where,</i> <br><b> templateName ( Required )</b> <i> </br> -  Template name(as defined in the <bpmn:process>) with `.bpmn` extension <br><b> templateVersion</b> <i> </br> - version of the template
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: templateName
          in: path
          required: true
          description: Template name with bpmn extension. Example - file.bpmn
          schema:
            type: string
        - name: templateVersion
          in: path
          required: true
          description: Template database version
          schema:
            type: integer
            format: int32

      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  statusInfo:
                    type: object
                    properties:
                      status:
                        type: string
                        enum:
                          - SUCCESS
                      response:
                        type: object
                        properties:
                          response:
                            type: string
                            format: xml
        "400":
          description: Invalid file format or BPMN doesn't exist
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
        "500":
          description: Internal Server Error
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/processes:
    get:
      tags:
        - process-controller
      operationId: fetchProcessListForDefinition
      summary: Fetches the process with Definition Id
      description: </br><b>definitionId</b> - Its the id created when the definition is created out of template
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: definitionId
          in: query
          required: true
          schema:
            type: string

      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                  response:
                    type: object
                    properties:
                      processes:
                        type: array
                        items:
                          type: object
                          properties:
                            processId:
                              type: string
                            recordId:
                              type: string
                            processStatus:
                              type: string
                            createdDate:
                              type: string
                              format: date-time
                            modifiedDate:
                              type: string
                              format: date-time
        "500":
          description: Internal Server Error - Only System Definition is Supported
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/history/process/{processInstanceId}:
    get:
      tags:
        - history-controller
      operationId: getProcessDetails
      summary: Fetches the process details
      description: </br><b>processInstanceId</b> - Its the process id obtained from process table
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: processInstanceId
          in: path
          required: true
          description: Process ID. Example - b83c33e5-ac01-11eb-b0db-3a98af507aff
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
                  response:
                    type: object
                    properties:
                      response:
                        type: object
                        properties:
                          businessKey:
                            type: string
                          processDefinitionName:
                            type: string
                          startTime:
                            type: string
                            format: date-time
                          endTime:
                            type: string
                            format: date-time
                          durationInMillis:
                            type: number
                          state:
                            type: string
                            enum:
                              - ACTIVE
        "500":
          description: Error Getting Details
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/history/variable-instance:
    post:
      tags:
        - history-controller
      operationId: getProcessVariableDetails
      summary: Fetches the process variable details
      description: </br><b>processInstanceId</b> - Its the process id obtained from process table
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
        - name: deserializeValues
          in: query
          required: true
          description: Determines whether serializable variable values (typically variables that store custom Java objects) should be deserialized on server side.
          schema:
            type: boolean
        - name: firstResult
          in: query
          required: false
          description: Pagination of results. Specifies the index of the first result to return.
          schema:
            type: integer
            default: 0
        - name: maxResults
          in: query
          required: false
          description: Pagination of results. Specifies the maximum number of results to return. Will return less results if there are no more results left.
          schema:
            type: integer
            default: 50
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                properties:
                  status:
                    type: string
                    enum:
                      - SUCCESS
                  response:
                    type: object
                    properties:
                      processVariables:
                        type: array
                        items:
                          type: object
                          properties:
                            type:
                              type: string
                            value:
                              type: object
                            valueInfo:
                              type: object
                            id:
                              type: string
                            name:
                              type: string
                            processDefinitionKey:
                              type: string
                            processDefinitionId:
                              type: string
                            processInstanceId:
                              type: string
                            executionId:
                              type: string
                            activityInstanceId:
                              type: string
                            state:
                              type: string
                            createTime:
                              type: string
                              format: date-time
                            removalTime:
                              type: string
                              format: date-time
                            rootProcessInstanceId:
                              type: string
        "500":
          description: Error Getting Details
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'

  /v1/history/migrate:
    post:
      tags:
        - definition-migration-controller
      operationId: migrate
      summary: Migrate existing definition to updated template
      description: </br><b>processInstanceId</b> - Its the process id obtained from process table
      parameters:
        - name: intuit_tid
          in: header
          required: false
          description: Transaction id can be any letters,symbols etc.,
          schema:
            type: string
            default: "00000-00000-00000"
        - name: intuit_was_offeringid
          in: header
          required: false
          description: Offering Id to support Multitenancy (Uses default if not specified)
          schema:
            type: string
            default: "sandbox"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                processVariables:
                  type: array
                  items:
                    type: object
                    properties:
                      bpmnDefinitionIds:
                        type: array
                        items:
                          type: string
                      updatedTemplateId:
                        type: string
                      offlineTicketRefresh:
                        type: boolean
        required: true
        description: Payload for trigger v1
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                oneOf:
                  - type: object
                    properties:
                      status:
                        type: string
                        enum:
                          - SUCCESS
                  - $ref: '#/components/schemas/WorkflowErrorDetails'
              examples:
                success:
                  summary: Example of a successful response
                  value:
                    status: SUCCESS
                error:
                  summary: Example of an error response
                  value:
                    status: FAILURE
                    error:
                      errorCode: WXXX
                      errorMessage: "Some error message"
                      errorDescription: "Some error description"
        "500":
          description: Error Getting Details
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/WorkflowErrorDetails'
components:
  schemas:
    TemplateSaveUpdateDetails:
      type: object
      properties:
        status:
          type: string
          enum:
            - SUCCESS
        response:
          type: object
          properties:
            response:
              type: string
    ProcessTriggerResponse:
      type: object
      properties:
        status:
          type: string
          enum:
            - SUCCESS
        response:
          type: object
          properties:
            triggers:
              type: object
              properties:
                definitionId:
                  type: string
                  description: cb8e0764-b18d-11eb-8bdd-ea53bf7acc9b
                definitionName:
                  type: string
                  description: Test-write
                processId:
                  type: string
                  description: eed76284-b18d-11eb-8bdd-ea53bf7acc9b
                status:
                  type: string
                  enum:
                    - PROCESS_STARTED
                    - PROCESS_SIGNALLED
    WorkflowErrorDetails:
      type: object
      properties:
        status:
          type: string
          enum:
            - FAILURE
        error:
          type: object
          properties:
            errorCode:
              type: string
            errorMessage:
              type: string
            errorDescription:
              type: string
    TriggerPayload:
      type: object
      description: <b>workflow</b> - workflow is the template name </br><b>entityType</b> - entity type of the workflow </br><b>entityChangeType</b> - mandatory entity details </br><b>entityId</b> - entityId should present in entity. This used to uniquely identify the process </br>
      example:
        eventHeaders:
          workflow: approval
          entityType: invoice
          entityChangeType: updated
        entity:
          Invoice:
            CurrencyRef:
              name: United States Dollar
              value: USD
            EmailStatus: NotSet
            AllowOnlineACHPayment: false
            AllowIPNPayment: false
            MetaData:
              CreateTime: '2020-01-30T21:37:01-08:00'
              LastUpdatedTime: '2020-01-30T21:37:01-08:00'
            DocNumber: '1013'
            PrintStatus: NeedToPrint
            DueDate: '2020-03-01'
            LinkedTxn: []
            AllowOnlinePayment: false
            TxnDate: '2020-01-31'
            DepartmentRef:
              name: dep1
              value: '1'
            Line:
              - LineNum: 1
                DetailType: SalesItemLineDetail
                Amount: 2
                SalesItemLineDetail:
                  TaxCodeRef:
                    value: NON
                  ItemAccountRef:
                    name: Services
                    value: '4'
                  ItemRef:
                    name: Gardening
                    value: '3'
                Id: '1'
              - SubTotalLineDetail: {}
                DetailType: SubTotalLineDetail
                Amount: 2
            SyncToken: '0'
            sparse: false
            TotalAmt: 800
            domain: QBO
            CustomField: []
            SalesTermRef:
              value: '3'
            Id: '23'
            Tag: []
            AllowOnlineCreditCardPayment: false
            CustomerRef:
              name: Customer1
              value: '1'
            Balance: 2
            ApplyTaxAfterDiscount: false
          time: '2020-01-30T21:37:28.661-08:00'

    TemplateMetaData:
      type: object
      properties:
        status:
          type: status
          enum:
            - enabled
            - disabled
        creatorType:
          type: creatorType
          enum:
            - system
            - user
        allowMultipleDefs:
          type: boolean
        definitionType:
          type: definitionType
          enum:
            - USER
            - SINGLE
            - SYSTEM
        templateCategory:
          type: templateCategory
          enum:
            - HUB
            - SYSTEM
            - CUSTOM
            - HIDDEN
            - QBDT
        validateTriggerNames:
          type: boolean
        validateHistoryTTL:
          type: boolean
          
  securitySchemes:
    Authorization:
      type: apiKey
      in: header
      name: Authorization
      description: Latest token should be replaced with intuit_token, for example - Intuit_IAM_Authentication intuit_token_type=XXXX,<b>intuit_token="XXXX",</b> intuit_appid=XXXX,intuit_app_secret=XXXX,intuit_userid=XXXX,intuit_realmid=XXXX

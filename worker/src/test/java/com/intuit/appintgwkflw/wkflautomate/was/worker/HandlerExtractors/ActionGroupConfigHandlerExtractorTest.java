package com.intuit.appintgwkflw.wkflautomate.was.worker.HandlerExtractors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ROOT_PROCESS_INSTANCE_ID;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.google.common.base.Charsets;
import com.google.common.io.Resources;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskEvent;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.async.execution.request.State;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import org.apache.commons.io.IOUtils;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ActionGroupConfigHandlerExtractorTest {

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @InjectMocks ActionGroupConfigHandlerExtractor actionGroupConfigHandlerExtractor;

  public static String DICTIONARY_PATH = "config.yaml";
  public static String YAML_KEY = "templateConfig";

  public static CustomWorkflowConfigFactory customWorkflowConfigFactory;

  public static String readResourceAsString(String path) {
    try (InputStream stream = Resources.getResource(path).openStream()) {
      return IOUtils.toString(stream, Charsets.UTF_8);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static CustomWorkflowConfig loadCustomConfig() {

    try {

      OldCustomWorkflowConfig oldCustomWorkflowConfig =
          new ObjectMapper(new YAMLFactory())
              .readValue(
                  readResourceAsString(DICTIONARY_PATH),
                  new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
              .get(YAML_KEY);
      oldCustomWorkflowConfig.afterPropertiesSet();
      CustomConfigV2 customConfigV2 = new CustomConfigV2();
      CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
      customWorkflowConfig.setCustomWorkflowConfigFactory(
          new CustomWorkflowConfigFactory (
              oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
      customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
      customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
      return customWorkflowConfig;
    } catch (Exception e) {
    }
    return null;
  }

  CustomWorkflowConfig customWorkflowConfig = loadCustomConfig();

  @Test
  public void testHandlerAlreadyPresent() {
    State inputRequest = new State();

    inputRequest.addValue(AsyncTaskConstants.HANDLER_DETAILS, "sampleHandler");

    State inputRequestResponse = actionGroupConfigHandlerExtractor.execute(inputRequest);
    Assert.assertNotNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
    Assert.assertEquals(
        inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS), "sampleHandler"
    );
  }

  @Test
  public void testRootProcessDefinitionNotFound() {
    State inputRequest = new State();

    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put("entityType", "invoice");

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, getExternalTask("createTask"));
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);

    when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        eq("rootProcessId")
    )).thenReturn(Optional.empty());

    try {
      State inputRequestResponse = actionGroupConfigHandlerExtractor.execute(inputRequest);
      Assert.fail();
    }
    catch (WorkflowGeneralException exception) {
      Assert.assertEquals(exception.getWorkflowError(), WorkflowError.UNSUPPORTED_HANDLER_DETAILS);
    }
  }


  @Test
  public void testHandlerDetailsFetching() {
    State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put(ENTITY_TYPE, "invoice");

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, getExternalTask("createTask"));
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);

    when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        eq("rootProcessId")
    )).thenReturn(Optional.of(getDefinitionDetails()));

    State inputRequestResponse = actionGroupConfigHandlerExtractor.execute(inputRequest);
    Assert.assertNotNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
    HandlerDetails handlerDetails =
        inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS);
    Assert.assertEquals("appconnect", handlerDetails.getTaskHandler());
    Assert.assertEquals("executeWorkflowAction", handlerDetails.getActionName());

  }


  @Test
  public void testRestHandlerDetailsFetching() {
    State inputRequest = new State();
    Map<String, String> parametersSchema = new HashMap<>();
    parametersSchema.put(ENTITY_TYPE, "invoice");

    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_EXTERNAL_TASK, getExternalTask("sendCompanyEmail"));
    inputRequest.addValue(AsyncTaskConstants.CAMUNDA_INPUT_VARIABLE_MAP, parametersSchema);
    inputRequest.addValue(AsyncTaskConstants.CUSTOM_WORKFLOW_CONFIG, customWorkflowConfig);

    when(processDetailsRepoService.findByProcessIdWithoutDefinitionData(
        eq("rootProcessId")
    )).thenReturn(Optional.of(getDefinitionDetails()));

    State inputRequestResponse = actionGroupConfigHandlerExtractor.execute(inputRequest);
    Assert.assertNotNull(inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS));
    HandlerDetails duzzitRestHandlerDetail =
        inputRequestResponse.getValue(AsyncTaskConstants.HANDLER_DETAILS);
    Assert.assertEquals("appconnect", duzzitRestHandlerDetail.getTaskHandler());
    Assert.assertEquals("executeDuzzitRestAction", duzzitRestHandlerDetail.getActionName());
  }

  private DefinitionDetails getDefinitionDetails() {
    TemplateDetails templateDetails = new TemplateDetails();
    templateDetails.setTemplateName("customApproval");
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setTemplateDetails(templateDetails);
    return definitionDetails;
  }

  private ExternalTaskEvent getExternalTask(String activityId) {
    ExternalTaskEvent externalTask = mock(ExternalTaskEvent.class);
    when(externalTask.getActivityId()).thenReturn(activityId);
    when(externalTask.getProcessDefinitionKey()).thenReturn("sendForApproval");
    when(externalTask.getVariables()).thenReturn(Map.of(
        ROOT_PROCESS_INSTANCE_ID, "rootProcessId"
    ));
    return externalTask;
  }


}

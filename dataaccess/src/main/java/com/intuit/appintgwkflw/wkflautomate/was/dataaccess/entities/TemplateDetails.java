package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.CreatorTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.ModelTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.RecordTypeConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter.StatusConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import java.sql.Timestamp;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "de_template_details")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@NamedNativeQuery(
    name = "TemplateDetails.getAllRecords",
    query =
        "SELECT temp.* FROM (SELECT template_name, MAX(created_date) AS created_date FROM de_template_details GROUP BY template_name) AS t1 INNER JOIN de_template_details temp ON temp.template_name = t1.template_name AND temp.created_date= t1.created_date where status='enabled' AND temp.template_category NOT IN ('CUSTOM')",
    resultClass = TemplateDetails.class)
public class TemplateDetails {

  @Id
  @Column(name = "template_id", unique = true, nullable = false)
  private String id;

  private String templateName;

  private String description;

  private Boolean allowMultipleDefinitions;

  // Template category could change and new will be added frequently. Can not tie it to enum.
  private String templateCategory;

  private int version;

  @Convert(converter = ModelTypeConverter.class)
  private ModelType modelType;

  private String parentId;

  private String offeringId;

  @Convert(converter = CreatorTypeConverter.class)
  private CreatorType creatorType;

  private Long createdByUserId;

  private Long modifiedByUserId;

  private String displayName;

  // Company Id in case of QBO
  private Long ownerId;

  @Convert(converter = RecordTypeConverter.class)
  private RecordType recordType;

  @Convert(converter = StatusConverter.class)
  private Status status;

  private Timestamp createdDate;

  private Timestamp modifiedDate;

  private byte[] templateData;
  
  // id of the definition deployed in bpmn engine
  private String deployedDefinitionId;

  // the version of the tag, used in the tag based roll-out
  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String tag;

  // type of deployed definition
  @Enumerated(EnumType.STRING)
  @Builder.Default
  private DefinitionType definitionType = DefinitionType.USER;

  @Column(name = "template_adjacency_values_md5_sum", unique = true)
  private String templateAdjacencyValuesMd5Sum;

  /* Custom constructor without  templateData */
  public TemplateDetails(
      String id,
      String templateName,
      String description,
      Boolean allowMultipleDefinitions,
      String templateCategory,
      int version,
      String parentId,
      String offeringId,
      String displayName,
      Long ownerId,
      RecordType recordType,
      DefinitionType definitionType,
      Object tagVersion) {
    this.id = id;
    this.templateName = templateName;
    this.description = description;
    this.allowMultipleDefinitions = allowMultipleDefinitions;
    this.templateCategory = templateCategory;
    this.version = version;
    this.parentId = parentId;
    this.offeringId = offeringId;
    this.displayName = displayName;
    this.ownerId = ownerId;
    this.recordType = recordType;
    this.definitionType = definitionType;
    this.tag = Objects.nonNull(tagVersion) ? (String)tagVersion : null;
  }
}

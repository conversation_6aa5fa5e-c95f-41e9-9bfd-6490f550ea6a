package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "overwatchproperties")
/**
 * A Config file to read the properties used for OverWatch automations.
 *
 * <AUTHOR>
 */
public class OverWatchConfig {

  private boolean connectorWorkflowId;

}
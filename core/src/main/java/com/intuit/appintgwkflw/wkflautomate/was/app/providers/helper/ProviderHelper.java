package com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.OPERATION_NOT_PERMITTED;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType.SYSTEM;
import static org.apache.commons.lang.StringUtils.isBlank;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.globalid.GlobalIdV41;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@AllArgsConstructor
public class ProviderHelper {
  private TemplateDetailsRepository templateDetailsRepository;
  private DefinitionDetailsRepository definitionDetailsRepository;
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;
  private MultiStepConfig multiStepConfig;

  /**
   * @param query : GraphQl Query as Input
   * @return : Output to determine if we need to parse workflowSteps or not.
   */
  @SuppressWarnings("unchecked")
  public boolean checkForWorkflowSteps(QueryHelper query) {

    List<Query.PreparedQuery> subQueries =
        (List<Query.PreparedQuery>)
            query.getPreparedQuery().get(WorkflowConstants.SUB_QUERIES_IDENTIFIER);

    return !CollectionUtils.isEmpty(subQueries)
        && subQueries.stream()
            .anyMatch(
                preparedQueryName ->
                    WorkflowConstants.WORKFLOW_STEP_IDENTIFIER.equalsIgnoreCase(
                        preparedQueryName.getName()));
  }

  /** Populates the RealmId in the GlobalId for Template records in the list */
  public void populateRealmInGlobalId(List<Template> templateList, String realm) {
    for (Template template : templateList) {
      template.setId(
          GlobalIdV41.builder()
              .setTypeId(WorkflowConstants.TEMPLATE_TYPE_ID)
              .setRealmId(realm)
              .setLocalId(template.getId().getLocalId())
              .build());
    }
  }

  /**
   * Get Template name to be used in case of custom workflow. During custom workflow create, we
   * won't be getting the template id in the mutation request. Instead we will determine from
   * payload(action key) what kinf of workflow (reminder/approval etc) user is trying to set up.
   * Based on the action key, we will find the template to be used from CustomWorkflowType.
   *
   * @param definition - Definition Object from the UI for Create Definition Request
   * @return
   */
  public String getTemplateNameForCustomWorkflow(Definition definition) {
    String actionKey = getActionKeyForCustomWorkflow(definition);
    String templateName = CustomWorkflowType.getTemplateName(actionKey);
    WorkflowLogger.logInfo("ActionKey=%s", actionKey);
    WorkflowVerfiy.verify(isBlank(templateName), WorkflowError.INVALID_ACTION_KEY);

    return templateName;
  }

  /**
   * Determine action key (e.g. reminder, approval) from payload
   *
   * @param definition
   * @return
   */
  public String getActionKeyForCustomWorkflow(Definition definition) {
    return definition.getWorkflowSteps().stream()
            .findFirst()
            .map(
                    workflowStep ->
                            workflowStep.getActions().stream()
                                    .findFirst()
                                    .map(ActionMapper::getActionKey)
                                    .orElse(null))
            .orElse(null);
  }

  /**
   * We will determine the template from the payload(action key of the first step of workflow)
   *
   * @param definition - Definition Object from the UI for Create Definition Request
   * @return
   */
  public String getTemplateNameForMultiStepWorkflow(Definition definition) {
    String actionKey = CustomWorkflowUtil.getActionKeyFromWorkflowSteps(definition);

    String templateName = CustomWorkflowType.getTemplateName(actionKey);
    WorkflowLogger.logInfo("step=fetchActionKeyAndTemplateName " +
            "actionKey=%s templateName=%s", actionKey, templateName);
    WorkflowVerfiy.verify(isBlank(templateName), WorkflowError.INVALID_ACTION_KEY);
    return templateName;
  }

  /**
   * Returns the latest (ordered by created date) active TemplateDetails object. Active template is
   * where status is enabled.
   *
   * @param definition
   * @return
   */
  private Optional<TemplateDetails> getTemplateDetails(Definition definition, boolean isActive) {
    if (CustomWorkflowUtil.isCustomWorkflow(definition.getTemplate())) {
      return getTemplateDetailsForCustomWorkflow(definition);
    } else {
      // check if template ID is null
      WorkflowVerfiy.verifyNull(
          definition.getTemplate().getId().getLocalId(), WorkflowError.TEMPLATE_NOT_FOUND);
      if (isActive) {
        return templateDetailsRepository.findTopByIdAndStatusOrderByCreatedDate(
            definition.getTemplate().getId().getLocalId(), Status.ENABLED);
      }
      return templateDetailsRepository.findById(definition.getTemplate().getId().getLocalId());
    }
  }

  /**
   * Non System user: get templateDetails by Name as template object is empty in custom workflow
   * Rollback/Migration:  migration/ rollback from single definition is happening
   *               As part of rollback, we have to update the definition to a lower template version
   *               fetching template details using provided ID in case of custom workflow for migration/ rollback use case
   *
   * Sdef workflows : If multiStepConfig is not empty, use that version of template
   * Multi-condition (non dynamic workflows) : Applicable for approval scenario - fetch template with latest version excluding -1
   * Multi-condition (dynamic workflows) : These template detail are not used for creating definitions, so just return the latest one excluding -1.
   * @param definition
   * @return
   */
  private Optional<TemplateDetails> getTemplateDetailsForCustomWorkflow(Definition definition){
    if (WASContext.isMigrationContext()) {
      return templateDetailsRepository.findTopByIdAndStatusOrderByCreatedDate(
          definition.getTemplate().getId().getLocalId(), Status.ENABLED);
    }
    boolean isMultiCondition = MultiStepUtil.isMultiCondition(definition);
    String templateName =  isMultiCondition ? getTemplateNameForMultiStepWorkflow(definition) :
            getTemplateNameForCustomWorkflow(definition);

    //If the template version for custom workflow is present in the multi-step config, then
    //pick that template otherwise pick the template with the latest version
    if(!isMultiCondition && Objects.nonNull(multiStepConfig.getWorkflowTemplates()) &&
            Objects.nonNull(multiStepConfig.getWorkflowTemplates().get(templateName))) {
      int version = multiStepConfig.getWorkflowTemplates().get(templateName).getSingleStepVersion();
      WorkflowLogger.logInfo("step=fetchTemplateForTemplateNameAndVersion " +
              "template=%s version=%s", templateName, version);
      return templateDetailsRepository.findByTemplateNameAndVersion(templateName, version);
    }
    // Dynamically created templates will have version as -1
    // For multi-condition dynamic workflows, these template details are not used for creating definitions, rather it is fetched
    // on the basis of hashSum in DynamicBpmnDefinitionProcessor class.
    return templateDetailsRepository.findTopByTemplateNameAndStatusAndVersionNotInOrderByCreatedDateDesc(
        templateName, Status.ENABLED, Collections.singletonList(-1));

  }

  public Optional<TemplateDetails> getTemplateDetails(Definition definition) {
    return getTemplateDetails(definition, false);
  }

  public Optional<TemplateDetails> getActiveTemplateDetails(Definition definition) {
    return getTemplateDetails(definition, true);
  }

  public void validateRequest(Definition definition, Authorization authorization) {
    // Remove whitespaces from definition display name
    definition.setDisplayName(StringUtils.trim(definition.getDisplayName()));

    validateCustomWorkflowRequest(definition, authorization.getRealm());

    // allow creating new definition/updating definition if the definition status in request is
    // disabled
    if (WorkflowStatusEnum.DISABLED.equals(definition.getStatus())) {
      return;
    }

    // Check if there is template details specified in definition is the latest active template
    TemplateDetails templateDetails =
        getActiveTemplateDetails(definition).orElseThrow(
            () -> new WorkflowGeneralException(WorkflowError.TEMPLATE_NOT_FOUND));

    // create definition is not allowed for system workflows
     WorkflowVerfiy.verify(SYSTEM == templateDetails.getDefinitionType(), OPERATION_NOT_PERMITTED);

     // Check if having multiple active definitions are allowed for this template
     checkMultipleDefsAllowed(definition.getId(), templateDetails, authorization, definition.getRecordType());

  }

  /**
   * If current definition is precanned, check if custom definition is present for same owner and
   * recordtype and action
   *
   * @param ownerId
   * @param templateName
   * @param recordType
   * @return : List<DefinitionDetails> in case there exists enabled Definitions or otherwise return
   *     empty list</>
   */
  private List<DefinitionDetails> findEnabledDefinitionForCustomTemplateAndRecordType(
      String ownerId, String templateName, RecordType recordType) {
    // example: templateName="invoiceapproval"
    CustomWorkflowType customWorkflowType = CustomWorkflowType.getCustomWorkflow(templateName
        .replace(recordType.getRecordType(), ""));
    if (!Objects.isNull(customWorkflowType)) {
      // templateNameCustom = "customApproval"
      String templateNameCustom = customWorkflowType.getTemplateName();
      return definitionDetailsRepository
          .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
              Long.parseLong(ownerId),
              recordType,
              templateNameCustom,
              Status.ENABLED,
              ModelType.BPMN)
          .orElse(Collections.EMPTY_LIST);
    }
    return Collections.EMPTY_LIST;
  }

  /**
   * Method to validate the Input Request. This will handle all the necessary validations that are
   * needed for Custom Workflow(s).
   *
   * @param definition : {@link Definition} : Definition Object
   * @param realm : {@link String} : Company Id
   */
  private void validateCustomWorkflowRequest(
      Definition definition, String realm) {

    // do nothing in case of Pre-canned templates
    if (!CustomWorkflowUtil.isCustomWorkflow(definition.getTemplate())) return;

    WorkflowVerfiy.verify(
        ObjectUtils.isEmpty(definition.getDisplayName()), WorkflowError.DISPLAY_NAME_NOT_FOUND);
    if(!MultiStepUtil.isMultiCondition(definition)) {
      WorkflowVerfiy.verify(
          ObjectUtils.isEmpty(CustomWorkflowUtil.getRulesFromDefinition(definition)),
          WorkflowError.RULES_NOT_FOUND);
    }

    validateDefinitionName(definition, realm);

    /** To check if precanned definition is already present for the custom workflow and it is not a migration */
    if (WorkflowStatusEnum.ENABLED.equals(definition.getStatus())
        && isActionPresent(definition) && !customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(definition)) {
      String workflowName = getActionKey(definition);
      RecordType recordType = RecordType.fromType(definition.getRecordType());
      String templateName = TriggerUtil.getTemplateName(recordType, workflowName);
      Optional<List<DefinitionDetails>> optionalDefinitionDetails =
              definitionDetailsRepository
                      .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                              Long.parseLong(realm),
                              ModelType.BPMN,
                              recordType,
                              templateName,
                              false);
      WorkflowVerfiy.verify(
              !(optionalDefinitionDetails.isEmpty()
                      || optionalDefinitionDetails.get().isEmpty()),
          WorkflowError.DEFINITION_ALREADY_EXISTS);
    }
  }

  private Boolean isActionPresent(Definition definition) {
    if(MultiStepUtil.isMultiCondition(definition)) {
      return ObjectUtils.isNotEmpty(definition.getWorkflowSteps().stream()
              .filter(workflowStep -> (MultiStepUtil.isActionStep(workflowStep) || MultiStepUtil.isCompositeStep(workflowStep)))
              .findFirst().get().getActionGroup());
    }

    return ObjectUtils.isNotEmpty(definition.getWorkflowSteps().stream()
            .findFirst().get().getActions());
  }

  private String getActionKey(Definition definition) {
    if(MultiStepUtil.isMultiCondition(definition)) {
      return definition.getWorkflowSteps().stream()
              .filter(workflowStep -> (MultiStepUtil.isActionStep(workflowStep) || MultiStepUtil.isCompositeStep(workflowStep)))
              .findFirst().get().getActionGroup().getActionKey();
    }

    return definition.getWorkflowSteps().stream().findFirst().get()
            .getActions().stream().findFirst().get().getActionKey();
  }

  /**
   * Method to validate User provided Display Name in the Input Request . We cannot have 2 enabled
   * definition with same name for custom workflow. It throws error in case another enabled
   * definition is found in the database with the same definition name.
   *
   * @param definition : {@link Definition} : Definition Object
   * @param realm : {@link String} : String Object
   */
  private void validateDefinitionName(Definition definition, String realm) {

    List<DefinitionDetails> definitionDetails =
        definitionDetailsRepository
            .findDefinitionsForCustomWorkflow(realm, definition.getDisplayName().trim())
            .orElse(null);

    // Case of Create Definition
    if (ObjectUtils.isEmpty(definition.getId())) {
      WorkflowVerfiy.verify(
          ObjectUtils.isNotEmpty(definitionDetails), WorkflowError.DEFINITION_NAME_ALREADY_EXISTS);
    } else {
      if (ObjectUtils.isEmpty(definitionDetails)) {
        return;
      }

    if (Objects.nonNull(definitionDetails)){
      WorkflowVerfiy.verify(
          definitionDetails.size() > 1, WorkflowError.DEFINITION_NAME_ALREADY_EXISTS);
      if (definitionDetails.size() == 1) {
        // verify if the definition name provided during update, matches with some other definition
        // name.
        WorkflowVerfiy.verify(
            !definitionDetails
                .get(0)
                .getDefinitionId()
                .equalsIgnoreCase(definition.getId().getLocalId()),
            WorkflowError.DEFINITION_NAME_ALREADY_EXISTS);
      }
    }

    }
  }

  /**
   * This check is to determine if a definition already exists for a given template. // Throws
   * Exception in case definition already exists and template supports creating only single
   * definition.
   *
   * @param definitionId : Definition id from the UI for Create Definition Request
   * @param authorization : Authorization Method throws an Exception if trying to create multiple
   *     definition if AllowMultipleDefinitions == false
   */
  public void checkMultipleDefsAllowed(
      GlobalId<?> definitionId,
      TemplateDetails templateDetails,
      Authorization authorization,
      String recordType) {
    isMultipleDefinitionAllowed(definitionId, templateDetails, authorization.getRealm(), recordType, false);
  }

  /**
   * To check whether to create a multiple definition for Custom and Precanned.
   * @param definitionId
   * @param templateDetails
   * @param ownerId
   * @param recordType
   * @param isDefinitionEnabled
   */
  public void isMultipleDefinitionAllowed(GlobalId<?> definitionId , TemplateDetails templateDetails, String ownerId,
      String recordType, boolean isDefinitionEnabled) {
    List<DefinitionDetails> definitionDetails = null;
    if (templateDetails.getAllowMultipleDefinitions()) {
      // check for custom workflow case, if multiple definitions are allowed for entity type
      CustomWorkflowType customWorkflowType =
          CustomWorkflowType.getCustomWorkflowForTemplateName(templateDetails.getTemplateName());
      if (Objects.nonNull(customWorkflowType)
          && !customWorkflowType.canCreateMultipleEnitityDefinitions()) {
        // change in query to fetch template by name and onwerId to avoid duplicate workflow creation post template  upadte
        definitionDetails =
            definitionDetailsRepository
                .findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
                    Long.parseLong(ownerId),
                    ModelType.BPMN,
                    RecordType.fromType(recordType),
                    templateDetails.getTemplateName(),
                    false)
                .orElse(Collections.EMPTY_LIST);
        // validate multiple definitions for create/update definition case
        validateMultipleDefinitions(definitionDetails, definitionId, isDefinitionEnabled);
      } else {
        WorkflowLogger.logDebug(
            "Allowed Multiple Definition is true", this.getClass().getSimpleName());
        return ;
      }
    }
    /*Only Precanned templates come here since all Custom Workflows have allowMultipleDefs=true*/
    else {
      // check if there exists a definition with status enabled for this realm
      Optional<List<DefinitionDetails>> definitionDetailsOptional =
                  definitionDetailsRepository.findLatestEnabledDefinitionsForOwnerIdRecordTypeAndTemplateName(
              Long.parseLong(ownerId), ModelType.BPMN, RecordType.fromType(recordType),
              templateDetails.getTemplateName(),
                      false);
      // check if there exists Multiple Enable Definition for the given Template, throw error as
      // AllowMultipleDefinitions == false
      definitionDetails = definitionDetailsOptional.orElse(null);

      // For precanned invoice approval check if custom workflow is already present
      // Todo: to be removed post migration
      if (CollectionUtils.isEmpty(definitionDetails)) {
        definitionDetails = findEnabledDefinitionForCustomTemplateAndRecordType(
            ownerId, templateDetails.getTemplateName(), RecordType.fromType(recordType));
      }
      // validate multiple definitions for create/update definition case
      validateMultipleDefinitions(definitionDetails, definitionId, isDefinitionEnabled);
    }
  }

  /**
   * Validate if multiple definitions exist For create definition / update definition
   *
   * @param requestDefinitionId
   * @param definitionDetailsList
   */
  private void validateMultipleDefinitions(
      List<DefinitionDetails> definitionDetailsList, GlobalId requestDefinitionId, boolean isDefinitionEnabled) {

    // Logging the size of the list of definitions returned.
    WorkflowLogger.logInfo("Total number of existing definition=%s",
        requestDefinitionId,
        Objects.nonNull(definitionDetailsList)
            ? definitionDetailsList.size()
            : 0, this.getClass().getName());

    // create & enable definition case
    if (Objects.isNull(requestDefinitionId) || isDefinitionEnabled) {
      WorkflowVerfiy.verify(
          !CollectionUtils.isEmpty(definitionDetailsList), WorkflowError.DEFINITION_ALREADY_EXISTS);
    } else { // update definition case
      if (ObjectUtils.isEmpty(definitionDetailsList)) {
        // It implies that no existing definition is Enabled in case of Update/Edit Definition,
        // simply return
        return;
      }
//    For Update check input defn with defn present in DB. For Create/Enable only check if there's already an enabled definition
      Optional<DefinitionDetails> definitionDetailOptional =
          definitionDetailsList.stream().findFirst();
      WorkflowVerfiy.verify(
          definitionDetailOptional.isPresent()
              && !definitionDetailOptional.get()
              .getDefinitionId()
              .equalsIgnoreCase(requestDefinitionId.getLocalId()),
          WorkflowError.DEFINITION_ALREADY_EXISTS);
    }
  }

}

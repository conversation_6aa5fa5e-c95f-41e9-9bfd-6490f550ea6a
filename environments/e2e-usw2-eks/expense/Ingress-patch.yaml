kind: Ingress
apiVersion: networking.k8s.io/v1
metadata:
  annotations:
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:914122326290:certificate/99389947-5448-4dfc-84c6-c8bec946a77e
    alb.ingress.kubernetes.io/security-groups: iks-intuit-cidr-ingress-tcp-443,iks-intuit-api-gw-ingress-preprod-tcp-443,iks-intuit-app-alb-custom-ingress,iks-intuit-ibp-ingress-tcp-443
    external-dns.alpha.kubernetes.io/hostname: appintgwkflw-e2e-was-event-in-expense.sbgqboppdusw2.iks2.a.intuit.com,appintgwkflw-e2e-was-event-in-expense-desired.sbgqboppdusw2.iks2.a.intuit.com,appintgwkflw-e2e-was-event-in-expense-stable.sbgqboppdusw2.iks2.a.intuit.com
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: '15'
  name: was-event-in-ingress
spec:
  rules:
  - host: appintgwkflw-e2e-was-event-in-expense-desired.sbgqboppdusw2.iks2.a.intuit.com
    http:
      paths:
      - backend:
          service:
            name: was-event-in-desired-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
  - host: appintgwkflw-e2e-was-event-in-expense-stable.sbgqboppdusw2.iks2.a.intuit.com
    http:
      paths:
      - backend:
          service:
            name: was-event-in-stable-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific
  - http:
      paths:
      - backend:
          service:
            name: was-event-in-root-service
            port:
              number: 443
        path: /*
        pathType: ImplementationSpecific

package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy.verify;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.VARIABLES;
import static java.util.Objects.nonNull;

import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Tracer;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import lombok.Getter;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

/**
 * Class represents V3 transaction payload received from QBO along with additional event and
 * workflow specific information
 */
@Getter
public class TransactionEntity {

  private Map<String, Object> v3EntityPayload;

  @Tracer(key = WASContextEnums.RECORD_ID)
  private String entityId;

  private Map<String, Object> entityObj;

  private EventHeaders eventHeaders;

  private ProcessVariableDetails variables;

  
  public TransactionEntity(Map<String, Object> entityPayload) {
    this.v3EntityPayload = entityPayload;
    this.eventHeaders = TransactionEntityHelper.getEventHeaders(entityPayload);
    this.entityObj = getEntity(entityPayload);
    this.variables = getProcessVariables();
    this.entityId = populateEntityId();
    logTransactionEntity();
  }

  public RecordType getEntityType() {
    return this.eventHeaders.getEntityType();
  }

  public String getEntityChangeType() {
    return eventHeaders.getEntityChangeType();
  }

  public String getWorkflowType() {
    return this.eventHeaders.getWorkflow();
  }

  /**
   * Extract entity map from transaction object in a case-insensitive manner based on the
   * record-type passed. ex: when record type is purchaseorder and the transaction entity from
   * trigger payload contains PurchaseOrder or Purchaseorder (or any case) - convert to a treemap
   * first and then perform a get based on record-type
   *
   * @param transactionEntityObj transaction entity object
   * @param recordType record-type ex: invoice, bill, purchaseorder
   * @return entity object map for a particular record-type
   */
  @SuppressWarnings("unchecked")
  public Map<String, Object> getEntityFromTransaction(
      Map<String, Object> transactionEntityObj, String recordType) {
    Map<String, Object> caseInsensitiveEntityMap = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
    caseInsensitiveEntityMap.putAll(transactionEntityObj);
    return (Map<String, Object>) caseInsensitiveEntityMap.getOrDefault(recordType, Collections.emptyMap());
  }

  /**
   * @param variableMap
   * @return
   */
  @SuppressWarnings("unchecked")
  private Map<String, Object> getEntity(Map<String, Object> variableMap) {
    return (Map<String, Object>) variableMap.get(WorkflowConstants.ENTITY);
  }

  /** @return entityId */
  private String populateEntityId() {
    return nonNull(this.variables) && MapUtils.isEmpty(entityObj)
        ? fetchEntityId()
        : this.getEntityId(entityObj);
  }

  /**
   * @param entityObject
   * @return
   */
  @SuppressWarnings("unchecked")
  private String getEntityId(Map<String, Object> entityObject) {
    WorkflowVerfiy.verify(MapUtils.isEmpty(entityObject), WorkflowError.INVALID_INPUT);

    // read entity id from event header if present
    String entityId = getEntityIdFromEventHeaders();

    Map<String, Object> entityObjectMap =
        getEntityFromTransaction(entityObject, this.eventHeaders.getEntityType().toString());
    WorkflowVerfiy.verify(MapUtils.isEmpty(entityObjectMap), WorkflowError.INPUT_INVALID, WorkflowConstants.ENTITY);

    // setting entityId as Id in the body so that it can be used as process variable while starting
    // the process
    if (StringUtils.isNotBlank(entityId)) {
      entityObjectMap.put(StringUtils.capitalize(WorkflowConstants.ID), entityId);
      return entityId;
    }

    Object id = entityObjectMap.get(StringUtils.capitalize(WorkflowConstants.ID));

    // throw error if id is empty
    WorkflowVerfiy.verify(
        Objects.isNull(id) || StringUtils.isBlank((String) id),
        WorkflowError.INPUT_INVALID,
        WorkflowConstants.ID);

    return (String) id;
  }

  /**
   * populate process variable details from entityPayload
   *
   * @return {@link ProcessVariableDetails}
   */
  private ProcessVariableDetails getProcessVariables() {
    Object variables = v3EntityPayload.get(VARIABLES);
    if (Objects.nonNull(variables)) {
      return ObjectConverter.convertObject(variables, ProcessVariableDetails.class);
    }
    return null;
  }

  /** @return return entityId from EventHeaders */
  private String getEntityIdFromEventHeaders() {
    return Optional.ofNullable(eventHeaders)
        .map(EventHeaders::getEntityId)
        .filter(StringUtils::isNotEmpty)
        .map(entityId -> entityId)
        .orElse(null);
  }

  /** @return entityId */
  private String fetchEntityId() {
    String entityId = getEntityIdFromEventHeaders();
    verify(StringUtils.isBlank(entityId), WorkflowError.INVALID_ENTITY_ID);
    return entityId;
  }

  private void logTransactionEntity() {
    WorkflowLogger.logInfo("workflow=%s, entityChangeType=%s, entityType=%s, entityId=%s, "
            + "providerWorkflowId=%s", eventHeaders.getWorkflow(), eventHeaders.getEntityChangeType(),
        eventHeaders.getEntityType(), entityId, eventHeaders.getProviderWorkflowId());
  }
  
}

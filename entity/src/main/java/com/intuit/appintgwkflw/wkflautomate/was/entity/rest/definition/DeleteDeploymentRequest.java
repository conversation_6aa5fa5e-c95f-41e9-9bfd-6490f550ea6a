package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import lombok.Value;

@Value
public class DeleteDeploymentRequest {

  // id of the definition or deployment
  private String id;

  /**
   * true, if all process instances, historic process instances and jobs for this process definition
   * should be deleted.
   */
  private boolean cascade;

  /** true, if only the built-in ExecutionListeners should be notified with the end event. */
  private boolean skipCustomListeners;
}

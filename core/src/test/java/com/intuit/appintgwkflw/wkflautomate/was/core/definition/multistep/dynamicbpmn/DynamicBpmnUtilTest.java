package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ELEMENT_TYPE;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BaseTemplateElementType.EXPLICIT;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BaseTemplateElementType.IMPLICIT;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 *
 * Test class for DynamicBpmnUtil
 */
@RunWith(MockitoJUnitRunner.class)
public class DynamicBpmnUtilTest {

  private Definition definition;
  private Definition multiConditionDefinition;

  private Definition dynamicBpmnDefinition;

  @Before
  @SneakyThrows
  public void setup() {
    definition = TestHelper.mockDefinitionEntity();
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    dynamicBpmnDefinition = TestHelper.mockMultiConditionDefinitionEntityWithMoreThan6Approvers();
  }

  @Test
  public void test_canGenerateBpmnDynamically_forApprovalWorkflows() {

    Assert.assertFalse(DynamicBpmnUtil.canGenerateBpmnDynamically(multiConditionDefinition));
    Assert.assertTrue(DynamicBpmnUtil.canGenerateBpmnDynamically(dynamicBpmnDefinition));
  }

  @Test
  public void test_canGenerateBpmnDynamically_forReminderWorkflows() {

    multiConditionDefinition.getWorkflowSteps().stream().filter(workflowStep -> Objects.nonNull(workflowStep.getActionGroup()))
            .forEach(workflowStep -> workflowStep.getActionGroup().setActionKey("reminder"));
    dynamicBpmnDefinition.getWorkflowSteps().stream().filter(workflowStep -> Objects.nonNull(workflowStep.getActionGroup()))
            .forEach(workflowStep -> workflowStep.getActionGroup().setActionKey("reminder"));

    Assert.assertTrue(DynamicBpmnUtil.canGenerateBpmnDynamically(multiConditionDefinition));
    Assert.assertTrue(DynamicBpmnUtil.canGenerateBpmnDynamically(dynamicBpmnDefinition));
  }

  @Test
  public void test_getWorkflowStepFromId() {
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();
    workflowStepMap.put("workflowStepId", new WorkflowStep());
    Assert.assertNotNull(DynamicBpmnUtil.getWorkflowStepFromId("workflowStepId", workflowStepMap));
    Assert.assertNull(DynamicBpmnUtil.getWorkflowStepFromId("workflowStepId1", workflowStepMap));
  }

  @Test
  public void test_getBaseTemplateResourcePath() {
    String actionKey = "approval";
    Assert.assertThrows(
        WorkflowGeneralException.class,
        () -> DynamicBpmnUtil.getBaseTemplateResourcePath(actionKey, null));
    Assert.assertEquals(
        "baseTemplates/bpmn/customApprovalBaseTemplate.bpmn",
        DynamicBpmnUtil.getBaseTemplateResourcePath(actionKey, ModelType.BPMN));
    Assert.assertEquals(
        "baseTemplates/dmn/customApprovalBaseTemplate.dmn",
        DynamicBpmnUtil.getBaseTemplateResourcePath(actionKey, ModelType.DMN));
  }

  @Test
  public void test_getMessageNameFromFlowNode() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .endEvent("endEvent")
            .messageEventDefinition()
            .message("dummyMsg")
            .done();
    FlowNode startEvent = bpmnModelInstance.getModelElementById("startEvent");
    Assert.assertNull(DynamicBpmnUtil.getMessageNameFromFlowNode(startEvent));
    FlowNode endEvent = bpmnModelInstance.getModelElementById("endEvent");
    Assert.assertEquals("dummyMsg", DynamicBpmnUtil.getMessageNameFromFlowNode(endEvent));
  }

  @Test
  public void test_getEscalationCodeFromFlowNode() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .escalation("123")
            .endEvent("endEvent")
            .done();
    FlowNode endEvent = bpmnModelInstance.getModelElementById("endEvent");
    Assert.assertNull(DynamicBpmnUtil.getEscalationCodeFromFlowNode(endEvent));
    FlowNode startEvent = bpmnModelInstance.getModelElementById("startEvent");
    Assert.assertEquals("123", DynamicBpmnUtil.getEscalationCodeFromFlowNode(startEvent));
  }

  @Test
  public void test_isNodeAnImplicitElement() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .endEvent("endEvent")
            .done();

    CamundaProperties camundaProperties = bpmnModelInstance.newInstance(CamundaProperties.class);
    CamundaProperty camundaProperty = bpmnModelInstance.newInstance(CamundaProperty.class);
    camundaProperty.setCamundaName(ELEMENT_TYPE);
    camundaProperty.setCamundaValue(String.valueOf(EXPLICIT));
    camundaProperties.addChildElement(camundaProperty);
    ExtensionElements extensionElements = bpmnModelInstance.newInstance(ExtensionElements.class);
    extensionElements.addChildElement(camundaProperties);

    FlowNode targetNode = bpmnModelInstance.getModelElementById("startEvent");

    Assert.assertFalse(DynamicBpmnUtil.isNodeAnImplicitElement(targetNode, bpmnModelInstance));

    targetNode.addChildElement(extensionElements);
    Assert.assertFalse(DynamicBpmnUtil.isNodeAnImplicitElement(targetNode, bpmnModelInstance));

    camundaProperty.setCamundaValue(String.valueOf(IMPLICIT));
    Assert.assertTrue(DynamicBpmnUtil.isNodeAnImplicitElement(targetNode, bpmnModelInstance));
  }

  @Test
  public void test_addConditionToSequenceFlows() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .businessRuleTask("decisionElement")
            .endEvent("endEvent")
            .done();

    FlowNode startEvent = bpmnModelInstance.getModelElementById("startEvent");
    SequenceFlow sequenceFlow = startEvent.getOutgoing().iterator().next();

    DynamicBpmnUtil.addConditionToSequenceFlows(sequenceFlow, startEvent, bpmnModelInstance);
    Assert.assertEquals(
        "${decisionResult=='startEvent'}", sequenceFlow.getConditionExpression().getTextContent());

    FlowNode businessRuleTask = bpmnModelInstance.getModelElementById("decisionElement");
    SequenceFlow decisionElementSequenceFlow = startEvent.getOutgoing().iterator().next();

    DynamicBpmnUtil.addConditionToSequenceFlows(
        decisionElementSequenceFlow, businessRuleTask, bpmnModelInstance);
    Assert.assertEquals(
        "${decisionResult=='decisionElement'}",
        decisionElementSequenceFlow.getConditionExpression().getTextContent());
  }

  @Test
  public void test_isSourceAndTargetNodesConnected() {
    BpmnModelInstance bpmnModelInstance =
        Bpmn.createExecutableProcess("testProcess")
            .startEvent("startEvent")
            .businessRuleTask("decisionElement")
            .endEvent("endEvent")
            .done();

    FlowNode startEvent = bpmnModelInstance.getModelElementById("startEvent");
    FlowNode businessRuleTask = bpmnModelInstance.getModelElementById("decisionElement");
    FlowNode endEvent = bpmnModelInstance.getModelElementById("endEvent");

    Assert.assertTrue(
        DynamicBpmnUtil.isSourceAndTargetNodesConnected(startEvent, businessRuleTask));
    Assert.assertTrue(DynamicBpmnUtil.isSourceAndTargetNodesConnected(businessRuleTask, endEvent));
    Assert.assertFalse(DynamicBpmnUtil.isSourceAndTargetNodesConnected(startEvent, endEvent));
  }

  @Test
  public void test_isExplicitElement() {
    Assert.assertFalse(DynamicBpmnUtil.isElementExplicit("implicit"));
    Assert.assertTrue(DynamicBpmnUtil.isElementExplicit("explicit"));
    Assert.assertFalse(DynamicBpmnUtil.isElementExplicit(null));
  }
}

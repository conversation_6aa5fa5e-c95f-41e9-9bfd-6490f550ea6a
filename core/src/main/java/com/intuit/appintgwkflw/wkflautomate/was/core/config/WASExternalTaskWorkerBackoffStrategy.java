package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CountSlidingWindow;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.camunda.bpm.client.backoff.ErrorAwareBackoffStrategy;
import org.camunda.bpm.client.exception.ExternalTaskClientException;
import org.camunda.bpm.client.exception.RestException;
import org.camunda.bpm.client.task.ExternalTask;

/**
 * <AUTHOR>
 * Computes the backoff in case of errors amd empty response from Camunda.
 */
public class WASExternalTaskWorkerBackoffStrategy implements ErrorAwareBackoffStrategy {

  private static final String LOG_PREFIX = "ExternalTaskPollerResiliency: ";

  private static final int MAX_ERROR_SCORE = 10;

  private final CountSlidingWindow countSlidingWindow;

  //List of http status for which error backoff will happen
  public List<Integer> backOffHttpStatus;

  // Initial backoff time when empty response is received
  public long initTime;
  // Exponential multiplier factor for empty response
  public float factor;
  // max backoff time allowed for empty response
  public long maxTime;
  // level to calculate backoff time
  public int emptyTaskDetailsLevel;
  // Initial backoff time when error response is received
  public long errorInitTime;
  // Exponential multiplier factor for error response
  public float errorFactor;
  // max backoff time allowed for error response
  public long errorMaxTime;
  // level to calculate backoff time in case of errors
  public int errorLevel;

  public WASExternalTaskWorkerBackoffStrategy(Worker worker, ImmutablePair<Client, Optional<Client>> pair) {
    this.backOffHttpStatus = pair.getRight().map(Client::getBackOffStatusCodes).orElse(pair.getLeft().getBackOffStatusCodes());
    this.initTime = pair.getRight().map(Client::getBackOffInitTime).orElse(pair.getLeft().getBackOffInitTime());
    this.factor = pair.getRight().map(Client::getBackOffFactor).orElse(pair.getLeft().getBackOffFactor());
    this.maxTime = pair.getRight().map(Client::getBackOffMaxTime).orElse(pair.getLeft().getBackOffMaxTime());
    this.errorInitTime = pair.getRight().map(Client::getErrorBackOffInitTime).orElse(pair.getLeft().getErrorBackOffInitTime());
    this.errorFactor = pair.getRight().map(Client::getErrorBackOffFactor).orElse(pair.getLeft().getErrorBackOffFactor());
    this.errorMaxTime = pair.getRight().map(Client::getErrorBackOffMaxTime).orElse(pair.getLeft().getErrorBackOffMaxTime());
    this.emptyTaskDetailsLevel = 0;
    this.errorLevel = 0;
    countSlidingWindow = CountSlidingWindow.of(pair.getLeft().getSlidingWindow(), MAX_ERROR_SCORE);
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className("WASExternalTaskWorkerBackoffStrategy")
                .message(
                    "BackoffStrategy selected=%s, topicName=%s, BackOffInitTime=%s, BackOffFactor=%s, BackOffMaxTime=%s",
                    "ERROR_EXPONENTIAL",
                    worker.getTopicName(),
                    this.initTime,
                    this.factor,
                    this.maxTime)
                .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK));
  }

  /**
   * Compute the error levels for empty response and exceptions
   * @param externalTasks list of external tasks
   * @param error exception
   */
  public void reconfigure(List<ExternalTask> externalTasks, ExternalTaskClientException error) {
    // externalTasks list is empty in case of exceptions as well.
    // so, check on error is needed first.
    if (error != null) {
      this.configureErrorLevel(error);
    } else if(externalTasks.isEmpty()){
      this.configureEmptyResponseLevel();
    } else {
      this.emptyTaskDetailsLevel = 0;
      this.errorLevel = 0;
      countSlidingWindow.recordSuccess();
    }
  }

  /**
   * Configure error level.
   * Increases the error level if response status code in in {@link #backOffHttpStatus} list
   * and error has {@link FetchAndLockException} in its cause.
   * 500 status is mostly received while DB fail overs in Camunda
   * 504 status can happen when database is under performing.
   * in case of non backoff http status code/non FetchAndLockException, emptyResponseLevel will be configured
   * @param error exception
   */
  private void configureErrorLevel(Exception error){
    if (ExceptionUtils.hasCause(error, ExternalTaskClientException.class)){
      int statusCode = this.getStatusCode(error);
      if (backOffHttpStatus.contains(statusCode)){
        countSlidingWindow.recordFailure();
        configureErrorLevel();
        this.emptyTaskDetailsLevel = 0;
      }
      else{
        configureEmptyResponseLevel();
      }
    }
    else{
      configureEmptyResponseLevel();
    }
  }

  /**
   * configure the empty response level
   */
  private void configureEmptyResponseLevel() {
    ++this.emptyTaskDetailsLevel;
    this.errorLevel = 0;
    countSlidingWindow.recordSuccess();
  }

  /**
   * Get http status code from exceptions
   * @param error exception
   * @return status code
   */
  private int getStatusCode(Exception error){
    int index = ExceptionUtils.indexOfThrowable(error, RestException.class);
    if (ExceptionUtils.hasCause(error, ExternalTaskClientException.class) && index >= 0 ){
      Throwable ex = error;
      for (int i = 0; i < index; i++){
        ex = ex.getCause();
      }
      RestException restException = (RestException) ex;
      return restException.getHttpStatusCode();
    }
    return -1;
  }


  private void configureErrorLevel(){
    int level = countSlidingWindow.failureScore();
    switch (level){
      case 0:
        errorLevel = 0;
        break;
      case 1:
        errorLevel = 1;
        break;
      case 2:
      case 3:
        errorLevel = 3;
        break;
      case 4:
        errorLevel = 5;
        break;
      default:
        errorLevel = 7;
        break;
    }
    WorkflowLogger.logInfo(LOG_PREFIX + "Configuring errorLevel threadId=%s level=%s",
        Thread.currentThread().getId(), errorLevel);
  }

  /**
   * calculates backoff time.
   * If both emptyTaskDetailsLevel and errorLevel are zero, backoffTime will be 0
   * @return backoff time in millis
   */
  public long calculateBackoffTime() {
    if (this.emptyTaskDetailsLevel == 0 && this.errorLevel == 0) {
      return 0L;
    }
    if(this.emptyTaskDetailsLevel > 0){
      return calculateEmptyResponseBackoffTime();
    }
    return calculateErrorResponseBackoffTime();
  }

  /**
   * calculates backoff time in case of empty response
   * backoff time is calculated by formula initTime * factor ^ (emptyTaskDetailsLevel -1)
   * @return minimum of the maxBackoff time allowed and backOffTime calculated by above formula
   */
  private long calculateEmptyResponseBackoffTime(){
    return calculateBackoffTime(this.initTime, this.factor, this.maxTime, this.emptyTaskDetailsLevel);
  }

  /**
   * calculates backoff time in case of exception
   * backoff time is calculated by formula initTime * errorFactor ^ (errorLevel -1)
   * @return minimum of the maxBackoff time allowed and backOffTime calculated by above formula
   */
  private long calculateErrorResponseBackoffTime(){
    long result = calculateBackoffTime(this.errorInitTime, this.errorFactor, this.errorMaxTime, this.errorLevel);
    WorkflowLogger.logInfo(LOG_PREFIX + "Error backOff time threadId=%s time=%s",
        Thread.currentThread().getId(), result);
    return result;
  }

  /**
   * @return minimum of the maxBackoff time allowed and backOffTime calculated by above formula
   */
  private long calculateBackoffTime(long initTime, float backOffFactor, long maxTime, int level){
    long backoffTime = (long)((double)initTime *
        Math.pow(backOffFactor, (double)level - 1));
    return Math.min(backoffTime, maxTime);
  }

}
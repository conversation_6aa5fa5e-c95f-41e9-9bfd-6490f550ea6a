package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.core.progressTracker.service.ProgressTrackingService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.progressTracker.response.WorkflowMilestoneStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Controller to fetch Milestone (Stage) info.
 */

@RestController
@RequestMapping("/v1/progressTrack")
@AllArgsConstructor
public class ProgressTrackingController {

	private ProgressTrackingService progressTrackingService;

	@Metric(name = MetricName.GET_PROGRESS_TRACKING, type = Type.API_METRIC)
	@GetMapping
	public WorkflowGenericResponse getProgressTrackingDetails(@RequestParam(required = true) final String recordId,
			@RequestParam(required = false) String status) {

		Set<WorkflowMilestoneStatus> statusList = statusList(status);
		return WorkflowGenericResponse.builder()
				.response(progressTrackingService.getProgressDetails(recordId, statusList))
					.status(ResponseStatus.SUCCESS).build();
	}

	private Set<WorkflowMilestoneStatus> statusList(String status) {
		Set<WorkflowMilestoneStatus> statusList = new HashSet<>();
		Optional.ofNullable(status).ifPresent((statusFilter) -> {
			List<String> statusStrList = Arrays.asList(status.split(","));
			statusList.addAll(statusStrList.stream().map(str -> WorkflowMilestoneStatus.from(str))
					.collect(Collectors.toSet()));
		});
		return statusList;
	}

}

package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConsumerRetryConfig {
    private String dlqGroupId;
    private String dlqAutoOffsetReset;
    private int retryMaxAttempts;
    private long retryBackoffPeriod;
    private boolean dlqEnabled;
    private int dlqConcurrency;
    private long dlqBackoffInitialInterval;
    private long dlqBackoffMaxInterval;
    private double dlqBackoffMultiplier;
    private int dlqRetryMaxAttempts;
    private int dlqMaxPollIntervalMs;
}

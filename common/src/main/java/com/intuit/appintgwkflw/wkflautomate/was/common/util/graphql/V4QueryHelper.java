package com.intuit.appintgwkflw.wkflautomate.was.common.util.graphql;

import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.Filter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.V4QueryRequest;
import com.intuit.v4.Query;
import com.intuit.v4.query.Expression;
import com.intuit.v4.query.FilterExpression;
import com.intuit.v4.query.visitable.CompoundExpression;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

/**
 * Creates Query for graphql APIs
 */
@Component
public class V4QueryHelper {

  public static final String FILTER_OPERATOR_EQUAL = "=";
  public static final String FILTER_OPERATOR_DOUBLE_EQUAL = "==";
  private static final String DOUBLE_AND = "&&";

  private static final String QUERY_TYPE = "/Query";

  /**
   * Takes a list of filters for a v4 query and creates:
   * <br> - {@link CompoundExpression} with "&&" operator for filters if there is more than one filter.
   * <br> - {@link FilterExpression} if there is only one filter.
   * Creates FilterExpressions with operations passed as part of Filter.
   *
   * @param filters list of filters
   * @return the v4 Expression
   */
  private Expression createExpression(List<Filter> filters) {
    List<Expression> filterExpressions = new ArrayList<>();
    FilterExpression filterExpression = null;
    if (CollectionUtils.isNotEmpty(filters)) {
      for (Filter filter : filters) {
        if (filter != null) {
          filterExpression = new com.intuit.v4.query.visitable.FilterExpression();
          filterExpression.setProperty(filter.getKey());
          filterExpression.setOp(filter.getOperator());

          List<Object> args = filter.getValue();
          filterExpression.setArgs(args);
          filterExpressions.add(filterExpression);
        }
      }
    }
    // Construct a compound expression only if there are more than 1 filter expressions (from "where" or "with" filters)
    if (filterExpressions.size() > 1) {
      CompoundExpression compoundExpression = new CompoundExpression();
      compoundExpression.setOp(DOUBLE_AND);
      compoundExpression.setArgs(filterExpressions);
      return compoundExpression;
    }
    return filterExpression;
  }

  /**
   * Builds the batch query that is used to make batch request using interaction protocol.
   *
   * @param v4QueryRequest - V4QueryRequestObject
   * @return Query query
   */
  public Query buildV4Query(V4QueryRequest v4QueryRequest) {
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType(v4QueryRequest.getType());

    Expression whereExpression = createExpression(v4QueryRequest.getWhereFilters());
    if (whereExpression != null) {
      preparedQuery.setWhere(whereExpression);
    }

    Expression withExpression = createExpression(v4QueryRequest.getWithFilters());
    if (withExpression != null) {
      preparedQuery.setWith(withExpression);
    }

    Query query = new Query().preparedQuery(preparedQuery);
    query.set$type(QUERY_TYPE);

    return query;
  }
}

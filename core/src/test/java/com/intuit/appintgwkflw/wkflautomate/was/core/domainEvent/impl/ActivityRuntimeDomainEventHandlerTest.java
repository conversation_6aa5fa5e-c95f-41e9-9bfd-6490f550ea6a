package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.TopicDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.ActivityRuntime;
import com.intuit.system.interfaces.BaseEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class ActivityRuntimeDomainEventHandlerTest {

  @InjectMocks private ActivityRuntimeDomainEventHandler activityRuntimeDomainEventHandler;
  @Mock private DomainEventConfig domainEventTopiConfig;
  @Mock private WASContextHandler contextHandler;
  @Mock private DomainEventRepository domainEventRepository;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
    ReflectionTestUtils.setField(
        activityRuntimeDomainEventHandler, "contextHandler", contextHandler);
    ReflectionTestUtils.setField(
        activityRuntimeDomainEventHandler, "domainEventTopiConfig", domainEventTopiConfig);
    ReflectionTestUtils.setField(
        activityRuntimeDomainEventHandler, "domainEventRepository", domainEventRepository);
  }

  @Test
  public void testGetDomainEventName() {
    Assert.assertEquals(
        DomainEventName.ACTIVITY_RUNTIME, activityRuntimeDomainEventHandler.getName());
  }

  @Test
  public void testTransform() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123l)
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("aId")
            .processDetails(processDetails)
            .name("testActivity")
            .status("created")
            .startTime(Timestamp.from(Instant.now()))
            .updatedTime(Timestamp.from(Instant.now()))
            .activityDefinitionDetail(prepareActivityDetails())
            .build();

    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(activityProgressDetails)
            .build();
    DomainEvent<BaseEntity> response =
        activityRuntimeDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    ActivityRuntime activityRuntime = ObjectConverter.fromJson(response.getPayload(),ActivityRuntime.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("aId", activityRuntime.getId());
    Assert.assertEquals("created", activityRuntime.getActivityStatus());
    Assert.assertEquals(
        processDetails.getProcessId(), activityRuntime.getProcessDetail().getProcessId());
    Assert.assertEquals("activityDetailId", activityRuntime.getActivityDetail().getActivityId());
  }

  @Test
  public void testTransformWithDomainEventsInCamelCase() {

    ProcessDetails processDetails =
            ProcessDetails.builder()
                    .processId("pId")
                    .recordId("recordId")
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(
                            DefinitionDetails.builder()
                                    .definitionId("dId")
                                    .definitionName("dName")
                                    .recordType(RecordType.INVOICE)
                                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                                    .build())
                    .ownerId(123l)
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build();
    ActivityProgressDetails activityProgressDetails =
            ActivityProgressDetails.builder()
                    .id("aId")
                    .processDetails(processDetails)
                    .name("testActivity")
                    .status("created")
                    .startTime(Timestamp.from(Instant.now()))
                    .updatedTime(Timestamp.from(Instant.now()))
                    .activityDefinitionDetail(prepareActivityDetails())
                    .build();

    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
            DomainEntityRequest.builder()
                    .eventHeaderEntity(EventHeaderEntity.builder().build())
                    .entityChangeAction(EntityChangeAction.CREATE)
                    .request(activityProgressDetails)
                    .build();
    DomainEvent<BaseEntity> response =
            activityRuntimeDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    ActivityRuntime activityRuntime = ObjectConverter.fromJson(response.getPayload(),ActivityRuntime.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("aId", activityRuntime.getId());
    Assert.assertEquals("created", activityRuntime.getActivityStatus());
    Assert.assertEquals(
            processDetails.getProcessId(), activityRuntime.getProcessDetail().getProcessId());
    Assert.assertEquals("activityDetailId", activityRuntime.getActivityDetail().getActivityId());

    Assert.assertEquals("123", response.getHeaders().getAccountId());
    Assert.assertEquals(EntityChangeAction.CREATE, response.getHeaders().getEntityChangeAction());
    Assert.assertEquals(EntityChangeAction.CREATE, response.getHeaders().getEntitychangeaction());
    Assert.assertEquals(activityRuntime.getId(), response.getHeaders().getEntityId());
    Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.ActivityRuntime", response.getHeaders().getEntityType());
    Assert.assertEquals(0, response.getHeaders().getEntityVersion().intValue());
    Assert.assertEquals(0, response.getHeaders().getEntityversion().intValue());
    Assert.assertEquals("tid", response.getHeaders().getIntuitTid());
    Assert.assertNull(response.getHeaders().getTrace());
    Assert.assertEquals("oId", response.getHeaders().getOfferingId());
    Assert.assertEquals(ActivityRuntime.SCHEMA_VERSION, response.getHeaders().getSchemaVersion());
    Assert.assertEquals(ActivityRuntime.URN, response.getHeaders().getIntuitEntityType());

  }

  @Test
  public void testTransformWithOutDomainEventsInCamelCase() {

    ProcessDetails processDetails =
            ProcessDetails.builder()
                    .processId("pId")
                    .recordId("recordId")
                    .processStatus(ProcessStatus.ACTIVE)
                    .definitionDetails(
                            DefinitionDetails.builder()
                                    .definitionId("dId")
                                    .definitionName("dName")
                                    .recordType(RecordType.INVOICE)
                                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                                    .build())
                    .ownerId(123l)
                    .modifiedDate(Timestamp.from(Instant.now()))
                    .build();
    ActivityProgressDetails activityProgressDetails =
            ActivityProgressDetails.builder()
                    .id("aId")
                    .processDetails(processDetails)
                    .name("testActivity")
                    .status("created")
                    .startTime(Timestamp.from(Instant.now()))
                    .updatedTime(Timestamp.from(Instant.now()))
                    .activityDefinitionDetail(prepareActivityDetails())
                    .build();

    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    DomainEntityRequest domainEntityRequest =
            DomainEntityRequest.builder()
                    .eventHeaderEntity(EventHeaderEntity.builder().build())
                    .entityChangeAction(EntityChangeAction.CREATE)
                    .request(activityProgressDetails)
                    .build();
    DomainEvent<BaseEntity> response =
            activityRuntimeDomainEventHandler.transform(domainEntityRequest);
    Assert.assertNotNull(response);
    boolean isTypeCorrect = response.getPayload() instanceof String;
    ActivityRuntime activityRuntime = ObjectConverter.fromJson(response.getPayload(),ActivityRuntime.class);
    Assert.assertTrue(isTypeCorrect);
    Assert.assertEquals("aId", activityRuntime.getId());
    Assert.assertEquals("created", activityRuntime.getActivityStatus());
    Assert.assertEquals(
            processDetails.getProcessId(), activityRuntime.getProcessDetail().getProcessId());
    Assert.assertEquals("activityDetailId", activityRuntime.getActivityDetail().getActivityId());

    Assert.assertEquals("123", response.getHeaders().getAccountId());
    Assert.assertNotNull(response.getHeaders().getEntityChangeAction());
    Assert.assertEquals(EntityChangeAction.CREATE, response.getHeaders().getEntitychangeaction());
    Assert.assertEquals(activityRuntime.getId(), response.getHeaders().getEntityId());
    Assert.assertEquals("com.intuit.foundation.workflow.workflowautomation.ActivityRuntime", response.getHeaders().getEntityType());
    Assert.assertNotNull(response.getHeaders().getEntityVersion());
    Assert.assertEquals(0, response.getHeaders().getEntityversion().intValue());
    Assert.assertEquals("tid", response.getHeaders().getIntuitTid());
    Assert.assertNull(response.getHeaders().getTrace());
    Assert.assertEquals("oId", response.getHeaders().getOfferingId());
    Assert.assertNotNull(response.getHeaders().getSchemaVersion());
    Assert.assertNotNull(response.getHeaders().getIntuitEntityType());

  }

  private ActivityDetail prepareActivityDetails() {
    return ActivityDetail.builder()
        .activityType("SystemTask")
        .activityId("activityDetailId")
        .activityName("testActivity")
        .templateDetails(
            TemplateDetails.builder().offeringId("oId").templateName("tName").id("tId").build())
        .build();
  }

  @Test
  public void testPublish() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123l)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("aId")
            .processDetails(processDetails)
            .name("testActivity")
            .status("created")
            .startTime(Timestamp.from(Instant.now()))
            .updatedTime(Timestamp.from(Instant.now()))
            .activityDefinitionDetail(prepareActivityDetails())
            .build();
    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    ActivityRuntime activityRuntime = prepareActivityRuntimePayload(activityProgressDetails);
    String obj = ObjectConverter.toJson(activityRuntime);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(activityProgressDetails)
            .build();
    DomainEvent<BaseEntity> response =
        activityRuntimeDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNotNull(response);
  }


  @Test
  public void testPublishWithOwnerIdIsNull() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123l)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("aId")
            .processDetails(processDetails)
            .name("testActivity")
            .status("created")
            .startTime(Timestamp.from(Instant.now()))
            .updatedTime(Timestamp.from(Instant.now()))
            .activityDefinitionDetail(prepareActivityDetails())
            .build();
    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn("tid");
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    ActivityRuntime activityRuntime = prepareActivityRuntimePayload(activityProgressDetails);
    String obj = ObjectConverter.toJson(activityRuntime);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(activityProgressDetails)
            .build();
    DomainEvent<BaseEntity> response =
        activityRuntimeDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNotNull(response);
  }

  @Test
  public void testPublishDisabledAtTopic() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123l)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("aId")
            .processDetails(processDetails)
            .name("testActivity")
            .status("created")
            .startTime(Timestamp.from(Instant.now()))
            .updatedTime(Timestamp.from(Instant.now()))
            .activityDefinitionDetail(prepareActivityDetails())
            .build();
    Map<String, String> topic = new HashMap<>();
    topic.put("topic", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(false);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(activityProgressDetails)
            .build();
    DomainEvent<BaseEntity> response =
        activityRuntimeDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNull(response);
  }

  private ActivityRuntime prepareActivityRuntimePayload(
      ActivityProgressDetails activityProgressDetails) {
    ActivityRuntime activityRuntime = new ActivityRuntime();
    activityRuntime.setId(activityProgressDetails.getId());
    ActivityDetail activityDetails = prepareActivityDetails();
    com.intuit.foundation.workflow.workflowautomation.types.ActivityDetail activityDetail =
        new com.intuit.foundation.workflow.workflowautomation.types.ActivityDetail();
    activityDetail.setActivityId(activityDetails.getActivityId());
    activityDetail.setActivityType(activityDetails.getActivityType());
    activityDetail.setName(activityDetails.getActivityName());
    // TODO: What goes here?
    activityDetail.setUserDefinedActivityId("dummy");
    activityRuntime.setActivityDetail(activityDetail);
    activityRuntime.setActivityStatus(activityProgressDetails.getStatus());
    return activityRuntime;
  }

  @Test
  public void testTidIsNull() {
    ProcessDetails processDetails =
        ProcessDetails.builder()
            .processId("pId")
            .recordId("recordId")
            .processStatus(ProcessStatus.ACTIVE)
            .definitionDetails(
                DefinitionDetails.builder()
                    .definitionId("dId")
                    .definitionName("dName")
                    .recordType(RecordType.INVOICE)
                    .templateDetails(TemplateDetails.builder().offeringId("oId").build())
                    .build())
            .ownerId(123l)
            .createdDate(Timestamp.from(Instant.now()))
            .modifiedDate(Timestamp.from(Instant.now()))
            .build();
    ActivityProgressDetails activityProgressDetails =
        ActivityProgressDetails.builder()
            .id("aId")
            .processDetails(processDetails)
            .name("testActivity")
            .status("created")
            .startTime(Timestamp.from(Instant.now()))
            .updatedTime(Timestamp.from(Instant.now()))
            .activityDefinitionDetail(prepareActivityDetails())
            .build();
    Map<String, String> topic = new HashMap<>();
    topic.put("process", "qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    Map<DomainEventName, TopicDetails> map = new HashMap<>();
    TopicDetails topicDetails = new TopicDetails();
    topicDetails.setTopic("qal.foundation.workflow.workflowautomation.activityRuntime.v1");
    topicDetails.setEnabled(true);
    map.put(DomainEventName.ACTIVITY_RUNTIME, topicDetails);
    Mockito.when(domainEventTopiConfig.isEnabled()).thenReturn(true);
    Mockito.when(domainEventTopiConfig.getTopic()).thenReturn(map);
    Mockito.when(domainEventTopiConfig.getRegion()).thenReturn("USW2");
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_TID)).thenReturn(null);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(null);
    ActivityRuntime activityRuntime = prepareActivityRuntimePayload(activityProgressDetails);
    String obj = ObjectConverter.toJson(activityRuntime);
    DomainEvent<BaseEntity> val = DomainEvent.builder().payload(obj).build();
    Mockito.when(domainEventRepository.save(Mockito.any())).thenReturn(val);
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(EventHeaderEntity.builder().build())
            .entityChangeAction(EntityChangeAction.CREATE)
            .request(activityProgressDetails)
            .build();
    DomainEvent<BaseEntity> response =
        activityRuntimeDomainEventHandler.publish(domainEntityRequest);
    Assert.assertNotNull(response);
  }
}

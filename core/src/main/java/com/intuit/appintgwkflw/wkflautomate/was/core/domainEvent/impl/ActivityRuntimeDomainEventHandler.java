package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.DomainEventPublisherCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.DomainEventMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEventHeaders;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.DomainEventName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.foundation.workflow.workflowautomation.ActivityRuntime;
import com.intuit.system.interfaces.BaseEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/**
 * Handler for handling Activity Runtime Domain Events
 *
 * <AUTHOR>
 */
@Component(WorkflowBeansConstants.ACTIVITY_RUNTIME_HANDLER)
@AllArgsConstructor
public class ActivityRuntimeDomainEventHandler
    extends DomainEventPublisherCapability<ActivityProgressDetails, BaseEntity> {
  private final WASContextHandler contextHandler;
  private final DomainEventRepository domainEventRepository;

  @Override
  public DomainEventName getName() {
    return DomainEventName.ACTIVITY_RUNTIME;
  }

  @Override
  public DomainEvent<? extends BaseEntity> transform(
      final DomainEntityRequest<ActivityProgressDetails> domainEntityRequest) {

    if (Objects.isNull(domainEntityRequest.getEventHeaderEntity())) {
      EventHeaderEntity eventHeaderEntity =
          EventHeaderEntity.builder()
              .tid(contextHandler.get(WASContextEnums.INTUIT_TID))
              .offeringId(contextHandler.get(WASContextEnums.OFFERING_ID))
              .build();
      domainEntityRequest.setEventHeaderEntity(eventHeaderEntity);
    }

    final DomainEventHeaders domainEventHeaders =
        prepareActivityRuntimeDomainEventHeaders(domainEntityRequest);

    ActivityRuntime activityRuntime =
        DomainEventMapper.mapEntityToActivityRuntimeDomainEvent(
            domainEntityRequest.getRequest(), domainEntityRequest.getDomainEventErrorDetails());

    DomainEvent<BaseEntity> domainEvent =
        DomainEvent.builder()
            .topic(getTopicDetails(DomainEventName.ACTIVITY_RUNTIME))
            .partitionKey(activityRuntime.getId())
            .headers(domainEventHeaders)
            .region(getRegionDetails())
            .payload(ObjectConverter.toJson(activityRuntime))
            .version(domainEventHeaders.getEntityversion())
            .build();

    return domainEvent;
  }

  private DomainEventHeaders prepareActivityRuntimeDomainEventHeaders(
      DomainEntityRequest<ActivityProgressDetails> domainEntityRequest) {
    ActivityProgressDetails activityProgressDetails = domainEntityRequest.getRequest();
    String accountId =
        ObjectUtils.isNotEmpty(contextHandler.get(WASContextEnums.OWNER_ID))
            ? contextHandler.get(WASContextEnums.OWNER_ID)
            : String.valueOf(activityProgressDetails.getProcessDetails().getOwnerId());
    DomainEventHeaders.DomainEventHeadersBuilder headersBuilder = DomainEventHeaders.builder()
        .intuitTid(Optional.ofNullable(contextHandler.get(WASContextEnums.INTUIT_TID))
                .orElse(UUID.randomUUID().toString()))
        .entityId(activityProgressDetails.getId()) // Unique Identifier
        .entityType(ActivityRuntime.class.getName())
        .accountId(accountId)
        .offeringId(
            StringUtils.isNoneBlank(domainEntityRequest.getEventHeaderEntity().getOfferingId())
                ? domainEntityRequest.getEventHeaderEntity().getOfferingId()
                : activityProgressDetails
                    .getActivityDefinitionDetail()
                    .getTemplateDetails()
                    .getOfferingId())
        .entitychangeaction(domainEntityRequest.getEntityChangeAction())
        .entityversion(activityProgressDetails.getVersion());

    addDomainEventHeaders(headersBuilder, ActivityRuntime.SCHEMA_VERSION, ActivityRuntime.URN, domainEntityRequest.getEntityChangeAction(), activityProgressDetails.getVersion());

    return headersBuilder.build();
  }
}

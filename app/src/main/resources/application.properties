# To leverage JSK security, provide the following properites
security.intuit.accessServiceEndpoint=https://access-e2e.platform.intuit.com/v2/tickets
# security.intuit.appSecret should not be part of your git project, but supplied at deploy time in Tomcat's conf folder's application-overrides.properties for example
security.intuit.ignored=/ping, /health/**, /swagger.json, /swagger.yaml, /metrics, /actuator/prometheus, /rest/**, /app/**, /api/**, /*/**
# To leverage JSK security, provide the following properites
#security.ignored=*/**
# Define a custom port instead of the default 8080
server.port=8443
# Tell Spring Security (if used) to require requests over HTTPS
#security.require-ssl=true
# The format used for the keystore
server.ssl.key-store-type=jks
# The path to the keystore containing the certificate
server.ssl.key-store=classpath:keystore.jks
# The password used to generate the certificate
server.ssl.key-store-password=password
# The alias mapped to the certificate
server.ssl.key-alias=tomcat
# Enable 8490 for actuator
# default port for actuator
management.server.port=8490
# default is to enable https for actuator
management.server.ssl.enabled=true
# key store type for actuator
management.server.ssl.key-store-type=jks
# set the certificate
management.server.ssl.key-store=classpath:management.jks
# The password used to generate the certificate
management.server.ssl.key-store-password=password
# The alias mapped to the certificate
management.server.ssl.key-alias=tomcat

management.endpoint.metrics.enabled=true
management.endpoint.health.show-details=always
management.endpoints.web.exposure.include=*

spring.liquibase.drop-first=false
jsk.lastmile.enabled=false
# graceful shutdown configuration
jsk.embedded.container.graceful.shutdown.wait-time=95s
# to avoid create lob error on console from postgres
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

spring.servlet.multipart.max-file-size=5MB

#Disabling the default orchestration by InteractionExecutionProvider
v4.servlet.context.graphql-converting-factory=false

#For increasing the acceptable HTTP request header size ( if the header size is above this, server will throw 400 BAD request)
server.max-http-header-size=64KB

#Added for spring doc open api 3
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.url=/swagger.yaml

#enable kafka consumer metrics
management.metrics.enable.kafka=true

# Disable statsd metrics export, which is flooding logs currently
management.metrics.export.statsd.enabled=false

#To disable GraphQL introspection, overriding globalContextInitializer bean is needed.
spring.main.allow-bean-definition-overriding=true
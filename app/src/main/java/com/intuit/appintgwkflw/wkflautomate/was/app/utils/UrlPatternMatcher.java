package com.intuit.appintgwkflw.wkflautomate.was.app.utils;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import java.util.Collections;
import java.util.Optional;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

/**
 * <AUTHOR>
 * matches the url based on patterns provided in the config.
 */
@Component
public class UrlPatternMatcher {

  private OfferingConfig offeringConfig;

  private AntPathMatcher antPathMatcher;

  public UrlPatternMatcher(OfferingConfig offeringConfig){
    this.offeringConfig = offeringConfig;
    this.antPathMatcher = new AntPathMatcher();
  }

  /**
   * checks if the url matches any pattern
   * @param url string to match pattern
   * @return
   */
  public boolean isMatching(String url){
    return Optional.ofNullable(offeringConfig.getAllowedSystemUserUrls())
        .orElse(Collections.emptyList())
        .stream()
        .anyMatch(urlPattern -> antPathMatcher.match(urlPattern, url));
  }

}

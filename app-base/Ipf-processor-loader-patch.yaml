apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: rollout
spec:
  template:
    spec:
      initContainers:
        - name: ipf-processor-loader
          image: docker.intuit.com/fdp/enrichment/ipf-processor-loader:latest
          env:
            - name: PROCESSOR_OUTPUT_PATH
              value: "/app/processors/"
            - name: PROCESSOR_SOURCE
              value: "-config"
            - name: REGISTRY_API_KEY
              value: "{secret}idps:/ipf/regSvcApiKey"
            - name: REGISTRY_ENDPOINT
              value: "https://financialdataschema-aws-e2e.api.intuit.com/"
            - name: RETRY_BACKOFF_MS
              value: "500"
            - name: SPRING_CONFIG_FILE_PATH
              value: "/app/config/bootstrap.yml"
            - name: "ARTIFACTORY_ENDPOINT"
              value: "https://artifact.intuit.com/artifactory/maven-public/"
            - name: "CONFIG_LOADED_PATH"
              value: "/app/config/"
            - name: "MAX_RETRY_COUNT"
              value: "3"
            - name: IDPS_ENDPOINT
              value: "vkm-e2e.ps.idps.a.intuit.com"
            - name: RUNTIME_ID
              value: "was-event-in-runtime"
            - name: ENTITY_LOADER_ENABLED
              value: "true"
            - name: IDPS_FORCE_GENERIC_POLICIES
              value: "true"
          volumeMounts:
            - mountPath: /app/processors
              name: processors
            - mountPath: /app/config
              name: config
      volumes:
        - name: processors
          emptyDir: {}

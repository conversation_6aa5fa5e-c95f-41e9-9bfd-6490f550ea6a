package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;


import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.DynamicBpmnFlowNodeProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.EndEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.flownodes.StartEventFlowNodeProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.ProcessType;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * <AUTHOR>
 * Test class for DynamicBpmnSubprocessEventHelper class.
 */
@RunWith(MockitoJUnitRunner.class)

public class DynamicBpmnSubprocessEventHelperTest {

  @InjectMocks private DynamicBpmnSubProcessEventHelper dynamicBpmnSubProcessEventHelper;

  @Mock
  private StartEventFlowNodeProcessor startEventFlowNodeProcessor;

  @Mock
  private EndEventFlowNodeProcessor endEventFlowNodeProcessor;

  @Mock
  private DynamicBpmnFlowNodeProcessorFactory dynamicBpmnFlowNodeProcessorFactory;

  private BpmnModelInstance bpmnModelInstance;

  private BpmnModelInstance baseTemplateBpmnModelInstance;

  @Before
  public void setup() {

  }

  @Test
  public void testEmptyBaseTemplateProcess(){

    baseTemplateBpmnModelInstance = Bpmn.createEmptyModel();
    bpmnModelInstance  = Bpmn.createExecutableProcess().startEvent("startEvent").done();
    dynamicBpmnSubProcessEventHelper.updateProcessElementAndAddSubprocess(bpmnModelInstance, baseTemplateBpmnModelInstance);

    Optional<SubProcess> subProcess = bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst();
    Assert.assertEquals(Optional.empty(), subProcess);
  }

  @Test
  public void testUpdateProcessElementAndAddSubprocess(){

    baseTemplateBpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");

    bpmnModelInstance  = Bpmn.createExecutableProcess().startEvent("startEvent").done();

    dynamicBpmnSubProcessEventHelper.updateProcessElementAndAddSubprocess(bpmnModelInstance, baseTemplateBpmnModelInstance);

    Optional<SubProcess> subProcess = bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst();
    Optional<Process> process = bpmnModelInstance.getModelElementsByType(Process.class).stream().findFirst();

    Assert.assertEquals("customApproval", process.get().getId());
    Assert.assertTrue(process.get().isExecutable());
    Assert.assertEquals("7", process.get().getCamundaHistoryTimeToLiveString());
    Assert.assertEquals(ProcessType.None, process.get().getProcessType());
    Assert.assertFalse(process.get().isClosed());
    Assert.assertEquals("Activity_0rx7x5h", subProcess.get().getId());
  }

  @Test
  public void testUpdateSubprocessWithChildEvents(){
    baseTemplateBpmnModelInstance =
        BpmnProcessorUtil.readBPMNFile("baseTemplates/bpmn/customApprovalBaseTemplate.bpmn");

    bpmnModelInstance  = Bpmn.createExecutableProcess()
        .startEvent("startEvent")
        .done();

    dynamicBpmnSubProcessEventHelper.updateProcessElementAndAddSubprocess(bpmnModelInstance, baseTemplateBpmnModelInstance);

    when(dynamicBpmnFlowNodeProcessorFactory.getProcessorFromFlowNode(any())).thenReturn(endEventFlowNodeProcessor);

    dynamicBpmnSubProcessEventHelper.updateSubprocessWithChildEvents(bpmnModelInstance, baseTemplateBpmnModelInstance);

    Optional<SubProcess> subProcess = bpmnModelInstance.getModelElementsByType(SubProcess.class).stream().findFirst();


    Assert.assertEquals("baseTemplateSubProcess", subProcess.get().getName());
    Mockito.verify(startEventFlowNodeProcessor, times(1))
        .addEventToSubProcess(any(), any(), any(), any());

    // one for invocation will be for service task, one for end event element present in the precanned subprocess.
    Mockito.verify(endEventFlowNodeProcessor, times(2))
        .addEventToSubProcess(any(), any(), any(), any());
    Mockito.verify(endEventFlowNodeProcessor, times(2))
        .addExtensionElements(any(), any(), any(), any());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask;

import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * <AUTHOR> stripathy1
 * ExternalTaskAssigned will be part of all publishers who are publishing to Event bus. It extends WorkflowMetaDataEntity
 * Currently this POJO is written manually, later after IEDM onboarding the schema will be moved to IEDM. These POJO will be automatially gennerated and can be
 * deleted from this folder
 */

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExternalTaskAssigned {
    private WorkflowMetaData workflowMetadata;
    private String taskName;
    private String businessEntityType;
    private String businessEntityId;
    @RedactSensitiveField
    private Map<String, Object> variables;
    @RedactSensitiveField
    private Map<String, String> extensions;
    //Downstream Id
    private String txnId;
}

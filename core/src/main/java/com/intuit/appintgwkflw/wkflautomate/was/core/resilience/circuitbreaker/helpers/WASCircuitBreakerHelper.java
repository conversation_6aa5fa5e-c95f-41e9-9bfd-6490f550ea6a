package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.helpers;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerRegistryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.micrometer.tagged.TaggedCircuitBreakerMetrics;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Set;

@Component
@AllArgsConstructor
public class WASCircuitBreakerHelper {
    private final MeterRegistry meterRegistry;

    /**
     * Creates circuit breaker with the passed registry configs and registers its metrics
     */
    public CircuitBreaker createCircuitBreaker(String cbName, WASCircuitBreakerRegistryConfig circuitBreakerRegistryConfig) {
        CircuitBreakerRegistry circuitBreakerRegistry = getCustomCircuitBreakerRegistry(circuitBreakerRegistryConfig);
        CircuitBreaker cb = circuitBreakerRegistry.circuitBreaker(cbName);
        registerCircuitBreakerMetrics(circuitBreakerRegistry);
        return cb;
    }

    /**
     * Registers all metrics associated with the circuit breaker which was created using the passed circuit breaker registry
     *
     * @param circuitBreakerRegistry
     */
    private void registerCircuitBreakerMetrics(CircuitBreakerRegistry circuitBreakerRegistry) {
        TaggedCircuitBreakerMetrics
                .ofCircuitBreakerRegistry(circuitBreakerRegistry)
                .bindTo(meterRegistry);
    }

    /**
     * Adds handler to the passed circuit breaker, for events generates by the circuit breaker
     * Transition from a specific state to the same state, doesn't result in state transition event
     *
     * @param circuitBreakerActionHandler
     */
    public void addCircuitBreakerEventHandler(CircuitBreaker circuitBreaker, CircuitBreakerActionHandler circuitBreakerActionHandler) {
        circuitBreaker
            .getEventPublisher()
            .onStateTransition(e -> {
                WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX +
                        "State transition occurred in circuit=%s transition=%s", e.getCircuitBreakerName(), e.getStateTransition());
                circuitBreakerActionHandler.handleCircuitBreakerStateTransitionEvent(e.getStateTransition());
            });
    }

    public void closeCircuitBreakers(Set<CircuitBreaker> circuitBreakerSet) {
        circuitBreakerSet
            .forEach(CircuitBreaker::transitionToClosedState);
    }

    public void disableCircuitBreakers(Set<CircuitBreaker> circuitBreakerSet) {
        circuitBreakerSet
                .forEach(CircuitBreaker::transitionToDisabledState);
    }

    /**
     * Checks if all circuit breakers in the passed set are closed
     *
     * @param circuitBreakerSet
     * @return
     */
    public boolean areAllCircuitBreakersClosed(Set<CircuitBreaker> circuitBreakerSet) {
        return circuitBreakerSet
                .stream()
                .allMatch(cb -> CircuitBreaker.State.CLOSED.equals(cb.getState()));
    }

    /**
     * Create custom circuit breaker registry using passed configurations
     *
     * @param circuitBreakerRegistryConfig
     * @return
     */
    private CircuitBreakerRegistry getCustomCircuitBreakerRegistry(WASCircuitBreakerRegistryConfig circuitBreakerRegistryConfig) {
        CircuitBreakerConfig circuitBreakerConfig = CircuitBreakerConfig.custom()
                .slidingWindowType(circuitBreakerRegistryConfig.getSlidingWindowType())
                .slidingWindowSize(circuitBreakerRegistryConfig.getSlidingWindowSize())
                .failureRateThreshold(circuitBreakerRegistryConfig.getFailureRateThreshold())
                .minimumNumberOfCalls(circuitBreakerRegistryConfig.getMinimumNumberOfCalls())
                .permittedNumberOfCallsInHalfOpenState(circuitBreakerRegistryConfig.getPermittedNumberOfCallsInHalfOpen())
                .waitDurationInOpenState(Duration.ofMillis(circuitBreakerRegistryConfig.getWaitDurationInOpen()))
                .automaticTransitionFromOpenToHalfOpenEnabled(circuitBreakerRegistryConfig.isAutoTransitionFromOpenToHalfOpen())
                .recordExceptions(circuitBreakerRegistryConfig.getRecordExceptions().toArray(new Class[0]))
                .build();

        return CircuitBreakerRegistry.of(circuitBreakerConfig);
    }
}

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

images:
- name: docker.intuit.com/appintgwkflw-wkflautomate/was-event-in/service/was-event-in
  newTag: "1.5.1"
- name: docker.intuit.com/dev/containers/segment-app-init/service/segment-app-init
  newTag: "1.3.1"
- name: docker.intuit.com/fdp/enrichment/config-loader
  newTag: "0.2.9"
- name: docker.intuit.com/fdp/enrichment/ipf-processor-loader
  newTag: "0.3.9"
- name: docker.intuit.com/fdp/enrichment/ipf-runtime-image
  newTag: "0.11.41"

#- manifest.yaml
resources:
- Iam-Role.yaml
- filtering-runtime-0
- enrichment-runtime-0
- salesreceipt
- vendorcredit

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.TimeDuration;
import com.intuit.v4.common.WeekOfMonthEnum;
import java.util.List;
import lombok.experimental.UtilityClass;
import org.joda.time.DateTime;

@UtilityClass
public class RecurrenceTestUtil {

  RecurrenceRule createRecurrenceRule(
      int interval,
      RecurTypeEnum recurType,
      List<Integer> daysOfMonth,
      List<DayOfWeekEnum> daysOfWeek,
      List<MonthsOfYearEnum> monthsOfYear,
      WeekOfMonthEnum weekOfMonth,
      DateTime startDate) {

    return new RecurrenceRule()
        .interval(interval)
        .recurType(recurType)
        .daysOfMonth(daysOfMonth)
        .daysOfWeek(daysOfWeek)
        .monthsOfYear(monthsOfYear)
        .weekOfMonth(weekOfMonth)
        .startDate(startDate)
        .recurrenceTime(new TimeDuration().hours(0).minutes(0));
  }
}

# RED (Request Error and Duration) metrics are collected at source and is enabled by default
# if you would rather like to do offline processing for
# red metrics, set this value to false.
intuit.metrics.red.metrics.enabled=true

# To capture HTTP Headers as span attribute
otel.instrumentation.http.capture-headers.server.request=intuit_tid,intuit_offeringid,intuit_realmid,intuit_userid

# To add alias for an attribute
intuit.attribute.aliases=http.request.header.intuit_tid=tid,http.request.header.intuit_offeringid=offeringid

# Set Trace propagators
otel.propagators=tracecontext,baggage,b3multi

# Exclude health check
server.span.exclude.list=http.url=(.*\/health.*),http.target=(.*\/health.*)
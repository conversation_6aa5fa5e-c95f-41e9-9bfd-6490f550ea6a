package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;

@Getter
public enum BPMNTriggerTypeDetails {
  STARTEVENT("org.camunda.bpm.model.bpmn.instance.StartEvent", BPMNTriggerType.EVENT),
  INTERMEDIATECATCHEVENT("org.camunda.bpm.model.bpmn.instance.IntermediateCatchEvent", BPMNTriggerType.EVENT),
  RECEIVETASK("org.camunda.bpm.model.bpmn.instance.ReceiveTask", BPMNTriggerType.TASK),
  BOUNDARYEVENT("org.camunda.bpm.model.bpmn.instance.BoundaryEvent", BPMNTriggerType.BOUNDARY_EVENT);

  private String bpmnTriggername;
  private BPMNTriggerType bpmnTriggerType;

  BPMNTriggerTypeDetails(String bpmnTriggername, BPMNTriggerType bpmnTriggerType) {
    this.bpmnTriggername = bpmnTriggername;
    this.bpmnTriggerType = bpmnTriggerType;
  }

  @Override
  public String toString() {
    return (bpmnTriggerType + " : " + bpmnTriggername);
  }

}

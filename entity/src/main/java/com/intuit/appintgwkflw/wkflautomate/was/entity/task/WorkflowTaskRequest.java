package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@Builder(access = AccessLevel.PUBLIC)
public class WorkflowTaskRequest {

  //External Task Id.
  @EqualsAndHashCode.Include
  private String id;

  @EqualsAndHashCode.Include
  private String txnId;

  @EqualsAndHashCode.Include
  private String processInstanceId;

  @EqualsAndHashCode.Include
  private TaskCommand command;

  private String status;

  //Skips downstream call.
  @EqualsAndHashCode.Include
  private boolean skipCallback;

  @EqualsAndHashCode.Include
  private String activityId;

  @EqualsAndHashCode.Include
  private String activityName;

  @EqualsAndHashCode.Include
  private String activityType;

  //For Create/Complete Command for publishing ExternalTaskAssigned events.
  @EqualsAndHashCode.Include
  private boolean publishExternalTaskEvent;

  //For Create/Update Command for publishing WorkflowState Transition events.
  @EqualsAndHashCode.Include
  private boolean publishWorkflowStateTransitionEvent;

  //Keeps execution variables.
  private TaskAttributes taskAttributes;

  //ActivityType should be defined as first class attribute.
  @EqualsAndHashCode.Include
  private TaskType taskType;

  //If set true, Transaction table will not be updated.
  @EqualsAndHashCode.Include
  @Default
  private boolean skipTxnDBUpdate = true;

  //ExternalTask WorkerId of Camunda
  @EqualsAndHashCode.Include
  private String workerId;

  //Workflow record/entityId.
  @EqualsAndHashCode.Include
  private String recordId;

  //Enable Downstream call on retry.
  @EqualsAndHashCode.Include
  private boolean invokeDownstreamOnRetry;

}
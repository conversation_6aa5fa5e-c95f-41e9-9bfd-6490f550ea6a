package com.intuit.appintgwkflw.wkflautomate.was.core.task.common;

import static org.mockito.ArgumentMatchers.any;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.incident.WorkflowIncident;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskCommand;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.impl.VariableMapImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class IncidentTaskManagerTest {

  @Mock
  private ActivityDetailsRepository activityDetailRepo;
  @Mock
  private WorkflowExternalTaskManager workflowTaskManager;
  @InjectMocks
  private IncidentTaskManager incidentTaskManager;
  @Mock
  private WorkflowTaskConfig workflowTaskConfig;
  @Mock
  private WorkflowCustomTaskHelper customTaskHelper;

  @Test
  public void testExecute_ConfigDisable() {
    WorkflowIncident event = getIncidentPayload();
    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .templateDetails(templateDtls).recordType(RecordType.DEPOSIT).build();
    ProcessDetails processDtl = ProcessDetails.builder().ownerId(1l).recordId("record1")
        .definitionDetails(definitionDetails).build();
    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);
    incidentTaskManager.execute(event, new HashMap<>(), processDtl);

    Mockito.verify(activityDetailRepo, Mockito.never())
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    Mockito.verify(workflowTaskManager, Mockito.never())
        .execute(any(WorkflowTaskRequest.class));

  }

  @Test
  public void testExecute_ActivityDetailNotFound() throws Exception {
    WorkflowIncident event = getIncidentPayload();
    event.setIncidentMsg(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.name());

    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .templateDetails(templateDtls).recordType(RecordType.DEPOSIT).build();
    ProcessDetails processDtl = ProcessDetails.builder().ownerId(1l).recordId("record1")
        .definitionDetails(definitionDetails).build();

    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.empty());

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.execute(event, new HashMap<>(), processDtl);

    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    Mockito.verify(workflowTaskManager, Mockito.never())
        .execute(any(WorkflowTaskRequest.class));
  }

  @Test
  public void testExecute_ProcessCustomTask_skipTxnDB_false() throws Exception {
    WorkflowIncident event = getIncidentPayload();
    event.setIncidentMsg(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.name());

    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .recordType(RecordType.DEPOSIT).templateDetails(templateDtls).build();
    ProcessDetails processDtl = ProcessDetails.builder()
        .processId(event.getWorkflowMetadata().getProcessInstanceId()).ownerId(1l)
        .recordId("record1")
        .definitionDetails(definitionDetails).build();

    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDtls)
        .activityId("act1")
        .activityName(event.getActivityName()).type(TaskType.HUMAN_TASK)
        .attributes(
            	ObjectConverter.toJson(WorkflowActivityAttributes.builder()
            			.modelAttributes(new HashMap<>()).runtimeAttributes(new HashMap<>())
            			.build()))
        .build();

    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetail));

    Map<String, String> header = new HashMap<>();
    header.put(EventHeaderConstants.ENTITY_ID, "task1");

    Mockito.when(workflowTaskManager.execute(any(WorkflowTaskRequest.class)))
        .thenReturn(WorkflowTaskResponse.builder().build());

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.execute(event, header, processDtl);

    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id(event.getExternalTaskId())
        .processInstanceId(processDtl.getProcessId()).activityId(event.getActivityId())
        .activityName(event.getActivityName()).taskType(activityDetail.getType())
        .command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED).publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(true).skipCallback(true).skipTxnDBUpdate(false)
        .build();
    Mockito.verify(workflowTaskManager, Mockito.times(1)).execute(Mockito.eq(taskRequest));
  }

  @Test
  public void testExecute_ProcessCustomTask_skipTxnDB_true() throws Exception {
    WorkflowIncident event = getIncidentPayload();
    event.setIncidentMsg(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED.name());

    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .recordType(RecordType.DEPOSIT).templateDetails(templateDtls).build();
    ProcessDetails processDtl = ProcessDetails.builder()
        .processId(event.getWorkflowMetadata().getProcessInstanceId()).ownerId(1l)
        .recordId("record1")
        .definitionDetails(definitionDetails).build();

    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDtls)
        .activityId("act1")
        .activityName(event.getActivityName()).type(TaskType.HUMAN_TASK)
        .attributes(
        	ObjectConverter.toJson(WorkflowActivityAttributes.builder().modelAttributes(new HashMap<>()).runtimeAttributes(new HashMap<>()).build()))
        .build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetail));

    Map<String, String> header = new HashMap<>();
    header.put(EventHeaderConstants.ENTITY_ID, "task1");

    Mockito.when(workflowTaskManager.execute(any(WorkflowTaskRequest.class)))
        .thenReturn(WorkflowTaskResponse.builder().build());

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.execute(event, header, processDtl);

    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id(event.getExternalTaskId())
        .processInstanceId(processDtl.getProcessId()).activityId(event.getActivityId())
        .activityName(event.getActivityName()).taskType(activityDetail.getType())
        .command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED).publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(true).skipCallback(true).skipTxnDBUpdate(true).build();
    Mockito.verify(workflowTaskManager, Mockito.times(1)).execute(Mockito.eq(taskRequest));
  }


  @Test
  public void testExecute_ProcessCustomTask_Exception() throws Exception {
    WorkflowIncident event = getIncidentPayload();
    event.setIncidentMsg(WorkflowError.CAMUNDA_COMPLETE_TASK_FAILED.name());

    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .recordType(RecordType.DEPOSIT).templateDetails(templateDtls).build();
    ProcessDetails processDtl = ProcessDetails.builder()
        .processId(event.getWorkflowMetadata().getProcessInstanceId()).ownerId(1l)
        .recordId("record1")
        .definitionDetails(definitionDetails).build();

    ActivityDetail activityDetail = ActivityDetail.builder().templateDetails(templateDtls)
        .activityId("act1")
        .activityName(event.getActivityName()).type(TaskType.HUMAN_TASK)
        .attributes(
            	ObjectConverter.toJson(WorkflowActivityAttributes.builder()
            			.modelAttributes(new HashMap<>()).runtimeAttributes(new HashMap<>())
            			.build()))
        .build();
    Mockito.when(
        activityDetailRepo.findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString())).thenReturn(Optional.of(activityDetail));

    Map<String, String> header = new HashMap<>();
    header.put(EventHeaderConstants.ENTITY_ID, "task1");

    Mockito.when(workflowTaskManager.execute(any(WorkflowTaskRequest.class)))
        .thenThrow(new WorkflowNonRetriableException(WorkflowError.ACTIVITY_DETAIL_NOT_FOUND));

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.execute(event, header, processDtl);

    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());

    WorkflowTaskRequest taskRequest = WorkflowTaskRequest.builder().id(event.getExternalTaskId())
        .processInstanceId(processDtl.getProcessId()).activityId(event.getActivityId())
        .activityName(event.getActivityName()).taskType(activityDetail.getType())
        .command(TaskCommand.FAILED)
        .status(ActivityConstants.TASK_STATUS_FAILED).publishExternalTaskEvent(false)
        .publishWorkflowStateTransitionEvent(true).skipCallback(true).skipTxnDBUpdate(true).build();
    Mockito.verify(workflowTaskManager, Mockito.times(1)).execute(Mockito.eq(taskRequest));
  }

  @Test
  public void testExecute_externalTask_notFound() throws Exception {
    WorkflowIncident event = getIncidentPayload();
    event.setIncidentMsg(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.name());
    event.setExternalTaskId(null);
    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails = DefinitionDetails.builder().definitionId("def1")
        .recordType(RecordType.DEPOSIT).templateDetails(templateDtls).build();
    ProcessDetails processDtl = ProcessDetails.builder()
        .processId(event.getWorkflowMetadata().getProcessInstanceId()).ownerId(1l)
        .recordId("record1")
        .definitionDetails(definitionDetails).build();

    Map<String, String> header = new HashMap<>();
    header.put(EventHeaderConstants.ENTITY_ID, "task1");

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.execute(event, header, processDtl);

    Mockito.verify(activityDetailRepo, Mockito.never())
        .findByTemplateDetailsAndActivityId(any(TemplateDetails.class),
            Mockito.anyString());
    Mockito.verify(workflowTaskManager, Mockito.never())
        .execute(any(WorkflowTaskRequest.class));
  }

  private WorkflowIncident getIncidentPayload() {
    WorkflowMetaData workflowMetaData = WorkflowMetaData.builder().workflowName("abc")
        .processInstanceId("p11")
        .workflowOwnerId("owww1").build();

    return WorkflowIncident.builder().activityName("a11").incidentMsg("failed task")
        .businessEntityId("eng1")
        .businessEntityType("engagement").workflowMetadata(workflowMetaData).activityId("act1")
        .executionId("execution1")
        .externalTaskId("ext1")
        .build();
  }

  @Test
  public void test_executeFailedCommand_Null() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    Mockito
        .when(customTaskHelper.prepareWorkflowTaskRequestForIncident(any(WorkerActionRequest.class)))
        .thenReturn(null);

    incidentTaskManager.executeFailedCommand(workerActionRequest);
    Mockito.verify(workflowTaskManager, Mockito.never()).execute(any());
  }


  @Test
  public void test_executeFailedCommand_RuntimeException() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    Mockito
        .when(customTaskHelper.prepareWorkflowTaskRequestForIncident(any(WorkerActionRequest.class)))
        .thenThrow(new RuntimeException("ETM exception"));

    incidentTaskManager.executeFailedCommand(workerActionRequest);
    Mockito.verify(workflowTaskManager, Mockito.never()).execute(any());
  }

  @Test
  public void test_executeFailedCommand_success() {

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    WorkflowTaskRequest.WorkflowTaskRequestBuilder builder = WorkflowTaskRequest.builder();
    Mockito
        .when(customTaskHelper.prepareWorkflowTaskRequestForIncident(any(WorkerActionRequest.class)))
        .thenReturn(builder);

    Mockito.when(workflowTaskManager.execute(any()))
        .thenReturn(WorkflowTaskResponse.builder().build());

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    incidentTaskManager.executeFailedCommand(workerActionRequest);

    Mockito.verify(customTaskHelper, Mockito.times(1))
        .prepareWorkflowTaskRequestForIncident(any(WorkerActionRequest.class));

    Mockito.verify(workflowTaskManager, Mockito.times(1)).execute(any());

  }

  @Test
  public void test_customActivityDisable() {
    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE, "HUMAN_TASK");
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);
    WorkerActionRequest workerActionRequest = WorkerActionRequest.builder().activityId("act1")
        .extensionProperties(extensionProperties).processInstanceId("processInstance1")
        .taskId("taskId1")
        .ownerId(1l).workerId("worker1").variableMap(variableMap1).build();

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(false);

    incidentTaskManager.executeFailedCommand(workerActionRequest);

    Mockito.verify(customTaskHelper, Mockito.never())
        .prepareWorkflowTaskRequest(any(WorkerActionRequest.class));
    Mockito.verify(workflowTaskManager, Mockito.never()).execute(any());
  }

  @Test
  public void test_executeFailedCommand_Ignore() {
    WorkflowIncident event = getIncidentPayload();
    TemplateDetails templateDtls = TemplateDetails.builder().id("tmplt1").build();
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .definitionId("def1")
            .templateDetails(templateDtls)
            .recordType(RecordType.ENGAGEMENT)
            .build();
    ProcessDetails processDtl =
        ProcessDetails.builder()
            .ownerId(1l)
            .recordId("record1")
            .processId("p11")
            .definitionDetails(definitionDetails)
            .build();
    ActivityDetail activityDetail =
        ActivityDetail.builder()
            .templateDetails(templateDtls)
            .activityId("act1")
            .activityName(event.getActivityName())
            .type(null)
            .attributes(
                ObjectConverter.toJson(
                    WorkflowActivityAttributes.builder()
                        .modelAttributes(new HashMap<>())
                        .runtimeAttributes(new HashMap<>())
                        .build()))
            .build();
    event.setIncidentMsg(WorkflowError.HUMAN_TASK_DOWNSTREAM_FAILURE.name());

    Map<String, String> extensionProperties = new HashMap<>();
    extensionProperties.put("estimate", "1");
    extensionProperties.put("priority", "1");

    Map<String, String> variableMap = new HashMap<>();
    variableMap.put("assigneeRoles", "[Role1, Role2]");
    variableMap.put("assigneeId", "assignee1");
    variableMap.put(WorkflowConstants.ID_KEY, "record1");
    VariableMap variableMap1 = new VariableMapImpl();
    variableMap1.putAll(variableMap);

    Mockito.when(workflowTaskConfig.isEnable()).thenReturn(true);

    Mockito.when(activityDetailRepo.findByTemplateDetailsAndActivityId(templateDtls, "act1"))
        .thenReturn(Optional.ofNullable(activityDetail));

    incidentTaskManager.execute(event, new HashMap<>(), processDtl);
    Mockito.verify(activityDetailRepo, Mockito.times(1))
        .findByTemplateDetailsAndActivityId(templateDtls, "act1");
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.intuit.paved-road.appintgwkflw-wkflautomate</groupId>
    <artifactId>was-event-in-app-e2e-integration</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>


    <url>https://github.intuit.com/fdpenrichment/Intuit.git</url>
    <organization>
        <name>Intuit, Inc. :: Your BU or group :: was-event-in</name>
        <url>https://github.intuit.com/fdpenrichment</url>
    </organization>

    <name>was-event-in - Integration Test</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot-maven-plugin.version>2.7.15</spring-boot-maven-plugin.version>
        <java.version>11</java.version>
        <junit-version>5.8.1</junit-version>
        <surefireArgLine />
        <karate.version>0.9.6</karate.version>
        <lombok.version>1.18.22</lombok.version>
        <ipf-runtime-validator.version>0.0.23</ipf-runtime-validator.version>
        <jacoco-maven-plugin.version>0.8.8</jacoco-maven-plugin.version>
        <maven-cucumber-reporting.version>5.7.1</maven-cucumber-reporting.version>
        <maven-compiler-plugin.version>3.10.1</maven-compiler-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-surefire-plugin.version>2.22.2</maven-surefire-plugin.version>
        <maven-surefire-report-plugin.version>2.22.2</maven-surefire-report-plugin.version>
        <project.testresult.directory>${project.build.directory}/test-results</project.testresult.directory>
        <central.repo>https://artifact.intuit.com/artifactory/maven-proxy</central.repo>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>net.masterthought</groupId>
            <artifactId>maven-cucumber-reporting</artifactId>
            <version>${maven-cucumber-reporting.version}</version>
            <!-- Note that maven-cucumber-reporting 3.8.0 (from June 2017), via cucumber-reporting, inappropriately
                 brings in log4j-core as a transitive dependency. This appears to be fixed at least as of 5.2.6.
                 The version of log4j-core _should be_ managed by jsk-bom. -->
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.intuit.fdp-enrichment.ipf-runtime-validator</groupId>
            <artifactId>ipf-runtime-validator</artifactId>
            <version>${ipf-runtime-validator.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- karate dependencies -->
        <dependency>
            <groupId>com.intuit.karate</groupId>
            <artifactId>karate-apache</artifactId>
            <version>${karate.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.intuit.karate</groupId>
            <artifactId>karate-junit5</artifactId>
            <version>${karate.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <testResources>
            <testResource>
                <directory>src/test/java</directory>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>@{argLine} @{surefireArgLine}</argLine>
                    <forkCount>0</forkCount>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central-repo</id>
            <url>${central.repo}</url>
            <name>Intuit Maven Cache</name>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central-plugin-repo</id>
            <url>${central.repo}</url>
            <name>Intuit Maven Cache (plugins)</name>
        </pluginRepository>
    </pluginRepositories>
</project>

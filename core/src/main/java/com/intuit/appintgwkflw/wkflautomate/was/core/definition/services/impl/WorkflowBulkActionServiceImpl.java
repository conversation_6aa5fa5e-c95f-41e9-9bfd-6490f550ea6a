package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.CommandHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.WorkflowBulkActionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.WorkflowsBulkAction;
import com.intuit.v4.WorkflowsBulkAction.WorkflowBulkActionDetails;
import com.intuit.v4.qbshared.bulk.definitions.BulkActionEnum;
import com.intuit.v4.qbshared.bulk.definitions.BulkActionModeEnum;
import com.intuit.v4.qbshared.bulk.definitions.BulkActionStatusEnum;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy.verify;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation.DELETE_ALL;
import static java.util.Objects.isNull;
import static org.apache.commons.lang3.StringUtils.isEmpty;

/** <AUTHOR> */
@Service
@AllArgsConstructor
public class WorkflowBulkActionServiceImpl implements WorkflowBulkActionService {

  private final WASContextHandler contextHandler;

  private final RunTimeService runTimeService;

  private final DefinitionServiceHelper definitionServiceHelper;

  private final AuthDetailsService authDetailsService;
  private final AuthDetailsServiceHelper authDetailsServiceHelper;
  private final EventScheduleHelper eventScheduleHelper;
  private final UserContributionService userContributionService;

  @Override
  @Metric(name = MetricName.DELETE_ALL_WORKFLOWS, type = Type.API_METRIC)
  public WorkflowsBulkAction deleteAll(final WorkflowBulkActionDetails workflowBulkActionDetails) {

    verify(
        isNull(workflowBulkActionDetails)
            || BulkActionModeEnum.ASYNC != workflowBulkActionDetails.getMode()
            || BulkActionEnum.DELETE != workflowBulkActionDetails.getAction()
            || !DELETE_ALL.name().equals(workflowBulkActionDetails.getName()),
        WorkflowError.INVALID_WORKFOW_DELETE_DETAILS);

    final String authorizationHeader = contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER);
    final Authorization authorization = new Authorization(authorizationHeader);

    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Downgrade request received for ownerId=%s", authorization.getRealm())
                .className(this.getClass().getName())
                .downstreamComponentName(DownstreamComponentName.WAS)
                .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));

    // fetch all definition details for a given company.
    final List<DefinitionDetails> definitionLists =
        definitionServiceHelper.getAllDefinitionList(Long.parseLong(authorization.getRealm()));

    if (Objects.isNull(definitionLists)){
      WorkflowLogger.info(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("No definitions for the company found. Skipping the delete workflows call")
                  .className(this.getClass().getName()));
      return new WorkflowsBulkAction()
          .id(GlobalId.create(authorization.getRealm(), UUID.randomUUID().toString()))
          .action(BulkActionEnum.DELETE)
          .status(BulkActionStatusEnum.IN_PROGRESS)
          .mode(BulkActionModeEnum.ASYNC)
          .inProgressEntities(CommandHelper.fetchDefinitionIdList(definitionLists))
          .name(DELETE_ALL.name());
    }
    // system definition will have a different owner id and it won't be picked up in the query.
    // These 2 Actions are required only when definitions exists in WAS
    final DefinitionInstance definitionInstance = new DefinitionInstance();
    definitionInstance.setDefinitionDetailsList(definitionLists);

    // fetch auth details for a given company
    final AuthDetails authDetails =
        authDetailsService.getAuthDetailsFromRealmIdSafe(authorization.getRealm());

    // to check if subscription is found or not.Since UI is always hitting this API so there can be
    // a case in which user does not have any Appconnect subscription in that case nothing needs to
    // be done.
    boolean subscriptionAvailable = true;

    /**
     * if auth details are not present generate the auth details so that unsubscribe can be done
     * from app-connect
     */
    if (isNull(authDetails) || isEmpty(authDetails.getSubscriptionId())) {
      /**
       * this is in sync as unsubscribe can execute before this call is completed if everything is
       * in async
       */
      try {
        authDetailsServiceHelper.populateAuthDetailsSync(authorization);
      } catch (final Exception e) {
        subscriptionAvailable = false;
        WorkflowLogger.info(
            () ->
                WorkflowLoggerRequest.builder()
                    .message("No Subscription details found")
                    .className(this.getClass().getName())
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .downstreamServiceName(DownstreamServiceName.WAS_DOWNGRADE));
      }
    }

    // Get definition with recordType as Downgrade and Start the downgrade process.
    if (subscriptionAvailable) {
      runTimeService.processTriggerMessageV2(
          TriggerUtil.prepareTriggerPayloadForDowngrade(
              contextHandler.get(WASContextEnums.OWNER_ID), workflowBulkActionDetails));
    }

    return new WorkflowsBulkAction()
        .id(GlobalId.create(authorization.getRealm(), UUID.randomUUID().toString()))
        .action(BulkActionEnum.DELETE)
        .status(BulkActionStatusEnum.IN_PROGRESS)
        .mode(BulkActionModeEnum.ASYNC)
        .inProgressEntities(CommandHelper.fetchDefinitionIdList(definitionLists))
        .name(DELETE_ALL.name());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.Map;

@UtilityClass
public class ActivityDetailsUtil {

    /**
     * Retrieves the extension properties from the given activity detail.
     *
     * <p>This method extracts the extension properties from the JSON attributes of the provided
     * {@link ActivityDetail}. It checks for the presence of model attributes within the extension
     * properties and returns them as a map. If no model attributes are found, it logs an informational
     * message and returns an empty map.
     *
     * @param activityDetail the {@link ActivityDetail} containing the JSON attributes to be processed
     * @return a map of extension properties if model attributes are found; otherwise, an empty map
     */
    public Map<String, Object> getExtensionProperties(ActivityDetail activityDetail) {
        Map<String, String> extensionProperties = ObjectConverter.fromJson(activityDetail.getAttributes(),
                new TypeReference<Map<String, Object>>() {
                });
        if (MapUtils.isNotEmpty(extensionProperties) &&
                extensionProperties.containsKey(ActivityConstants.ACTIVITY_DETAILS_TABLE_ATTRIBUTES_KEY_MODEL_ATTRIBUTES)) {
            return ObjectConverter.fromJson(
                    ObjectConverter.toJson(extensionProperties.get(ActivityConstants.ACTIVITY_DETAILS_TABLE_ATTRIBUTES_KEY_MODEL_ATTRIBUTES)),
                    new TypeReference<Map<String, Object>>() {
                    });
        }
        WorkflowLogger.info(
                () ->
                        WorkflowLoggerRequest.builder()
                                .message("No model attributes found for activity detail id: " + activityDetail.getId())
                                .className(ActivityDetailsUtil.class.getName()));
        return new HashMap<>();
    }
}

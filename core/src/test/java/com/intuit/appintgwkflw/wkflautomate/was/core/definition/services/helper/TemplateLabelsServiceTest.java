package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.PrecannedTemplateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.NameValue;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Template;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TemplateLabelsServiceTest {

  @Mock
  private TranslationService translationService;

  @Mock
  private CustomWorkflowConfig customWorkflowConfig;

  @Mock
  private PrecannedTemplateConfig precannedTemplateConfig;

  @Mock
  private WASContextHandler wasContextHandler;

  @InjectMocks
  private TemplateLabelsService templateLabelsService;

  @Before
  public void init(){
    NameValue nameValue1 = new NameValue();
    nameValue1.setValue("value1");
    nameValue1.setName("name1");
    NameValue nameValue2 = new NameValue();
    nameValue2.setValue("value2");
    nameValue2.setName("name2");
    List<NameValue> nameValueList = new ArrayList<>();
    nameValueList.add(nameValue1); nameValueList.add(nameValue2);
    ConfigTemplate configTemplate = new ConfigTemplate();
    configTemplate.setLabels(nameValueList);
    configTemplate.setId("invoiceoverduereminder");
    Mockito.when(customWorkflowConfig.getTemplateMap()).thenReturn(
        new HashMap<>(){{
          put(configTemplate.getId(), configTemplate);
        }}
    );

    ConfigTemplate precannedTemplate = new ConfigTemplate();
    configTemplate.setLabels(nameValueList);
    configTemplate.setId("bankDepositReminder");
    precannedTemplate.setLabels(nameValueList);
    Mockito.when(precannedTemplateConfig.getTemplateConfigMap()).thenReturn(
        new HashMap<>(){{
          put("bankDepositReminder", precannedTemplate);
        }}
    );

    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn(null);
    Mockito.when(translationService.getString("value1",null)).thenReturn("value1");
    Mockito.when(translationService.getString("value2",null)).thenReturn("value2");


  }

  @Test
  public void test_CustomConfig(){
    Template template = new Template();
    template.setCategory(TemplateCategory.CUSTOM.name());
    template.setName("invoiceoverduereminder");
    templateLabelsService.fill(template);
    Assert.assertEquals(2, template.getLabels().size());
    Assert.assertEquals("value1", template.getLabels(0).getValue());
    Assert.assertEquals("value2", template.getLabels(1).getValue());
    Assert.assertEquals("name1", template.getLabels(0).getName());
    Assert.assertEquals("name2", template.getLabels(1).getName());
  }

  @Test
  public void test_CustomConfig_ConfigNotPresent(){
    Template template = new Template();
    template.setCategory(TemplateCategory.CUSTOM.name());
    template.setName("invoiceoverduereminder1");
    templateLabelsService.fill(template);
    Assert.assertEquals(0, template.getLabels().size());
  }

  @Test
  public void test_PrecannedConfig(){
    Template template = new Template();
    template.setName("bankDepositReminder");
    templateLabelsService.fill(template);
    Assert.assertEquals(2, template.getLabels().size());
    Assert.assertEquals("value1", template.getLabels(0).getValue());
    Assert.assertEquals("value2", template.getLabels(1).getValue());
    Assert.assertEquals("name1", template.getLabels(0).getName());
    Assert.assertEquals("name2", template.getLabels(1).getName());
  }

  @Test
  public void test_Precanned_ConfigNotPresent(){
    Template template = new Template();
    template.setName("bankDepositReminder1");
    templateLabelsService.fill(template);
    Assert.assertEquals(0, template.getLabels().size());
  }

  @Test
  public void test_CustomConfig_WithLocalisation(){
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("it");
    Mockito.when(translationService.getString("value1","it")).thenReturn("value3");
    Mockito.when(translationService.getString("value2","it")).thenReturn("value4");


    Template template = new Template();
    template.setCategory(TemplateCategory.CUSTOM.name());
    template.setName("invoiceoverduereminder");
    templateLabelsService.fill(template);
    Assert.assertEquals(2, template.getLabels().size());
    Assert.assertEquals("value3", template.getLabels(0).getValue());
    Assert.assertEquals("value4", template.getLabels(1).getValue());
    Assert.assertEquals("name1", template.getLabels(0).getName());
    Assert.assertEquals("name2", template.getLabels(1).getName());
  }

  @Test
  public void test_PrecannedConfig_WithLocalisation(){
    Mockito.when(wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE)).thenReturn("it");
    Mockito.when(translationService.getString("value1","it")).thenReturn("value3");
    Mockito.when(translationService.getString("value2","it")).thenReturn("value4");


    Template template = new Template();
    template.setName("bankDepositReminder");
    templateLabelsService.fill(template);
    Assert.assertEquals(2, template.getLabels().size());
    Assert.assertEquals("value3", template.getLabels(0).getValue());
    Assert.assertEquals("value4", template.getLabels(1).getValue());
    Assert.assertEquals("name1", template.getLabels(0).getName());
    Assert.assertEquals("name2", template.getLabels(1).getName());
  }

  @Test
  public void testTemplateList(){
    Template template1 = new Template();
    template1.setCategory(TemplateCategory.CUSTOM.name());
    template1.setName("invoiceoverduereminder");

    Template template2 = new Template();
    template2.setName("bankDepositReminder");

    List<Template> templates = new ArrayList<>();
    templates.add(template1);
    templates.add(template2);

    templateLabelsService.fill(templates);

    template1 = templates.get(0);
    template2 = templates.get(1);

    Assert.assertEquals(2, template1.getLabels().size());
    Assert.assertEquals("value1", template1.getLabels(0).getValue());
    Assert.assertEquals("value2", template1.getLabels(1).getValue());
    Assert.assertEquals("name1", template1.getLabels(0).getName());
    Assert.assertEquals("name2", template1.getLabels(1).getName());

    Assert.assertEquals(2, template2.getLabels().size());
    Assert.assertEquals("value1", template2.getLabels(0).getValue());
    Assert.assertEquals("value2", template2.getLabels(1).getValue());
    Assert.assertEquals("name1", template2.getLabels(0).getName());
    Assert.assertEquals("name2", template2.getLabels(1).getName());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.StringExpressionParser;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import lombok.experimental.UtilityClass;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.xml.ModelInstance;

import java.util.Objects;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_OWNER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.UNDERSCORE;

/**
 * This class is responsible for transforming the templateDetails object in WAS as expected by Camunda for dynamically created Bpmns.
 *
 * <AUTHOR>
 */

@UtilityClass
public class DynamicBpmnWasCamundaTransformer {

    /**
     * This method take transforms the templateDetails object for dynamically created Bpmns.
     *
     * @param templateMetadata
     * @param modelType
     * @param modelInstance
     * @param templateDetails
     */

    public static final int MINUS_ONE = -1;


    /**
     * This method take transforms the templateDetails object for dynamically created Bpmns.
     *
     * @param templateMetadata - TemplateMetadata
     * @param modelInstance    - BpmnModelInstance
     * @param templateDetails  - TemplateDetails
     */
    public void transformTemplateDetailsValuesForBpmn(
            final TemplateMetadata templateMetadata,
            final ModelInstance modelInstance,
            TemplateDetails templateDetails) {

        // return if the bpmn is not created dynamically.
        if (!templateMetadata.isTemplateCreatedDynamically()) {
            return;
        }

        //  there is no concept of update template in dynamic bpmn. Therefore, we always have version as -1
        templateDetails.setVersion(MINUS_ONE);

        String templateName = StringExpressionParser.splitStringByPattern(
                templateDetails.getTemplateName(), UNDERSCORE)[0];

        templateDetails.setTemplateName(templateName);
        templateDetails.setDisplayName(SchemaDecoder.getTemplateDisplayName(
                templateName, (BpmnModelInstance) modelInstance));
        templateDetails.setTemplateAdjacencyValuesMd5Sum(
                templateMetadata.getTemplateAdjacencyValuesMd5Sum());
        templateDetails.setTemplateData(
                Bpmn.convertToString((BpmnModelInstance) modelInstance).getBytes());
        templateDetails.setOwnerId(Long.valueOf(SYSTEM_OWNER_ID));
    }

    /**
     * This method take transforms the templateDetails object for dynamically created Dmn.
     *
     * @param templateMetadata - TemplateMetadata
     * @param modelInstance    - DmnModelInstance
     * @param templateDetails  - TemplateDetails
     */
    public void transformTemplateDetailsValuesForDmn(
            final TemplateMetadata templateMetadata,
            final ModelInstance modelInstance,
            TemplateDetails templateDetails) {

        // return if the bpmn is not created dynamically.
        if (!templateMetadata.isTemplateCreatedDynamically()) {
            return;
        }

        //  there is no concept of update template in dynamic bpmn. Therefore, we always have version as -1
        templateDetails.setVersion(MINUS_ONE);

        // For dynamically generated templates the processId will of format : templateName_hashValue
        // Like for approval template, the processId will be something like "customApproval_1234567890"
        // But we need to save the template in database with the name "customApproval", so we are splitting the
        // templateName by underscore and taking the first part of it.
        String templateName = StringExpressionParser.splitStringByPattern(
                templateDetails.getTemplateName(), UNDERSCORE)[0];

        templateDetails.setTemplateName(templateName);
        templateDetails.setTemplateData(
                Dmn.convertToString((DmnModelInstance) modelInstance).getBytes());
        templateDetails.setOwnerId(Long.valueOf(SYSTEM_OWNER_ID));
    }

    /**
     * This method takes the dmnTemplateDetails and bpmnTemplateDetails object created in WAS DB
     * and maps the template name as expected in Camunda for dynamically created Bpmns.
     *
     * @param wasDmnTemplateDetails  - TemplateDetails
     * @param wasBpmnTemplateDetails - TemplateDetails
     * @return String
     */
    public String getTemplateNameCamundaDeployment(TemplateDetails wasDmnTemplateDetails,
                                                    TemplateDetails wasBpmnTemplateDetails) {

        StringBuilder stringBuilder = new StringBuilder(wasDmnTemplateDetails.getTemplateName());
        if (Objects.nonNull(wasBpmnTemplateDetails.getTemplateAdjacencyValuesMd5Sum())) {
            stringBuilder.append(UNDERSCORE).append(wasBpmnTemplateDetails.getTemplateAdjacencyValuesMd5Sum());
        }
        return stringBuilder.toString();
    }

}

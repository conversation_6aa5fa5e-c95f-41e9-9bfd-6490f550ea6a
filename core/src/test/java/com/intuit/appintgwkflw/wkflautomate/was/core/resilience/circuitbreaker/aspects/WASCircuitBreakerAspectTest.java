package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.aspects;

import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ApplyCircuitBreaker;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowCircuitOpenException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;
import java.util.function.Supplier;

@RunWith(MockitoJUnitRunner.class)
public class WASCircuitBreakerAspectTest {

    @Mock private WASCircuitBreakerService wasCircuitBreakerService;
    @Mock private WASCircuitBreakerConfiguration wasCircuitBreakerConfiguration;
    @Mock private ProceedingJoinPoint proceedingJoinPoint;
    @Mock private ApplyCircuitBreaker applyCircuitBreaker;
    @Mock private CircuitBreaker circuitBreaker;

    @InjectMocks private WASCircuitBreakerAspect wasCircuitBreakerAspect;

    @Test
    public void testWhenCircuitBreakerDisabled(){
        Mockito.when(wasCircuitBreakerConfiguration.isEnabled()).thenReturn(false);
        wasCircuitBreakerAspect.execute(proceedingJoinPoint, applyCircuitBreaker);
        Mockito.verify(wasCircuitBreakerService, Mockito.times(0))
                .getCircuitBreakerForOffering(Mockito.any());
    }

    @Test
    public void testWhenCircuitBreakerEnabled(){
        Mockito.when(wasCircuitBreakerConfiguration.isEnabled()).thenReturn(true);
        wasCircuitBreakerAspect.execute(proceedingJoinPoint, applyCircuitBreaker);
        Mockito.verify(wasCircuitBreakerService)
                .getCircuitBreakerForOffering(Mockito.any());
    }

    @Test
    public void testExecuteSupplierWithCircuitBreaker(){
        Supplier supplier = () -> wasCircuitBreakerService.getCircuitBreaker("test");
        Mockito.when(circuitBreaker.decorateSupplier(Mockito.any())).
                thenReturn(supplier);
        wasCircuitBreakerAspect.executeSupplier(Optional.of(circuitBreaker), proceedingJoinPoint);
        Mockito.verify(wasCircuitBreakerService, Mockito.times(1))
                .getCircuitBreaker("test");
    }

    @Test
    public void testExecuteSupplierWithoutCircuitBreaker(){
        wasCircuitBreakerAspect.executeSupplier(Optional.empty(), proceedingJoinPoint);
        Mockito.verify(wasCircuitBreakerService, Mockito.times(0))
                .getCircuitBreaker("test");
        try {
            Mockito.verify(proceedingJoinPoint, Mockito.times(1)).proceed();
        } catch (Throwable e) {
            Assert.fail();
        }
    }

    @Test(expected = WorkflowCircuitOpenException.class)
    public void testExecuteSupplierWithOpenCircuitBreaker(){
        CircuitBreaker cb = CircuitBreaker.ofDefaults("test");
        cb.transitionToOpenState();
        wasCircuitBreakerAspect.executeSupplier(Optional.of(cb), proceedingJoinPoint);
    }

    @Test(expected = WorkflowRetriableException.class)
    public void testExecuteSupplierRetriableException() throws Throwable{
        CircuitBreaker cb = CircuitBreaker.ofDefaults("test");
        Mockito.when(proceedingJoinPoint.proceed()).
                thenThrow(new WorkflowRetriableException(WorkflowError.CIRCUIT_OPEN_ERROR));
        wasCircuitBreakerAspect.executeSupplier(Optional.of(cb), proceedingJoinPoint);
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testExecuteSupplierRuntimeException() throws Throwable{
        CircuitBreaker cb = CircuitBreaker.ofDefaults("test");
        Mockito.when(proceedingJoinPoint.proceed()).thenThrow(new RuntimeException());
        wasCircuitBreakerAspect.executeSupplier(Optional.of(cb), proceedingJoinPoint);
    }
}

package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkflowTaskResponse {

  private String txnId;
  private String status;
  //Value to set in Camunda as ProcessInstance variable.
  private Map<String, Object> responseMap;
  //Task response body in case of GET call.
  private Object response;
}

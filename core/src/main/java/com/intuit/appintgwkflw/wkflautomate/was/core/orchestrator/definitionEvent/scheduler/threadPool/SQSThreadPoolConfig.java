package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool;

import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/** <AUTHOR> This class creates worker thread pool for sqs. */
@Configuration
@ConfigurationProperties(prefix = "ess-sqs.thread-pool")
@Getter
@Setter
@ConditionalOnExpression("${ess-sqs.enabled:false}")
public class SQSThreadPoolConfig {
  private int minThreads;
  private int maxThreads;
  private int keepAliveTimeInSec;
  private int queueSize;

  @Bean(name = WorkflowConstants.SQS_EXECUTOR_THREAD_BEAN)
  public ThreadPoolExecutor threadPoolSQSQueue() {
    return ThreadPoolExecutorFactory.createExecutor(
        WorkflowConstants.SQS_EXECUTOR_THREAD,
        queueSize,
        minThreads,
        maxThreads,
        keepAliveTimeInSec);
  }
}

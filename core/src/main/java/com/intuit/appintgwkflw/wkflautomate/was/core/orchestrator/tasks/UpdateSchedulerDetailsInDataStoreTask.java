package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.request.State;
import java.util.List;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 *     <p>This class update the definitionId of the schedulers.
 */
@AllArgsConstructor
public class UpdateSchedulerDetailsInDataStoreTask implements Task {
  private final SchedulerDetailsRepository schedulerDetailsRepository;

  @Override
  public State execute(State state) {
    try {

      String definitionId = state.getValue(AsyncTaskConstants.DEFINITION_ID_KEY);
      WorkflowVerfiy.verify(
          StringUtils.isBlank(definitionId),
          WorkflowError.INPUT_INVALID,
          AsyncTaskConstants.DEFINITION_ID_KEY);
      List<String> scheduleIds = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS);
      if (CollectionUtils.isEmpty(scheduleIds)) {
        logInfo("No schedulerIds received for the definitionId=%s", definitionId);
        return state;
      }
      logInfo("Updating definitionId=%s for scheduleIds=%s", definitionId, scheduleIds);
      // update definitionId for the schedulerIds
      schedulerDetailsRepository.updateDefinitionIdForSchedulers(definitionId, scheduleIds);
      logInfo("DefinitionId=%s for scheduleIds=%s successfully updated", definitionId, scheduleIds);
    } catch (Exception exception) {
      logError("Exception occurred while updating scheduler details to the database", exception);
      state.addValue(AsyncTaskConstants.UPDATE_SCHEDULE_DETAILS_TASK_FAILURE, true);
      state.addValue(AsyncTaskConstants.UPDATE_SCHEDULE_IN_DATASTORE_EXCEPTION, exception);
    }

    return state;
  }


  /**
   * Log the info
   *
   * @param message
   * @param workflowMessageArgs
   */
  private void logInfo(String message, Object... workflowMessageArgs) {
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.ESS)
                .downstreamServiceName(DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS));
  }

  /**
   * Log the error
   *
   * @param message
   * @param workflowMessageArgs
   */
  private void logError(String message, Object... workflowMessageArgs) {
    WorkflowLogger.error(
        () ->
            WorkflowLoggerRequest.builder()
                .className(this.getClass().getSimpleName())
                .message(message, workflowMessageArgs)
                .downstreamComponentName(DownstreamComponentName.ESS)
                .downstreamServiceName(DownstreamServiceName.UPDATE_SCHEDULES_STATUS_ESS));
  }
}

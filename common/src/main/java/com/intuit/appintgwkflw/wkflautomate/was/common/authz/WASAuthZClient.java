package com.intuit.appintgwkflw.wkflautomate.was.common.authz;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AuthorizationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.identity.authz.sdk.client.AuthZClient;
import com.intuit.identity.authz.sdk.client.AuthZClientFactory;
import com.intuit.identity.authz.sdk.client.AuthZConfig;
import com.intuit.identity.authz.sdk.client.AuthZConfigBuilder;
import com.intuit.identity.authz.sdk.client.ConnectingEnvironment;
import io.opentracing.noop.NoopTracerFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 *     <p></> Reference Doc:
 *     https://devportal.intuit.com/app/dp/capability/351/capabilityDocs/main/docs/assets/authorization/integration/authz-java-sdk.md
 *     <br>
 *     </>
 */
@Component
@RequiredArgsConstructor
public class WASAuthZClient {

  private final AppConfig appConfig;

  private final AuthorizationConfig authorizationConfig;

  private AuthZClient wasAuthZClient;

  @PostConstruct
  public void init() {
    try {
      AuthZConfig authZConfig =
          new AuthZConfigBuilder()
              .withAppId(appConfig.getAppId())
              .withappSecret(appConfig.getAppSecret())
              .withConnectingEnvironment(
                  ConnectingEnvironment.valueOf(authorizationConfig.getEnv()))
              .withConnectionTimeOutMs(authorizationConfig.getConnectionTimeOutMs())
              .build();

      this.wasAuthZClient =
          AuthZClientFactory.getAuthZClient(authZConfig, NoopTracerFactory.create());
    } catch (Exception e) {
      throw new WorkflowGeneralException(WorkflowError.FAILED_TO_INITIALIZE_AUTHZ_CLIENT, e);
    }
  }

  public AuthZClient getAuthZClient() {
    return this.wasAuthZClient;
  }
}

### March 2021 - during transition period, enable legacy mode for Spring Boot 2.4
### See https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-Config-Data-Migration-Guide#legacy-mode
### And https://wiki.intuit.com/display/CTODevSP/Spring+Boot+2.4+Migration+Notes
### And https://spring.io/blog/2020/08/14/config-file-processing-in-spring-boot-2-4#using-legacy-processing
spring:
  config:
    use-legacy-processing: true
  # API-1900: setting spring active profile to local for developer experience
  # This default 'spring.profiles.active' value is usually overridden in "entry.sh"; see https://docs.spring.io/spring-boot/docs/current/reference/html/howto.html
  profiles:
    active: default
### AND to have Spring process bootstrap files - which was default behavior prior to Spring Boot 2.4 and
### Spring Cloud 2020 - the project must either include spring-cloud-parent-bootstrap as a dependency,
### *which we now do*, or else set the environment variable SPRING_CLOUD_BOOTSTRAP_ENABLED=true (!)
### See above and https://docs.spring.io/spring-cloud-config/docs/3.0.0/reference/html/#config-data-import
### ------------------------------------------------

#security:
#  intuit:
#    appId: Intuit.appintgwkflw.wkflautomate.waseventin
#    appSecret: '{secret}idps:/messageMesh/appSecret'

jsk:
  lastmile:
    enabled: false
  mesh:
    enabled: ${MESH_ENABLED:false}
    port: ${MESH_TRAFFIC_PORT:8090}
  servlet:
    logging:
      utils:
        formatType: NameValue
        mdcIntuitTidNameValueFormat: '%s'
      filter:
        mdcTypeRequestEvent: RequestEvent
        typeDefault: GenericEvent
        formatRequestInfo: req vsvr=%s method=%s url="%s" user_agent="%s"

logging:
  level:
    root: INFO
    com.intuit.v4.fdpenrichment.ipf.runtime: INFO
    org.springframework: ERROR
  charset:
    console: "UTF-8"
  pattern:
    console: "%d{yyyy-MM-ddTHH:mm:ss.SSSZ} %-5level %logger{36} - thread=%t intuit_correlation_id=%X{intuit_correlation_id} intuit_tid=%X{intuit_tid} provider_id=%X{provider_id} entity_type=%X{entity_type} intuitentitytype=%X{intuitentitytype} intuit_userid=%X{intuit_userid} intuit_realmid=%X{intuit_realmid} connection_id=%X{connection_id} credential_set_id=%X{credential_set_id} processor_name=%X{processor_name} intuit_offeringid=%X{intuit_offeringid} aggregation_mode=%X{aggregation_mode} %msg%n"

# Configuration of TLS Termination in service layer
server:
  max-http-header-size: 32KB
  port: 8443 # Set it to 8080 or some other value, if running in clear text mode
  include-debug-info: true
  error:
    include-message: always # Might leak info if exceptions are sent as part of the error message
  ssl:
    enabled: true # Set to false to run in clear text mode, be sure to set 'service_layer_tls_termination' to no in entry.sh
    enabled-protocols: TLSv1.2
    key-alias: tomcat
    key-store: /app/tmp/keystore.pkcs12
    key-store-password: ${TLS_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
  servlet:
    session:
      cookie:
        secure: true
        http-only: true
  tomcat:
    accesslog:
      request-attributes-enabled: true

management:
  server:
    port: 8490
    ssl:
      enabled: true
      enabled-protocols: TLSv1.2
      key-alias: tomcat
      key-store: classpath:management.jks
      key-store-password: password
      key-store-type: jks
  endpoints:
    # endpoints disabled for security concerns (API-1850)
    enabled-by-default: false
    web:
      exposure:
        include: ["health","info","prometheus"]
  endpoint:
    prometheus:
      enabled: true
    info:
      enabled: false
    health:
      enabled: false

# --------------------------------- IPF Core Configs ---------------------------------  #
ipf:
  logging: # Config mentioning the header fields to be masked when being logged.
    masking:
      headers:
        - intuit_token
        - intuit_appsecret
        - spring_json_header_types
        - auth_header

# IPF Core Metrics Config
metrics:
  additionalAllowList:
    - olt.flow.duration
    - spring.integration.send
    - gep.get.flow.duration
    - gep.post.flow.duration
    - dedupe.flow.duration
    - persist.flow.duration
    - categorize.flow.duration
    - intuit.fdp.enrichment.messagemesh.gepHttpPostCalls
    - intuit.fdp.enrichment.messagemesh.oltHttpGetCalls
    - intuit.fdp.enrichment.messagemesh.gepHttpGetCalls
    - intuit.fdp.enrichment.messagemesh.olt.latency
    - intuit.fdp.enrichment.messagemesh.gep.get.latency
    - intuit.fdp.enrichment.messagemesh.gep.put.latency
    - intuit.fdp.enrichment.messagemesh.cat.post.latency
    - intuit.fdp.enrichment.messagemesh.processor.latency
    - intuit.fdp.enrichment.messagemesh.credentialServiceCalls
    - intuit.fdp.enrichment.messagemesh.credentialServiceCallHttpStatus
    - intuit.fdp.enrichment.messagemesh.duplicateEntityCount
  tagIgnoreList:
    - provider_id
    - providerId
    - flow_exception
    - exception

# Asset Information - Needed to invoke Kafka Retry Service for Kafka Publishing failures
messageMesh:
  assetId: 4110678153014047831
  appId: Intuit.appintgwkflw.wkflautomate.waseventin
  appSecret: '{secret}idps:/messageMesh/appSecret' #TODO: !environment specific. Update based on IDPS secret location
---
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  kafka:
    producer:
      properties:
        compressor.type: "gzip"
  codec:
    max-in-memory-size: 30MB


# Logging Configs
logging:
  level:
    root: INFO
    org.springframework: ERROR
  charset:
    console: "UTF-8"
  pattern:
    console: "%d{yyyy-MM-ddTHH:mm:ss.SSSZ} %-5level %logger{36} - thread=%t intuit_correlation_id=%X{intuit_correlation_id} intuit_tid=%X{intuit_tid} provider_id=%X{provider_id} entity_type=%X{entity_type} intuitentitytype=%X{intuitentitytype} intuit_userid=%X{intuit_userid} intuit_realmid=%X{intuit_realmid} connection_id=%X{connection_id} credential_set_id=%X{credential_set_id} processor_name=%X{processor_name} %msg%n"


cache.type: redis

jsk:
  config:
    client:
      polling-enabled: false #disable config client polling
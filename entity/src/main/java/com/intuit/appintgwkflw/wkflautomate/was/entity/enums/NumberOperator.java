package com.intuit.appintgwkflw.wkflautomate.was.entity.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum Stores the configuration which is returned to the front end for Number(Long, Integer,
 * Double) related parameters.
 */
@RequiredArgsConstructor
@Getter
public enum NumberOperator {
  OPERATOR_GREATER_THAN("GT", "~double.GT" /*"Greater Than"*/),
  OPERATOR_GREATER_THAN_EQUAL_TO("GTE", "~double.GTE" /*"Greater Than Or Equal To" */),
  OPERATOR_LESS_THAN("LT", "~double.LT" /*"Less Than"*/),
  OPERATOR_LESS_THAN_EQUAL_TO("LTE", "~double.LTE" /*"Less Than Or Equal To"*/),
  EQUALS("EQ", "~double.EQ" /*"Equals"*/);

  private final String symbol;
  private final String description;

  public static Map<String, String> possibleOperatorValueMap() {

    Map<String, String> possibleOperatorValueMap = new HashMap<>();
    for (NumberOperator s : values()) {
      possibleOperatorValueMap.put(s.symbol, s.description);
    }
    return possibleOperatorValueMap;
  }

  public static NumberOperator getDefaultNumberOperator() {
    return NumberOperator.OPERATOR_GREATER_THAN_EQUAL_TO;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomAttributesUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.DmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.DmnModelElementInstance;
import org.camunda.bpm.model.dmn.instance.Input;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.InputExpression;
import org.camunda.bpm.model.dmn.instance.OutputEntry;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.camunda.bpm.model.dmn.instance.Text;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.text.MessageFormat;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.LinkedHashMap;


/**
 * This class updates DMN with decision inputs based on the rules supplied by the end users with the
 * help of template configuration for custom workflows
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CustomWorkflowDecisionHandler {
  private final CustomWorkflowConfig customWorkflowConfig;
  private final FeatureFlagManager featureFlagManager;

  /**
   * In case of FEEL expression, parameter name is not formatted like "${parameterName}"
   * @param inputName
   */
  private String getTextContent(String inputName, boolean useFeelExpr) {
    if (useFeelExpr) {
      return inputName;
    } else {
        return MessageFormat.format("{0}{1}{2}", WorkflowConstants.DMN_VAR_OPEN_BRACE, inputName,
                WorkflowConstants.DMN_VAR_CLOSE_BRACE);
    }
  }

  /**
   * Custom workflow DMN has only output defined. The decision inputs will be added dynamically from
   * the rules provided in the create/update definition.
   *
   * <p>Custom workflow DMN has only output defined. The inputs will be filled dynamically from the
   * rules provided in the create definition. Definition has one or more rulelines and each ruleline
   * has list of rules. A rule has conditionalExpression and parameterName. ParameterName will serve
   * as Label as well camunda input variable. Input expression will be created using template config
   * entry for corresponding attribute.
   *
   * @param dmnModelInstance {@link DmnModelInstance}
   * @param definition {@link Definition}
   * @return
   */

  public DmnModelInstance createDecisionInputs(
      DmnModelInstance dmnModelInstance, Definition definition, boolean useFeelExpr) {
    // Get the Decision element. This has the same id which is present in decisionRef of bpmn.
    Decision decision =
        dmnModelInstance.getModelElementsByType(Decision.class).stream().findFirst().orElse(null);
    if (ObjectUtils.isEmpty(decision)) {
      return null;
    }

    // Get attribute entries for the rules passed for the recordtype
    List<Attribute> ruleAttributes = getAttributesForRules(definition).stream()
              .collect(Collectors.toMap(Attribute::getId, Function.identity(), (p, q) -> p, LinkedHashMap::new))
              .values().stream().collect(Collectors.toList());

    // Get the decisionTable element
    DecisionTable decisionTable =
        dmnModelInstance.getModelElementsByType(DecisionTable.class).stream()
            .findFirst()
            .orElse(null);

    if (ObjectUtils.isEmpty(decisionTable)) {
      return null;
    }

    // Add decision Inputs
    addDecisionInputs(dmnModelInstance, decisionTable, ruleAttributes, useFeelExpr);
    // Prepare Default Row
    addEmptyRule(dmnModelInstance, decisionTable, ruleAttributes);
    return dmnModelInstance;
  }

  /**
   * This method returns the set of parameter names used in creating rules
   *
   * @param definition {@link Definition}
   * @return
   */
  private List<Attribute> getAttributesForRules(Definition definition) {
    List<String> parameterNames =
        CustomWorkflowUtil.getRulesFromDefinition(definition).stream()
            .map(RuleLine.Rule::getParameterName)
            .collect(Collectors.toList());

    // Return list of attributes using record attribute and global attribute
    Record record = customWorkflowConfig.getRecordObjForType(definition.getRecordType());
    Map<String, Attribute> nameToAttributeMapping =
        record.getAttributes().stream()
            .collect(Collectors.toMap(Attribute::getName, Function.identity()));

    // Getting the conventional predefined input parameter attribute mapping

    List<RuleLine.Rule> rules = CustomWorkflowUtil.getRulesFromDefinition(definition);
    List<Attribute> defaultAttributes = parameterNames.stream()
        .filter(nameToAttributeMapping::containsKey)
        .map(nameToAttributeMapping::get)
        .collect(Collectors.toList());

      /**
       * A function which takes rules as input and returns the list of custom fields attributes for
       * the custom field rules
       *
       * <p>For example -> "rules": [ { "parameterName": "TxnAmount", "conditionalExpression": "GTE
       * 100", "parameterType": "DOUBLE" }, { "parameterName": "3600000000000155715",
       * "conditionalExpression": "GTE 120", "parameterType": "DOUBLE" } ] It will be taking a list
       * of rules and creating custom field attributes which are appended with the default
       * attributes
       *
       * for example a generic DoubleCustomField :
       * Attribute(name:DoubleCustomField, id:DoubleCustomField, type:Double)
       * It will be transformed into
       * Attribute(name:CF3600000000000155715, id:CF3600000000000155715, type:Double)
       *
       */
      List<Attribute> customFieldAttributes =
          CustomAttributesUtil.getRulesForCustomAttributes(nameToAttributeMapping, rules);
          return Stream.of(defaultAttributes, customFieldAttributes).
                  flatMap(Collection::stream).collect(Collectors.toList());
  }

    /**
     * Add decision inputs in the DMN.
     *
     * @param modelInstance {@link DmnModelInstance} : DMN Model Instance
     * @param parentElement {@link DmnModelElementInstance} : Decision Table Element
     * @param conditionAttrs {@link List<Attribute>}: Metadata of Attributes
     */
    public void addDecisionInputs(DmnModelInstance modelInstance,
                                  DmnModelElementInstance parentElement,
                                  List<Attribute> conditionAttrs, boolean useFeelExpr) {
        AtomicInteger columnCounter = new AtomicInteger(1);

        //adding the columns for all the other attributes of payload in the dmn table
        conditionAttrs.forEach(
                conditionAttr -> {
                    addDecisionInput(columnCounter, conditionAttr.getName(),
                            conditionAttr.getType(),
                            modelInstance, parentElement, useFeelExpr);
                    columnCounter.getAndIncrement();
                });
    }

    /**
     * This function is used to add decision input(dmn column header) in the dmn table
     *
     * Input has different fields:- <br>
     *      Label - Attribute's name <br>
     *      inputVariable (Camunda variable) - Attribute's name <br>
     *      inputExpression's text - Attribute's id
     *
     * <p>A input looks like this
     *      *
     *      * <pre>{@code
     *      * <input camunda:inputVariable="TxnAmount" id="input_1" label="TxnAmount">
     *      *   <inputExpression id="inputExpression_1" typeRef="double">
     *      *     <text>${amount}</text>
     *      *   </inputExpression>
     *      * </input>
     *      * }</pre>
     *
     * @param counter dmn table column index at which this column header will be added
     * @param inputName dmn table column header name
     * @param inputType dmn table column header data type
     * @param modelInstance {@link DmnModelInstance}
     * @param parentElement {@link DmnModelElementInstance}
     */
    public void addDecisionInput(AtomicInteger counter,
                                 String inputName, String inputType,
                                 DmnModelInstance modelInstance,
                                 DmnModelElementInstance parentElement,boolean useFeelExpr) {
        Input input =
                DmnProcessorUtil.createElementWithLabel(
                        modelInstance,
                        parentElement,
                        MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER, WorkflowConstants.INPUT, counter),
                        inputName,// Label
                        Input.class);
        input.setCamundaInputVariable(inputName.replace(WorkflowConstants.SPACE, StringUtils.EMPTY));

        // Create input expression -  <inputExpression id="inputExpression_1" typeRef="double">
        // <text>${TotalAmt}</text>
        InputExpression inputExpression =
                DmnProcessorUtil.createElement(
                        modelInstance,
                        input,
                        MessageFormat.format(
                                WorkflowConstants.MESSAGE_PLACE_HOLDER, WorkflowConstants.INPUT_EXPRESSION, counter),
                        null,
                        InputExpression.class);
        inputExpression.setTypeRef(inputType.toLowerCase());

    Text text = modelInstance.newInstance(Text.class);
    text.setTextContent(getTextContent(inputName, useFeelExpr));
    inputExpression.setText(text);
  }

  /**
   * Populate DMN with empty rule. This is done as the processing later looks for collection of
   * rule/InputEntry elements. This is to keep it consistent with pre-canned workflows' DMNs
   *
   * @param modelInstance {@link DmnModelInstance} : DMN Model Instance
   * @param parentElement {@link DmnModelElementInstance} : Decision Table Element
   * @param attributes {@link List<Attribute>}
   */
  private void addEmptyRule(DmnModelInstance modelInstance,
      DmnModelElementInstance parentElement, List<Attribute> attributes) {
    Rule rule =
        DmnProcessorUtil.createElement(
            modelInstance, parentElement, WorkflowConstants.DECISION_RULE, null, Rule.class);
    attributes.forEach(
        conditionAttr -> {
          InputEntry inputEntry =
              DmnProcessorUtil.createElement(
                  modelInstance,
                  rule,
                  MessageFormat.format(
                      WorkflowConstants.MESSAGE_PLACE_HOLDER,
                      WorkflowConstants.INPUT_ENTRY,
                      UUID.randomUUID().toString().substring(0, 6)),
                  null,
                  InputEntry.class);
          Text text = modelInstance.newInstance(Text.class);
          inputEntry.addChildElement(text);
        });

    // Prepare Output Entry.
    OutputEntry outputEntry = DmnProcessorUtil.createElement(
            modelInstance, rule,
            MessageFormat.format(
                WorkflowConstants.MESSAGE_PLACE_HOLDER,
                WorkflowConstants.OUTPUT_ENTRY,
                UUID.randomUUID().toString().substring(0, 6)),
            null,
            OutputEntry.class);
    Text text = modelInstance.newInstance(Text.class);
    outputEntry.addChildElement(text);
  }
}

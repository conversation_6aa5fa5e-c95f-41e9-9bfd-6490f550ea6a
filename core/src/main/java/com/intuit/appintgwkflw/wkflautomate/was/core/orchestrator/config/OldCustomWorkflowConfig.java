package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> <br>
 *     </>
 *     <p>Root of the Configuration File. This has multiple list of <br>
 *     </> 1. Supported Record Types [like Invoice, Bill, Estimates] <br>
 *     </> 2. supported Attributes [DMN Condition/ Conditiional Attributes] <br>
 *     </> 3. Operators [List of Supported Operators.] <br>
 *     </> 4. List of Actions/ CTAs supported.
 */
@Configuration
@Data
@ConfigurationProperties("template-config")
public class OldCustomWorkflowConfig extends CustomWorkflowConfigBase implements InitializingBean {

  /**
   * Consolidate records by doing a look up in template config and finding corresponding attributes
   * and actions. If anything is overridden in record, it will take higher precedence.
   *
   * <p>We are doing this to message the data for each request. One config is changes, this map is
   * recreated.
   */
  @Override
  public void afterPropertiesSet() {
    populateRecordConfig();
    populateTemplateConfig();
  }



}

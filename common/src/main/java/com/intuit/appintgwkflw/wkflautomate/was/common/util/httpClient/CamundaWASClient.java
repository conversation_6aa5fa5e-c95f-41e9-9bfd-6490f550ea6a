package com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient;

import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceName.CAMUNDA;
import static com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.ServiceType.HTTP;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.ServiceMetric;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.RetryHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Component;
import io.github.resilience4j.retry.annotation.Retry;

/**
 * Http client used to call Camunda api's. This has retry & circuitBreaker capabilities for Camunda
 *
 * <AUTHOR>
 */
@Retry(name = ResiliencyConstants.CAMUNDA)
@Component
public class CamundaWASClient {

  @Autowired
  private WASHttpClient client;

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP, methodName = "GET")
  public <T> WASHttpResponse<T> getResponse(
      final String url,
      @SuppressWarnings("rawtypes") final HttpEntity requestEntity,
      final Class<T> responseType) {

    return this.getResponse(url, requestEntity, responseType, RetryHandlerName.STATUS_CODE);
  }

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP, methodName = "POST")
  public <T> WASHttpResponse<T> postResponse(
      final String url, final HttpEntity<?> requestEntity, final Class<T> responseType) {

    return this.postResponse(url, requestEntity, responseType, RetryHandlerName.OPTIMISTIC_LOCK);
  }

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest) {
    return httpResponse(wasHttpRequest, RetryHandlerName.OPTIMISTIC_LOCK);
  }

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP)
  public <REQUEST, RESPONSE> WASHttpResponse<RESPONSE> httpResponse(
      final WASHttpRequest<REQUEST, RESPONSE> wasHttpRequest, RetryHandlerName retryHandlerName) {
    wasHttpRequest.setRetryHandler(retryHandlerName);
    return client.httpResponse(wasHttpRequest);
  }

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP, methodName = "GET")
  public <T> WASHttpResponse<T> getResponse(
    final String url, @SuppressWarnings("rawtypes") final HttpEntity requestEntity,
    final Class<T> responseType, final RetryHandlerName retryHandler) {

    return client.getResponse(url, requestEntity, responseType, retryHandler);
  }

  @ServiceMetric(serviceName = CAMUNDA, type = HTTP, methodName = "POST")
  public <T> WASHttpResponse<T> postResponse(
    final String url, final HttpEntity<?> requestEntity, final Class<T> responseType,
    final RetryHandlerName retryHandler) {

    return client.postResponse(url, requestEntity, responseType, retryHandler);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DefinitionId;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.WorkflowStepCondition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.ByteArrayInputStream;
import java.util.Collections;
import java.util.List;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.CONDITION_ID_XOR;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.MAPPED_KEY_XOR;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@RunWith(SpringRunner.class)
public class ExclusiveGatewayHandlerTest {

  @Mock private ExclusiveGatewayHandler exclusiveGatewayHandler;

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapprovalTest.bpmn");

  private Definition definition = TestHelper.mockDefinitionEntity();

  @Test
  public void testProcess_Success() {
    RuleLine ruleLineXOR =
        new RuleLine()
            .mappedActionKey(MAPPED_KEY_XOR)
            .rule(new RuleLine.Rule().conditionalExpression(Boolean.TRUE.toString()));
    WorkflowStepCondition workflowStepCondition =
        new WorkflowStepCondition()
            .id(TestHelper.getGlobalId(CONDITION_ID_XOR))
            .ruleLine(ruleLineXOR);
    BpmnModelInstance modelInstance =
        Bpmn.readModelFromStream(new ByteArrayInputStream(BPMN_XML.getBytes()));
    List list = Collections.EMPTY_LIST;
    DefinitionId definitionId =
        DefinitionId.builder().realmId(DefinitionTestConstants.REALM_ID).uniqueId("123").build();
    doCallRealMethod()
        .when(exclusiveGatewayHandler)
        .process(
            any(WorkflowStepCondition.class),
            any(BpmnModelInstance.class),
            anyList(),
            any(DefinitionId.class),
            any(Definition.class), eq(false));
    exclusiveGatewayHandler.process(
        workflowStepCondition, modelInstance, list, definitionId, definition, false);
    verify(exclusiveGatewayHandler, times(1))
        .process(workflowStepCondition, modelInstance, list, definitionId, definition, false);
  }
}

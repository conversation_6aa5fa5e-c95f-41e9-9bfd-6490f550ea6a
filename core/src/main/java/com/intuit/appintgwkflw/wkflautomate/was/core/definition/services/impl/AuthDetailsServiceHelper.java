package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.*;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.AuthHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.AuthDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AuthDetailsServiceHelper {
  private final AuthDetailsRepository authDetailsRepository;
  private final AppConnectService appConnectService;
  private final AuthHelper authHelper;

  /**
   * populate auth details in DB in sync manner
   *
   * @param authorization input authorization details
   * @param createSubscription if true creates the subscription if not present
   */
  public void populateAuthDetailsSync(
      final Authorization authorization, boolean createSubscription) {
    try {
      final State inputReq = new State();
      inputReq.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
      inputReq.addValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD, createSubscription);
      getAuthDetailsChain(inputReq).execute();
    } catch (final Exception exception) {
      WorkflowLogger.error(
              () ->
                      WorkflowLoggerRequest.builder()
                              .message("Error while trying to generate auth details, realmId=%s", authorization.getRealm())
                              .stackTrace(exception)
                              .downstreamComponentName(DownstreamComponentName.WAS)
                              .downstreamServiceName(DownstreamServiceName.POPULATE_AUTH_DETAILS));
      throw exception;
    }
  }

  /**
   * populate auth details in DB in sync manner will not create subscription if not present
   *
   * @param authorization input authorization details
   */
  public void populateAuthDetailsSync(Authorization authorization) {
    populateAuthDetailsSync(authorization, false);
  }

  /**
   * @param authorization
   */
  public void populateAuthDetails(final Authorization authorization) {
    try {
      final State inputReq = new State();
      inputReq.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
      getAuthDetailsChain(inputReq)
              .executeAsync();
    } catch (final Exception e) {
      WorkflowLogger.error(
              () ->
                      WorkflowLoggerRequest.builder()
                              .message("Error while trying to generate auth details, realmId=%s", authorization.getRealm())
                              .stackTrace(e)
                              .downstreamComponentName(DownstreamComponentName.WAS)
                              .downstreamServiceName(DownstreamServiceName.POPULATE_AUTH_DETAILS));
    }
  }


  /**
   * @param inputReq      input state request
   * @return ExecutionChain for populating auth details
   */
  private RxExecutionChain getAuthDetailsChain(final State inputReq) {
    return new RxExecutionChain(
            inputReq,
            new GetSubscriptionDetailsTask(appConnectService, authDetailsRepository, authHelper))
        .next(new SaveAuthDetailsWithoutTicketTask(authDetailsRepository));
  }
}

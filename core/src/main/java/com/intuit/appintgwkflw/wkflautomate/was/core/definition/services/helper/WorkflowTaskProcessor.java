package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Definitions;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.IntermediateCatchEvent;
import org.camunda.bpm.model.bpmn.instance.IntermediateThrowEvent;
import org.camunda.bpm.model.bpmn.instance.SendTask;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;

@UtilityClass
public class WorkflowTaskProcessor {

  public static final String SERVICE_TASK = "servicetask";
  public static final String SEND_TASK = "sendtask";

  public static final String EVENTS = "events";

  // Contains type of elements that will be processed and saved in Activity Tables
  private static List<Class> bpmnElementList =
      List.of(
          StartEvent.class,
          EndEvent.class,
          IntermediateThrowEvent.class,
          IntermediateCatchEvent.class,
          SendTask.class);

  /**
   * BPMN is parsed to identify tasks whose lifecycle needs to be maintained in WAS.
   *
   * @param bpmnModelInstance - {@link BpmnModelInstance} of bpmn file
   * @param activityIdToActivityDetailsMap - Map to store activity details
   */
  public static void processBPMNElementsForDomainEvents(BpmnModelInstance bpmnModelInstance,
                                                        Map<String, ActivityDetail> activityIdToActivityDetailsMap) {
    bpmnElementList
                .forEach(element -> processBPMNElements(bpmnModelInstance, element, activityIdToActivityDetailsMap));
  }

  /**
   * Legacy Code, will merge it with processBPMNElements method once domain events is enabled in all
   * environments
   *
   * @param bpmnModelInstance
   * @param activityIdToActivityDetailsMap
   */
  public static void processBPMNElementsForServiceTask(
        BpmnModelInstance bpmnModelInstance, Map<String, ActivityDetail> activityIdToActivityDetailsMap) {
    processBPMNElements(bpmnModelInstance, ServiceTask.class, activityIdToActivityDetailsMap);
  }

  /**
   * Processes the initial start event element in the BPMN model.
   *
   * @param bpmnModelInstance - {@link BpmnModelInstance} of bpmn file
   * @param activityIdToActivityDetailsMap - Map to store activity details
   */
  public static void processInitialStartEventElement(
      BpmnModelInstance bpmnModelInstance, Map<String, ActivityDetail> activityIdToActivityDetailsMap) {
    Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
    StartEvent initialStartEvent = BpmnStartElementUtil.fetchInitialStartEventForAProcess(startEvents);
    // For Call activities in expert-hiring, we may not have a initial start event to process, so just return in that case
    if(ObjectUtils.isEmpty(initialStartEvent)) {
        WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
                .message("Initial Start Event with startable events not found in the BPMN model"));
        return;
    }
    if (activityIdToActivityDetailsMap.containsKey(initialStartEvent.getId())) {
        WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
              .message("Initial Start Event was already processed in previous processing handlers"));
        return;
    }
    Map<String, String> extensionPropertiesMap = BpmnProcessorUtil.getMapOfCamundaProperties(initialStartEvent.getExtensionElements());
    processFlowElementForActivityDetail(initialStartEvent, activityIdToActivityDetailsMap, extensionPropertiesMap);
  }

  /**
   * Processes BPMN elements of a specific class type.
   *
   * @param bpmnModelInstance - {@link BpmnModelInstance} of bpmn file
   * @param classType - Class type of the BPMN element
   * @param activityIdToActivityDetailsMap - Map to store activity details
   */
  private static void processBPMNElements(
      BpmnModelInstance bpmnModelInstance, Class classType, Map<String, ActivityDetail> activityIdToActivityDetailsMap) {
    Definitions def = bpmnModelInstance.getDefinitions();
    Collection<? extends FlowElement> elements =
        def.getModelInstance().getModelElementsByType(classType);
    elements.stream()
        .filter(isExtensionElementsAvailable())
        .forEach(element -> populateActivityDetails(activityIdToActivityDetailsMap, element));
  }

  /**
   * Generic Method to cater all BPMN elements having either type or events configuration
   *
   * @param activityIdToActivityDetailsMap - Map to store activity details
   * @param flowElement - BPMN flow element
   */
  private static void populateActivityDetails(
      Map<String, ActivityDetail> activityIdToActivityDetailsMap, FlowElement flowElement) {
    try {
      Map<String, String> extensionPropertiesMap =
          BpmnProcessorUtil.getMapOfCamundaProperties(flowElement.getExtensionElements());

      if (!extensionPropertiesMap.containsKey(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE)
          && !extensionPropertiesMap.containsKey(EVENTS)) {
        return;
      }

      processFlowElementForActivityDetail(flowElement, activityIdToActivityDetailsMap, extensionPropertiesMap);

    } catch (RuntimeException ex) {
      throw new WorkflowGeneralException(WorkflowError.ERROR_PROCESSING_TASK_DETAILS, ex);
    }
  }

  /**
   * Processes a flow element to extract activity details and store them in the map.
   *
   * @param flowElement - BPMN flow element
   * @param activityIdToActivityDetailsMap - Map to store activity details
   * @param extensionPropertiesMap - Map of extension properties
  */
  private void processFlowElementForActivityDetail(FlowElement flowElement, Map<String, ActivityDetail> activityIdToActivityDetailsMap,
        Map<String, String> extensionPropertiesMap) {
      final TaskType type =
          ObjectUtils.isNotEmpty(
                  extensionPropertiesMap.get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE))
              ? TaskType.valueOf(
                  extensionPropertiesMap.get(ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_TYPE))
              : TaskType.MILESTONE;

      Map<String, Object> runtimeAttributes =
          new HashMap<>(
              BpmnProcessorUtil.getMapOfInputOutputParameters(flowElement.getExtensionElements()));

      WorkflowActivityAttributes workflowActivityAttributesForTasks =
          WorkflowActivityAttributes.builder()
              .modelAttributes(extensionPropertiesMap)
              .runtimeAttributes(runtimeAttributes)
              .build();

      WorkflowActivityAttributes workflowActivityAttributesForEvents =
          WorkflowActivityAttributes.builder().modelAttributes(extensionPropertiesMap).build();

      activityIdToActivityDetailsMap.put(flowElement.getId(),
          ActivityDetail.builder()
              .activityId(flowElement.getId())
              .activityName(
                  extensionPropertiesMap.computeIfAbsent(
                      ActivityConstants.EXTENSION_PROPERTY_ACTIVITY_NAME,
                      key -> flowElement.getName()))
              .activityType(flowElement.getElementType().getTypeName())
              .attributes(
                  ObjectConverter.toJson(
                      isTask(flowElement)
                          ? workflowActivityAttributesForTasks
                          : workflowActivityAttributesForEvents))
              .type(type)
              .build());
    }

  /**
   * Returns true if flowElement is a Task (Service Task/Send Task)
   *
   * @param flowElement
   * @return
   */
  private static boolean isTask(FlowElement flowElement) {
    return SEND_TASK.equalsIgnoreCase(flowElement.getElementType().getTypeName())
        || SERVICE_TASK.equalsIgnoreCase(flowElement.getElementType().getTypeName());
  }

  /**
   * Generic Predicate will cater all BPMN elements including taks and events
   *
   * @return
   */
  private static Predicate<? super FlowElement> isExtensionElementsAvailable() {
    return element ->
        null != element.getExtensionElements()
            && !CollectionUtils.isEmpty(
                element.getExtensionElements().getChildElementsByType(CamundaProperties.class));
  }
}

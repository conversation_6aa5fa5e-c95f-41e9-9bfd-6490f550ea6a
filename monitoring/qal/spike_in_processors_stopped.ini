[IPF - was-event-in - QAL - Spike in Processors Stopped]
alert_metric_name = rawsum(align(60s, sum, ratediff(ts("iks.intuit.dataexchange.pipelineframework.processor.exit.duration.seconds.count" and flow.status="ERROR" and processor.stop.processing.on.error="true" and namespace=appintgwkflw-wkflautomate-waseventin-usw2-qal))))
severity = SEVERE
threshold = 20
operator = >
alert_target_id = 
wait_before_alert = 5
additional_info =
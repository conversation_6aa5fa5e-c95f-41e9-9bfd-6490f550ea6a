package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadCompositeStepHandler;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import java.util.Map;
import org.springframework.stereotype.Component;

/**
 * This class has functions to handle the processing of a composite step for read definition
 *
 * <AUTHOR>
 */
@Component
public class CompositeStepBuilder extends ReadCompositeStepHandler {

  /**
   * This function adds the workflowStepConditon to the compositeStepIdToWorkflowStepConditionMap map to be
   * used for creation of composite step and links the compositeStep to the parentStep.
   *
   * @param nextObj
   * @param compositeStepIdToWorkflowStepConditionMap
   * @param compositeStepId
   * @param workflowStepCondition
   * @param parentStep
   */
  @Override
  public void addToMap(WorkflowStep.StepNext nextObj,
      Map<GlobalId, WorkflowStepCondition> compositeStepIdToWorkflowStepConditionMap,
      GlobalId compositeStepId, WorkflowStepCondition workflowStepCondition,
      WorkflowStep parentStep,
      GlobalId conditionStepId) {
    compositeStepIdToWorkflowStepConditionMap.put(compositeStepId, workflowStepCondition);

    // Will be null when there is just a WORKFLOWSTEP and no condition step
    if (parentStep != null) {
      parentStep.getNext().stream()
          .filter(step -> conditionStepId.toString().equalsIgnoreCase(step.getWorkflowStepId()))
          .findFirst().get().setWorkflowStepId(
              nextObj.getWorkflowStepId());
    }

  }
}

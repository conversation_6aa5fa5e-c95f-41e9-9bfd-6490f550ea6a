package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.annotations.Trace;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineRunTimeServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.TriggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.bpmn.BpmnStartElementUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.StartProcessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter.TransactionEntity;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class V3StartProcess {

  private BPMNEngineRunTimeServiceRest bpmnEngineRunTimeServiceRest;

  private V3RunTimeHelper runtimeHelper;
  private WASContextHandler contextHandler;

  /**
   * Instantiate the process from definitionDetails with v3TransactionEntity payload
   *
   * @param transactionEntity v3 transaction entity with event headers
   * @param definitionDetails definition with which process has to be instantiated
   * @param startEvents BPMN startEvents in the definitions
   * @return
   */
  @SuppressWarnings("unchecked")
  @Trace
  public Map<String, Object> startProcess(TransactionEntity transactionEntity,
      DefinitionDetails definitionDetails, Map<String, Object> initialStartEventExtensionPropertiesMap) {
    Map<String, Object> startProcessResult = new HashMap<>();
    List<String> startableEventTypesList = BpmnStartElementUtil.getStartableEventTypes(initialStartEventExtensionPropertiesMap);
    Optional<String> startableEventTypeOptional = startableEventTypesList
            .stream()
            .filter(
                startableEventType ->
                    startableEventType.equals(transactionEntity.getEntityChangeType()))
            .findAny();
    String businessKey = TriggerUtil.fetchBusinessKey(transactionEntity, contextHandler);
    if (startableEventTypeOptional.isPresent()) {
      WorkflowLogger.info(() ->
              WorkflowLoggerRequest.builder()
                      .className(this.getClass().getSimpleName())
                      .methodName("startProcess")
                      .message(
                              "Begin processing trigger message to start process recordId=%s recordType=%s entityChangeType=%s workflowType=%s definitionId=%s definitionCreatedByUserId=%s businessKey=%s",
                              transactionEntity.getEntityId(),
                              transactionEntity.getEntityType().toString(),
                              transactionEntity.getEntityChangeType(),
                              transactionEntity.getWorkflowType(),
                              definitionDetails.getDefinitionId(),
                              definitionDetails.getCreatedByUserId(),
                              businessKey
                      ).downstreamComponentName(DownstreamComponentName.CAMUNDA)
                      .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS));


      /** Fetch process variables details from entity section */
      Map<String, Object> entityObjMap = runtimeHelper.getEntityObjectMap(transactionEntity);

      // if entityObjMap is empty check details in global process variables
      final boolean isEntityEmpty = MapUtils.isEmpty(entityObjMap);
      if (isEntityEmpty) {
        entityObjMap = Optional.of(transactionEntity)
                .map(TransactionEntity::getVariables)
                .map(globalVar -> globalVar.getGlobal())
                .orElse(new HashMap<>());
      }
      populateEntityObjFromPayload(transactionEntity.getV3EntityPayload(),entityObjMap);

      Map<String, Object> processVariables = runtimeHelper.getVariablesMap(
              transactionEntity, entityObjMap, true, initialStartEventExtensionPropertiesMap, definitionDetails, !isEntityEmpty, null, !isEntityEmpty);

      StartProcessRequest startProcessRequest = new StartProcessRequest(getDefinitionId(definitionDetails),
              businessKey, (Map<String, Object>) processVariables.get(
                  WorkflowConstants.BPMN_DMN_VARIABLES));
      contextHandler.addKey(WASContextEnums.MESSAGE_EVENT, transactionEntity.getEntityChangeType());

      startProcessResult = bpmnEngineRunTimeServiceRest.startProcess(startProcessRequest);

    } else {
      WorkflowLogger.warn(() ->
              WorkflowLoggerRequest.builder()
                  .className(this.getClass().getSimpleName())
                  .methodName("startProcess")
                  .message("Can not start process for the recordId=%s entityChangeType=%s Incorrect startableEvents.",
                      transactionEntity.getEntityId(), transactionEntity.getEntityChangeType())
                  .downstreamComponentName(DownstreamComponentName.CAMUNDA)
                  .downstreamServiceName(DownstreamServiceName.CAMUNDA_START_PROCESS));
      startProcessResult.put(WorkflowConstants.ID, null);
    }
    return startProcessResult;
  }

  /**
   * This method populates the entityObjMap with values from the payload.
   * @param v3EntityPayload
   * @param entityObjMap
   */
  private void populateEntityObjFromPayload(Map<String, Object> v3EntityPayload, Map<String, Object> entityObjMap) {
    if((boolean)v3EntityPayload.getOrDefault(WorkflowConstants.ON_DEMAND_APPROVAL,false)) {
      entityObjMap.put(WorkflowConstants.ON_DEMAND_APPROVAL, v3EntityPayload.get(WorkflowConstants.ON_DEMAND_APPROVAL));
    }

    if(v3EntityPayload.containsKey(WorkflowConstants.INTUIT_WAS_LOCALE)){
      entityObjMap.put(WorkflowConstants.INTUIT_WAS_LOCALE, v3EntityPayload.get(WorkflowConstants.INTUIT_WAS_LOCALE));
    }
  }

  /**
   * Method to get the correct definitionId for USER and SINGLE definition.
   * @param definitionDetails
   * @return definitionId
   */
  private String getDefinitionId(DefinitionDetails definitionDetails) {
    boolean isSingleDefinition = definitionDetails.getTemplateDetails().getDefinitionType().equals(
        DefinitionType.SINGLE);
    return isSingleDefinition ? definitionDetails.getTemplateDetails().getDeployedDefinitionId() :
        definitionDetails.getDefinitionId();
  }

  /**
   * * Iterates through collection of start events of an BPMN and finds the startableEvents property
   * specifies in extension elements Builds a list events which can possibly instantiate process
   * from the definition
   *
   * @param startEvents collection of startEVents
   * @return
   */
  private List<String> getStartableEventTypes(Collection<StartEvent> startEvents) {
    List<String> startableEventTypes = new ArrayList<>();
    for (StartEvent startEvent : startEvents) {
      Map<String, String> properties = BpmnProcessorUtil
          .getMapOfCamundaProperties(startEvent.getExtensionElements());
      if (MapUtils.isEmpty(properties)) continue;
      String startableEventsDetailsData = properties
          .getOrDefault(WorkFlowVariables.PROCESS_STARTABLE_EVENTS_KEY.getName(), null);
      if (StringUtils.isEmpty(startableEventsDetailsData)) continue;
      startableEventTypes = ObjectConverter.fromJson(
              startableEventsDetailsData, new TypeReference<List<String>>() {});
    }
    WorkflowVerfiy
        .verify(startableEventTypes.isEmpty(), WorkflowError.TRIGGER_STARTABLE_EVENTS_DETAILS_NOT_FOUND);
    return startableEventTypes;
  }

}

package com.intuit.appintgwkflw.wkflautomate.was.common.config.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.FilterUtil;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.query.CompoundExpression;
import com.intuit.v4.query.Expression;
import com.intuit.v4.query.FilterExpression;
import org.junit.Assert;
import org.junit.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;

public class FilterUtilTest {

    @Test
    public void testCheckForQueryFilters() {
        QueryHelper queryHelper = TestHelper.mockQueryHelperWithFilterDefintionQuery();
        FilterExpression filterExpression = (FilterExpression) FilterUtil.getQueryFilterExpression(queryHelper);
        Assert.assertNotNull(filterExpression);
        Assert.assertEquals("in", filterExpression.getOp());
    }

    @Test
    public void testCheckForNoQueryFilters() {
        QueryHelper queryHelper = TestHelper.mockQueryHelperWithoutFilterDefintionQuery();
        FilterExpression filterExpression = (FilterExpression) FilterUtil.getQueryFilterExpression(queryHelper);
        Assert.assertNull(filterExpression);
    }

    @Test
    public void testCheckForQueryWithCompoundFilters() {
        QueryHelper queryHelper = TestHelper.mockQueryHelperWithCompoundFilterDefinitionQuery();
        Expression expression = FilterUtil.getQueryFilterExpression(queryHelper);
        Assert.assertNotNull(expression);
        assertTrue(expression instanceof CompoundExpression);
        Assert.assertEquals("&&", expression.getOp());
        CompoundExpression compoundExpression = (CompoundExpression) expression;
        Assert.assertEquals("in", compoundExpression.getArgs().get(0).getOp());
        Assert.assertEquals("in", compoundExpression.getArgs().get(1).getOp());
    }

    @Test
    public void testCheckForQueryWithSimpleFilters() {
        QueryHelper queryHelper = TestHelper.mockQueryHelperWithFilterDefintionQuery();
        Expression expression = FilterUtil.getQueryFilterExpression(queryHelper);
        Assert.assertNotNull(expression);
        assertTrue(expression instanceof FilterExpression);
        Assert.assertEquals("in", expression.getOp());
    }
}

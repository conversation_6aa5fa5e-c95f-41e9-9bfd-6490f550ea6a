package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.CallActivityOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import java.nio.charset.Charset;
import java.util.Collections;
import org.apache.commons.io.IOUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

/**
 * author btolani
 */
@RunWith(MockitoJUnitRunner.class)
public class CallActivityOutgoingActivityMapperTest {

  @InjectMocks
  private CallActivityOutgoingActivityMapper callActivityOutgoingActivityMapper;

  private BpmnModelInstance multiStepBpmnModelInstance;
  private BpmnModelInstance invalidMultiStepBpmnModelInstance;
  private DmnModelInstance dmnModelInstance;
  private Definition multiConditionDefinition;

  private static final String MULTI_STEP_INVALID_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiStep_invalid.bpmn");

  private static final String MULTI_STEP_WORKFLOW_BPMN_XML =
      TestHelper.readResourceAsString("bpmn/customApproval_multiStep.bpmn");

  private static final String CUSTOM_WORKFLOW_DMN_XML =
      TestHelper.readResourceAsString("dmn/customWorkflow.dmn");

  @Before
  public void setUp() {
    multiConditionDefinition = TestHelper.mockMultiConditionDefinitionEntity();
    multiStepBpmnModelInstance = Bpmn.readModelFromStream(
        IOUtils.toInputStream(MULTI_STEP_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    invalidMultiStepBpmnModelInstance = Bpmn.readModelFromStream(
        IOUtils.toInputStream(MULTI_STEP_INVALID_WORKFLOW_BPMN_XML, Charset.defaultCharset()));
    dmnModelInstance =
        Dmn.readModelFromStream(
            IOUtils.toInputStream(CUSTOM_WORKFLOW_DMN_XML, Charset.defaultCharset()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_fetchOutgoingActivityIds_throwsException() {
    DefinitionInstance definitionInstance = new DefinitionInstance(multiConditionDefinition,
        invalidMultiStepBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());
    String businessRuleTaskId = ((StartEvent) CustomWorkflowUtil.findStartEventElement(
        invalidMultiStepBpmnModelInstance))
        .getOutgoing().stream().findFirst().get().getTarget().getId();
    String callActivityId = "action-1";
    callActivityOutgoingActivityMapper.fetchOutgoingActivityIds(callActivityId, definitionInstance);
  }

  @Test
  public void test_fetchOutgoingActivityIds() {
    DefinitionInstance definitionInstance = new DefinitionInstance(multiConditionDefinition,
        multiStepBpmnModelInstance,
        Collections.singletonList(dmnModelInstance), new TemplateDetails());
    String businessRuleTaskId = ((StartEvent) CustomWorkflowUtil.findStartEventElement(
        multiStepBpmnModelInstance))
        .getOutgoing().stream().findFirst().get().getTarget().getId();

    ((BusinessRuleTask) multiStepBpmnModelInstance.getModelElementById(
        businessRuleTaskId)).getOutgoing()
        .stream().filter(flowElement -> flowElement.getTarget() instanceof CallActivity)
        .forEach(flowElement ->
            Assert.notEmpty(callActivityOutgoingActivityMapper.fetchOutgoingActivityIds(
                flowElement.getTarget().getId(), definitionInstance)));

  }
}

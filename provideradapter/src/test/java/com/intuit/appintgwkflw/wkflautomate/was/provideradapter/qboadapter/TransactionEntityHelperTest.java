package com.intuit.appintgwkflw.wkflautomate.was.provideradapter.qboadapter;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;

import java.util.HashMap;
import java.util.Map;

import org.junit.Assert;
import org.junit.Test;

/** <AUTHOR> */
public class TransactionEntityHelperTest {

  @Test(expected = WorkflowGeneralException.class)
  public void testEmpty() {
    TransactionEntityHelper.getEventHeaders(new HashMap<>());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testNull() {
    TransactionEntityHelper.getEventHeaders(null);
  }

  @Test
  public void testEventHeaders() {
    Assert.assertNotNull(TransactionEntityHelper.getEventHeaders(getTriggerPayload()));
  }

  private Map<String, Object> getTriggerPayload() {
    Map<String, Object> trigger = new HashMap<>();
    Map<String, Object> eventHeaders = new HashMap<>();
    eventHeaders.put("workflow", "approval");
    eventHeaders.put("entityType", RecordType.INVOICE);
    eventHeaders.put("entityChangeType", "created");
    eventHeaders.put("entityId", "35");
    trigger.put("eventHeaders", eventHeaders);
    return trigger;
  }
}

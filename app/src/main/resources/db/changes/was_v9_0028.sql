-- Adding columns to track when the definition was initially created and by whom. Existing created_at
-- column is tracking row level updates and it is data loss for the customer as we are not
-- able to find the original created date of the definition as it gets updated as the
-- old definition becomes STALE

ALTER TABLE was.de_definition_details
   ADD COLUMN IF NOT EXISTS original_setup_date timestamp,
   ADD COLUMN IF NOT EXISTS original_setup_user bigint;
package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.custom;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.RepositoryConstants;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsCustomRepository;
import org.javatuples.Pair;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.Optional;

/**
 * <AUTHOR>
 *     <p>Custom Repository Class for Process Details</>
 */
public class ProcessDetailsCustomRepositoryImpl implements ProcessDetailsCustomRepository {

  @PersistenceContext(name = DataAccessConstants.OFFERING_AWARE_EM)
  private EntityManager entityManager;

  @Override
  public Optional<ProcessDetails> getProcessDetailsWithoutDefinitionData(String processId) {
    Pair<CriteriaQuery<ProcessDetails>, Root<ProcessDetails>> criteriaQueryRootPair =
        getCriteriaForAllProcesses(processId);

    prepareSelectVariablesWithoutPlaceholderValues(
        criteriaQueryRootPair.getValue1(), criteriaQueryRootPair.getValue0());

    ProcessDetails result;
    try {
      result = entityManager.createQuery(criteriaQueryRootPair.getValue0()).getSingleResult();
    } catch (final NoResultException ex) {
      result = null;
    }
    return Optional.ofNullable(result);
  }

  /**
   * Prepares criteria for all the process
   *
   * @param processId
   * @return
   */
  private Pair<CriteriaQuery<ProcessDetails>, Root<ProcessDetails>> getCriteriaForAllProcesses(
      String processId) {
    CriteriaBuilder criteriaBuilder = entityManager.getCriteriaBuilder();
    CriteriaQuery<ProcessDetails> criteriaQuery = criteriaBuilder.createQuery(ProcessDetails.class);
    Root<ProcessDetails> detailsRoot = criteriaQuery.from(ProcessDetails.class);
    Predicate processIdPredicate =
        criteriaBuilder.equal(detailsRoot.get(RepositoryConstants.PROCESS_INSTANCE_ID), processId);
    criteriaQuery.where(processIdPredicate);
    return new Pair<>(criteriaQuery, detailsRoot);
  }

  /**
   * Custom Query to fetch without definition data and lookup keys.
   *
   * @param detailsRoot
   * @param criteriaQuery
   */
  private void prepareSelectVariablesWithoutPlaceholderValues(
      Root<ProcessDetails> detailsRoot, CriteriaQuery<ProcessDetails> criteriaQuery) {
    criteriaQuery.multiselect(
        detailsRoot.get(RepositoryConstants.PROCESS_INSTANCE_ID),
        detailsRoot.get(RepositoryConstants.PROCESS_STATUS),
        detailsRoot.get(RepositoryConstants.INTERNAL_STATUS),
        detailsRoot.get(RepositoryConstants.OWNER_ID),
        detailsRoot.get(RepositoryConstants.RECORD_ID));
  }
}

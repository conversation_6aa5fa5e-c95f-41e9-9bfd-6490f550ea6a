package com.intuit.appintgwkflw.wkflautomate.was.core.jira.service;


import com.intuit.appintgwkflw.wkflautomate.was.common.config.JiraConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.WASHttpClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.jira.helper.JiraServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.request.JiraRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.jira.response.JiraResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 */

@RunWith(MockitoJUnitRunner.class)
public class JiraServiceManagerTest {

  @InjectMocks
  private JiraServiceManager jiraServiceManager;
  @Mock
  private JiraConfig vocJiraConfig;
  @Mock
  private WASHttpClient jiraHttpClient;

  @Test
  public void createJiraSuccess() {
    JiraResponse jiraResponse = new JiraResponse();
    Mockito.when(jiraHttpClient.httpResponse(Mockito.any()))
        .thenReturn(WASHttpResponse.builder().isSuccess2xx(true).status(HttpStatus.CREATED)
            .response(jiraResponse).build());
    jiraServiceManager.createJira(JiraRequest.builder().build());
    Mockito.verify(jiraHttpClient, Mockito.times(1)).httpResponse(Mockito.any());
  }

  @Test
  public void createJiraSuccessWithCustomFielf() {
    JiraResponse jiraResponse = new JiraResponse();
    Mockito.when(jiraHttpClient.httpResponse(Mockito.any()))
        .thenReturn(WASHttpResponse.builder().isSuccess2xx(true).status(HttpStatus.CREATED)
            .response(jiraResponse).build());
    jiraServiceManager.createJira(JiraRequest.builder().customfield_13505("test").customfield_17002("test").build());
    Mockito.verify(jiraHttpClient, Mockito.times(1)).httpResponse(Mockito.any());
  }

  @Test
  public void createJiraFailed() {
    Mockito.when(jiraHttpClient.httpResponse(Mockito.any()))
        .thenReturn(
            WASHttpResponse.builder().isSuccess2xx(false).status(HttpStatus.INTERNAL_SERVER_ERROR)
                .response(null).response("Jira failed").build());
    try {
      jiraServiceManager.createJira(JiraRequest.builder().build());
      Assert.fail("Unexpected results");
    } catch (WorkflowGeneralException workflowGeneralException) {
      Assert.assertTrue(
          workflowGeneralException.getWorkflowError() == WorkflowError.JIRA_API_CALL_FAILURE);
    }

    Mockito.verify(jiraHttpClient, Mockito.times(1)).httpResponse(Mockito.any());
  }

  @Test
  public void testDueDateNull() {
    String date = JiraServiceHelper.getDueDate(null);
    Assert.assertNull(date);
  }


  @Test
  public void testSignleDigitDueDate() {
    String date = JiraServiceHelper.getDueDate(SLACK_MESSAGE_TEXT);
    Assert.assertNotNull(date);
    Assert.assertEquals("2024-05-03", date);

  }
  @Test
  public void testDueDateWithoutTime() {
    String date = JiraServiceHelper.getDueDate(SLACK_MESSAGE_TEXT_WITH_DATE);
    Assert.assertEquals("2024-05-12", date);
  }

  @Test
  public void testDueDateWithoutTimeSingleDigit() {
    String date = JiraServiceHelper.getDueDate(SLACK_MESSAGE_TEXT_WITH_DATE_SINGLE_DIGIT);
    Assert.assertEquals("2024-05-02", date);
  }

  private final String SLACK_MESSAGE_TEXT = "From : Workflow Survey\n"
      + "User Realm ID : 9130355314946946\n"
      + "Name: Noah asdasdasdasda\n"
      + "Company Email : <EMAIL>\n"
      + "Region : CA\n"
      + "SKU: ADVANCED\n"
      + "Date : May 3, 2024 13:22:32\n"
      + "---------------------------------------------\n"
      + "Rating : 9\n"
      + "Survey Question : Do you have any suggestions for new templates?\n"
      + "Response: Create the ability to send Sales Reciepts to customers much like how Invoices can be sent after their creation through a workflow automation. This would assist in \"sending reciepts\" to customers after using a POS system and asking for email reciepts.";

  private final String BOLD_RESPONSE_SLACK_MESSAGE_TEXT =
      "From: QBO Advanced Workflows Creation CES Survey\n"
          + "User Realm ID : ***************\n"
          + "Name: Rick Copeland\n"
          + "Company Email : <mailto:<EMAIL>|<EMAIL>>\n"
          + "Region : US\n"
          + "Date : July 20, 2024\n"
          + "---------------------------------------------\n"
          + "&gt; QuickBooks made it easy for me to set up a workflow (out of 5) : 1 \n"
          + "*Survey Question* : What can we improve?\n"
          + "*Response* : We are a very small nonprofit with a staff of only 2 - the ED and the Bookkeeper/Accountant. The Bookeeper does ALL the bookkeeping functions EXCEPT for approve payments. The ED does not know QBO, so the Bookkeeper need to set up a workflow for use by the ED, so that she is the sole approver. THE TRICK IS - The Bookkeeper needs to set it up in such a way that he DOES NOT HAVE PERMISSION TO CHANGE OR OVERRIDE IT. So that we are protected from this avenue of internal fraud.";

  private final String EMPTY_RESPONSE_SLACK_MESSAGE_TEXT =
      "From: QBO Advanced Workflows Creation CES Survey\n"
          + "User Realm ID : ***************\n"
          + "Name: Rick Copeland\n"
          + "Company Email : <mailto:<EMAIL>|<EMAIL>>\n"
          + "Region : US\n"
          + "Date : July 20, 2024\n"
          + "---------------------------------------------\n"
          + "&gt; QuickBooks made it easy for me to set up a workflow (out of 5) : 1 \n"
          + "*Survey Question* : What can we improve?\n"
          + "Response:";


  private final String SLACK_MESSAGE_TEXT_WITH_DATE = "From : Workflow Survey\n"
      + "User Realm ID : 9130355314946946\n"
      + "Name: Noah asdasdasdasda\n"
      + "Company Email : <EMAIL>\n"
      + "Region : CA\n"
      + "SKU: ADVANCED\n"
      + "Date : May 12, 2024\n"
      + "---------------------------------------------\n"
      + "Rating : 9\n"
      + "Survey Question : Do you have any suggestions for new templates?\n"
      + "Response: Create the ability to send Sales Reciepts to customers much like how Invoices can be sent after their creation through a workflow automation. This would assist in \"sending reciepts\" to customers after using a POS system and asking for email reciepts.";

  private final String SLACK_MESSAGE_TEXT_WITH_DATE_SINGLE_DIGIT = "From : Workflow Survey\n"
      + "User Realm ID : 9130355314946946\n"
      + "Name: Noah asdasdasdasda\n"
      + "Company Email : <EMAIL>\n"
      + "Region : CA\n"
      + "SKU: ADVANCED\n"
      + "Date : May 2, 2024\n"
      + "---------------------------------------------\n"
      + "Rating : 9\n"
      + "Survey Question : Do you have any suggestions for new templates?\n"
      + "Response: Create the ability to send Sales Reciepts to customers much like how Invoices can be sent after their creation through a workflow automation. This would assist in \"sending reciepts\" to customers after using a POS system and asking for email reciepts.";
}


package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PlaceholderUserVariableExtractorType;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

/**
 * This extractor will get user variables associated with a particular activity from the
 * placeholders in case of NON multi-step process e.g. These are the placeholders of a definition
 * "user_variables": { "approval:createTask": { "selected": true, "parameters": { "Assignee": {
 * "fieldValue": [ "9130358018375316" ] }, "TaskName": { "fieldValue": [ "Approval due for Invoice
 * [[DocNumber]]" ] } } } Output map will be {"Assignee":"9130358018375316", "TaskName":"Approval
 * due for Invoice [[DocNumber]]"} This map will be added as part of run time attributes in domain
 * event mapper for activityRunTimeEvent
 */

@Component
@AllArgsConstructor
public class DefaultPlaceholderUserVariableExtractor implements PlaceholderUserVariableExtractor {

  @Override

  /**
   *
   * @param definitionDetails
   * @param activityId (ActivityId as defined in User variable map)
   * @return List of attributes which hold the user variables associated with particular taskID
   * Parse all user variables associated with the definition and fetch parameters of appropriate actionId in attributes
   */
  public Map<String, String> getUserVariablesForActivity(
      ActivityProgressDetails activityProgressDetails, Map<String, String> runtimeAttributes) {

    DefinitionDetails definitionDetails = activityProgressDetails.getProcessDetails()
        .getDefinitionDetails();
    String activityId = getActivityId(activityProgressDetails.getId());

    Map<String, Object> userVariableMap = Optional.ofNullable(
            definitionDetails.getPlaceholderValue())
        .map(placeholder -> (Map<String, Object>) ObjectConverter.fromJson(placeholder,
            new TypeReference<Map<String, Object>>() {
            }))
        .map(placeholderMap -> (Map<String, Object>) placeholderMap.get(
            WorkflowConstants.USER_VARIABLES)).orElse(Collections.emptyMap());

    return populateUserVariableMap(userVariableMap, activityId);
  }

  private Map<String, String> populateUserVariableMap(Map<String, Object> userVariables,
      String activityId) {
    Map<String, String> resultMap = new HashMap<>();
    userVariables.entrySet().stream()
        //Only fetch the variables associated with the current activityId
        .filter(action -> action.getKey().contains(activityId))
        .forEach(action -> {
              //Fetch all parameters and create field variable map
              Map<String, String> fieldVariableMap = new HashMap<>();

              Optional.ofNullable(action.getValue())
                  .map(actionValue -> (Map<String, Object>) actionValue)
                  .map(actionData -> (Map<String, Object>) actionData.get(WorkflowConstants.PARAMETERS))
                  .orElse(Collections.emptyMap())
                  .entrySet()
                  .stream()
                  .filter(
                      paramEntry -> ((Map<String, Object>) Optional.ofNullable(paramEntry.getValue())
                          .orElse(Collections.emptyMap())).containsKey(WorkflowConstants.FIELD_VALUE))
                  .forEach(paramEntry -> {
                    List<String> fieldValues = (List<String>) ((Map<String, Object>) Optional.ofNullable(
                        paramEntry.getValue()).orElse(Collections.emptyMap())).get(
                        WorkflowConstants.FIELD_VALUE);
                    Optional.ofNullable(fieldValues)
                        .flatMap(fieldValue -> fieldValue.stream().findFirst()).ifPresent(value -> {
                          fieldVariableMap.put(paramEntry.getKey(), value);
                        });
                  });

              if (MapUtils.isNotEmpty(fieldVariableMap)) {
                resultMap.putAll(fieldVariableMap);
              }
            }
        );
    return resultMap;
  }

  private String getActivityId(String activityProgressDetailsId) {
    String activityId = activityProgressDetailsId.split(WorkflowConstants.COLON)[0];
    return activityId;
  }

  @Override
  public PlaceholderUserVariableExtractorType getName() {
    return PlaceholderUserVariableExtractorType.DEFAULT;
  }


}
package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import java.util.Collections;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

public class SaveDefinitionInDataStoreTaskTest {

  private SaveDefinitionInDataStoreTask saveDefinitionInDataStoreTask;

  private DefinitionServiceHelper definitionServiceHelper =
      Mockito.mock(DefinitionServiceHelper.class);

  private State state = new State();

  private Authorization authorization = new Authorization();

  private DefinitionInstance definitionInstance = Mockito.mock(DefinitionInstance.class);

  private ArgumentCaptor<DeployDefinitionResponse> deployDefinitionResponseArgumentCaptor;

  @Before
  public void setUp() {
    deployDefinitionResponseArgumentCaptor = ArgumentCaptor.forClass(DeployDefinitionResponse.class);
    authorization.putRealm("realm");
    authorization.putAuthId("auth");
    saveDefinitionInDataStoreTask =
        new SaveDefinitionInDataStoreTask(
            definitionServiceHelper, definitionInstance, authorization, false);
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, "realm");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_RESPONSE_KEY,
        Mockito.mock(DeployDefinitionResponse.class));
  }

  @Test
  public void testExecute() {
    saveDefinitionInDataStoreTask.execute(state);
    Mockito.verify(definitionServiceHelper).saveUpdateDefinitionDetails(
        any(DeployDefinitionResponse.class), eq(definitionInstance), eq(authorization), eq(false));
  }

  @Test
  public void testExecute_SingleDefinition() {
    state.addValue(AsyncTaskConstants.IS_SINGLE_DEFINITION, true);
    BpmnModelInstance bpmnModelInstance = Mockito.mock(BpmnModelInstance.class);
    Process process = Mockito.mock(Process.class);
    Mockito.when(definitionInstance.getBpmnModelInstance()).thenReturn(bpmnModelInstance);
    Mockito.when(bpmnModelInstance.getModelElementsByType(eq(Process.class))).thenReturn(
        Collections.singletonList(process));
    Mockito.when(process.getId()).thenReturn("bpmn_realmId_uuid");
    saveDefinitionInDataStoreTask.execute(state);
    Mockito.verify(definitionServiceHelper).saveUpdateDefinitionDetails(
        deployDefinitionResponseArgumentCaptor.capture(), eq(definitionInstance), eq(authorization),
        eq(false));
    Assert.assertNotNull(deployDefinitionResponseArgumentCaptor.getValue());
    Assert.assertNotNull(
        deployDefinitionResponseArgumentCaptor.getValue().getDeployedProcessDefinitions());
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import org.junit.After;
import org.junit.Assert;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;
import uk.org.webcompere.systemstubs.rules.EnvironmentVariablesRule;

/**
 * <AUTHOR>
 * Class to hold UTs for DeploymentUtil.
 */

@RunWith(SpringRunner.class)
public class DeploymentUtilTest {
  @Rule
  public EnvironmentVariablesRule environmentVariablesRule = new EnvironmentVariablesRule();

  @After
  public void cleanUp() {
    System.clearProperty(DeploymentUtil.APP_NAME);
  }

  @Test
  public void testInvokeForAppPod() {
    environmentVariablesRule.set(DeploymentUtil.APP_NAME, DeploymentUtil.WORKFLOW_APP_SERVICE_NAME);
    Assert.assertTrue(DeploymentUtil.isAppDeploymentPod());
  }

  @Test
  public void testInvokeForChaosPod() {
    environmentVariablesRule.set(DeploymentUtil.APP_NAME, "chaos");
    Assert.assertFalse(DeploymentUtil.isAppDeploymentPod());
  }

  @Test
  public void testInvokeForNullPod() {
    Assert.assertFalse(DeploymentUtil.isAppDeploymentPod());
  }
}
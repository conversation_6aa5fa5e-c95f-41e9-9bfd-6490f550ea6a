package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Action;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.EntityActionGroupSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.EntitySet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Handler;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Parameter;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ParameterOverrideSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedMultiCondSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedParamSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.PreCannedSet;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.v4.workflows.StepTypeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * This class consolidates attributes and actions for each record type. For each record, we figure
 * out the supported attributes and actions and populate them from the list of global attributes and
 * actions. If record has overridden anything corresponding to an attribute or action, it will take
 * precedence.
 */
@UtilityClass
public class CustomWorkflowConfigUtil {

  public void populateRecordConfig(CustomWorkflowConfigBase customWorkflowConfig) {
    populateRecordAttributes(customWorkflowConfig);
    populateRecordActions(customWorkflowConfig);
  }

  public void populateTemplateConfig(CustomWorkflowConfigBase customWorkflowConfig) {
    populateTemplateAttributes(customWorkflowConfig);
    populateTemplateActions(customWorkflowConfig);
  }

  /**
   * For each record, populate copy of attributes supported by the record. If an attribute field is
   * overridden in the record, it will take precedence
   */
  private void populateRecordAttributes(CustomWorkflowConfigBase customWorkflowConfig) {
    // Create map of attributes from the config with attribute id as key and attribute object
    // as value
    Map<String, Attribute> globalAttributeMap =
        customWorkflowConfig.getAttributes().stream()
            .collect(Collectors.toMap(Attribute::getId, Function.identity()));

    for (Record record : customWorkflowConfig.getRecords()) {
      // Override the properties defined in global attribute from entity's attribute. Any property
      // defined in record attribute takes higher precedence.
      record.getAttributes().stream()
          .forEach(
              recordAttribute -> {
                if (globalAttributeMap.containsKey(recordAttribute.getId())) {
                  copyFromAttribute(
                      globalAttributeMap.get(recordAttribute.getId()), recordAttribute);
                }
              });
    }
  }

  public static void copyFromAttribute(Attribute src, Attribute target) {
    if (ObjectUtils.isEmpty(target.getId())) {
      target.setId(src.getId());
    }
    if (ObjectUtils.isEmpty(target.getName())) {
      target.setName(src.getName());
    }
    if (ObjectUtils.isEmpty(target.getType())) {
      target.setType(src.getType());
    }
    if (ObjectUtils.isEmpty(target.getDefaultOperator())) {
      target.setDefaultOperator(src.getDefaultOperator());
    }
    if (ObjectUtils.isEmpty(target.getDefaultValue())) {
      target.setDefaultValue(src.getDefaultValue());
    }
    if (ObjectUtils.isEmpty(target.getMultiSelect())) {
      target.setMultiSelect(src.getMultiSelect());
    }
    if (ObjectUtils.isEmpty(target.getRequired())) {
      target.setRequired(src.getRequired());
    }
    if (ObjectUtils.isEmpty(target.getConfigurable())) {
      target.setConfigurable(src.getConfigurable());
    }
    if (ObjectUtils.isEmpty(target.getFieldValueOptions())) {
      target.setFieldValueOptions(src.getFieldValueOptions());
    }
    if (ObjectUtils.isEmpty(target.getUnsupportedOperators())) {
      target.setUnsupportedOperators(new ArrayList<>(src.getUnsupportedOperators()));
    }
    if (ObjectUtils.isEmpty(target.getHidden())) {
      target.setHidden(src.getHidden());
    }
    if (ObjectUtils.isEmpty(target.getObfuscate())) {
      target.setObfuscate(src.getObfuscate());
    }
    if (ObjectUtils.isEmpty(target.getControllerFF())) {
      target.setControllerFF(src.getControllerFF());
    }
  }

  /**
   * For each record, populate copy of actions supported by the record. If an action parameter is
   * overridden in the record, it will take precedence
   *
   * <pre>
   *   Parameter field value priority (decreasing order)
   *   1. Defined for record action parameter
   *   2. Defined at global action parameter
   *   3. Defined in global parameter list
   *
   *   for example for parameter Subject -> value in parameter list: subject1, value in global action
   *   parameter: subject2, value in record action parameter: subject3 -> The final parameter value for
   *   record action will be subject3
   *
   *   TODO: Define required parameters at handler level in the config
   * </pre>
   */
  private void populateRecordActions(CustomWorkflowConfigBase customWorkflowConfig) {
    populateGlobalActionParams(customWorkflowConfig);
    populateRecordActionHandler(customWorkflowConfig);

    // Map of actions from the config with action id as key and action object as value
    Map<String, Action> globalActionMap =
        customWorkflowConfig.getActions().stream()
            .collect(Collectors.toMap(Action::getId, Function.identity()));

    // Iterate over all actions for each record
    customWorkflowConfig.getRecords().stream()
        .filter(record -> ObjectUtils.isNotEmpty(record.getActionGroups()))
        .map(Record::getActionGroups)
        .flatMap(Collection::stream)
        .forEach(
            recordActionGroup -> {
              // Get relevant actions for action group
              List<Action> globalActions =
                  getActionIdsForGroup(customWorkflowConfig, recordActionGroup).stream()
                      .map(globalActionMap::get)
                      .collect(Collectors.toList());

              // Get actions for a record
              List<Action> recordActions = recordActionGroup.getActions();

              // Global parameter map. Key -> ParameterName, value -> Parameter
              Map<String, Parameter> globalParamMap =
                  customWorkflowConfig.getParameters().stream()
                      .collect(Collectors.toMap(Parameter::getName, Function.identity()));

              // Override global actions with fields defined in record actions
              List<Action> mergedGlobalActions =
                  populateRecordActions(recordActions, globalActions, globalParamMap);

              // If actionIdMapper exists for a record action group, then create the parent action
              // sub-action structure
              // and add it to the mergedGlobalActions list and set it as part of record
              // actions.This call activity action
              // will be returned as part of templateData during read definition/template
              if (Objects.nonNull(recordActionGroup.getActionIdMapper())
                  && Objects.nonNull(recordActionGroup.getActionIdMapper().getActionId())) {
                populateParentAndSubAction(
                    customWorkflowConfig,
                    globalParamMap,
                    globalActionMap,
                    mergedGlobalActions,
                    recordActionGroup,
                    recordActionGroup.getActionIdMapper().getActionId());
              }
              // Update record action group with consolidated actions
              recordActionGroup.setActions(mergedGlobalActions);
            });

    addHelpVariablesInActionParameters(customWorkflowConfig.getRecords());
    replaceTokensInParameters(customWorkflowConfig);
  }

  /**
   * For each record's action group, create an action object with subactions basis actionIdMapper
   * (i.e a call activity building block) defined in the custom workflow config
   *
   * <p>For Example, assume the following config defined - actionIdMapper: actionId: sendForApproval
   * -> will be converted to parent action, values defined at global and record level will be merged
   * subActionIds: - createTask - sendCompanyEmail - sendPushNotification
   *
   * <p>actionIds defined in the subActionIds list will be converted to subActions, these will also
   * be merged basis values defined at global and record level. These actions are returned as part
   * of templateSteps
   *
   * <p>The above call activity building block will be converted to a record action like follows -
   * action: { name: "Send For Approval" id:
   * "djQuMTo5MTMwMzU4Mzk0NTM1Nzk2OmQ2NTUzY2E4NTI:sendForApproval" parameters: {} subActions: [{
   * name: "Create a task" id: "djQuMTo5MTMwMzU4Mzk0NTM1Nzk2OmQ2NTUzY2E4NTI:createTask" parameters:
   * {} required: true selected: false }, { name: "Send a Company Email" id:
   * "djQuMTo5MTMwMzU4Mzk0NTM1Nzk2OmQ2NTUzY2E4NTI:sendCompanyEmail" parameters: {} required: true
   * selected: false }] }
   *
   * @param customWorkflowConfig
   * @param globalParams
   * @param globalActionMap
   * @param recordActions
   * @param recordActionGroup
   * @param parentActionId
   */
  private void populateParentAndSubAction(
      CustomWorkflowConfigBase customWorkflowConfig,
      Map<String, Parameter> globalParams,
      Map<String, Action> globalActionMap,
      List<Action> recordActions,
      ActionGroup recordActionGroup,
      String parentActionId) {

    Action tempParentAction = new Action();
    tempParentAction.setId(parentActionId);
    Action parentAction = recordActionGroup.getActions().stream().filter(action -> parentActionId.equalsIgnoreCase(
        action.getId())).findFirst().orElse(tempParentAction);

    Action mergedParentAction =
        populateRecordActions(
                Arrays.asList(globalActionMap.get(parentActionId)),
                Arrays.asList(parentAction),
                globalParams)
            .get(0);

    List<Action> workflowSubActions = new ArrayList();
    // If subActionIds list is not defined under actionIdMapper in the config, then override them
    // with record actionIds
    // or globally defined action whichever is applicable
    List<String> subActionIds =
        ObjectUtils.isNotEmpty(recordActionGroup.getActionIdMapper().getSubActionIds())
                && recordActionGroup.getActionIdMapper().getSubActionIds().size() > 0
            ? recordActionGroup.getActionIdMapper().getSubActionIds()
            : getActionIdsForGroup(customWorkflowConfig, recordActionGroup);

    recordActions.stream()
        .forEach(
            action -> {
              if (subActionIds.contains(action.getId())) {
                workflowSubActions.add((action));
              }
            });
    mergedParentAction.setSubActions(workflowSubActions);
    recordActions.add(mergedParentAction);
  }

  // Replace record tokens with recordType. for example in case of invoice, %Record% becomes Invoice
  // and %record% becomes bill
  private void replaceTokensInParameters(CustomWorkflowConfigBase customWorkflowConfig) {
    BiFunction<Record, String, String> replaceFn =
        (record, fieldValue) ->
            fieldValue
                .replace(
                    "%Record%",
                    StringUtils.capitalize(RecordType.fromType(record.getId()).getDisplayValue()))
                .replace(
                    "%record%",
                    RecordType.fromType(record.getId()).getDisplayValue().toLowerCase());

    customWorkflowConfig
        .getRecords()
        .forEach(
            record ->
                record.getActionGroups().stream()
                    .map(ActionGroup::getActions)
                    .flatMap(Collection::stream)
                    .map(Action::getParameters)
                    .flatMap(Collection::stream)
                    .filter(parameter -> ObjectUtils.isNotEmpty(parameter.getFieldValues()))
                    .forEach(
                        parameter -> {
                          List<String> fieldValues = parameter.getFieldValues();
                          List<String> replacedFieldValues =
                              fieldValues.stream()
                                  .map(fieldValue -> replaceFn.apply(record, fieldValue))
                                  .collect(Collectors.toList());
                          parameter.setFieldValues(replacedFieldValues);
                        }));
  }

  // Populate fields from global parameters to action parameters. We will not
  // replace fields which are defined under action parameter
  private void populateGlobalActionParams(CustomWorkflowConfigBase customWorkflowConfig) {
    // Create map of global parameter list for faster access of parameters.
    // key -> parameterId, value -> Parameter
    Map<String, Parameter> globalParamMap =
        customWorkflowConfig.getParameters().stream()
            .collect(Collectors.toMap(Parameter::getId, Function.identity()));

    // For each global action, copy action's parameter fields from global parameter. Field value
    // defined under action takes higher precedence
    customWorkflowConfig.getActions().stream()
        .forEach(
            action ->
                action.getParameters().stream()
                    .forEach(
                        parameter -> {
                          if (globalParamMap.containsKey(parameter.getId())) {
                            copyParameterFields(globalParamMap.get(parameter.getId()), parameter);
                          }
                        }));
  }

  /**
   * Populate handler details in record action and global action based on handlerId
   *
   * <p>Example:
   *
   * <p>Record: - id: bill - actionGroup: - id : approval - action: - id: createTask handler: id:
   * was-create-task-and-add-constraint ----> recordActionHandler - given high precedence Actions: -
   * id: createTask - handler: id: was- create-task ----> globalActionHandler - low precedence
   */
  private void populateRecordActionHandler(CustomWorkflowConfigBase customWorkflowConfig) {
    customWorkflowConfig.getActions().stream()
        .forEach(
            action -> {
              setHandlerInAction(customWorkflowConfig.getHandlers(), action);
            });
    /**
     * set handler details in record action if there is handler id in action the record action
     * handlers are given more precedence compared to global action handler
     */
    customWorkflowConfig.getRecords().stream()
        .forEach(
            record ->
                record.getActionGroups().stream()
                    .forEach(
                        actionGroup -> {
                          populateTriggerDetailsInActionGroup(
                              customWorkflowConfig.getHandlers(), actionGroup);
                          actionGroup.getActions().stream()
                              .forEach(
                                  action ->
                                      setHandlerInAction(
                                          customWorkflowConfig.getHandlers(), action));
                        }));
  }

  private void populateTriggerDetailsInActionGroup(
      List<Handler> handlers, ActionGroup actionGroup) {
    Trigger trigger = actionGroup.getTrigger();
    if (trigger == null || trigger.getHandler() == null) {
      return;
    }

    final Handler triggerHandler = trigger.getHandler();

    final Handler configHandlerRecord =
        handlers.stream()
            .filter(handler -> handler.getId().equalsIgnoreCase(triggerHandler.getId()))
            .findFirst()
            .orElse(null);
    if (configHandlerRecord != null) {
      HandlerDetails configHandlerDetails = configHandlerRecord.getHandlerDetail();
      if (triggerHandler.getHandlerDetail() == null) {
        triggerHandler.setHandlerDetail(new HandlerDetails());
      }

      mergeHandlerDetails(configHandlerDetails, triggerHandler.getHandlerDetail());
    }
  }

  // Merge handler details properties from source to target. It will not override the
  // properties though
  private void mergeHandlerDetails(HandlerDetails src, HandlerDetails target) {
    if (ObjectUtils.isEmpty(target.getHandlerId())) {
      target.setHandlerId(src.getHandlerId());
    }
    if (ObjectUtils.isEmpty(target.getTaskHandler())) {
      target.setTaskHandler(src.getTaskHandler());
    }
    if (ObjectUtils.isEmpty(target.getActionName())) {
      target.setActionName(src.getActionName());
    }
    if (ObjectUtils.isEmpty(target.getResponseFields())) {
      target.setResponseFields(src.getResponseFields());
    }
  }

  /** populate handler details from global handlers to action handler */
  private void setHandlerInAction(List<Handler> handlers, Action action) {
    if (ObjectUtils.isNotEmpty(action.getHandler())) {
      String handlerId = action.getHandler().getId();
      if (StringUtils.isNotBlank(handlerId)) {
        Handler handler = CustomWorkflowUtil.getHandler(handlers, handlerId);
        if (handler != null) {
          action.getHandler().setHandlerDetail(handler.getHandlerDetail());
          action.getHandler().setDuzzitRestHandlerDetail(handler.getDuzzitRestHandlerDetail());
        }
      }
    }
  }

  private void copyParameterFields(Parameter src, Parameter target) {
    if (ObjectUtils.isEmpty(target.getId())) {
      target.setId(src.getId());
    }
    if (ObjectUtils.isEmpty(target.getName())) {
      target.setName(src.getName());
    }
    if (ObjectUtils.isEmpty(target.getFieldType())) {
      target.setFieldType(src.getFieldType());
    }
    if (ObjectUtils.isEmpty(target.getPossibleFieldValues())) {
      target.setPossibleFieldValues(new ArrayList<>(src.getPossibleFieldValues()));
    }
    if (ObjectUtils.isEmpty(target.getHandlerFieldName())) {
      target.setHandlerFieldName(src.getHandlerFieldName());
    }
    if (ObjectUtils.isEmpty(target.getConfigurable())) {
      target.setConfigurable(src.getConfigurable());
    }
    if (ObjectUtils.isEmpty(target.getRequiredByHandler())) {
      target.setRequiredByHandler(src.getRequiredByHandler());
    }
    if (ObjectUtils.isEmpty(target.getRequiredByUI())) {
      target.setRequiredByUI(src.getRequiredByUI());
    }
    if (ObjectUtils.isEmpty(target.getHelpVariablesRequired())) {
      target.setHelpVariablesRequired(src.getHelpVariablesRequired());
    }
    if (ObjectUtils.isEmpty(target.getHelpVariables())) {
      target.setHelpVariables(new ArrayList<>(target.getHelpVariables()));
    }
    if (ObjectUtils.isEmpty(target.getMultiSelect())) {
      target.setMultiSelect(src.getMultiSelect());
    }
    if (ObjectUtils.isEmpty(target.getActionByUI())) {
      target.setActionByUI(src.getActionByUI());
    }
    if (ObjectUtils.isEmpty(target.getFieldValues())) {
      target.setFieldValues(new ArrayList<>(target.getFieldValues()));
    }
    if (ObjectUtils.isEmpty(target.getValueType())) {
      target.setValueType(src.getValueType());
    }
    if (ObjectUtils.isEmpty(target.getComputedVariableType())) {
      target.setComputedVariableType(src.getComputedVariableType());
    }
    if (ObjectUtils.isEmpty(target.getComputedVariableDetails())) {
      target.setComputedVariableDetails(src.getComputedVariableDetails());
    }
    if (ObjectUtils.isEmpty(target.getGetOptionsForFieldValue())) {
      target.setGetOptionsForFieldValue(src.getGetOptionsForFieldValue());
    }
    if (ObjectUtils.isEmpty(target.getObfuscate())) {
      target.setObfuscate(src.getObfuscate());
    }
    if (ObjectUtils.isEmpty(target.getIsOverridable())) {
      target.setIsOverridable(src.getIsOverridable());
    }
  }

  /**
   * Get list of action ids for a given action group
   *
   * @param recordActionGroup
   * @return
   */
  private List<String> getActionIdsForGroup(
      CustomWorkflowConfigBase customWorkflowConfig, ActionGroup recordActionGroup) {
    if (ObjectUtils.isNotEmpty(recordActionGroup.getActionIds())) {
      // If action group Ids are defined along with record, return them. For example bill
      // supports only create task and internal email for reminder
      return recordActionGroup.getActionIds();
    }

    ActionGroup globalActionGroup =
        customWorkflowConfig.getActionGroups().stream()
            .filter(group -> group.getId().equalsIgnoreCase(recordActionGroup.getId()))
            .findFirst()
            .orElse(null);
    if (globalActionGroup != null) {
      // Fetch all action ids for action group
      return globalActionGroup.getActionIds();
    }
    // TODO: Throw exception if no action found for an action group
    return new LinkedList<>();
  }

  /**
   * Override global actions with fields defined in record actions
   *
   * @param globalActions global actions specified in config
   * @param recordActions actionIds specified under record in config
   * @param globalParamMap
   */
  private List<Action> populateRecordActions(
      List<Action> recordActions,
      List<Action> globalActions,
      Map<String, Parameter> globalParamMap) {
    List<Action> actionList = new ArrayList<>();
    globalActions.stream()
        .filter(Objects::nonNull)
        .forEach(
            globalAction -> {
              // Create a clone of global action as we don't want to use the original action object
              // which is shared
              Action clonedGlobalAction =
                  copyFromSourceAction(globalAction, new Action(), Collections.emptyMap());

              Action recordAction = getRecordActionForId(recordActions, globalAction.getId());
              // If record doesn't have action overridden, use the global action as it is
              if (null == recordAction) {
                actionList.add(clonedGlobalAction);
                return;
              }

              // If record has overridden action, merge the fields from record action to global
              // action
              Action fullyPopulatedRecordAction =
                  copyFromSourceAction(recordAction, clonedGlobalAction, globalParamMap);
              actionList.add(fullyPopulatedRecordAction);
            });
    return actionList;
  }

  /**
   * Add help variables in action parameters A list of help variables can be defined at record level
   * or at individual parameter. If parameter has helpVariablesRequired field in parameter, we will
   * add all the helpvariables defined for the record. If helpvariables are defined at record, only
   * these help variables will be applied for that parameter
   *
   * @param records
   */
  private void addHelpVariablesInActionParameters(List<? extends Record> records) {
    Predicate<Parameter> helpVarsPredicate =
        parameter -> BooleanUtils.toBoolean(parameter.getHelpVariablesRequired());

    records.forEach(
        record ->
            getRecordActions(record).stream()
                .forEach(
                    action ->
                        action.getParameters().stream()
                            .filter(helpVarsPredicate)
                            .forEach(
                                parameter ->
                                    parameter.setHelpVariables(
                                        mergeAndRemoveDuplicates(
                                            parameter.getHelpVariables(),
                                            record.getHelpVariables(),
                                            action.getHelpVariables())))));
  }

  /*
   * 1. Record help variables
   * 2. actionGroup help variables (not implemented)
   * 3. action help variables
   * 4. parameter help variables
   *
   * 3 and 4 are NULL: return record help variables
   * 4 is NULL: merge 1 and 3 and return
   * 3 and 4 are not NULL: merge 1 and 3. then merge it with 4 .
   * */
  private List<String> mergeAndRemoveDuplicates(
      List<String> parameterHelpVariables,
      List<String> recordHelpVariables,
      List<String> actionHelpVariables) {

    // If no help variables are defined at parameter or action level, return record help variables
    if (ObjectUtils.isEmpty(parameterHelpVariables) && ObjectUtils.isEmpty(actionHelpVariables)) {
      return recordHelpVariables;
    }
    // If no help variables are defined at action level, merge record and parameter help variables
    if (ObjectUtils.isEmpty(actionHelpVariables)) {
      return mergeList(recordHelpVariables, parameterHelpVariables);
    }
    // if no help variables are defined at parameter level, merge record and action help variables
    if (ObjectUtils.isEmpty(parameterHelpVariables)) {
      return mergeList(recordHelpVariables, actionHelpVariables);
    }
    // If help variables are defined at all levels, merge them
    List<String> mergedlist = mergeList(recordHelpVariables, actionHelpVariables);
    return mergeList(mergedlist, parameterHelpVariables);
  }

  private List<String> mergeList(List<String> src, List<String> dest) {
    Set<String> mergedlist = src.stream().collect(Collectors.toSet());
    mergedlist.addAll(dest);
    return new ArrayList<>(mergedlist);
  }

  // Utility method to get actions for a record
  private List<Action> getRecordActions(Record record) {
    return record.getActionGroups().stream()
        .map(ActionGroup::getActions)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  private Action getRecordActionForId(List<Action> recordActions, String actionId) {
    if (ObjectUtils.isNotEmpty(recordActions)) {
      return recordActions.stream()
          .filter(t -> actionId.equalsIgnoreCase(t.getId()))
          .findFirst()
          .orElse(null);
    }
    return null;
  }

  /**
   * @param src Object which takes precedence
   * @param target
   * @param globalParamMap
   * @return
   */
  private Action copyFromSourceAction(
      Action src, Action target, Map<String, Parameter> globalParamMap) {
    if (src != null) {
      target.setId(src.getId());
      if (ObjectUtils.isNotEmpty(src.getName())) {
        target.setName(src.getName());
      }
      if (ObjectUtils.isNotEmpty(src.getHandler())) {
        target.setHandler(src.getHandler());
      }
      if (ObjectUtils.isNotEmpty((src.getHelpVariables()))) {
        target.setHelpVariables(src.getHelpVariables());
      }
      if (ObjectUtils.isNotEmpty(src.getParameters())) {
        target.setParameters(
            mergeParameterList(src.getParameters(), target.getParameters(), globalParamMap));
      }
      // Adding fields for action is selected or not
      if (ObjectUtils.isNotEmpty(src.getSelected())) {
        target.setSelected(src.getSelected());
      }
      // Adding fields for action that is mandatory
      if (ObjectUtils.isNotEmpty(src.getRequired())) {
        target.setRequired(src.getRequired());
      }

      // Adding fields for action that is mandatory
      if (ObjectUtils.isNotEmpty(src.getObfuscate())) {
        target.setObfuscate(src.getObfuscate());
      }
    }
    return target;
  }

  /**
   * Merge parameter from source to target
   *
   * @param srcParams
   * @param targetParams
   * @param globalParamMap
   * @return
   */
  private List<Parameter> mergeParameterList(
      List<Parameter> srcParams,
      List<Parameter> targetParams,
      Map<String, Parameter> globalParamMap) {
    // Create a map of parameter name and parameter object
    Map<String, Parameter> clonedTargetParamMap =
        targetParams.stream()
            .map(targetParam -> copyParameter(targetParam, new Parameter()))
            .collect(Collectors.toMap(Parameter::getName, Function.identity()));

    for (Parameter srcParam : srcParams) {
      // If target parameter list has the source parameter, merge the fields from source to target
      if (clonedTargetParamMap.containsKey(srcParam.getName())) {
        copyParameter(srcParam, clonedTargetParamMap.get(srcParam.getName()));
      } else {
        // If a source parameter is not part of target param, take the parameter from global param
        // list, clone it and merge the field from source
        Parameter targetParameter = new Parameter();
        if (globalParamMap.containsKey(srcParam.getName())) {
          copyParameter(globalParamMap.get(srcParam.getName()), targetParameter);
        }
        clonedTargetParamMap.put(srcParam.getName(), copyParameter(srcParam, targetParameter));
      }
    }
    return new ArrayList<>(clonedTargetParamMap.values());
  }

  /**
   * Copy source parameter fields to a target object
   *
   * @param src
   * @param dest
   * @return
   */
  private Parameter copyParameter(Parameter src, Parameter dest) {
    if (ObjectUtils.isNotEmpty(src.getName())) {
      dest.setName(src.getName());
    }
    if (ObjectUtils.isNotEmpty(src.getFieldType())) {
      dest.setFieldType(src.getFieldType());
    }
    if (ObjectUtils.isNotEmpty(src.getHandlerFieldName())) {
      dest.setHandlerFieldName(src.getHandlerFieldName());
    }
    if (ObjectUtils.isNotEmpty(src.getIsOverridable())) {
      dest.setIsOverridable(src.getIsOverridable());
    }
    if (ObjectUtils.isNotEmpty(src.getConfigurable())) {
      dest.setConfigurable(src.getConfigurable());
    }
    if (ObjectUtils.isNotEmpty(src.getRequiredByHandler())) {
      dest.setRequiredByHandler(src.getRequiredByHandler());
    }
    if (ObjectUtils.isNotEmpty(src.getRequiredByUI())) {
      dest.setRequiredByUI(src.getRequiredByUI());
    }
    if (ObjectUtils.isNotEmpty(src.getRequiredByUI())) {
      dest.setRequiredByUI(src.getRequiredByUI());
    }
    if (ObjectUtils.isNotEmpty(src.getActionByUI())) {
      dest.setActionByUI(src.getActionByUI());
    }
    if (ObjectUtils.isNotEmpty(src.getMultiSelect())) {
      dest.setMultiSelect(src.getMultiSelect());
    }
    if (ObjectUtils.isNotEmpty(src.getValueType())) {
      dest.setValueType(src.getValueType());
    }
    if (ObjectUtils.isNotEmpty(src.getComputedVariableType())) {
      dest.setComputedVariableType(src.getComputedVariableType());
    }
    if (ObjectUtils.isNotEmpty(src.getComputedVariableDetails())) {
      dest.setComputedVariableDetails(src.getComputedVariableDetails());
    }
    if (ObjectUtils.isNotEmpty(src.getGetOptionsForFieldValue())) {
      dest.setGetOptionsForFieldValue(src.getGetOptionsForFieldValue());
    }
    if (ObjectUtils.isNotEmpty(src.getHelpVariablesRequired())) {
      dest.setHelpVariablesRequired(src.getHelpVariablesRequired());
    }
    if (ObjectUtils.isNotEmpty(src.getObfuscate())) {
      dest.setObfuscate(src.getObfuscate());
    }

    if (ObjectUtils.isNotEmpty(src.getHelpVariables())) {
      List<String> helpVariables = new ArrayList<>();
      helpVariables.addAll(dest.getHelpVariables());
      helpVariables.addAll(src.getHelpVariables());
      dest.setHelpVariables(helpVariables);
    }

    if (ObjectUtils.isNotEmpty(src.getPossibleFieldValues())) {
      List<String> possibleFieldValues = new ArrayList<>();
      possibleFieldValues.addAll(src.getPossibleFieldValues());
      dest.setPossibleFieldValues(possibleFieldValues);
    }

    if (ObjectUtils.isNotEmpty(src.getFieldValues())) {
      List<String> fieldValue = new ArrayList<>();
      fieldValue.addAll(src.getFieldValues());
      dest.setFieldValues(fieldValue);
    }

    return dest;
  }

  /**
   * It will consolidate all the attributes required for the template
   *
   * @param customWorkflowConfig
   */
  private void populateTemplateAttributes(CustomWorkflowConfigBase customWorkflowConfig) {

    for (ConfigTemplate template : customWorkflowConfig.getConfigTemplates()) {
      Record record = customWorkflowConfig.getRecordObjForType(template.getRecord());

      // Map of Attribute id and respective object for a particular Template
      Map<String, Attribute> defaultAttributeMap =
          template.getAttributes().stream()
              .collect(Collectors.toMap(Attribute::getId, Function.identity()));

      final List<Attribute> templateAttributes = new ArrayList<>();
      record.getAttributes().stream()
          .forEach(
              recordAttribute -> {
                Attribute templateAttribute = recordAttribute;
                if (defaultAttributeMap.containsKey(recordAttribute.getId())) {
                  // Get the Attribute from the Map and pass it on to the method.
                  templateAttribute = defaultAttributeMap.get(recordAttribute.getId());

                  copyFromAttribute(recordAttribute, templateAttribute);
                }
                templateAttributes.add(templateAttribute);
              });

      // if precanned template has steps defined then we will merge the attributes defined at
      // each step level with record level attributes
      if (!CollectionUtils.isEmpty(template.getSteps())) {
        populateStepLevelAttributes(customWorkflowConfig, template);
      }
      // Setting Default Attributes
      template.setDefaultAttributes(defaultAttributeMap.keySet());
      // Setting the attributes
      template.setAttributes(templateAttributes);
      // Setting HelpVariables based on Record Type
      template.setHelpVariables(record.getHelpVariables());
    }
  }

  /**
   * This function merges all properties of record level attributes with step level attributes
   * defined in custom config and precanned config
   *
   * @param customWorkflowConfig custom workflow config
   * @param template precanned template with steps defined
   */
  private void populateStepLevelAttributes(
      CustomWorkflowConfigBase customWorkflowConfig, ConfigTemplate template) {
    Record record = customWorkflowConfig.getRecordObjForType(template.getRecord());
    template.getSteps().stream()
        .forEach(
            configStep -> {
              if (!CollectionUtils.isEmpty(configStep.getAttributes())) {
                configStep.getAttributes().stream()
                    .forEach(
                        stepAttribute -> {
                          Attribute recordAttribute =
                              record.getAttributes().stream()
                                  .filter(
                                      attribute ->
                                          attribute.getId().equalsIgnoreCase(stepAttribute.getId()))
                                  .findFirst()
                                  .orElse(null);
                          if (Objects.nonNull(recordAttribute)) {
                            // copy all values from record level attribute to step level attribute
                            copyFromAttribute(recordAttribute, stepAttribute);
                          }
                        });
              }
            });
  }

  private void populateTriggerDetailsInConfigTemplate(
      Trigger precannedTrigger, Trigger customTrigger) {
    if (Objects.isNull(precannedTrigger.getHandler())
        || Objects.isNull(precannedTrigger.getHandler().getHandlerDetail())) {
      precannedTrigger.setHandler(customTrigger.getHandler());
    } else {
      mergeHandlerDetails(
          customTrigger.getHandler().getHandlerDetail(),
          precannedTrigger.getHandler().getHandlerDetail());
    }
    Map<String, Parameter> triggerParameterMap =
        precannedTrigger.getParameters().stream()
            .collect(Collectors.toMap(Parameter::getName, Function.identity()));
    List<Parameter> mergedParameters =
        mergeParameterList(
            precannedTrigger.getParameters(), customTrigger.getParameters(), triggerParameterMap);
    precannedTrigger.setParameters(mergedParameters);
  }

  private void populateTemplateActions(CustomWorkflowConfigBase customWorkflowConfig) {

    // Iterate over all action groups for each Template
    for (ConfigTemplate configTemplate : customWorkflowConfig.getConfigTemplates()) {

      // Every Template Will have only 1 ActionGroup (For instance, either a reminder or an approval
      // template)
      String templateActionGroupId =
          configTemplate.getActionGroups().stream().findFirst().map(ActionGroup::getId).get();
      Record record = customWorkflowConfig.getRecordObjForType(configTemplate.getRecord());
      // Create map of actions from the config with action id as key and action object as
      // value
      Map<String, Action> actionIdToActionMap =
          record.getActionGroups().stream()
              .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(templateActionGroupId))
              .map(ActionGroup::getActions)
              .flatMap(List::stream)
              .collect(Collectors.toMap(Action::getId, Function.identity()));

      // ActionGroup entity based on parent record of template(For ex, Bill, Invoice etc.)
      ActionGroup actionGroupEntity =
          record.getActionGroups().stream()
              .filter(actionGroup -> actionGroup.getId().equalsIgnoreCase(templateActionGroupId))
              .findFirst()
              .get();

      for (ActionGroup actionGroup : configTemplate.getActionGroups()) {
        /** Here processing actionGroup based on Template type : {Reminder / Approval} etc */
        if (ObjectUtils.isNotEmpty(actionGroup)
            && actionGroup.getId().equalsIgnoreCase(templateActionGroupId)) {
          List<String> actionsIdsForGroup = getActionIdsForGroup(customWorkflowConfig, actionGroup);

          List<Action> actionsForGroup =
              actionsIdsForGroup.stream()
                  .filter(actionId -> ObjectUtils.isNotEmpty(actionIdToActionMap.get(actionId)))
                  .map(actionIdToActionMap::get)
                  .collect(Collectors.toList());
          // Get actions for a record
          List<Action> recordActions = actionGroup.getActions();

          // Override global actions with fields defined in record actions
          actionsForGroup =
              populateRecordActions(recordActions, actionsForGroup, Collections.emptyMap());

          // Update record action group with consolidated actions
          actionGroup.setActions(actionsForGroup);

          // Set unsupported attribute list
          actionGroup.setUnsupportedAttributes(actionGroupEntity.getUnsupportedAttributes());

          // Set Default attribute list specific to ActionGroup
          actionGroup.setDefaultAttributes(actionGroupEntity.getDefaultAttributes());

          // Set Trigger in the action group of precanned template
          if (Objects.isNull(actionGroup.getTrigger())) {
            actionGroup.setTrigger(actionGroupEntity.getTrigger());
          } else {
            populateTriggerDetailsInConfigTemplate(
                actionGroup.getTrigger(), actionGroupEntity.getTrigger());
          }
        }
      }
      // popluate parent action and sub-actions within configTemplate object
      populateConfigTemplateParentAndSubActions(
          actionGroupEntity, customWorkflowConfig, configTemplate);
    }
  }

  /**
   * For each record's action group, create an action object with sub-actions basis actionIdMapper
   * (i.e a call activity building block) defined in the custom workflow config
   *
   * @param actionGroupEntity actionGroup entity based on parent record of template(For ex, Bill,
   *     Invoice etc.)
   * @param customWorkflowConfig custom workflow config object
   * @param configTemplate config template object (pre-canned template)
   */
  private void populateConfigTemplateParentAndSubActions(
      ActionGroup actionGroupEntity,
      CustomWorkflowConfigBase customWorkflowConfig,
      ConfigTemplate configTemplate) {
    if (Objects.nonNull(actionGroupEntity.getActionIdMapper())
        && Objects.nonNull(actionGroupEntity.getActionIdMapper().getActionId())) {

      Map<String, Parameter> globalParams =
          customWorkflowConfig.getParameters().stream()
              .collect(Collectors.toMap(Parameter::getName, Function.identity()));

      Map<String, Action> customConfigGlobalActions =
          customWorkflowConfig.getActions().stream()
              .collect(Collectors.toMap(Action::getId, Function.identity()));

      // set the actionIdMapper within ConfigTemplate object here itself so that it can be
      // referred from here instead of the record object
      configTemplate.getActionGroups().stream()
          .findFirst()
          .get()
          .setActionIdMapper(actionGroupEntity.getActionIdMapper());

      populateParentAndSubAction(
          customWorkflowConfig,
          globalParams,
          customConfigGlobalActions,
          configTemplate.getActionGroups().stream().findFirst().get().getActions(),
          actionGroupEntity,
          actionGroupEntity.getActionIdMapper().getActionId());
    }
  }

  /**
   * @param customConfigV2
   * @returns Map<entity, EntityActionGroups> for example ("invoice", <"reminder", "approval" ,
   *     "send" >) these entity-groups have all the related information of entity-actionGroup
   *     binding ex (enabledActions, enabledAttributes, defaultAttributes etc)
   */
  public Map<String, List<EntityActionGroupSet>> getActionGroups(CustomConfigV2 customConfigV2) {
    return customConfigV2.getEntityActionGroupSet().stream()
        .collect(Collectors.groupingBy(EntityActionGroupSet::getEntityId));
  }

  /**
   * @param customConfigV2
   * transforms the entities in the custom config to create in list<records> to support
   * for templates
   * */

  public void createRecordFromConfig(CustomConfigV2 customConfigV2) {
    Map<String, List<EntityActionGroupSet>> entiyActionGroupMap = getActionGroups(customConfigV2);

    customConfigV2.getRecords().addAll(
        customConfigV2.getEntitySet().stream()
            .map(entitySet -> createEntityRecord(entitySet, customConfigV2, entiyActionGroupMap))
            .collect(Collectors.toList()));
  }

  /**
   * @param entitySet
   * @param customConfigV2
   * @param entiyActionGroupMap
   * @return record
   *
   * This function transforms through the individual entity and transforms it into record
   * */
  public Record createEntityRecord(
      EntitySet entitySet,
      CustomConfigV2 customConfigV2,
      Map<String, List<EntityActionGroupSet>> entiyActionGroupMap) {

    return Record.builder()
        .id(entitySet.getId())
        .defaultAttributes(entitySet.getDefaultAttributes())
        .helpVariables(new ArrayList<>(entitySet.getHelpVariables()))
        .attributes(populateEntityAttributes(entitySet, customConfigV2))
        .actionGroups(getActionForRecords(customConfigV2, entitySet, entiyActionGroupMap))
        .build();
  }

  /**
   * @param entitySet
   * @param customConfigV2
   * @return List of attributes
   * this function filter the attributes for particular entity-set of configv2
   * */

  public List<Attribute> populateEntityAttributes(
      EntitySet entitySet, CustomConfigV2 customConfigV2) {
    return customConfigV2.getAttributes().stream()
        .filter(attribute -> entitySet.getAttributes().contains(String.valueOf(attribute.getId())))
        .collect(Collectors.toList());
  }

  /**
   * returns the overridden parameter of particular entities for templates
   *
   * @param customConfigV2
   * @param entitySet
   * @return Map(String, ParameterOverrideSet) for example ("invoice" , {... "parameter": "sendTo"
   *     fieldValues: '~invoice.reminder.sendExternalEmail.SendTo' }
   */
  public Map<String, ParameterOverrideSet> getParameterOverride(
      CustomConfigV2 customConfigV2, EntitySet entitySet) {
    List<ParameterOverrideSet> parameterOverrideSets =
        customConfigV2.getParameterOverrideSet().stream()
            .filter(action -> action.getEntityId().equalsIgnoreCase(entitySet.getId()))
            .collect(Collectors.toList());

    // preserving the output order of actions for parameter override
    return parameterOverrideSets.stream()
        .collect(Collectors.toMap(ParameterOverrideSet::getActionGroup, Function.identity(), (existing, replacement) -> existing, LinkedHashMap::new));
  }

  /**
   * @return List of actions for templates
   * @param entitySet
   * @param entiyActionGroupMap
   * @param customConfigV2
   *     <p>creates the list of action for a particular template from the entity set and
   *     entity-set-action-group
   */
  private List<ActionGroup> getActionForRecords(
      CustomConfigV2 customConfigV2,
      EntitySet entitySet,
      Map<String, List<EntityActionGroupSet>> entiyActionGroupMap) {
    List<ActionGroup> recordActionGroups = new LinkedList<>();

    Map<String, ParameterOverrideSet> actionGroupParameterOverrideMap =
        getParameterOverride(customConfigV2, entitySet);

    entiyActionGroupMap.getOrDefault(entitySet.getId(), new ArrayList<>()).stream()
        .forEach(
            entityActionGroup -> {
              recordActionGroups.add(
                  ActionGroup.builder()
                      .id(entityActionGroup.getActionGroup())
                      .precannedTemplateId(entityActionGroup.getPrecannedTemplateId())
                      .actionIdMapper(entityActionGroup.getActionIdMapper())
                      .actionIds(new ArrayList<>(entityActionGroup.getEnabledActions()))
                      .defaultAttributes(entityActionGroup.getDefaultAttributes())
                      .trigger(entityActionGroup.getTrigger())
                      .unsupportedAttributes(
                          entitySet.getAttributes().stream()
                              .filter(
                                  attribute ->
                                      !entityActionGroup.getEnabledAttributes().contains(attribute))
                              .collect(Collectors.toSet()))
                      .actions(
                          Optional.ofNullable(
                                      actionGroupParameterOverrideMap.get(
                                          entityActionGroup.getActionGroup()))
                                  .isPresent()
                              ? (actionGroupParameterOverrideMap
                                  .get(entityActionGroup.getActionGroup())
                                  .getActions()
                                  .stream()
                                  .filter(
                                      action ->
                                          entityActionGroup
                                              .getEnabledActions()
                                              .contains((action.getId())))
                                  .collect(Collectors.toList()))
                              : new ArrayList<>())
                      .build());
            });
    return recordActionGroups;
  }
  /**
   * @return map or parameter override for pre-canned template
   * */

  public Map<String, PreCannedParamSet> getPrecannedParameterOverride(
      CustomConfigV2 customConfigV2) {
    return customConfigV2.getPreCannedParamSet().stream()
        .collect(Collectors.toMap(PreCannedParamSet::getId, Function.identity()));
  }

  /**
   * @return Map of multi-condition set based on template id
   * */
  public Map<String, List<PreCannedMultiCondSet>> getMultiConditionSet(
      CustomConfigV2 customConfigV2) {
    return customConfigV2.getPreCannedMultiCondSets().stream()
        .collect(Collectors.groupingBy(PreCannedMultiCondSet::getPrecannedId));
  }

  /**
   * transforms all the elements of precanned-set into list of config-template updating details of
   * actionsGroups,actions, entities
   */
  public void createTemplateConfig(CustomConfigV2 customConfigV2) {

    Map<String, PreCannedParamSet> preCannedParamSetMap =
        getPrecannedParameterOverride(customConfigV2);

    Map<String, List<PreCannedMultiCondSet>> preCannedMultiCondSetMap =
        getMultiConditionSet(customConfigV2);

    customConfigV2.getPreCannedSet().stream()
        .map(
            preCannedSet ->
                getConfigTemplate(
                    preCannedParamSetMap.get(preCannedSet.getId()),
                    preCannedMultiCondSetMap.get(preCannedSet.getId()),
                    preCannedSet))
        .forEach(customConfigV2.getConfigTemplates()::add);

  }

  /**
   * updating one entity to precanned template with selected actions and multicondition check if
   * template is multi-condition
   */
  private ConfigTemplate getConfigTemplate(
      PreCannedParamSet preCannedParamSet,
      List<PreCannedMultiCondSet> preCannedMultiCondSet,
      PreCannedSet preCannedSet) {
    ConfigTemplate configTemplate =
        ConfigTemplate.builder()
            .name(preCannedSet.getName())
            .description(preCannedSet.getDescription())
            .labels(preCannedSet.getLabels())
            .record(preCannedSet.getEntity())
            .attributes(Optional.ofNullable(preCannedSet.getAttributes())
                .orElse(new LinkedList<>()))
            .id(preCannedSet.getId())
            .build();

    // creating actionGroups with the enabled actions -> mapping the actions with the preCannedSet
    ActionGroup preCannedActionGroup =
        ActionGroup.builder()
            .id(preCannedSet.getActionGroup())
            .actions(
                Optional.ofNullable(preCannedParamSet.getActions()).isPresent()
                    ? (preCannedParamSet.getActions().stream()
                        .filter(action -> preCannedSet.getSelectedAction().contains(action.getId()))
                        .collect(Collectors.toList()))
                    : new ArrayList<>())
            .build();

    configTemplate.setActionGroups(Collections.singletonList(preCannedActionGroup));

    // setting the steps which are for multi-condition
    Optional.ofNullable(preCannedSet.getMulticonditionStatus()).ifPresent( status -> {
      if (status) {
        configTemplate.setSteps(getMulticonditionSteps(preCannedMultiCondSet));
      }
    });
    return configTemplate;
  }

  /**
   * @return List of steps for multi-condition templates
   * creates the list of steps from multicondition override set for config templates
   * */
  public List<Steps> getMulticonditionSteps(
      List<PreCannedMultiCondSet> preCannedMultiCondSet) {
    List<Steps> multiCondSteps = new ArrayList<>();
    preCannedMultiCondSet.stream()
        .forEach(
            preCannedMultiCondInstance -> {
              Steps steps =
                  Steps.builder()
                      .stepId(preCannedMultiCondInstance.getSequenceId())
                      .stepType(String.valueOf(preCannedMultiCondInstance.getModelType()))
                      .build();
              // only the decision elements will have attributes and nexts steps allocated
              if (preCannedMultiCondInstance.getModelType().toUpperCase().equals(StepTypeEnum.CONDITION.value())) {
                steps.setAttributes(preCannedMultiCondInstance.getDecisions());
                steps.setNexts(preCannedMultiCondInstance.getNexts());
              } else {
                steps.setAction(preCannedMultiCondInstance.getAction());
              }
              multiCondSteps.add(steps);
            });
    return multiCondSteps;
  }

}

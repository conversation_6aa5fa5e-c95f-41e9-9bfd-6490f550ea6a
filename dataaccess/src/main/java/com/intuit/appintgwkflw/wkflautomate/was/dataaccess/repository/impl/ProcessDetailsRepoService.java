package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DomainEvent;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DomainEventRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessesResponse;
import com.intuit.system.interfaces.BaseEntity;
import lombok.AllArgsConstructor;
import org.apache.commons.beanutils.PropertyUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class ProcessDetailsRepoService {

  private ProcessDetailsRepository processDetailsRepository;
  private DomainEventRepository domainEventRepository;

  // TODO add retry
  public int updateStatusAndPublishDomainEvent(
      ProcessDetails processDetails, DomainEvent<? extends BaseEntity> processDomainEvent) {
    // update in WAS table
    processDetailsRepository.updateProcessStatusAndEntityVersion(
        processDetails.getProcessId(),
        processDetails.getProcessStatus(),
        processDetails.getEntityVersion());
    domainEventRepository.save(processDomainEvent);
    WorkflowLogger.logInfo(
        "Executing command=DomainEvent action=processEnd description=Domain Event Published Successfully on Process id=%s. Event Id=%s",
        processDetails.getProcessId(), processDomainEvent.getEventId());
    return 1;
  }

  public int updateStatus(String processId, ProcessStatus status) {
    // update in WAS table
    return processDetailsRepository.updateProcessStatus(processId, status);
  }

  public Optional<List<ProcessDetails>> findIncompleteProcesses(
      Long realmId, String processInstanceId) {
    return processDetailsRepository.findByOwnerIdAndProcessStatusAndProcessIdNot(
        realmId, ProcessStatus.ACTIVE, processInstanceId);
  }

  // TODO add retry
  public int updateProcessStatus(InternalStatus internalStatus, List<String> processIds) {
    return processDetailsRepository.updateStatus(internalStatus, processIds);
  }

  public Optional<List<ProcessDetails>> findByDefinitionDetailsListAndProcessStatus(
      List<DefinitionDetails> definitionDetailsList, ProcessStatus processStatus) {
    return processDetailsRepository.findByDefinitionDetailsInAndProcessStatus(
        definitionDetailsList, processStatus);
  }

  public Optional<List<ProcessDetails>> findByRealmIdAndProcessStatus(
      Long ownerId, ProcessStatus processStatus) {
    return processDetailsRepository.findByOwnerIdAndProcessStatus(ownerId, processStatus);
  }

  /**
   * @param processDetails : List<ProcessDetails> objects that needs some status updates. Updates to
   *     non-primary attributes might result in Constraint violation. So advisable to use only when
   *     ids and other mandatory details are not altered.</>
   * @return
   */
  public List<ProcessDetails> saveOrUpdateProcess(List<ProcessDetails> processDetails) {
    return processDetailsRepository.saveAll(processDetails);
  }

  /**
   * Get process details for a particular definition and ownerId combination
   *
   * @param definitionId
   * @return WorkflowProcessesResponse object
   */
  public WorkflowProcessesResponse getAllProcessesForDefinition(String definitionId) {
    // Given that the process repo definition supports filtering only by definition details object,
    // creating a new instance using the definition ID provided.
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId(definitionId);
    List<ProcessDetails> processDetails =
        processDetailsRepository.findByOwnerIdAndDefinitionDetailsIn(
            WASContext.getOwnerId(), Arrays.asList(definitionDetails));

    List<WorkflowProcessResponse> processResponse =
        processDetails.stream()
            .map(
                processDetail -> {
                  WorkflowProcessResponse response = new WorkflowProcessResponse();
                  try {
                    PropertyUtils.copyProperties(response, processDetail);
                  } catch (IllegalAccessException
                      | InvocationTargetException
                      | NoSuchMethodException e) {
                    WorkflowLogger.logError(e, "Error while mapping process detail.");
                  }
                  return response;
                })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

    return new WorkflowProcessesResponse(processResponse);
  }

  /**
   * Get process details for a particular process id
   *
   * @param processId
   * @return ProcessDetails object
   */
  public Optional<ProcessDetails> findByProcessId(String processId) {
    return processDetailsRepository.findByProcessId(processId);
  }

  /**
   * Get definition details for a particular process id without definition data
   *
   * @param processId
   * @return ProcessDetails object
   */
  public Optional<DefinitionDetails> findByProcessIdWithoutDefinitionData(String processId) {
    return processDetailsRepository.findDefinitionDetailsUsingProcessId(processId);
  }


  public ProcessDetails getProcessDetails(String processId) {
    return processDetailsRepository.getProcessDetailsWithoutDefinitionData(processId).orElse(null);
  }

  public ProcessDetails updateEntityVersion(ProcessDetails processDetails) {
    processDetailsRepository.updateEntityVersion(
        processDetails.getProcessId(), processDetails.getEntityVersion() + 1);
    processDetails.setEntityVersion(processDetails.getEntityVersion() + 1);
    return processDetails;
  }
}

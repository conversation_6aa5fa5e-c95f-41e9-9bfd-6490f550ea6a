package com.intuit.appintgwkflw.wkflautomate.was.core.task.providerservice;

import com.intuit.appintgwkflw.wkflautomate.was.entity.graphql.TaskRequest;
import com.intuit.appintgwkflw.wkflautomate.was.provideradapter.task.WorkflowTaskRequestDetails;
import com.intuit.v4.workflows.tasks.Task;

import java.util.List;

/**
 * <AUTHOR> Handles Workflow task CRUD requests
 */
public interface WorkflowTaskService {

  List<Task> getWorkflowTasks(TaskRequest taskRequest);

  Task updateWorkflowTasks(TaskRequest taskRequest);
  
  List<Task> getWkflTasks(WorkflowTaskRequestDetails taskRequest);
}

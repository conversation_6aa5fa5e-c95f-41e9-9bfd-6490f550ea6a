package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.impl.DmnModelConstants;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Definitions;
import org.camunda.bpm.model.dmn.instance.DmnModelElementInstance;
import org.camunda.bpm.model.dmn.instance.Output;
import org.springframework.util.CollectionUtils;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

/** A Utility Class */
@UtilityClass
public class DmnProcessorUtil {
  
  private static final String XMLNS = "xmlns";
  private static final String DEFINITIONS = "definitions";

/**
   * Helper method to create an element and bind it to the parent with id and name field
   *
   * @param parentElement
   * @param id
   * @param name
   * @param elementClass
   * @param <T>
   * @return child class of DmnModelElementInstance
   */
  public <T extends DmnModelElementInstance> T createElement(
      DmnModelInstance modelInstance,
      DmnModelElementInstance parentElement,
      String id,
      String name,
      Class<T> elementClass) {
    T element = modelInstance.newInstance(elementClass);
    element.setAttributeValue("id", id, true);
    if (StringUtils.isNotBlank(name)) {
      element.setAttributeValue("name", name, true);
    }
    parentElement.addChildElement(element);
    return element;
  }

  /**
   * Helper method to create an element and bind it to the parent with id and label field
   *
   * @param modelInstance
   * @param parentElement
   * @param id
   * @param label
   * @param elementClass
   * @param <T>
   * @return child class of DmnModelElementInstance
   */
  public <T extends DmnModelElementInstance> T createElementWithLabel(
      DmnModelInstance modelInstance,
      DmnModelElementInstance parentElement,
      String id,
      String label,
      Class<T> elementClass) {
    T element = modelInstance.newInstance(elementClass);
    element.setAttributeValue("id", id, true);
    if (StringUtils.isNotBlank(label)) {
      element.setAttributeValue("label", label, true);
    }
    parentElement.addChildElement(element);
    return element;
  }

  /**
   * Prepare Definitions XML metadata
   *
   * @return xml definitions object
   */
  public Definitions prepareDefinitionsMetaData(DmnModelInstance modelInstance) {
    Definitions definitions = modelInstance.newInstance(Definitions.class);
    definitions.setNamespace("http://camunda.org/schema/1.0/dmn");
    definitions.setName(DEFINITIONS);
    definitions.setId(DEFINITIONS);
    definitions.setExporterVersion("4.0.0");
    definitions.setExporter("Camunda Modeler");
    definitions.setName("DRD");
    modelInstance.setDefinitions(definitions);
    return definitions;
  }
  
  /**
   * Replacing xmlns from original DMN file input stream.
   * @param inputStream - DMN File input stream
   * @return byte[] DMN file.
   */
  public void validateDMN(InputStream inputStream) {
	try (inputStream) {
	  DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
	  dbf.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
      dbf.setFeature(Constants.EXTERNAL_GENERAL_ENTITIES, false);
      dbf.setFeature(Constants.EXTERNAL_PARAMETER_ENTITIES, false);
	  DocumentBuilder db = dbf.newDocumentBuilder();
	  Document doc = db.parse(inputStream);
	  NodeList definitionsNode = doc.getElementsByTagName(DEFINITIONS);
	  
	  WorkflowVerfiy.verify(validateDefinitionXMLNode(definitionsNode),
			  WorkflowError.DMN_V1_3_TEMPLATE_FAILURE); 
	  
	  String xmlNS = definitionsNode.item(0).getAttributes().getNamedItem(XMLNS).getNodeValue();
	  
	  WorkflowVerfiy.verify(!DmnModelConstants.DMN13_NS.equals(xmlNS),
			  WorkflowError.DMN_V1_3_TEMPLATE_FAILURE); 
	  
	} catch(ParserConfigurationException|IOException|SAXException ex) {
	  throw new WorkflowGeneralException(WorkflowError.TEMPLATE_READ_EXCEPTION, ex);
	} 
  }

  /**
   * Validate DMN XML node.
   * @param definitionsNode
   * @return
   */
  private boolean validateDefinitionXMLNode(NodeList definitionsNode) {
	return null == definitionsNode || definitionsNode.getLength() == 0
			  || null == definitionsNode.item(0).getAttributes() 
			  || null == definitionsNode.item(0).getAttributes().getNamedItem(XMLNS);
  }

  public Decision prepareDecisionMetaDataFromBaseTemplate(DmnModelInstance modelInstance,
      DmnModelInstance baseTemplateDmnModelInstance) {
    Collection<Decision> baseTemplateDecision = baseTemplateDmnModelInstance.getModelElementsByType(
        Decision.class);
    Decision decision = modelInstance.newInstance(Decision.class);

    if (!CollectionUtils.isEmpty(baseTemplateDecision)) {
      Decision baseDecision = baseTemplateDecision.stream().findFirst().get();
      decision.setId(baseDecision.getId());
      decision.setName(baseDecision.getName());
    }
    return decision;
  }

  public DecisionTable prepareDecisionTableMetaDataFromBaseTemplate(DmnModelInstance modelInstance,
      DmnModelInstance baseTemplateDmnModelInstance) {
    Collection<DecisionTable> baseTemplateDecisionTable = baseTemplateDmnModelInstance.getModelElementsByType(
        DecisionTable.class);
    DecisionTable decisionTable = modelInstance.newInstance(DecisionTable.class);

    if (!CollectionUtils.isEmpty(baseTemplateDecisionTable)) {
      DecisionTable baseDecisionTable = baseTemplateDecisionTable.stream().findFirst().get();
      decisionTable.setId(baseDecisionTable.getId());
      decisionTable.setHitPolicy(baseDecisionTable.getHitPolicy());
    }
    return decisionTable;
  }

  public Output prepareOutputMetaDataFromBaseTemplate(DmnModelInstance modelInstance,
      DmnModelInstance baseTemplateDmnModelInstance) {
    Collection<Output> baseTemplateOutput = baseTemplateDmnModelInstance.getModelElementsByType(
        Output.class);
    Output output = modelInstance.newInstance(Output.class);

    if (!CollectionUtils.isEmpty(baseTemplateOutput)) {
      Output baseOutput = baseTemplateOutput.stream().findFirst().get();
      output.setId(baseOutput.getId());
      output.setName(baseOutput.getName());
      output.setLabel(baseOutput.getLabel());
      output.setTypeRef(baseOutput.getTypeRef());
    }
    return output;
  }

  /**
   * * This method updates the name of DMN to the user provided definition name while creating
   * definition. This is required to locate DMN in the cockpit as in absence of this, every dmn is
   * getting created with the same name and it becomes really difficult to debug what is getting
   * created. IDs are intact and unchanged, but only name is changed for better de-bugging on the
   * cock-pit for Decision Definitions
   *
   * <p>This method will update all the DMNs with the definition name provided for bpmn so there is
   * no ambiguity in WAS DB and Camunda Cockpit Name</>
   *
   * @param dmnModelInstanceList : List of DMN Model Instance object
   * @return
   */
  public List<DmnModelInstance> updateDmnName(
      final List<DmnModelInstance> dmnModelInstanceList, final String bpmnDefinitionName) {
    return dmnModelInstanceList.stream()
        .map(
            dmnModelInstance -> {
              final Collection<Decision> decisionElement =
                  dmnModelInstance.getModelElementsByType(Decision.class);
              /**
               * There is one decision in the dmn and that decision has the decision table. Camunda
               * is picking the dmnDefinitionName from this field here. Updating the decision
               * dmnDefinitionName here with the display named supplied by the user [in display name
               * field.].
               */
              final Optional<Decision> decisionOptional = decisionElement.stream().findFirst();
              if (decisionOptional.isPresent()) {
                final Decision decision = decisionOptional.get();
                decision.setName(bpmnDefinitionName);
                decisionElement.clear();
                decisionElement.add(decision);
              }
              return dmnModelInstance;
            })
        .collect(Collectors.toList());
  }

}

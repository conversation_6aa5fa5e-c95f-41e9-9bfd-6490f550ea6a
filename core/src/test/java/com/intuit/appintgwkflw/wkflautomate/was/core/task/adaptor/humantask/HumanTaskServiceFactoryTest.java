package com.intuit.appintgwkflw.wkflautomate.was.core.task.adaptor.humantask;

import com.intuit.appintgwkflw.wkflautomate.was.entity.task.HumanTask;
import java.util.UUID;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HumanTaskServiceFactoryTest {

  @Mock
  HumanTaskIPMService humanTaskIPMService;

  @Mock
  HumanTaskITMService humanTaskITMService;
  
  @InjectMocks
  HumanTaskServiceFactory humanTaskServiceFactory;

  @Test
  public void testProjectServiceFetched() {
    Assert.assertTrue(
        humanTaskServiceFactory.getService(getHumanTaskRequest(true))
            instanceof HumanTaskIPMService);
  }

  @Test
  public void testTaskServiceDelegatorFetched() {
    Assert.assertTrue(
            humanTaskServiceFactory.getService(getHumanTaskRequest(false))
                    instanceof HumanTaskITMService);
  }  

  private HumanTask getHumanTaskRequest(boolean isProject) {
    return HumanTask.builder()
        .id(UUID.randomUUID().toString())
        .taskName("TestTask-01")
        .status("created")
        .dueDate("2021-12-31")
        .taskType("SAMPLE_TASK_TYPE")
        .assigneeId("assignId")
        .recordId("1")
        .project(isProject)
        .build();
  }
}

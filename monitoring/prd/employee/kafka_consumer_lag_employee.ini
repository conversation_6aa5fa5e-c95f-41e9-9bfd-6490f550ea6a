[WAS EVENT IN - PRD - EMPLOYEE Kafka Consumer LAG]
alert_metric_name = rawsum(align(60s, sum, ratediff(ts("telegraf.kafka.consumer.group.ConsumerLagMetrics.Value" and env="prd" and topic="prd.work.workermanagement.employeemanagement.employee.v1" and location="us-west-2" and groupId="was-event-in-processor-group-prd-employee" and app="eventbus-sbseg" and name=SumOffsetLag))))
severity = SEVERE
threshold = 18000
operator = >
alert_target_id = <EMAIL>
custom_tags = sbseg.qbo.adv.workflows.eventin.prd
wait_before_alert = 5
additional_info =
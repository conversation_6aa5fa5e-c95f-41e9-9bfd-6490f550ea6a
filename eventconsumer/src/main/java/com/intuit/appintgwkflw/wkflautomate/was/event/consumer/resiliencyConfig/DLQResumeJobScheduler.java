package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.resiliencyConfig;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service.WASCircuitBreakerService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.SpringContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

@Component
@RequiredArgsConstructor
@EnableScheduling
public class DLQResumeJobScheduler {

    private final KafkaConsumerStarter kafkaConsumerStarter;
    private final WASCircuitBreakerService wasCircuitBreakerService;
    private final ScheduledAnnotationBeanPostProcessor postProcessor;
    private int pausedDuration;
    private static final String LOG_PREFIX = " scheduler=DLQCronJob ";

    // Atomic to avoid creating separate schedulers when different circuit breakers open
    private AtomicBoolean dlqCronRunning = new AtomicBoolean(true);


    @Value("${event.consumer.retryConfig.dlqMaxPauseCount:7}")
    private int MAX_DLQ_PAUSE_COUNT; // in minutes

    /**
     * Check if the DLQ has been paused continuously for the specified duration by checking status of the circuits. If yes, resume it and close all circuits.
     * Runs every 1 minute only if any circuit breaker is open
     */
    @Scheduled(fixedRateString = "${event.consumer.retryConfig.dlqCronCheckInterval:60000}")
    public void checkDLQStatus() {
        dlqCronRunning.set(true);
        try {
            WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + LOG_PREFIX + "Checking status of circuit breakers - Cron has been running for %s minutes", pausedDuration);
            if (wasCircuitBreakerService.areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT)) {
                stopScheduler();
            }
            else{
                ++pausedDuration;
                if (pausedDuration > MAX_DLQ_PAUSE_COUNT) {
                    WorkflowLogger.logWarn(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + LOG_PREFIX + "Closing circuit breakers and resuming DLQ which has been paused for too long");
                    wasCircuitBreakerService.closeAllCircuitBreakersOfActionType(CircuitBreakerActionType.EXTERNAL_TASK_COMPLETE_EVENT);
                    kafkaConsumerStarter.resumeConsumer(KafkaConstants.EXTERNAL_TASK_DLQ_KAFKA_CONSUMER_ID);
                    pausedDuration = 0;
                    stopScheduler();
                }
            }
        }
        catch (Exception ex) {
            EventingLoggerUtil.logError(
                    ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + LOG_PREFIX + "Error in DLQ Resume Job Scheduler. Error=%s",
                    this.getClass().getSimpleName(),
                    ex
            );
        }
    }

    /**
     * Creates a scheduler if not already created.
     */
    public void startScheduler() {
        if(dlqCronRunning.compareAndSet(false, true)) {
            WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + LOG_PREFIX + "Starting DLQ Scheduler");
            DLQResumeJobScheduler schedulerBean = SpringContext.getApplicationContext().getBean(this.getClass());
            postProcessor.postProcessAfterInitialization(schedulerBean, this.getClass().getName());
        }
    }

    /**
     * Destroys scheduler if it is running
     */
    public void stopScheduler() {
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + LOG_PREFIX + "Stopping DLQ Scheduler");
        pausedDuration = 0; // reset counter
        dlqCronRunning.compareAndSet(true, false);
        DLQResumeJobScheduler schedulerBean = SpringContext.getApplicationContext().getBean(this.getClass());
        postProcessor.postProcessBeforeDestruction(schedulerBean, this.getClass().getName());
    }
}
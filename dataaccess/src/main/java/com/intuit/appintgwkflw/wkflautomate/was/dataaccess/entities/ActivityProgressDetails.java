package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Optional;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Version;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.UpdateTimestamp;


/**
 * <AUTHOR>
 * <p>
 * ActivityProgressDetails represents an entity(row) form table - ru_activity_progress_details An
 * ExternalTask(of camunda) lifecycle state is saved along with other essentials.
 */

@Entity
@Table(name = "ru_activity_progress_details", indexes = {
    @Index(name = "ru_activity_progress_details_workflow_id_idx", columnList = "workflow_id", unique = false),
    @Index(name = "ru_activity_progress_details_txn_progress_details_id_idx", columnList = "txn_progress_details_id", unique = false)})
@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class ActivityProgressDetails {

  public ActivityProgressDetails(String id, TransactionDetails txnDetails) {
    this.id = id;
    this.txnDetails = txnDetails;
  }

  @Id
  @Column(name = "id", unique = true, nullable = false)
  private String id;

  @Column(name = "name")
  private String name;

  @Column(name = "status")
  private String status;

  @Type(type = "jsonb")
  @Column(columnDefinition = "jsonb")
  private String attributes;

  @Column(columnDefinition = "start_time")
  @CreationTimestamp
  private Timestamp startTime;

  @Column(columnDefinition = "end_time", nullable = true)
  private Timestamp endTime;

  @Column(columnDefinition = "updated_time", nullable = true)
  @UpdateTimestamp
  private Timestamp updatedTime;

  @Version
  @Column(name = "version")
  private int version;

  @ManyToOne
  @JoinColumn(name = "txn_progress_details_id", foreignKey = @ForeignKey(name = "id"), nullable = true)
  @JsonBackReference
  private TransactionDetails txnDetails;

  @ManyToOne
  @JoinColumn(name = "workflow_id", foreignKey = @ForeignKey(name = "processId"), nullable = false)
  private ProcessDetails processDetails;

  @ManyToOne
  @JoinColumn(name = "activity_details_id", foreignKey = @ForeignKey(name = "id"), nullable = false)
  private ActivityDetail activityDefinitionDetail;

  public ActivityProgressDetails(
      String id,
      String name,
      String status,
      Object attributes,
      Date startTime,
      Date endTime,
      Date updatedTime,
      String processId,
      Long transactionDetailsId,
      String txnId,
      long activityDefId,
      TaskType type,
      String activityId,
      String activityName,
      String activityType,
      Object activityAttributes,
      long parentId,
      String templateId,
      int version) {

    this.id = id;
    this.name = name;
    this.status = status;
    this.attributes = (String)attributes;
    this.startTime = Optional.ofNullable(startTime)
        .map(time -> Timestamp.from(time.toInstant())).orElse(null);
    this.endTime = Optional.ofNullable(endTime)
        .map(time -> Timestamp.from(time.toInstant())).orElse(null);
    this.updatedTime = Optional.ofNullable(updatedTime)
        .map(time -> Timestamp.from(time.toInstant())).orElse(null);

    this.processDetails = ProcessDetails.builder().processId(processId).build();

    this.txnDetails = TransactionDetails.builder()
        .id(transactionDetailsId)
        .txnId(txnId)
        .build();

    this.activityDefinitionDetail = ActivityDetail.builder()
        .id(activityDefId)
        .type(type)
        .activityId(activityId)
        .activityName(activityName)
        .activityType(activityType)
        .attributes((String)activityAttributes)
        .parentId(parentId)
        .templateDetails(
            TemplateDetails.builder().id(templateId).build()
        ).build();
    this.version = version;
  }

  /**
   * Copy Constructor
   *
   * @param activityProgressDetails
   */
  public ActivityProgressDetails(ActivityProgressDetails activityProgressDetails) {
    id = activityProgressDetails.getId();
    name = activityProgressDetails.getName();
    status = activityProgressDetails.getStatus();
    attributes = activityProgressDetails.getAttributes();
    startTime = activityProgressDetails.getStartTime();
    endTime = activityProgressDetails.getEndTime();
    updatedTime = activityProgressDetails.getUpdatedTime();
    processDetails = activityProgressDetails.getProcessDetails();
    txnDetails = activityProgressDetails.getTxnDetails();
    activityDefinitionDetail = activityProgressDetails.getActivityDefinitionDetail();
    version = activityProgressDetails.getVersion();
  }
}
package com.intuit.appintgwkflw.wkflautomate.was.core.camunda.service;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.CAMUNDA_DEPLOYMENT_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DELETE_DEFINITION_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.DELETE_DEPLOYMENT_FAILED;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.UPDATE_DEFINITION_STATUS_FAILED;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowCoreConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.offlineticket.OfflineTicketClient;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.httpClient.CamundaWASClient;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CamundaRestUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CamundaRestConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDefinitionKeyRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeleteDeploymentRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@AllArgsConstructor
public class CamundaDefinitionServiceRest implements BPMNEngineDefinitionServiceRest {

  
  
  private CamundaWASClient httpClient;
  private WorkflowCoreConfig workflowCoreConfig;
  private WASContextHandler contextHandler;
  private OfflineTicketClient offlineTicketClient;
  private CamundaRestUtil camundaRestUtil;

  /**
   * *
   *
   * @param deployDefinition: Contains the file that needs to be deployed, auth headers and reponse
   *     type
   * @return Response: Deploys the BPMN and DMNs, returns the response
   */
  @SuppressWarnings("unchecked")
  public <T> WASHttpResponse<T> deployDefinition(DeployDefinition deployDefinition) {
    MultiValueMap<String, Object> requestBody = new LinkedMultiValueMap<>();
    
    requestBody.add(WorkflowConstants.DEPLOYMENT_NAME, deployDefinition.getDeploymentName());
    
    requestBody.add(
        deployDefinition.getBpmnDefinitionFile().getName(),
        new FileSystemResource(deployDefinition.getBpmnDefinitionFile()));
    
    deployDefinition
        .getDmnDefinitionFileList()
        .forEach(
            dmnDefinitionFile ->
                requestBody.add(
                    dmnDefinitionFile.getName(), new FileSystemResource(dmnDefinitionFile)));
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.MULTIPART_FORM_DATA);

    HttpEntity<MultiValueMap<String, Object>> requestEntity =
        new HttpEntity<>(requestBody, requestHeaders);
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointDefinitionDeployment());
    WASHttpResponse<T> response =
        httpClient.postResponse(
            deployDefinitionUrl.toString(), requestEntity, deployDefinition.getResponseType());
    WorkflowVerfiy.verify(!response.isSuccess2xx(), CAMUNDA_DEPLOYMENT_FAILED, response.getError());
    return response;
  }

  @Override
  public void updateProcessDefinitionStatus(
      @NonNull final String definitionId, final boolean status) {
    final Map<String, Object> requestBody = new HashMap<>();
    requestBody.put(CamundaRestConstants.SUSPENDED, status);
    requestBody.put(CamundaRestConstants.INCLUDE_PROCESS_INSTANCES, status);

    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    StringBuilder url = new StringBuilder();
    url.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append("/")
        .append(definitionId)
        .append(workflowCoreConfig.getRestEndpointSuspendInstance());

    WASHttpRequest<Map<String, Object>, String> wasHttpRequest =
        WASHttpRequest.<Map<String, Object>, String>builder()
            .httpMethod(HttpMethod.PUT)
            .request(requestBody)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<String>() {})
            .url(url.toString())
            .build();
    WorkflowLogger.info(
        () ->
            WorkflowLoggerRequest.builder()
                .message(
                    "Invoking Camunda services with url=%s, request body=%s", url, requestBody));
    WASHttpResponse<String> response = httpClient.httpResponse(wasHttpRequest);
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), UPDATE_DEFINITION_STATUS_FAILED, response.getError());
  }

  @Override
  public void deleteDefinition(@NonNull DeleteDeploymentRequest deleteDeploymentRequest) {
    StringBuilder url = new StringBuilder();
    url.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append("/")
        .append(deleteDeploymentRequest.getId());

    WASHttpResponse<String> response = makeDeleteRequest(deleteDeploymentRequest, url.toString());

    /* Handle the API error */
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), DELETE_DEFINITION_FAILED, response.getError());
  }

  @Override
  public void deleteDeployment(@NonNull DeleteDeploymentRequest deleteDeploymentRequest) {

    StringBuilder url = new StringBuilder();
    url.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointDefinitionDeploymentCrud())
        .append("/")
        .append(deleteDeploymentRequest.getId());
    WASHttpResponse<String> response = makeDeleteRequest(deleteDeploymentRequest, url.toString());
    /* Handle the API error */
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), DELETE_DEPLOYMENT_FAILED, response.getError());
  }

  @Override
  public WASHttpResponse<BpmnResponse> getBPMNXMLDefinition(String definitionId) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append(MessageFormat.format("/{0}/xml", definitionId));
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);

    return httpClient.getResponse(
        deployDefinitionUrl.toString(), requestEntity, BpmnResponse.class);
  }

  @Override
  public WASHttpResponse<DmnResponse> getDMNXMLDefinition(String definitionId) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointDecisionDefinition())
        .append(MessageFormat.format("/{0}/xml", definitionId));
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);
    return httpClient.getResponse(deployDefinitionUrl.toString(), requestEntity, DmnResponse.class);
  }

  @Override
  public void deleteDefinitionByKey(
      @NonNull DeleteDefinitionKeyRequest deleteDefinitionKeyRequest) {
    StringBuilder url = new StringBuilder();
    url.append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append(workflowCoreConfig.getProcessKey())
        .append("/")
        .append(deleteDefinitionKeyRequest.getKey())
        .append(workflowCoreConfig.getDeleteEndPoint());

    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);

    // When a call comes via External Task Worker then Authorisation is not set in MDC. So, to make a
    // call from WAS
    // to Camunda we will be needing System Offline Ticket to make a call.
    if (StringUtils.isEmpty(contextHandler.get(WASContextEnums.AUTHORIZATION_HEADER))) {
      requestHeaders.set(
          HttpHeaders.AUTHORIZATION, offlineTicketClient.getSystemOfflineHeadersForOfflineJob());
    }
    UriComponentsBuilder uriComponentsBuilder =
        buildUriComponents(
            url,
            deleteDefinitionKeyRequest.isCascade(),
            deleteDefinitionKeyRequest.isSkipCustomListeners());

    WASHttpRequest<Map<String, Object>, String> wasHttpRequest =
        WASHttpRequest.<Map<String, Object>, String>builder()
            .httpMethod(HttpMethod.DELETE)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<String>() {})
            .url(uriComponentsBuilder.build().toUriString())
            .build();
    WASHttpResponse<String> response = httpClient.httpResponse(wasHttpRequest);
    /* Handle the API error */
    WorkflowVerfiy.verify(
        !response.isSuccess2xx(), DELETE_DEFINITION_FAILED, response.getError());
  }

  @Override
  public WASHttpResponse<DeploymentResponse> getDeploymentDetails(String definitionId) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    StringBuilder deployDefinitionUrl = new StringBuilder();
    deployDefinitionUrl
        .append(camundaRestUtil.getCamundaBaseURL())
        .append(workflowCoreConfig.getRestEndpointProcessDefinition())
        .append(MessageFormat.format("/{0}", definitionId));
    HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestHeaders);

    return httpClient.getResponse(
        deployDefinitionUrl.toString(), requestEntity, DeploymentResponse.class);
  }

  /**
   * Prepares the UriComponentsBuilder class
   *
   * @param url : String Builder of URL string
   * @param isCascade : Cascade Flag
   * @param isSkipCustomListener : Skip Custom Listener Flag
   * @return
   */
  private UriComponentsBuilder buildUriComponents(
      StringBuilder url, boolean isCascade, boolean isSkipCustomListener) {
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(url.toString());
    uriComponentsBuilder.queryParam(CamundaRestConstants.CASCADE, isCascade);
    uriComponentsBuilder.queryParam(
        CamundaRestConstants.SKIP_CUSTOM_LISTENERS, isSkipCustomListener);
    return uriComponentsBuilder;
  }

  private WASHttpResponse<String> makeDeleteRequest(
      DeleteDeploymentRequest deleteDeploymentRequest, String url) {
    HttpHeaders requestHeaders = new HttpHeaders();
    requestHeaders.setContentType(MediaType.APPLICATION_JSON);
    UriComponentsBuilder uriComponentsBuilder =
        buildUriComponents(
            new StringBuilder(url),
            deleteDeploymentRequest.isCascade(),
            deleteDeploymentRequest.isSkipCustomListeners());
    uriComponentsBuilder.queryParam(
        CamundaRestConstants.CASCADE, deleteDeploymentRequest.isCascade());
    uriComponentsBuilder.queryParam(
        CamundaRestConstants.SKIP_CUSTOM_LISTENERS,
        deleteDeploymentRequest.isSkipCustomListeners());

    WASHttpRequest<Map<String, Object>, String> wasHttpRequest =
        WASHttpRequest.<Map<String, Object>, String>builder()
            .httpMethod(HttpMethod.DELETE)
            .requestHeaders(requestHeaders)
            .responseType(new ParameterizedTypeReference<String>() {})
            .url(uriComponentsBuilder.build().toUriString())
            .build();
    return httpClient.httpResponse(wasHttpRequest);
  }
}

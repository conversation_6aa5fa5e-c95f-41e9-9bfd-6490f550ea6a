package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.BusinessRuleTaskOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.CallActivityOutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.mappers.OutgoingActivityMapper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BaseElement;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.camunda.bpm.model.bpmn.instance.CallActivity;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OutgoingActivityMapperFactoryTest {

  @InjectMocks
  private OutgoingActivityMapperFactory outgoingActivityMapperFactory;

  @Mock
  private BusinessRuleTaskOutgoingActivityMapper businessRuleTaskOutgoingActivityMapper;

  @Mock
  private CallActivityOutgoingActivityMapper callActivityOutgoingActivityMapper;

  @Mock
  DefinitionInstance definitionInstance;

  private BpmnModelInstance bpmnModelInstance;

  private static final String MULTI_CONDITION_CUSTOM_APPROVAL_BPMN = "bpmn/customApproval_multiCondition.bpmn";

  /**
   * @param fileName
   * @return : BPMN Model Instance
   */
  private static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        OutgoingActivityMapperFactoryTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  @Before
  public void setUp() {
    bpmnModelInstance = readBPMNFile(MULTI_CONDITION_CUSTOM_APPROVAL_BPMN);
    definitionInstance = new DefinitionInstance(new Definition(), bpmnModelInstance,
        new ArrayList<>(), new TemplateDetails());
  }

  @Test
  public void testBpmnBusinessRuleTaskOutgoingActivityMapper() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(BusinessRuleTask.class).stream().findFirst().get();
    OutgoingActivityMapper actualOutput = outgoingActivityMapperFactory.getHandler(element);
    Assert.assertTrue(actualOutput instanceof BusinessRuleTaskOutgoingActivityMapper);
  }

  @Test
  public void testBpmnCallActivityOutgoingActivityMapper() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(CallActivity.class).stream().findFirst().get();
    OutgoingActivityMapper actualOutput = outgoingActivityMapperFactory.getHandler(element);
    Assert.assertTrue(actualOutput instanceof CallActivityOutgoingActivityMapper);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testExceptionScenario() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(SequenceFlow.class).stream().findFirst().get();
    OutgoingActivityMapper actualOutput = outgoingActivityMapperFactory.getHandler(element);
    Assert.assertFalse(actualOutput instanceof CallActivityOutgoingActivityMapper);
    Assert.assertFalse(actualOutput instanceof BusinessRuleTaskOutgoingActivityMapper);
  }

  @Test
  public void testBpmnCallActivityMapper() {
    BaseElement element =
        bpmnModelInstance.getModelElementsByType(CallActivity.class).stream().findFirst().get();
    OutgoingActivityMapper outgoingActivityMapper = outgoingActivityMapperFactory.getHandler(
        element);
    Mockito.when(
            outgoingActivityMapper.fetchOutgoingActivityIds(element.getId(), definitionInstance))
        .thenReturn(new ArrayList<>());
    Assert.assertNotNull(outgoingActivityMapperFactory.fetchOutgoingActivityIds(element.getId(),
        definitionInstance));
  }

}

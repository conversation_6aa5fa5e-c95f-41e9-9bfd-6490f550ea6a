[IDX - SPP - NONPCI - Pod Memory Utilization]
alert_metric_name = mcount(2m, ts("heapster.pod.memory.working_set", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-e2e)) >= 2 and mcount(2m, ts("heapster.pod.memory.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-e2e)) >= 2 and ts("heapster.pod.memory.working_set", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-e2e) / ts("heapster.pod.memory.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-e2e) * 100
severity = SEVERE
threshold = 95
operator = >
alert_target_id = <EMAIL>
wait_before_alert = 15
additional_info =
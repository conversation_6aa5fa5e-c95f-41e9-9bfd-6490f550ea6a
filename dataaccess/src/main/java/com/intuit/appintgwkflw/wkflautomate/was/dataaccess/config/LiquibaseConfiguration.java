package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import java.util.Map;
import javax.sql.DataSource;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

/**
 * This auto configuration overrides the LiquibaseAutoConfiguration implementation.
 * This configuration class is responsible for setting up the Liquibase data sources for multiple offerings.
 */
@Configuration
@EnableConfigurationProperties({LiquibaseProperties.class})
@AllArgsConstructor
public class LiquibaseConfiguration {

  private LiquibaseProperties properties;
  private OfferingConfig dataSourceProperties;

  /**
   * This method will inject a wrapper Liquibase implementation which will run the liquibase changelogs
   * for all the offerings that are listed for the given swimlane.
   * @param dataSources Map of datasources of different offerings
   * @return Wrapper Liquibase object
   */
  @Bean(name = DataAccessConstants.OFFERING_AWARE_LIQUIBASE)
  @DependsOn(DataAccessConstants.ROUTING_DATASOURCE)
  public MultiOfferingDataSourceSpringLiquibase liquibaseMultiTenancy(
      Map<Object, Object> dataSources) {
    // to run changeSets of the liquibase asynchronous
    MultiOfferingDataSourceSpringLiquibase liquibase =
        new MultiOfferingDataSourceSpringLiquibase(null);
    
    dataSources.forEach(
        (tenant, dataSource) -> liquibase.addDataSource((String) tenant, (DataSource) dataSource));

    dataSourceProperties
        .getDownstreamServices()
        .forEach(
            dbProperty -> {
              if (ObjectUtils.isNotEmpty(dbProperty.getDataSource())
                  && ObjectUtils.isNotEmpty(dbProperty.getDataSource().getLiquibase())) {
                liquibase.addLiquibaseProperties(
                    dbProperty.getOfferingId(),
                    (LiquibaseProperties) dbProperty.getDataSource().getLiquibase());
              }
            });

    liquibase.setProperties(properties);
    return liquibase;
  }
}

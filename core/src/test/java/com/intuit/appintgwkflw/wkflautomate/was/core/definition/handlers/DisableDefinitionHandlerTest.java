package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionCommands;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.command.DefinitionDeleteCommand;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DisableDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.UpdateDefinitionStatusInDataStoreService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/** <AUTHOR> */
public class DisableDefinitionHandlerTest {
  private static final String REALM_ID = "12345";
  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  private final Definition definition = TestHelper.mockDefinitionEntity();

  @Mock private UpdateDefinitionStatusInDataStoreService updateDefinitionStatusInDataStoreService;

  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @Mock private TemplateDetails bpmnTemplateDetail;

  @Mock private DefinitionDeleteCommand command;

  @InjectMocks private DisableDefinitionHandler disableDefinitionHandler;

  @Mock private ProcessDetailsRepoService processDetailsRepoService;

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
    Mockito.when(command.getName()).thenReturn(CrudOperation.DISABLED.name());
    DefinitionCommands.addCommand(command.getName(), command);
  }

  @Test
  public void definitionIdNotSet() {
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    try {
      disableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.INVALID_DEFINITION_DETAILS.getErrorMessage(), e.getMessage());
    }
  }

  @Test(expected = WorkflowGeneralException.class)
  public void templateNotFound() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenThrow(new WorkflowGeneralException(WorkflowError.INVALID_DEFINITION_DETAILS));
    disableDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void fetchDefinitionInvoked() {

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessId("pId");
    List<ProcessDetails> processDetailsList = Collections.singletonList(processDetails);
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());
    Mockito.when(
            definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetails);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                definitionDetails, ProcessStatus.ACTIVE))
        .thenReturn(Optional.ofNullable(processDetailsList));

    DefinitionInstance def =
        disableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
  }

  @Test
  public void updateDefnStatusFailed() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);
    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessId("pId");
    List<ProcessDetails> processDetailsList = Collections.singletonList(processDetails);

    definitionInstance.setProcessDetails(processDetailsList);
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());

    Mockito.when(
            definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetails);

    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                definitionDetails, ProcessStatus.ACTIVE))
        .thenReturn(Optional.ofNullable(processDetailsList));

    Mockito.doThrow(
            new WorkflowGeneralException(WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED))
        .when(updateDefinitionStatusInDataStoreService)
        .updateStatusForDisabled(Mockito.any());

    try {
      disableDefinitionHandler.process(definitionInstance, authorization.getRealm());
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.ENABLE_DISABLE_DEFINITION_STATUS_FAILED.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void fetchDefinitionInvokedWithUpdatedDefinitionVerison() {

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance = new DefinitionInstance(definition, null, null, null);
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetailsWithNonZeroVersion(
            definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    ProcessDetails processDetails = new ProcessDetails();
    processDetails.setProcessId("pId");
    List<ProcessDetails> processDetailsList = Collections.singletonList(processDetails);
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(
                TestHelper.getGlobalId(DEF_ID).getLocalId(), REALM_ID))
        .thenReturn(definitionDetails.stream().findAny().get());
    Mockito.when(
            definitionServiceHelper.getDefinitionListByWorkflowId(
                definitionDetail.getWorkflowId(), Long.valueOf(REALM_ID)))
        .thenReturn(definitionDetails);
    Mockito.when(
            processDetailsRepoService.findByDefinitionDetailsListAndProcessStatus(
                definitionDetails, ProcessStatus.ACTIVE))
        .thenReturn(Optional.ofNullable(processDetailsList));

    DefinitionInstance def =
        disableDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertNotNull(def);
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.UpdateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
/**
 * Author: Nitin Gupta Date: 21/01/20 Description: Test cases for {@link
 * com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.UpdateDefinitionHandler}
 */
@RunWith(MockitoJUnitRunner.class)
public class UpdateDefinitionHandlerTest {

  private static final String REALM_ID = "12345";
  @InjectMocks
  private UpdateDefinitionHandler updateDefinitionHandler;
  @Mock
  private TemplateDetails bpmnTemplateDetail;
  @Mock
  private CustomWorkflowQueryCapability customWorkflowQueryCapability;
  @Mock
  private DefinitionServiceHelper definitionServiceHelper;
  private final Definition definition = TestHelper.mockDefinitionEntity();

  private final DefinitionInstance definitionInstance =
      TestHelper.mockDefinitionInstance(definition, bpmnTemplateDetail, null, null);

  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = NullPointerException.class)
  public void test_DefinitionIdNotPresent() {
    updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void test_TemplateIdNotPresent() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionInstance definitionInstance =
        TestHelper.mockDefinitionInstance(definition, bpmnTemplateDetail, null, null);
    definitionInstance.getDefinition().getTemplate().setId(null);
    updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  public List<DefinitionDetails> initForHappyCase() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetail = Collections.singletonList(definitionDetails);

    definitionInstance.getDefinition().setId(TestHelper.getGlobalId(DEF_ID));
    return definitionDetail;
  }

  @Test
  public void testHappyCaseStatusNotSet() {
    List<DefinitionDetails> definitionDetails = initForHappyCase();
    Mockito.when(
            definitionServiceHelper.fetchDefinitions(
                definition.getTemplate().getId().getLocalId(), authorization.getRealm()))
        .thenReturn(definitionDetails);

    Mockito.when(
            definitionServiceHelper.getDefinitionDetailsById(
                definitionDetails, definition.getId().getLocalId()))
        .thenReturn(Optional.of(Mockito.mock(DefinitionDetails.class)));

    final DefinitionInstance process =
        updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertEquals(definitionInstance, process);
  }

  @Test
  public void testHappyCaseStatusSet() {
    List<DefinitionDetails> definitionDetails = initForHappyCase();
    definitionInstance.getDefinition().setStatus(WorkflowStatusEnum.ENABLED);
    Mockito.when(
            definitionServiceHelper.fetchDefinitions(
                definition.getTemplate().getId().getLocalId(), authorization.getRealm()))
        .thenReturn(definitionDetails);
    Mockito.when(
            definitionServiceHelper.getDefinitionDetailsById(
                definitionDetails, definition.getId().getLocalId()))
        .thenReturn(Optional.of(Mockito.mock(DefinitionDetails.class)));
    final DefinitionInstance process =
        updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
    Assert.assertEquals(definitionInstance, process);
    Assert.assertNull(definitionInstance.getModifiedBy());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void definitionIdNotFound() {
    initForHappyCase();
    updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void customExceptionDefinitionFound() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionInstance.getDefinition().setId(TestHelper.getGlobalId(DEF_ID));
    String realmId = authorization.getRealm();
    try {
      updateDefinitionHandler.process(definitionInstance, realmId);
      Assert.fail();
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          WorkflowError.DEFINITION_NOT_FOUND.getErrorMessage(), e.getMessage());
    }
  }

  @Test
  public void updateDefinitionForRealmUser() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setVersion(1);
    definitionInstance.getDefinition().setId(TestHelper.getGlobalId(DEF_ID));
    String realmId = authorization.getRealm();
    Mockito.when(
            definitionServiceHelper.findByDefinitionId(definition.getId().getLocalId()))
        .thenReturn(definitionDetails);

    WASContext.setMigrationContext(true);
    DefinitionInstance definitionInstance1 = updateDefinitionHandler.process(definitionInstance,
        realmId);
    Assert.assertEquals(definitionInstance1.getDefinitionDetails().getVersion(), 2);
    Assert.assertEquals(definitionInstance1.getDefinitionDetails().getWorkflowId(),
        definitionDetails.getWorkflowId());
    Assert.assertEquals(definitionInstance1.getModifiedBy(),
            definitionDetails.getModifiedByUserId());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testForInvalidDefnIdInTemplateDetails() {
    List<DefinitionDetails> definitionDetails = initForHappyCase();
    Mockito.when(
            definitionServiceHelper.fetchDefinitions(
                definition.getTemplate().getId().getLocalId(), authorization.getRealm()))
        .thenReturn(definitionDetails);

    Mockito.when(
            definitionServiceHelper.getDefinitionDetailsById(
                definitionDetails, definition.getId().getLocalId()))
        .thenReturn(Optional.empty());

    updateDefinitionHandler.process(definitionInstance, authorization.getRealm());
  }

  @Test
  public void testForPrecannedToCustomMigrationCase() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setVersion(1);
    WASContext.setMigrationContext(false);
    definitionInstance.getDefinition().setId(TestHelper.getGlobalId(DEF_ID));

    Assert.assertFalse(
        updateDefinitionHandler.isMigration(definitionInstance));

    Template template = TestHelper.mockTemplateEntity();
    template.setCategory(TemplateCategory.CUSTOM.toString());
    template.setName("customApproval");
    definitionInstance.getDefinition().setTemplate(template);
    Assert.assertFalse(
        updateDefinitionHandler.isMigration(definitionInstance));

    Mockito.when(
            customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(
                definition))
        .thenReturn(true);
    Assert.assertTrue(
        updateDefinitionHandler.isMigration(definitionInstance));
  }

  @After
  public void cleanup() {
    WASContext.setMigrationContext(false);
  }

}

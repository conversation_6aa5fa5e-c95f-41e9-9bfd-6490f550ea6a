package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn;

import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaIn;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputOutput;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaInputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaOut;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaOutputParameter;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperties;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * This class serves as a helper class to add extension elements to flow nodes
 * created dynamically in the workflow.
 */
@Component
public class DynamicBpmnExtensionElementsHelper {

    public void addAllValidExtensionElements(
            FlowNode flowNode, FlowNode baseTemplateFlowNode, BpmnModelInstance bpmnModelInstance) {

        // add camundaProperties
        addCamundaPropertiesToExtensionElement(flowNode, baseTemplateFlowNode, bpmnModelInstance);

        // add camundaIn
        addCamundaInputToExtensionElement(flowNode, baseTemplateFlowNode, bpmnModelInstance);

        // add camundaInputOutput
        addCamundaInputOutputToExtensionElement(flowNode, baseTemplateFlowNode, bpmnModelInstance);

        // add camundaOut
        addCamundaOutputToExtensionElement(flowNode, baseTemplateFlowNode, bpmnModelInstance);
    }

    public CamundaInputOutput populateCamundaInputOutputParameter(
            BpmnModelInstance bpmnModelInstance,
            FlowNode precannedFlowNode,
            Map<String, String> existingCamundaInputOutputMap) {
        List<CamundaInputOutput> baseTemplateCamundaInputOutputList =
                precannedFlowNode
                        .getExtensionElements()
                        .getElementsQuery()
                        .filterByType(CamundaInputOutput.class)
                        .list();

        CamundaInputOutput camundaInputOutput = bpmnModelInstance.newInstance(CamundaInputOutput.class);

        if (!CollectionUtils.isEmpty(baseTemplateCamundaInputOutputList)) {

            baseTemplateCamundaInputOutputList
                    .get(0)
                    .getCamundaInputParameters()
                    .forEach(
                            precannedInputParameter -> {
                                if ((CollectionUtils.isEmpty(existingCamundaInputOutputMap)
                                        || !existingCamundaInputOutputMap.containsKey(
                                        precannedInputParameter.getCamundaName()))
                                        && Objects.nonNull(precannedInputParameter.getCamundaName())
                                        && Objects.nonNull(precannedInputParameter.getTextContent())) {
                                    camundaInputOutput.addChildElement(
                                            addCamundaInputParameter(bpmnModelInstance, precannedInputParameter));
                                }
                            });

            baseTemplateCamundaInputOutputList
                    .get(0)
                    .getCamundaOutputParameters()
                    .forEach(
                            precannedOutputParameter -> {
                                if ((CollectionUtils.isEmpty(existingCamundaInputOutputMap)
                                        || !existingCamundaInputOutputMap.containsKey(
                                        precannedOutputParameter.getCamundaName()))
                                        && Objects.nonNull(precannedOutputParameter.getCamundaName())
                                        && Objects.nonNull(precannedOutputParameter.getTextContent())) {
                                    camundaInputOutput.addChildElement(
                                            addCamundaOutputParameter(bpmnModelInstance, precannedOutputParameter));
                                }
                            });
        }
        return camundaInputOutput;
    }

    private void addCamundaPropertiesToExtensionElement(
            FlowNode flowNode, FlowNode baseTemplateFlowNode, BpmnModelInstance bpmnModelInstance) {

        if (CollectionUtils.isEmpty(
                BpmnProcessorUtil.getMapOfCamundaProperties(baseTemplateFlowNode.getExtensionElements()))) {
            return;
        }

        List<CamundaProperties> baseTemplateCamundaProperties =
                baseTemplateFlowNode
                        .getExtensionElements()
                        .getElementsQuery()
                        .filterByType(CamundaProperties.class)
                        .list();

        if (CollectionUtils.isEmpty(baseTemplateCamundaProperties)) {
            return;
        }

        CamundaProperties camundaProperties = bpmnModelInstance.newInstance(CamundaProperties.class);

        Map<String, String> currentCamundaPropertiesMap =
                BpmnProcessorUtil.getMapOfCamundaProperties(flowNode.getExtensionElements());

        Optional<CamundaProperties> baseTemplateCamundaPropertiesOptional =
                baseTemplateCamundaProperties.stream()
                        .filter(properties -> Objects.nonNull(properties.getCamundaProperties()))
                        .findFirst();

        if (baseTemplateCamundaPropertiesOptional.isEmpty()) {
            return;
        }

        baseTemplateCamundaPropertiesOptional
                .get()
                .getCamundaProperties()
                .forEach(
                        precannedCamundaProperty -> {
                            if ((CollectionUtils.isEmpty(currentCamundaPropertiesMap)
                                    || !currentCamundaPropertiesMap.containsKey(
                                    precannedCamundaProperty.getCamundaName()))
                                    && Objects.nonNull(precannedCamundaProperty.getCamundaName())
                                    && Objects.nonNull(precannedCamundaProperty.getCamundaValue())) {
                                camundaProperties.addChildElement(
                                        addCamundaProperty(bpmnModelInstance, precannedCamundaProperty));
                            }
                        });

        if (CollectionUtils.isEmpty(camundaProperties.getCamundaProperties())) {
            return;
        }

        flowNode.builder().addExtensionElement(camundaProperties);
    }

    private void addCamundaInputOutputToExtensionElement(
            FlowNode flowNode, FlowNode baseTemplateFlowNode, BpmnModelInstance bpmnModelInstance) {

        if (CollectionUtils.isEmpty(
                BpmnProcessorUtil.getMapOfInputOutputParameters(
                        baseTemplateFlowNode.getExtensionElements()))) {
            return;
        }
        Map<String, String> mapOfInputOutputParameters =
                BpmnProcessorUtil.getMapOfInputOutputParameters(flowNode.getExtensionElements());

        CamundaInputOutput camundaInputOutput =
                populateCamundaInputOutputParameter(
                        bpmnModelInstance, baseTemplateFlowNode, mapOfInputOutputParameters);

        // if both input and output parameters are empty, do not add the extension element
        if (CollectionUtils.isEmpty(camundaInputOutput.getCamundaOutputParameters())
                && CollectionUtils.isEmpty(camundaInputOutput.getCamundaInputParameters())) {
            return;
        }

        flowNode.builder().addExtensionElement(camundaInputOutput);
    }

    private void addCamundaInputToExtensionElement(
            FlowNode flowNode, FlowNode baseTemplateFlowNode, BpmnModelInstance bpmnModelInstance) {

        if (CollectionUtils.isEmpty(
                BpmnProcessorUtil.getListOfCamundaInParameters(
                        baseTemplateFlowNode.getExtensionElements()))) {
            return;
        }

        List<CamundaIn> camundaInList = populateCamundaInParameters(bpmnModelInstance, baseTemplateFlowNode);

        camundaInList.forEach(camundaIn -> flowNode.builder().addExtensionElement(camundaIn));
    }

    private void addCamundaOutputToExtensionElement(
            FlowNode flowNode, FlowNode baseTemplateFlowNode, BpmnModelInstance bpmnModelInstance) {

        if (CollectionUtils.isEmpty(
                BpmnProcessorUtil.getListOfCamundaOutParameters(
                        baseTemplateFlowNode.getExtensionElements()))) {
            return;
        }

        List<CamundaOut> camundaOutList = populateCamundaOutParameters(bpmnModelInstance, baseTemplateFlowNode);

        camundaOutList.forEach(camundaOut -> flowNode.builder().addExtensionElement(camundaOut));
    }

    private List<CamundaIn> populateCamundaInParameters(
            BpmnModelInstance bpmnModelInstance, FlowNode precannedFlowNode) {

        List<CamundaIn> baseTemplateCamundaInList =
                precannedFlowNode
                        .getExtensionElements()
                        .getElementsQuery()
                        .filterByType(CamundaIn.class)
                        .list();

        List<CamundaIn> camundaInList = new ArrayList<>();

        for (CamundaIn baseTemplateCamundaIn : baseTemplateCamundaInList) {
            CamundaIn camundaIn = bpmnModelInstance.newInstance(CamundaIn.class);
            camundaIn.setCamundaSource(baseTemplateCamundaIn.getCamundaSource());
            camundaIn.setCamundaTarget(baseTemplateCamundaIn.getCamundaTarget());
            camundaIn.setCamundaVariables(baseTemplateCamundaIn.getCamundaVariables());
            camundaIn.setCamundaBusinessKey(baseTemplateCamundaIn.getCamundaBusinessKey());
            camundaInList.add(camundaIn);
        }

        return camundaInList;
    }

    private List<CamundaOut> populateCamundaOutParameters(
            BpmnModelInstance bpmnModelInstance, FlowNode precannedFlowNode) {

        List<CamundaOut> baseTemplateCamundaOutList =
                precannedFlowNode
                        .getExtensionElements()
                        .getElementsQuery()
                        .filterByType(CamundaOut.class)
                        .list();

        List<CamundaOut> camundaOutList = new ArrayList<>();

        for (CamundaOut baseTemplateCamundaOut : baseTemplateCamundaOutList) {
            CamundaOut camundaOut = bpmnModelInstance.newInstance(CamundaOut.class);
            camundaOut.setCamundaSource(baseTemplateCamundaOut.getCamundaSource());
            camundaOut.setCamundaTarget(baseTemplateCamundaOut.getCamundaTarget());
            camundaOut.setCamundaVariables(baseTemplateCamundaOut.getCamundaVariables());
            camundaOutList.add(camundaOut);
        }

        return camundaOutList;
    }

    private CamundaProperty addCamundaProperty(
            BpmnModelInstance bpmnModelInstance, CamundaProperty precannedValue) {
        CamundaProperty camundaProperty = bpmnModelInstance.newInstance(CamundaProperty.class);
        camundaProperty.setCamundaName(precannedValue.getCamundaName());
        camundaProperty.setCamundaValue(precannedValue.getCamundaValue());
        return camundaProperty;
    }

    private CamundaInputParameter addCamundaInputParameter(
            BpmnModelInstance bpmnModelInstance, CamundaInputParameter precannedValue) {
        CamundaInputParameter camundaInputParameter =
                bpmnModelInstance.newInstance(CamundaInputParameter.class);
        camundaInputParameter.setCamundaName(precannedValue.getCamundaName());
        camundaInputParameter.setTextContent(precannedValue.getTextContent());
        return camundaInputParameter;
    }

    private CamundaOutputParameter addCamundaOutputParameter(
            BpmnModelInstance bpmnModelInstance, CamundaOutputParameter precannedValue) {
        CamundaOutputParameter camundaOutputParameter =
                bpmnModelInstance.newInstance(CamundaOutputParameter.class);
        camundaOutputParameter.setCamundaName(precannedValue.getCamundaName());
        camundaOutputParameter.setTextContent(precannedValue.getTextContent());
        return camundaOutputParameter;
    }
}

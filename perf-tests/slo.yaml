version: '0.1.0'
objectives:
  - name: cpu Perc99
    source: wavefront
    query: sum(align(1m, ts("heapster.pod.cpu.usage_rate", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf ))/1000)/sum(align(1m, ts("heapster.pod.cpu.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf ))/1000) * 100
    conditions:
      criteria:
        - label: cpu
          aggregation_fn: perc99
          expression: "<=60"
  - name: no pod restarts
    source: wavefront
    query: align(1m, ts(custom.iks.kube.pod.container.status.restarts.total.counter, namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf ))
    conditions:
      criteria:
        - label: restart count
          aggregation_fn: sum
          expression: "==0"
  - name: max memory usage
    source: wavefront
    query: (sum(align(1m, ts("heapster.pod.memory.rss", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf and label.app=was-event-in))) + sum(align(1m, ts("heapster.pod.memory.page_faults_rate", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf and label.app=was-event-in ))))/sum(align(1m, ts("heapster.pod.memory.request", namespace_name=appintgwkflw-wkflautomate-waseventin-usw2-prf  ))) * 100
    conditions:
      criteria:
        - label: memory check
          aggregation_fn: mean
          expression: "<=40"
  - name: disk space
    source: wavefront
    query: ts("telegraf.disk.free",  app="frontline" and role="controller")
    conditions:
      criteria:
        - label: max disk usage
          aggregation_fn: max
          expression: "<=66674558976"
        - label: mean disk usage
          aggregation_fn: mean
          expression: "==46674558976"
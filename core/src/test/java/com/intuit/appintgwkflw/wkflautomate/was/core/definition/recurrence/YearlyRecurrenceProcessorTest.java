package com.intuit.appintgwkflw.wkflautomate.was.core.definition.recurrence;

import com.intuit.v4.common.DayOfWeekEnum;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.payments.schedule.DayOfWeekType;
import com.intuit.v4.payments.schedule.RecurrencePattern;
import com.intuit.v4.payments.schedule.RecurrencePatternType;
import com.intuit.v4.payments.schedule.WeekIndexType;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.joda.time.DateTime;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

public class YearlyRecurrenceProcessorTest {

  @InjectMocks private YearlyRecurrenceProcessor recurrenceProcessor;
  private final int presentYear = LocalDateTime.now().getYear();

  @Before
  public void setup() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testRecurrenceYearly_EveryYear() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1, RecurTypeEnum.YEARLY, null, null, null, null, new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? * * ").append(presentYear).append("/1");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_Every3Years() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3, RecurTypeEnum.YEARLY, null, null, null, null, new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? * * ").append(presentYear).append("/3");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_EveryYearInJanuaryAndMarch() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            1,
            RecurTypeEnum.YEARLY,
            null,
            null,
            Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.MARCH),
            null,
            new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? 1,3 * ").append(presentYear).append("/1");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_Every2Years1stAnd5ThDayInJanuaryAndMarch() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            2,
            RecurTypeEnum.YEARLY,
            Arrays.asList(1, 5),
            null,
            Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.MARCH),
            WeekOfMonthEnum.FIRST,
            new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 1,5 1,3 ? ").append(presentYear).append("/2");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_Every2YearsEveryMonthEveryLastThursday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            2,
            RecurTypeEnum.YEARLY,
            null,
            Arrays.asList(DayOfWeekEnum.THURSDAY),
            null,
            WeekOfMonthEnum.LAST,
            new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? * 5L ").append(presentYear).append("/2");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_Every2YearsEveryMonthEveryLastThursdayFriday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            2,
            RecurTypeEnum.YEARLY,
            null,
            Arrays.asList(DayOfWeekEnum.THURSDAY, DayOfWeekEnum.FRIDAY),
            null,
            WeekOfMonthEnum.LAST,
            new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? * 5L,6 ").append(presentYear).append("/2");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testRecurrenceYearly_Every3YearsEveryMonthEveryThursdayAndFriday() {
    RecurrenceRule recurrenceRule =
        RecurrenceTestUtil.createRecurrenceRule(
            3,
            RecurTypeEnum.YEARLY,
            null,
            Arrays.asList(DayOfWeekEnum.THURSDAY, DayOfWeekEnum.FRIDAY),
            null,
            null,
            new DateTime());
    StringBuilder expected = new StringBuilder("0 0 0 ? * 5,6 ").append(presentYear).append("/3");
    Assert.assertEquals(expected.toString(), recurrenceProcessor.getRecurrence(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryYear() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    1, RecurTypeEnum.YEARLY, null, null, null, null, new DateTime());
    List<Integer> monthIndex = null;
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(1)
                    .type(RecurrencePatternType.ABSOLUTEYEARLY)
                    .monthIndex(monthIndex)
                    .daysOfMonth(List.of());

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every3Years() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    3, RecurTypeEnum.YEARLY, null, null, null, null, new DateTime());
    List<Integer> monthIndex = null;
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(3)
                    .type(RecurrencePatternType.ABSOLUTEYEARLY)
                    .monthIndex(monthIndex)
                    .daysOfMonth(List.of());

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_EveryYearInJanuaryAndMarch() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    1,
                    RecurTypeEnum.YEARLY,
                    null,
                    null,
                    Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.MARCH),
                    null,
                    new DateTime());
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(1)
                    .type(RecurrencePatternType.ABSOLUTEYEARLY)
                    .monthIndex(List.of(1,3))
                    .daysOfMonth(List.of());

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every2Years1stAnd5ThDayInJanuaryAndMarch() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    2,
                    RecurTypeEnum.YEARLY,
                    Arrays.asList(1, 5),
                    null,
                    Arrays.asList(MonthsOfYearEnum.JANUARY, MonthsOfYearEnum.MARCH),
                    null,
                    new DateTime());
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(2)
                    .type(RecurrencePatternType.ABSOLUTEYEARLY)
                    .monthIndex(List.of(1,3))
                    .daysOfMonth(List.of(1,5));

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every2YearsEveryJanuaryLastThursday() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    2,
                    RecurTypeEnum.YEARLY,
                    null,
                    Arrays.asList(DayOfWeekEnum.THURSDAY),
                    List.of(MonthsOfYearEnum.JANUARY),
                    WeekOfMonthEnum.LAST,
                    new DateTime());
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(2)
                    .type(RecurrencePatternType.RELATIVEYEARLY)
                    .monthIndex(List.of(1))
                    .weekIndex(WeekIndexType.LAST)
                    .daysOfWeek(List.of(DayOfWeekType.THU));

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every2YearsEveryMonthEveryLastThursdayFriday() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    2,
                    RecurTypeEnum.YEARLY,
                    null,
                    Arrays.asList(DayOfWeekEnum.THURSDAY, DayOfWeekEnum.FRIDAY),
                    null,
                    WeekOfMonthEnum.LAST,
                    new DateTime());
    List<Integer> monthIndex = null;
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(2)
                    .type(RecurrencePatternType.RELATIVEYEARLY)
                    .monthIndex(monthIndex)
                    .weekIndex(WeekIndexType.LAST)
                    .daysOfWeek(List.of(DayOfWeekType.THU, DayOfWeekType.FRI));

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }

  @Test
  public void testBuildESSRecurrencePattern_Every3YearsEveryMonthEveryThursdayAndFriday() {
    RecurrenceRule recurrenceRule =
            RecurrenceTestUtil.createRecurrenceRule(
                    3,
                    RecurTypeEnum.YEARLY,
                    null,
                    Arrays.asList(DayOfWeekEnum.THURSDAY, DayOfWeekEnum.FRIDAY),
                    null,
                    null,
                    new DateTime());

    List<Integer> monthIndex = null;
    RecurrencePattern recurrencePattern =
            new RecurrencePattern()
                    .interval(3)
                    .type(RecurrencePatternType.ABSOLUTEYEARLY)
                    .monthIndex(monthIndex).daysOfMonth(List.of());

    Assert.assertEquals(
            recurrencePattern, recurrenceProcessor.buildESSRecurrencePattern(recurrenceRule));
  }
}

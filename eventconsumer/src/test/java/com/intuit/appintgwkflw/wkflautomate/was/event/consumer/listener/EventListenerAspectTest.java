package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.aop.MetricLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowCircuitOpenException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.ExternalTaskKafkaConsumer;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import com.intuit.eventbus.utils.Result.Success;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;

/** <AUTHOR> */
public class EventListenerAspectTest {

  @Mock private WASContextHandler contextHandler;

  @Mock private ProceedingJoinPoint proceedingJoinPoint;

  @Mock private EventListener eventListener;

  @Mock private Acknowledgment acknowledgment;
  
  @Mock private MetricLogger metricLogger;

  @InjectMocks private EventListenerAspect eventListenerAspect;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testInvalidHeaders() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    when(signature.getMethod())
        .thenReturn(ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg()));
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs());
    when(metricLogger.getIsWarning(Mockito.any())).thenReturn(true);
    eventListenerAspect.event(proceedingJoinPoint, eventListener);
    Mockito.verify(acknowledgment).acknowledge();
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEmptyConsumerRecord() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    when(signature.getMethod())
        .thenReturn(ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg()));
    Object[] joinPointArgs = joinPointArgs();
    joinPointArgs[0] = null;
    when(proceedingJoinPoint.getArgs()).thenReturn(joinPointArgs);
    eventListenerAspect.event(proceedingJoinPoint, eventListener);
    Mockito.verify(acknowledgment).acknowledge();
  }

  @Test
  public void testDLQException() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    Method method = ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg());
    when(signature.getMethod()).thenReturn(method);

    Object[] params = new Object[3];
    params[0] = getSuccessMessage();
    params[2] = acknowledgment;
    Map<String, Object> headers = new HashMap<>();

    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotenceKey");
    headers.put(EventHeaderConstants.ENTITY_ID, "entityId");
    headers.put(EventHeaderConstants.OFFERING_ID, "ttlive");
    headers.put(KafkaConstants.KAFKA_TOPIC_HEADER, "test-dlq");
    params[1] = new MessageHeaders(headers);
    when(proceedingJoinPoint.getArgs()).thenReturn(params);

    Mockito.doThrow(new Exception()).when(proceedingJoinPoint).proceed();
    Object response = eventListenerAspect.event(proceedingJoinPoint, eventListener);
    Mockito.verify(acknowledgment).acknowledge();
    Assert.assertNull(response);
  }

  @Test(expected = Exception.class)
  public void testNonDLQException() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    Method method = ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg());
    when(signature.getMethod()).thenReturn(method);

    Object[] params = new Object[3];
    params[0] = getSuccessMessage();
    params[2] = acknowledgment;
    Map<String, Object> headers = new HashMap<>();

    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotenceKey");
    headers.put(EventHeaderConstants.ENTITY_ID, "entityId");
    headers.put(EventHeaderConstants.OFFERING_ID, "ttlive");
    headers.put(KafkaConstants.KAFKA_TOPIC_HEADER, "test");
    params[1] = new MessageHeaders(headers);
    when(proceedingJoinPoint.getArgs()).thenReturn(params);

    Mockito.doThrow(new Exception()).when(proceedingJoinPoint).proceed();
    eventListenerAspect.event(proceedingJoinPoint, eventListener);
  }

  @Test(expected = WorkflowCircuitOpenException.class)
  public void testCircuitOpenException() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    Method method = ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg());
    when(signature.getMethod()).thenReturn(method);

    Object[] params = new Object[3];
    params[0] = getSuccessMessage();
    params[2] = acknowledgment;
    Map<String, Object> headers = new HashMap<>();

    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotenceKey");
    headers.put(EventHeaderConstants.ENTITY_ID, "entityId");
    headers.put(EventHeaderConstants.OFFERING_ID, "ttlive");
    headers.put(KafkaConstants.KAFKA_TOPIC_HEADER, "test");
    params[1] = new MessageHeaders(headers);
    when(proceedingJoinPoint.getArgs()).thenReturn(params);
    CircuitBreaker circuitBreaker = CircuitBreaker.ofDefaults("test");
    CallNotPermittedException ex =
            CallNotPermittedException.createCallNotPermittedException(circuitBreaker);
    Mockito.doThrow(new WorkflowCircuitOpenException(
            WorkflowError.CIRCUIT_OPEN_ERROR, ex)).when(proceedingJoinPoint).proceed();
    eventListenerAspect.event(proceedingJoinPoint, eventListener);
  }

  @Test
  public void testSuccess() throws Throwable {
    MethodSignature signature = mock(MethodSignature.class);
    when(proceedingJoinPoint.getSignature()).thenReturn(signature);
    Method method = ExternalTaskKafkaConsumer.class.getDeclaredMethod("receive", methodArg());
    when(signature.getMethod()).thenReturn(method);

    Object[] params = new Object[3];
    params[0] = getSuccessMessage();
    params[2] = acknowledgment;
    Map<String, Object> headers = new HashMap<>();
    
    headers.put(EventHeaderConstants.INTUIT_TID, "tid");
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "idempotenceKey");
    headers.put(EventHeaderConstants.ENTITY_ID, "entityId");
    headers.put(EventHeaderConstants.OFFERING_ID, "ttlive");
    params[1] = new MessageHeaders(headers);
    when(proceedingJoinPoint.getArgs()).thenReturn(params);
    eventListenerAspect.event(proceedingJoinPoint, eventListener);
    Mockito.verify(acknowledgment).acknowledge();
  }

  /** @return */
  private Object[] joinPointArgs() {
    Object[] args = new Object[3];
    args[0] = getSuccessMessage();
    args[1] = new MessageHeaders(Collections.emptyMap());
    args[2] = acknowledgment;
    return args;
  }

  @SuppressWarnings("rawtypes")
  private Class[] methodArg() {
    Class[] methodArg = new Class[3];
    methodArg[0] = ConsumerRecord.class;
    methodArg[1] = MessageHeaders.class;
    methodArg[2] = Acknowledgment.class;
    return methodArg;
  }

  private ConsumerRecord<String, Result<FormatException, String>> getSuccessMessage() {
    Result<FormatException, String> body = new Success<FormatException, String>("value");
    return new ConsumerRecord<>(
        "topic", 1, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", body);
  }
}

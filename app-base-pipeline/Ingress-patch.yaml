apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    alb.ingress.kubernetes.io/backend-protocol: HTTPS
    alb.ingress.kubernetes.io/certificate-arn: TODO:certificate-ARN
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "60"
    alb.ingress.kubernetes.io/healthcheck-path: /health/full
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTPS
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
    alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=false
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/security-groups: iks-intuit-cidr-ingress-tcp-443, iks-intuit-api-gw-ingress-preprod-tcp-443,
      iks-intuit-app-alb-custom-ingress, iks-intuit-ibp-ingress-tcp-443
    alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS-1-2-2017-01
    argocd.argoproj.io/sync-wave: "-1"
    external-dns.alpha.kubernetes.io/hostname: TODO:albDnsHostname
  labels:
    app: was-event-in
  name: was-event-in-ingress
spec:
  ingressClassName: aws-alb
  rules:
    - http:
        paths:
          - backend:
              service:
                name: was-event-in-root-service
                port:
                  number: 443
            path: /*
            pathType: ImplementationSpecific

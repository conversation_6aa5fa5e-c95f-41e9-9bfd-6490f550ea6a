package com.intuit.appintgwkflw.wkflautomate.was.event.util;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.MDCContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.KafkaConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.MessageHeaders;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.DEFAULT_OFFERING;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.REALM_ID;
import static org.junit.jupiter.api.Assertions.*;

@RunWith(MockitoJUnitRunner.class)
public class EventUtilTest {


  @Test
  public void testMessageHeadersTransform() {
    Map<String, Object> map = new HashMap<>();
    map.put("key", 1);
    map.put("key2", null);

    MessageHeaders messageHeaders = new MessageHeaders(map);
    Map<String, String> result = EventUtil.transformHeader(messageHeaders);
    Assert.assertEquals("1", result.get("key"));
    Assert.assertNull(result.get("key2"));

  }

  @Test
  public void testHeadersTransform() {
    RecordHeaders headers = new RecordHeaders();
    headers.add("key1", "value1".getBytes());
    headers.add("key2", null);
    Map<String, String> result = EventUtil.transformHeader(headers);
    Assert.assertEquals("value1", result.get("key1"));
    Assert.assertNull(result.get("key2"));
  }

  @Test
  public void testPopulateEventContext() {
    MDCContextHandler handler = new  MDCContextHandler();
    Map<String, String> headers = new HashMap<>();
    EventUtil.populateEventContext(handler, headers);
    Assert.assertEquals(handler.get(WASContextEnums.IS_EVENT),"true");
  }

  @Test
  public void testPopulateEventContextDLQ() {
    MDCContextHandler handler = new MDCContextHandler();
    Map<String, String> headers = new HashMap<>();
    headers.put(KafkaConstants.KAFKA_TOPIC_HEADER, "test-dlq");
    EventUtil.populateEventContext(handler, headers);
    Assert.assertEquals(handler.get(WASContextEnums.IS_DLQ), "1");
  }

  @Test
  public void testPopulateEventContextContainsIntuitUserId() {
    MDCContextHandler handler = new MDCContextHandler();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.INTUIT_USER_ID, "1234");
    headers.put(EventHeaderConstants.OWNER_ID,"5678");
    EventUtil.populateEventContext(handler, headers);
    Assert.assertTrue(handler.get(WASContextEnums.AUTHORIZATION_HEADER).contains("intuit_userid"));
  }

  @Test
  public void testPopulateEventContextNotContainsIntuitUserId() {
    MDCContextHandler handler = new MDCContextHandler();
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OWNER_ID,"5678");
    EventUtil.populateEventContext(handler, headers);
    Assert.assertFalse(handler.get(WASContextEnums.AUTHORIZATION_HEADER).contains("intuit_userid"));
  }

  @Test
  public void testPopulateEventContextNonDLQ() {
    MDCContextHandler handler = new MDCContextHandler();
    Map<String, String> headers = new HashMap<>();
    headers.put(KafkaConstants.KAFKA_TOPIC_HEADER, "test");
    EventUtil.populateEventContext(handler, headers);
    Assert.assertEquals(handler.get(WASContextEnums.IS_DLQ), "0");
  }

  @Test
  public void tesIsDLQ() {
    Assert.assertFalse(EventUtil.isDLQ(null));
    Assert.assertFalse(EventUtil.isDLQ(1));
    Assert.assertFalse(EventUtil.isDLQ("test"));
    Assert.assertTrue(EventUtil.isDLQ("test-dlq"));
  }

  @Test
  public void getDLQHeader() {
    Map<String, String> headers =  EventUtil.getDLQHeaders(getHeaders(true, true), "test");
    Assert.assertNotNull(headers.get(EventHeaderConstants.IDEMPOTENCY_KEY));
    Assert.assertNotNull(headers.get(EventHeaderConstants.INTUIT_TID));
    Assert.assertEquals("test", headers.get(KafkaHeaders.TOPIC));
  }


  @Test
  public void getDLQHeaderDefaultTidAndIdemp() {
    Map<String, String> headers =  EventUtil.getDLQHeaders(getHeaders(false, false), "test");
    Assert.assertNotNull(headers.get(EventHeaderConstants.IDEMPOTENCY_KEY));
    Assert.assertNotNull(headers.get(EventHeaderConstants.INTUIT_TID));
    Assert.assertEquals("test", headers.get(KafkaHeaders.TOPIC));
  }

  @Test
  public void testSetOwnerIdWhenOwnerIdIsPresent() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OWNER_ID, "owner123");

    String ownerId = EventUtil.getOwnerId(headers);

    Assert.assertEquals("owner123", ownerId);
  }

  @Test
  public void testSetOfferingIdWhenOfferingIdIsPresent() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OFFERING_ID, "offering123");

    EventUtil.setOfferingId(headers);

    Assert.assertEquals(Optional.of("offering123"), WASContext.getOfferingId());
  }

  @Test
  public void testSetOfferingIdWhenOfferingIdIsNotPresent() {
    Map<String, String> headers = new HashMap<>();

    EventUtil.setOfferingId(headers);

    Assert.assertEquals(Optional.of(DEFAULT_OFFERING), WASContext.getOfferingId());
  }

  @Test
  public void testSetOwnerIdWhenOwnerIdIsNotPresent() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.ACCOUNT_ID, "account123");

    String ownerId = EventUtil.getOwnerId(headers);

    Assert.assertEquals("account123", ownerId);
  }

  @Test
  public void testSetOwnerIdWhenOwnerIdAndAccountIdAreNotPresent() {
    Map<String, String> headers = new HashMap<>();

    String ownerId = EventUtil.getOwnerId(headers);

    Assert.assertNull(ownerId);
  }

  @Test
  public void testTransformHeaders_NonEmptyHeaders() {
    Map<String, String> headers = new HashMap<>();
    headers.put("key1", "value1");
    headers.put("key2", "value2");

    MessageHeaders messageHeaders = EventUtil.transformHeaders(headers);

    assertEquals("value1", messageHeaders.get("key1"));
    assertEquals("value2", messageHeaders.get("key2"));
  }

  @Test
  public void testGetGCHeaders_EmptyHeaders() {
    Map<String, String> headers = new HashMap<>();
    EventEntityType entityType = EventEntityType.TRIGGER;

    MessageHeaders messageHeaders = EventUtil.getGCHeaders(headers, entityType);

    assertEquals(entityType.toString(), messageHeaders.get(EventHeaderConstants.ENTITY_TYPE));
    assertTrue(messageHeaders.containsKey(EventHeaderConstants.ENTITY_TYPE));
    assertTrue(messageHeaders.containsKey(REALM_ID));
  }

  @Test
  public void testGetGCHeaders_NonEmptyHeaders() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OWNER_ID, "owner123");
    EventEntityType entityType = EventEntityType.TRIGGER;

    MessageHeaders messageHeaders = EventUtil.getGCHeaders(headers, entityType);

    assertEquals(entityType.toString(), messageHeaders.get(EventHeaderConstants.ENTITY_TYPE));
    assertEquals("owner123", messageHeaders.get(REALM_ID));
    assertTrue(messageHeaders.containsKey(EventHeaderConstants.ENTITY_TYPE));
    assertTrue(messageHeaders.containsKey(REALM_ID));
  }

  @Test
  public void testGetGCHeaders_NonEmptyHeadersWithId() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OWNER_ID, "owner123");
    headers.put("id", "123");
    EventEntityType entityType = EventEntityType.TRIGGER;

    MessageHeaders messageHeaders = EventUtil.getGCHeaders(headers, entityType);

    assertEquals(entityType.toString(), messageHeaders.get(EventHeaderConstants.ENTITY_TYPE));
    assertEquals("owner123", messageHeaders.get(REALM_ID));
    assertNotEquals("123", messageHeaders.get("id"));
    assertTrue(messageHeaders.containsKey(EventHeaderConstants.ENTITY_TYPE));
    assertTrue(messageHeaders.containsKey(REALM_ID));
  }
  
  @Test
  public void testGetGCHeaders_NonEmptyHeadersWithTimestamp() {
    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OWNER_ID, "owner123");
    headers.put("timestamp", "1234567");
    EventEntityType entityType = EventEntityType.TRIGGER;

    MessageHeaders messageHeaders = EventUtil.getGCHeaders(headers, entityType);

    assertEquals(entityType.toString(), messageHeaders.get(EventHeaderConstants.ENTITY_TYPE));
    assertEquals("owner123", messageHeaders.get(REALM_ID));
    assertTrue(messageHeaders.containsKey(EventHeaderConstants.ENTITY_TYPE));
    Assert.assertNull(messageHeaders.get("timestamp"));
    assertTrue(messageHeaders.containsKey(REALM_ID));
  }

  private Map<String, String> getHeaders(boolean tid, boolean idempotencyKey) {
    Map<String, String> headers = new HashMap<String, String>();
    headers.put(EventHeaderConstants.OWNER_ID, "1234");
    headers.put(EventHeaderConstants.KAFKA_TOPIC_NAME, EventEntityType.TRIGGER.getEntityType());
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP, Instant.now().toString());
    headers.put(EventHeaderConstants.KAFKA_TIMESTAMP_TYPE, "logAppendTime");
    if (tid) {
      headers.put(EventHeaderConstants.INTUIT_TID, UUID.randomUUID().toString());
    }
    headers.put(EventHeaderConstants.ENTITY_ID, UUID.randomUUID().toString());
    headers.put(EventHeaderConstants.OFFERING_ID, "default");
    if (idempotencyKey) {
      headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, "default");
    }
    return headers;
  }
}

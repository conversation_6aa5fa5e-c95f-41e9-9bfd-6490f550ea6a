# was-event-in-config

This repository holds configurations for [was-event-in](https://github.intuit.com/appintgwkflw-wkflautomate/was-event-in) and is an alternative to the [message-mesh-core configurations](https://github.intuit.com/fdp-enrichment/message-mesh-config).  
The env-specific configurations are added in the env-specific branches.

[![Build Status](https://build.intuit.com/qbo-2/buildStatus/buildIcon?job=appintgwkflw-wkflautomate/was-event-in/was-event-in/master)](https://build.intuit.com/qbo-2/job/appintgwkflw-wkflautomate/job/was-event-in/job/was-event-in/job/master/)
[![Code Coverage](https://build.intuit.com/qbo-2/buildStatus/coverageIcon?job=appintgwkflw-wkflautomate/was-event-in/was-event-in/master)](https://build.intuit.com/qbo-2//job/appintgwkflw-wkflautomatejob/was-event-in/job/was-event-in/job/master/)


### Usage

* Add the processor logic in the [event-processor](https://github.intuit.com/appintgwkflw-wkflautomate/event-processor) repository.
* Add the configurations in the appropriate branch in either the [message mesh repository](https://github.intuit.com/fdp-enrichment/message-mesh-config) or [was-event-in config repository](https://github.intuit.com/appintgwkflw-wkflautomate/was-event-in-config).
* Deployment configurations are added [here](https://github.intuit.com/appintgwkflw-wkflautomate/was-event-in-deployment).
* Once ready for deployment, head over to [jenkins](https://build.intuit.com/qbo-2/job/appintgwkflw-wkflautomate/job/was-event-in/job/was-event-in/job/master/build?delay=0sec), and trigger the jenkins build for the required branch.


## Local Development
The pipeline definition can either be merged to dev profile in config repo Or developer can create a config folder in project base directory and place the config files at that location.



## Contributing

- Please refer to the [Contributing Guide](./CONTRIBUTING.md) for details of how to contribute and build locally



## Support

[Reach us](https://github.intuit.com/pages/appintgwkflw-wkflautomate/workflow-platform-docs/#/support/support)
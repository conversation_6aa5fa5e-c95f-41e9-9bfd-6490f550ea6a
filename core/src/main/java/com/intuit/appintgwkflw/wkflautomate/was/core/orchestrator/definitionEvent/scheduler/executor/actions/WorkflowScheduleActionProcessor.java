package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import java.util.HashMap;
import java.util.Map;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.foundation.workflow.scheduling.Execution;

/** <AUTHOR> */
public interface WorkflowScheduleActionProcessor {

  WorkflowNameEnum getWorkflowName();

  //TODO: remove this method after scheduling migration
  Map<String, String> process(
      final SchedulerDetails schedulerDetails, final EventScheduleMessageData eventScheduleMessageData);

  default Map<String, String> process(
          final DefinitionDetails definitionDetails, final Execution schedulingEvent){
    return new HashMap<>();
  }
}

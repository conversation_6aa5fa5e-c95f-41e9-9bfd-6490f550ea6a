package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.MISSING_HANDLER_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.PROCESS_DETAILS_NOT_FOUND_ERROR;
import static com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError.WAS_DB_CONNECTION_ERROR;

import com.google.common.collect.ImmutableMap;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowEventException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventPublisherUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ExternalTaskPublishHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.InternalEventsUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.TaskHandlerName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.externaltask.ExternalTaskAssigned;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.ExternalTaskAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import com.intuit.appintgwkflw.wkflautomate.was.event.api.EventPublisherCapability;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;


/**
 * This class is implementation for executing an external task of BPMN to publish a Kafka event to
 * Intuit Event bus. It will map input variables to Kafka event adhering to V1 schema and push these
 * events to Event bus using a Kafka Producer. It will return a map with "pending" key so that the
 * external task is not marked as complete by Worker Executor. For all activities which want to
 * publish an event to bus we will set fatal property as true in taskDetails so that worker retry
 * kicks in in addition to eventbus SDK retry mechanism. Otherwise in case of failure, the task will
 * be marked as completed and workflow will move to next state.
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
public class PublishEventHandler extends WorkflowTaskHandler {

  private final ProcessDetailsRepository processDetailsRepository;
  private final EventPublisherCapability eventPublisherCapability;
  private final WASContextHandler contextHandler;
  private final EventPublisherUtil eventProducerUtil;

  @Override
  public TaskHandlerName getName() {
    return TaskHandlerName.WAS_PUBLISH_EVENT_HANDLER;
  }

  @Override
  public <T> Map<String, Object> executeAction(T inputRequest) {
    WorkerActionRequest workerActionRequest = (WorkerActionRequest) inputRequest;


    EventingLoggerUtil.logInfo(
        "Publish Event Started step=eventPublishStarted processInstanceId=%s taskId=%s entityId=%s",
        this.getClass().getSimpleName(),
        workerActionRequest.getProcessInstanceId(),
        workerActionRequest.getTaskId(),
        // To add workerId in entityId
        transformEntityId(workerActionRequest));

    // Throw WorkflowNonRetriableException if handlerId is not present
    WorkflowVerfiy.verify(
        StringUtils.isBlank(workerActionRequest.getHandlerId()),
        () -> {
          throw new WorkflowEventException(new WorkflowNonRetriableException(MISSING_HANDLER_ID));
        });

    // Get Process details from DB
    // When DB is down we get JDBCException, catching it and throwing RetriableException to kick in retry mechanism
    Optional<ProcessDetails> processDetails;
    try {
      processDetails = processDetailsRepository
          .findByIdWithoutDefinitionData(workerActionRequest.getProcessInstanceId());
    } catch (Exception e) {
      throw new WorkflowEventException(new WorkflowRetriableException(WAS_DB_CONNECTION_ERROR));
    }

    // Throw WorkflowRetriableException if process details are not available
    ProcessDetails processDetail = processDetails.orElseThrow(() -> new WorkflowEventException(
        new WorkflowRetriableException(PROCESS_DETAILS_NOT_FOUND_ERROR)));

    EventHeaderEntity eventHeaderEntity = buildEventHeader(workerActionRequest, processDetail);
    // Create and publish payload
    try {
      // Send result can be used in the future for logging producer record and adding logic
      eventPublisherCapability
          .publish(eventHeaderEntity, buildEventPayload(workerActionRequest, processDetail));
    } catch (WorkflowEventException e) {
      // Log and Throw when WorkflowEventException is received
      EventingLoggerUtil.logError(
          "Publish Event Failed step=eventPublishFailed processInstanceId=%s taskId=%s entityId=%s error=%s",
          this.getClass().getSimpleName(),
          workerActionRequest.getProcessInstanceId(),
          workerActionRequest.getTaskId(),
          eventHeaderEntity.getEntityId(),
          e);
      throw e;
    }

    EventingLoggerUtil.logInfo(
        "Publish Event Successful step=eventPublishSuccessful processInstanceId=%s taskId=%s entityId=%s",
        this.getClass().getSimpleName(),
        workerActionRequest.getProcessInstanceId(),
        workerActionRequest.getTaskId(),
        eventHeaderEntity.getEntityId());

        /*  Pending state is being used to notify the Worker Executor to filter out these external tasks
            so that they are not marked as completed. For eventing model, the external tasks will be marked
            as completed when acknowledgement is received from client. That functionality will be handled by Kafka Consumer.
         */
    return new HashMap<>(
        ImmutableMap.of(WorkFlowVariables.PENDING_EXTEND_LOCK.getName(), Boolean.TRUE)
    );
  }

  @Override
  protected void logErrorMetric(Exception exception,
      WorkerActionRequest workerActionRequest) {
    /** For eventing we have to rethrow the exception and trigger executeFailure() in WorkerBaseExecutor for all failures */
    WorkflowVerfiy.verify(exception instanceof WorkflowEventException,
        () -> {
          WorkflowGeneralException workflowGeneralException = ((WorkflowEventException) exception).getWorkflowGeneralException();
          WorkflowLogger.error(
              () ->
                  WorkflowLoggerRequest.builder()
                      .message("Task failed activityId=%s taskId=%s processInstanceId=%s errorMsg=%s", workerActionRequest.getActivityId(), workerActionRequest.getTaskId(), workerActionRequest.getProcessInstanceId(), workflowGeneralException.getError().getMessage())
                      .stackTrace(exception)
                      .downstreamServiceName(DownstreamServiceName.WORKER_EXECUTOR)
                      .className(this.getClass().getSimpleName()));
          throw workflowGeneralException;
        });
  }

  private EventHeaderEntity buildEventHeader(WorkerActionRequest workerActionRequest, ProcessDetails processDetails) {

    Map<String, String> headers = new HashMap<>();
    headers.put(EventHeaderConstants.OFFERING_ID, eventProducerUtil.getOfferingId());
    headers.put(EventHeaderConstants.ENTITY_ID, transformEntityId(workerActionRequest));
    headers.put(EventHeaderConstants.IDEMPOTENCY_KEY, workerActionRequest.getTaskId());
    headers.put(EventHeaderConstants.INTUIT_TID, contextHandler.get(WASContextEnums.INTUIT_TID));
    headers.put(EventHeaderConstants.TARGET_ASSET_ALIAS, workerActionRequest.getHandlerId());
    headers.put(EventHeaderConstants.OWNER_ID, String.valueOf(processDetails.getOwnerId()));

    return InternalEventsUtil.buildEventHeader(
            headers,
            PublishEventType.getPublishEventType(PublishEventType.EXTERNAL_TASK, workerActionRequest.getHandlerScope()),
            EventEntityType.EXTERNALTASK);
  }

  public ExternalTaskAssigned buildEventPayload(WorkerActionRequest workerActionRequest,
      ProcessDetails processDetails) {

    ExternalTaskAttributes externalTaskAttributes = ExternalTaskAttributes.builder()
        .extensionAttributes(workerActionRequest.getExtensionProperties())
        .variableMap(workerActionRequest.getVariableMap())
        .build();

    return ExternalTaskPublishHelper.buildEventPayload(processDetails, externalTaskAttributes);
  }

  /**
   * @return String It sends the entityId as taskId:workerId.
   */

  private String transformEntityId(WorkerActionRequest workerActionRequest) {
    return ExternalTaskPublishHelper.transformEntityId(
        workerActionRequest.getTaskId(), workerActionRequest.getWorkerId());
  }
}

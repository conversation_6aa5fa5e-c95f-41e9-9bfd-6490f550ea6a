package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events;

import com.intuit.appintgwkflw.wkflautomate.was.entity.redactPII.RedactSensitiveField;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/** <AUTHOR> */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityMetaData {
  private String scope;
  private String activityId;
  private String activityName;
  private String parentActivityId;
  @RedactSensitiveField
  private Map<String, String> properties; // extension properties
  @RedactSensitiveField
  private Map<String, Object> variables; // process variables
  private String externalTaskId;
}

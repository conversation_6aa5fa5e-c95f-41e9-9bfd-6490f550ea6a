package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.rollback;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.TaskType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.async.execution.request.State;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class SaveTaskDetailRollbackTaskTest {

  private SaveTaskDetailRollBackTask task;

  @Test
  public void testExecute() {

    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, true);

    State state = new State();
    TemplateDetails template = new TemplateDetails();
    template.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);

    List<ActivityDetail> activityDetails = new ArrayList<>();
    
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    .activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);

    state = task.execute(state);
    Assert.assertNotNull(state);
  }

  @Test
  public void testExecuteWithoutTemplateDeatils() {

    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, true);

    State state = new State();

    List<ActivityDetail> activityDetails = new ArrayList<>();
    
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    .activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
    
    state = task.execute(state);
    Assert.assertNotNull(state);
    verify(activityDetailRepository, never())
        .deleteByTemplateDetails(Mockito.any(TemplateDetails.class));
  }

  @Test
  public void testExecuteWithoutTaskDetails() {

    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, true);

    State state = new State();
    TemplateDetails template = new TemplateDetails();
    template.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);
    state = task.execute(state);
    Assert.assertNotNull(state);
    verify(activityDetailRepository, never())
        .deleteByTemplateDetails(Mockito.any(TemplateDetails.class));
  }

  @Test
  public void testExecuteError() {

    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, true);

    State state = new State();
    TemplateDetails template = new TemplateDetails();
    template.setId("id");
    state.addValue(AsyncTaskConstants.SAVE_TEMPLATE_RESPONSE_KEY, template);

    List<ActivityDetail> activityDetails = new ArrayList<>();
    
    ActivityDetail humantask =  ActivityDetail.builder().activityId("activityId1").type(TaskType.HUMAN_TASK)
    .activityName("ActivityName1").attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(humantask);
    
    ActivityDetail systemTask = ActivityDetail.builder().activityId("activityId1")
    		.type(TaskType.SYSTEM_TASK).activityName("ActivityName1")
    		.attributes(ObjectConverter.toJson(WorkflowActivityAttributes.builder()
    		.modelAttributes(ObjectConverter.fromJson("{\"key1\":\"value1\"}", new TypeReference<Map<String, String>>() {
            })).build())).build();
    activityDetails.add(systemTask);
    
    state.addValue(AsyncTaskConstants.TASKS_DETAILS_KEY, activityDetails);
    
    Mockito.doThrow(new NullPointerException()).when(activityDetailRepository)
        .deleteByTemplateDetails(Mockito.any(TemplateDetails.class));
    state = task.execute(state);
    Assert.assertNotNull(state);

  }

  @Test
  public void testRollbackFeatureOff() {
    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, false);
    task.execute(new State());
    Mockito.verifyNoInteractions(activityDetailRepository);
  }
  
  @Test
  public void testIsFatal() {
    ActivityDetailsRepository activityDetailRepository = Mockito
        .mock(ActivityDetailsRepository.class);

    task = new SaveTaskDetailRollBackTask(activityDetailRepository, false);
    Assert.assertFalse(task.isFatal());

  }

}

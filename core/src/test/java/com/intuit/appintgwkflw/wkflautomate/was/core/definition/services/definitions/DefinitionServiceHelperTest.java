package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.internal.verification.VerificationModeFactory.times;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConnectConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.EventScheduleConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheNonRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.CacheRetriableException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.cache.service.EnabledDefinitionCacheService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.TemplateModelInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.RecurrenceUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.DefinitionDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.mappers.ActionModelToScheduleRequestMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.capability.CustomWorkflowQueryCapability;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.AppConnectService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.EventScheduleService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.SchedulingService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.AppConnectServiceMockImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.CorrelateAllMessageTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.MultiStepSaveDefinitionDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveEventScheduleWorkflowTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.SaveScheduleDetailsInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.StartRecurringProcessTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventScheduleTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateEventSchedulingTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks.UpdateSchedulerDetailsInDataStoreTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.triggerHandler.TriggerRecurringProcessHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.EventScheduleHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ProcessDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.SchedulerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.AsyncTaskConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.EntityChangeAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ProcessStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DeployDefinition;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.EventScheduleWorkflowActionModel;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model.SchedulingMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.request.SchedulingSvcRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.EventScheduleResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.respone.SchedulingSvcResponse;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.common.Metadata;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.RecurrenceRule;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.payments.schedule.ScheduleStatus;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.dao.DataAccessException;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.CollectionUtils;

/**
 * Author: Nitin Gupta Date: 28/01/20 Description: {@link com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper}
 */
@RunWith(MockitoJUnitRunner.class)
public class DefinitionServiceHelperTest {

  private static final String templateId = "templateId";
  private static final String definitionId = "def-id";
  private static final String definitionKey = "def-key";

  private static final Long originalSetupUser = 123L;
  private static final String workflowId = "wkid";
  private static final String REALM_ID = "12345";
  private static final String exceptionExpected = "Exception expected";
  private static final String INVOICE_APPROVAL_BPMN = "bpmn/invoiceapproval.bpmn";
  private static final String CUSTOM_REMINDER_WITH_BPMN = "bpmn/customReminderWithSystemTag.bpmn";
  private static final String INVOICE_APPROVAL_DMN = "dmn/decision_invoiceapproval.dmn";
  private static final String CUSTOM_APPROVAL_MULTI_CONDITION_BPMN = "bpmn/customApproval_multiCondition.bpmn";
  private static final String CUSTOM_REMINDER_MULTI_CONDITION_BPMN = "bpmn/customReminder_multiCondition.bpmn";
  private static final String SEND_FOR_APPROVAL_CHILD_BPMN_XML = "bpmn/sendForApproval.bpmn";
  private static final String SEND_FOR_REMINDER_CHILD_BPMN_XML = "bpmn/sendForReminder.bpmn";
  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);

  private static final String KEY = "123";
  private static final int ASSERT_SIZE = 1;
  @InjectMocks private AppConnectServiceMockImpl appConnectServiceMockImpl;
  @Mock
  public static AppConnectConfig appConnectConfig;
  @InjectMocks
  private DefinitionServiceHelper definitionServiceHelper;
  @Mock private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  @Mock private AuthDetailsService authDetailsService;
  @Mock private WASContextHandler contextHandler;
  @Mock private AppConnectService appConnectService;
  @Mock private AuthDetailsServiceHelper authDetailsServiceHelper;
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private TemplateDetailsRepository templateDetailsRepository;
  @Mock private ProcessDetailsRepository processDetailsRepository;
  @Mock private DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private TriggerRecurringProcessHelper recurringProcessHelper;
  @Mock private StartRecurringProcessTask startRecurringProcessTask;
  @Mock private CorrelateAllMessageTask correlateAllMessageTask;
  @Mock private CustomWorkflowQueryCapability customWorkflowQueryCapability;
  @Mock private DefinitionDomainEventHandler definitionDomainEventHandler;
  @Mock private EventScheduleHelper eventScheduleHelper;
  @Mock private SchedulerDetailsRepository schedulerDetailsRepository;
  @Mock private EventScheduleService eventScheduleService;
  @Mock private MultiStepSaveDefinitionDataStoreTask multiStepSaveDefinitionDataStoreTask;
  @Mock private EnabledDefinitionCacheService enabledDefinitionCacheService;
  @Mock private EventScheduleConfig eventScheduleConfig;
  @Mock private ActionModelToScheduleRequestMapper actionModelToScheduleRequestMapper;
  @Mock private SchedulingService schedulingService;
  @Mock private IXPManager ixpManager;

  private Definition definition;
  private Definition definitionDisabled;
  private DefinitionInstance definitionInstance;
  private DefinitionInstance definitionInstanceDisabled;
  private DeployDefinition deployDefinition;

  @Captor ArgumentCaptor<State> stateArgumentCaptor;

  public static BpmnModelInstance readBPMNFile(String fileName) {
    return Bpmn.readModelFromStream(
        DefinitionServiceHelperTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  /**
   * @param fileName
   * @return : DMN Model Instance
   */
  public static DmnModelInstance readDMNFile(String fileName) {
    return Dmn.readModelFromStream(
        DefinitionServiceHelperTest.class.getClassLoader().getResourceAsStream(fileName));
  }

  @Before
  public void setup() {
    MockitoAnnotations.openMocks(this);
    Mockito.doReturn("98765").when(appConnectConfig).getWorkflowId();
    ReflectionTestUtils.setField(
        definitionServiceHelper, "appConnectService", appConnectServiceMockImpl);
    definition = TestHelper.mockDefinitionEntity();
    definitionDisabled = TestHelper.mockDefinitionEntityDisabled();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    Definition definitionWithId = definition;
    Definition definitionWithIdDisabled = definitionDisabled;
    definitionWithId.setId(TestHelper.getGlobalId("123"));
    definitionInstance =
        TestHelper.mockDefinitionInstanceWithPlaceholder(
            definitionWithId,
            bpmnTemplateDetail,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance));
    definitionInstance.setDefinitionDetails(
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization));
    definitionInstanceDisabled =
        TestHelper.mockDefinitionInstanceWithPlaceholder(
            definitionWithIdDisabled,
            bpmnTemplateDetail,
            bpmnModelInstance,
            Collections.singletonList(dmnModelInstance));
    definitionInstance.setDefinitionDetails(
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization));
    deployDefinition =
        TestHelper.createDeployDefinitionRequest(
            definitionInstance.getBpmnModelInstance(),
            definitionInstance.getDmnModelInstanceList());
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setId("456");
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedProcess = new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("123");
    deployedProcess.put("123", deployedDefinition);
    deployDefinitionResponse.setDeployedProcessDefinitions(deployedProcess);
    Mockito.when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
        .thenReturn(
            WASHttpResponse.builder()
                .response(deployDefinitionResponse)
                .isSuccess2xx(true)
                .build());
    ReflectionTestUtils.setField(
        definitionServiceHelper, "eventScheduleHelper", eventScheduleHelper);
    ReflectionTestUtils.setField(definitionServiceHelper, "schedulingService", schedulingService);
    ReflectionTestUtils.setField(
        definitionServiceHelper, "ixpManager", ixpManager);
  }

  @Test
  public void fetchDefinitionsDBInteraction() {
    String templateId = definition.getTemplate().getId().getLocalId();
    List<DefinitionDetails> definitionDetails =
        definitionServiceHelper.fetchDefinitions(templateId, authorization.getRealm());
    Assert.assertEquals(0, definitionDetails.size());
    Mockito.verify(definitionDetailsRepository)
        .findByOwnerIdAndTemplateDetails(
            Mockito.eq(Long.valueOf(authorization.getRealm())),
            Mockito.eq(TemplateDetails.builder().id(templateId).build()));
  }

  @Test
  public void fetchDefinitionsException() {
    String templateId = definition.getTemplate().getId().getLocalId();
    Mockito.when(
            definitionDetailsRepository.findByOwnerIdAndTemplateDetails(
                Long.parseLong(authorization.getRealm()),
                TemplateDetails.builder().id(templateId).build()))
        .thenReturn(Optional.empty());
    List<DefinitionDetails> definitionDetails =
        definitionServiceHelper.fetchDefinitions(templateId, authorization.getRealm());
    Assert.assertEquals(0, definitionDetails.size());
  }

  @Test
  public void fetchDefinitionsNoDefIdException() {
    String templateId = definition.getTemplate().getId().getLocalId();

    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);
    Mockito.when(
            definitionDetailsRepository.findByOwnerIdAndTemplateDetails(
                Long.parseLong(authorization.getRealm()),
                TemplateDetails.builder().id(templateId).build()))
        .thenReturn(Optional.of(definitionDetails));

      Assert.assertFalse(definitionServiceHelper.fetchDefinitions(templateId, authorization.getRealm()).isEmpty());
  }

  @Test
  public void executeDisableDeleteWorkflowTest() {
    definitionServiceHelper.executeDisableDeleteWorkflowTasks(REALM_ID, new ArrayList<>());
    Mockito.verifyNoInteractions(definitionDetailsRepository);
    Mockito.verifyNoInteractions(appConnectService);
  }

  @Test
  public void fetchDefinitionsNoException() {
    String templateId = definition.getTemplate().getId().getLocalId();

    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);
    Mockito.when(
            definitionDetailsRepository.findByOwnerIdAndTemplateDetails(
                Long.parseLong(authorization.getRealm()),
                TemplateDetails.builder().id(templateId).build()))
        .thenReturn(Optional.of(definitionDetails));
    final List<DefinitionDetails> definitionDetails1 =
        definitionServiceHelper.fetchDefinitions(templateId, authorization.getRealm());
    Assert.assertEquals(definitionDetails1, definitionDetails);
  }

  @Test
  public void getCountOfActiveProcessDetailsInteraction() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    definitionServiceHelper.getCountOfActiveProcessDetails(definition.getId().getLocalId());

    Mockito.verify(processDetailsRepository)
        .countByDefinitionDetailsAndProcessStatus(
            Mockito.eq(
                DefinitionDetails.builder().definitionId(definition.getId().getLocalId()).build()),
            Mockito.eq(ProcessStatus.ACTIVE));
  }

  @Test
  public void getActiveDefinitionCountZero() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setStatus(Status.DISABLED);
    List<DefinitionDetails> definitionDetails = Collections.singletonList(definitionDetail);

    final long activeDefinitionCount =
        definitionServiceHelper.getActiveDefinitionCount(definitionDetails);
    Assert.assertEquals(0, activeDefinitionCount);
  }

  @Test
  public void testExecuteAsyncWorkflowTasks() {
    DefinitionServiceHelper definitionServiceHelperSpy = Mockito.spy(definitionServiceHelper);
    Mockito.doReturn(new State())
        .when(definitionServiceHelperSpy)
        .createWithRollBack(anyBoolean(), any(), any(), any());

    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getVersion()).thenReturn(1);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    Definition definition = new Definition();
    definition.setDisplayName("Invoice Approval");
    definition.setName("customApproval");
    definition.setRecordType(RecordType.INVOICE.getRecordType());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    Authorization authorization = new Authorization();
    authorization.putRealm("134");
    WASContext.setAuthContext(authorization);
    Definition updatedDefinition =
        definitionServiceHelperSpy.executeAsyncWorkflowTasks(
            definitionInstance, authorization, false);
    Assert.assertNotNull(updatedDefinition);
    Assert.assertEquals(updatedDefinition.getRecordType(), RecordType.INVOICE.getRecordType());
    verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
    WASContext.clear();
  }

  @Test
  public void testExecuteAsyncWorkflowTasks_withEntityOps() {
    DefinitionServiceHelper definitionServiceHelperSpy = Mockito.spy(definitionServiceHelper);
    Mockito.doReturn(new State())
        .when(definitionServiceHelperSpy)
        .createWithRollBack(anyBoolean(), stateArgumentCaptor.capture(), any(), any());

    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getVersion()).thenReturn(2);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    Definition definition = new Definition();
    definition.setDisplayName("Invoice Approval");
    definition.setName("customApproval");
    definition.setRecordType(RecordType.INVOICE.getRecordType());

    WorkflowStep workflowStep = new WorkflowStep();
    Trigger trigger = new Trigger();
    InputParameter parameter = new InputParameter();
    parameter.setParameterName("entityOperation");
    parameter.setFieldValues(0, "create");
    parameter.setFieldValues(1, "update");
    trigger.add("parameters", List.of(parameter));
    workflowStep.setTrigger(trigger);
    definition.setWorkflowSteps(List.of(workflowStep));

    when(definitionInstance.getDefinition()).thenReturn(definition);
    Authorization authorization = new Authorization();
    authorization.putRealm("134");
    WASContext.setAuthContext(authorization);
    Definition updatedDefinition =
        definitionServiceHelperSpy.executeAsyncWorkflowTasks(
            definitionInstance, authorization, false);
    Assert.assertNotNull(updatedDefinition);
    Assert.assertEquals(updatedDefinition.getRecordType(), RecordType.INVOICE.getRecordType());
    Assert.assertEquals(
        "create,update",
        stateArgumentCaptor.getValue().getValue(AsyncTaskConstants.ENTITY_OPERATION));
    verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
    WASContext.clear();
  }

  @Test
  public void testStartRecurringProcessTaskInExecutionChain() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customScheduledActions");
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setRecurrence(
        new RecurrenceRule()
            .interval(1)
            .recurType(RecurTypeEnum.MONTHLY)
            .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.doReturn(startRecurringProcessTask)
        .when(recurringProcessHelper)
        .getStartRecurringProcessTask(any(DefinitionInstance.class), any(State.class));

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      verify(recurringProcessHelper, times(1)).getStartRecurringProcessTask(any(), any());
      verify(recurringProcessHelper, never()).prepareCorrelateMessageTask(any(), any());
    }
  }

  @Test
  public void testCorrelateAllMessageTaskInExecutionChain() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setRecurrence(
        new RecurrenceRule()
            .interval(1)
            .recurType(RecurTypeEnum.MONTHLY)
            .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.doReturn(startRecurringProcessTask)
        .when(recurringProcessHelper)
        .getStartRecurringProcessTask(any(DefinitionInstance.class), any(State.class));
    Mockito.doReturn(correlateAllMessageTask)
        .when(recurringProcessHelper)
        .prepareCorrelateMessageTask(any(DefinitionInstance.class), any(State.class));

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
    verify(recurringProcessHelper, times(1)).getStartRecurringProcessTask(any(), any());
    verify(recurringProcessHelper, times(1)).prepareCorrelateMessageTask(any(), any());
  }

  @Test
  public void testStartRecurringProcessTaskInExecutionChain_EventScheduleEnabled() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setRecurrence(
        new RecurrenceRule()
            .interval(1)
            .recurType(RecurTypeEnum.MONTHLY)
            .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.when(eventScheduleHelper.isEventSchedulingEnabledForWorkflow(any(), any())).thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
      verify(recurringProcessHelper, never()).prepareCorrelateMessageTask(any(), any());
    }
  }

  @Test
  public void testUpdateRecurringWorkflowInExecutionChain_EventScheduleEnabled_OldDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setRecurrence(
        new RecurrenceRule()
            .interval(1)
            .recurType(RecurTypeEnum.MONTHLY)
            .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.doReturn(correlateAllMessageTask)
        .when(recurringProcessHelper)
        .prepareCorrelateMessageTask(any(DefinitionInstance.class), any(State.class));
    Mockito.when(eventScheduleHelper.isEventSchedulingEnabledForWorkflow(any(), any())).thenReturn(true);
    Mockito.when(recurringProcessHelper.isDefinitionSupportingRecurringProcess(any()))
        .thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);

    verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
    verify(recurringProcessHelper, times(1)).prepareCorrelateMessageTask(any(), any());
    verify(eventScheduleHelper, times(1)).prepareScheduleCreateTasks(any(), any());
  }

  @Test
  public void testUpdateRecurringWorkflowInExecutionChain_EventScheduleDisabled_OldDefinitionDisabled() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setStatus(WorkflowStatusEnum.DISABLED);
    definition.setRecurrence(
            new RecurrenceRule()
                    .interval(1)
                    .recurType(RecurTypeEnum.MONTHLY)
                    .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.when(eventScheduleHelper.isEventSchedulingEnabledForWorkflow(any(), any())).thenReturn(false);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);

    verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
    verify(recurringProcessHelper, never()).prepareCorrelateMessageTask(any(), any());
    verify(eventScheduleHelper, never()).prepareScheduleCreateTasks(any(), any());
  }

  @Test
  public void testUpdateRecurringWorkflowInExecutionChain_EventScheduleEnabled_NewDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setRecurrence(
        new RecurrenceRule()
            .interval(1)
            .recurType(RecurTypeEnum.MONTHLY)
            .startDate(new DateTime()));
    when(definitionInstance.getDefinition()).thenReturn(definition);

    Mockito.when(eventScheduleHelper.isEventSchedulingEnabledForWorkflow(any(), any())).thenReturn(true);
    Mockito.when(recurringProcessHelper.isDefinitionSupportingRecurringProcess(any()))
            .thenReturn(false);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);

    verify(recurringProcessHelper, never()).getStartRecurringProcessTask(any(), any());
    verify(recurringProcessHelper, never()).prepareCorrelateMessageTask(any(), any());
    verify(eventScheduleHelper, never()).prepareScheduleCreateTasks(any(), any());
  }

  @Test
  public void getActiveDefinitionCountOne() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setStatus(Status.ENABLED);

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail2.setStatus(Status.DISABLED);

    List<DefinitionDetails> definitionDetails = Arrays.asList(definitionDetail, definitionDetail2);

    final long activeDefinitionCount =
        definitionServiceHelper.getActiveDefinitionCount(definitionDetails);
    Assert.assertEquals(1, activeDefinitionCount);
  }

  @Test
  public void isDefinitionDeletePermissibleTrue() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setStatus(Status.ENABLED);

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail2.setStatus(Status.DISABLED);

    Mockito.when(
            processDetailsRepository.countByDefinitionDetailsAndProcessStatus(
                DefinitionDetails.builder().definitionId(definition.getId().getLocalId()).build(),
                ProcessStatus.ACTIVE))
        .thenReturn((long) 0);
    final boolean definitionDeletePermissible =
        definitionServiceHelper.isDefinitionDeletePermissible(definition.getId().getLocalId());

    Assert.assertTrue(definitionDeletePermissible);
  }

  @Test
  public void isDefinitionDeletePermissibleFalse() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setStatus(Status.ENABLED);

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail2.setStatus(Status.DISABLED);

    Mockito.when(
            processDetailsRepository.countByDefinitionDetailsAndProcessStatus(
                DefinitionDetails.builder().definitionId(definition.getId().getLocalId()).build(),
                ProcessStatus.ACTIVE))
        .thenReturn((long) 1);
    final boolean definitionDeletePermissible =
        definitionServiceHelper.isDefinitionDeletePermissible(definition.getId().getLocalId());

    Assert.assertFalse(definitionDeletePermissible);
  }

  @Test
  public void isDefinitionDeletePermissibleTrue1() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail.setStatus(Status.DISABLED);

    DefinitionDetails definitionDetail2 =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetail2.setStatus(Status.DISABLED);

    final boolean definitionDeletePermissible =
        definitionServiceHelper.isDefinitionDeletePermissible(definition.getId().getLocalId());

    Assert.assertTrue(definitionDeletePermissible);
  }

  @Test
  public void testFailedSaveDefinitionRollBack() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE));
    }
  }

  @Test
  public void testFailedAppConnectCallRollBack() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);

    State state = new State();
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");

    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(
          e.getMessage(), WorkflowError.INVALID_BPMN_MODEL_INSTANCE.getErrorMessage());
    }
  }

  @Test
  public void testFailedDBUpdateCallRollBack() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));

    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    // Mock DB update task passing
    doThrow(new RuntimeException())
        .when(definitionDetailsRepository)
        .setWorkflowId(anyString(), anyString());
    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(), WorkflowError.DEFINITION_UPDATE_ERROR.getErrorMessage());
    }
  }

  @Test
  public void testCreateRollbackSingleDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");

    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    when(definitionInstance.getDmnModelInstanceList())
        .thenReturn(Collections.singletonList(readDMNFile(INVOICE_APPROVAL_DMN)));
    definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    // assert that deployment task is not executed
    verifyNoInteractions(bpmnEngineDefinitionServiceRest);
    // verify appconnect task is executed
    // state should have workflowId set
    Assert.assertNotNull(AsyncTaskConstants.WORKFLOW_ID_KEY);
    // no updateWorkflowId task needs to be called
    verify(definitionDetailsRepository, never()).setWorkflowId(anyString(), anyString());
    verify(definitionDetailsRepository, times(1)).saveAll(anyIterable());
    verify(enabledDefinitionCacheService, Mockito.times(1))
            .updateCacheWithDefinitionDetails(Mockito.any());
  }

  @Test
  public void testCreateRollbackUserDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    State state1 =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);

    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    WASContext.clear();
  }

  @Test
  public void testCreateRollbackForMultiConditionDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockMultiConditionDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    WASContext.setMigrationContext(false);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    State state1 =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNull(state1.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    verifyNoInteractions(bpmnEngineDefinitionServiceRest);
    verify(definitionDetailsRepository, never()).setWorkflowId(anyString(), anyString());
    WASContext.clear();
  }

  @Test
  public void testCreateRollbackMigrationDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition =
        TestHelper.mockCustomWorkflowDefinition(
            RecordType.INVOICE.getRecordType(), CustomWorkflowType.APPROVAL.getActionKey());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getDefinitionDetails())
        .thenReturn(mock(DefinitionDetails.class));
    when(definitionInstance.getDefinitionDetails().getStatus()).thenReturn(Status.ENABLED);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    when(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(any()))
        .thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    State migrationState =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(migrationState.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(migrationState.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(migrationState.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(migrationState.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));

    try {
      Definition definition1 = TestHelper.mockDefinitionEntity();
      definition1.setStatus(null);
      when(definitionInstance.getDefinition()).thenReturn(definition1);
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE));
    }
    WASContext.clear();
  }

  
  @Test
  public void testCreateRollback_RealmSystemUserDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition =
        TestHelper.mockCustomWorkflowDefinition(
            RecordType.INVOICE.getRecordType(), CustomWorkflowType.APPROVAL.getActionKey());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getDefinitionDetails())
        .thenReturn(mock(DefinitionDetails.class));
    when(definitionInstance.getDefinitionDetails().getStatus()).thenReturn(Status.ENABLED);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    when(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(any()))
        .thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    WASContext.setRealmSystemUserContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    State createState =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(createState.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(createState.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(createState.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(createState.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));

    try {
      Definition definition1 = TestHelper.mockDefinitionEntity();
      definition1.setStatus(null);
      when(definitionInstance.getDefinition()).thenReturn(definition1);
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE));
    }
    WASContext.clear();
  }
  
  @Test
  public void testRollbackMigrationDefinitionWhenCacheUpdateFailsWithCreate() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition =
            TestHelper.mockCustomWorkflowDefinition(
                    RecordType.INVOICE.getRecordType(), CustomWorkflowType.APPROVAL.getActionKey());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getDefinitionDetails())
            .thenReturn(mock(DefinitionDetails.class));
    when(definitionInstance.getDefinitionDetails().getStatus()).thenReturn(Status.ENABLED);

    doThrow(
            new CacheRetriableException(
                    WorkflowError.POPULATE_CACHE_FAILED,
                    new Exception()
            )
    ).when(enabledDefinitionCacheService).updateCacheWithDefinitionDetails(any());

    when(customWorkflowQueryCapability.isPrecannedDefinitionPresentDuringMigration(any()))
            .thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertTrue(state.getValue(AsyncTaskConstants.SAVE_DEFINITION_TASK_FAILURE));
      WorkflowGeneralException err = (WorkflowGeneralException) e.getCause();
      Assert.assertEquals(WorkflowError.POPULATE_CACHE_FAILED, err.getWorkflowError());
    }
    WASContext.clear();
  }

  @Test
  public void testCreateRollbackUpdateDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.USER);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getDefinitionDetails())
        .thenReturn(mock(DefinitionDetails.class));
    when(definitionInstance.getDefinitionDetails().getStatus()).thenReturn(Status.ENABLED);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    State state1 =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    WASContext.clear();
  }

  @Test
  public void testCreateRollbackSingleDefinitionRealmUser() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");

    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    when(definitionInstance.getDmnModelInstanceList())
        .thenReturn(Collections.singletonList(readDMNFile(INVOICE_APPROVAL_DMN)));
    WASContext.setMigrationContext(true);
    State state1 =
        definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
    // assert that deployment task is not executed
    verifyNoInteractions(bpmnEngineDefinitionServiceRest);
    // verify appconnect task is executed
    // state should have workflowId set
    Assert.assertNotNull(AsyncTaskConstants.WORKFLOW_ID_KEY);
    //  Verify AuthDetails aren't called
    Assert.assertNull(state1.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    WASContext.clear();
  }

  @Test
  public void testfindByOwnerIdAndModelType() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetail =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    List<DefinitionDetails> definitionDetails = new ArrayList<>();
    definitionDetails.add(definitionDetail);
    long realm = Long.parseLong(REALM_ID);
    Mockito.when(definitionDetailsRepository.findByOwnerIdAndModelType(realm, ModelType.BPMN))
        .thenReturn(Optional.of(definitionDetails));
    Assert.assertNotNull(definitionServiceHelper.getAllDefinitionList(realm));
  }

  @Test
  public void findByOwnerIdAndModelTypeNull() {
    long realm = Long.parseLong(REALM_ID);
    Mockito.when(definitionDetailsRepository.findByOwnerIdAndModelType(realm, ModelType.BPMN))
        .thenReturn(Optional.empty());
    Assert.assertNull(definitionServiceHelper.getAllDefinitionList(realm));
  }

  @Test
  public void findEnabledDefinitionForTemplateTestSuccess() {
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, templateId);
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndTemplateDetails_IdAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID), templateId, Status.ENABLED, ModelType.BPMN))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
    Assert.assertNotNull(
        definitionServiceHelper.findEnabledDefinitionForTemplate(
            templateId, REALM_ID, Status.ENABLED));
  }

  @Test
  public void findEnabledDefinitionForTemplateTestNull() {
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndTemplateDetails_IdAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID), templateId, Status.ENABLED, ModelType.BPMN))
        .thenReturn(Optional.empty());
    Assert.assertNull(
        definitionServiceHelper.findEnabledDefinitionForTemplate(
            templateId, REALM_ID, Status.ENABLED));
  }

  @Test
  public void getDefinitionListByWorkflowIdTest() {
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, templateId);
    Mockito.when(
            definitionDetailsRepository.findByWorkflowIdAndOwnerId(
                workflowId, Long.valueOf(REALM_ID)))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
      Assert.assertFalse(definitionServiceHelper
              .getDefinitionListByWorkflowId(workflowId, Long.valueOf(REALM_ID)).isEmpty());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void getDefinitionListByWorkflowIdTestError() {
    Mockito.when(
            definitionDetailsRepository.findByWorkflowIdAndOwnerId(
                workflowId.concat("1"), Long.valueOf(REALM_ID)))
        .thenThrow(WorkflowGeneralException.class);
    definitionServiceHelper.getDefinitionListByWorkflowId(
        workflowId.concat("1"), Long.valueOf(REALM_ID));
  }

  @Test
  public void getDefinitionListByDefinitionKeyTest() {
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, templateId);
    Mockito.when(
            definitionDetailsRepository.findByDefinitionKeyAndOwnerId(
                definitionKey, Long.valueOf(REALM_ID)))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
      Assert.assertFalse(definitionServiceHelper
              .getDefinitionListByDefinitionKeyAndOwnerId(definitionKey, Long.valueOf(REALM_ID)).isEmpty());
  }

  @Test(expected = WorkflowGeneralException.class)
  public void getDefinitionListByDefinitionKeyTestError() {
    Mockito.when(
            definitionDetailsRepository.findByDefinitionKeyAndOwnerId(
                definitionKey, Long.valueOf(REALM_ID)))
        .thenThrow(WorkflowGeneralException.class);
    definitionServiceHelper.getDefinitionListByDefinitionKeyAndOwnerId(
        definitionKey, Long.valueOf(REALM_ID));
  }

  @Test
  public void testGetDefinitionList() {
    QueryHelper queryHelper = mock(QueryHelper.class);
    definitionServiceHelper.getDefinitionList(Long.parseLong(REALM_ID), queryHelper);
    verify(definitionDetailsRepository)
        .findAllDefinitions(Long.valueOf(REALM_ID), ModelType.BPMN, queryHelper);
  }

  @Test
  public void testGetDefinitionListWithoutWorkflowSteps() {
    QueryHelper queryHelper = mock(QueryHelper.class);
    definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
        Long.parseLong(REALM_ID), queryHelper);
    verify(definitionDetailsRepository)
        .findAllDefinitionsWithoutWorkflowSteps(
            Long.valueOf(REALM_ID), ModelType.BPMN, queryHelper);
  }

  @Test
  public void testFindByDefinitionId() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .ownerId(Long.valueOf(REALM_ID))
            .templateDetails(TemplateDetails.builder().templateName("tempname").build())
            .build();
    Mockito.when(
            definitionDetailsRepository.findByDefinitionIdAndOwnerId(
                definitionId, Long.parseLong(REALM_ID)))
        .thenReturn(Optional.ofNullable(definitionDetails));

    Mockito.doNothing()
        .when(contextHandler)
        .addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());

    Assert.assertEquals(
        definitionDetails, definitionServiceHelper.findByDefinitionId(definitionId, REALM_ID));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFindByDefinitionIdError() {
    Mockito.when(
            definitionDetailsRepository.findByDefinitionIdAndOwnerId(
                workflowId, Long.parseLong(REALM_ID)))
        .thenReturn(Optional.empty());
    definitionServiceHelper.findByDefinitionId(workflowId, REALM_ID);
  }

  @Test
  public void testFindByDefinitionIdOrParentId() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .ownerId(Long.valueOf(REALM_ID))
            .build();
    Mockito.when(
            definitionDetailsRepository.findByDefinitionIdOrParentId(definitionId, definitionId))
        .thenReturn(Optional.of(Collections.singletonList(definitionDetails)));
    Assert.assertTrue(
        (!definitionServiceHelper.findByDefinitionIdOrParentId(definitionId).isEmpty()));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testFindByDefinitionIdOrParentIdException() {
    Mockito.when(definitionDetailsRepository.findByDefinitionIdOrParentId(workflowId, workflowId))
        .thenReturn(Optional.empty());
    definitionServiceHelper.findByDefinitionIdOrParentId(workflowId);
  }

  @Test
  public void testUpdateDefinitionEntriesByDefinitionId() {
    Mockito.doReturn(1)
        .when(definitionDetailsRepository)
        .updateInternalStatus(
            InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId));
    definitionServiceHelper.updateDefinitionEntriesByDefinitionId(
        definitionId, true);
    Assert.assertEquals(
        1,
        definitionDetailsRepository.updateInternalStatus(
            InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId)));
  }

  @Test(expected = NullPointerException.class)
  public void testUpdateDefinitionEntriesByDefinitionIdError() {
    Mockito.when(
            definitionDetailsRepository.updateInternalStatus(
                eq(InternalStatus.STALE_DEFINITION), any()))
        .thenThrow(NullPointerException.class);
    definitionServiceHelper.updateDefinitionEntriesByDefinitionId(
        definitionId, true);
    Assert.fail();
  }

  @Test
  public void testUpdateDefinitionState() {
    Mockito.when(
            definitionDetailsRepository.updateInternalStatus(
                InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId)))
        .thenReturn(1);
    definitionServiceHelper.updateDefinitionState(definitionId, InternalStatus.STALE_DEFINITION);
    Assert.assertEquals(
        1,
        definitionDetailsRepository.updateInternalStatus(
            InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId)));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUpdateDefinitionStateFail() {
    Mockito.when(
            definitionDetailsRepository.updateInternalStatus(
                InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId)))
        .thenReturn(0);
    definitionServiceHelper.updateDefinitionState(definitionId, InternalStatus.STALE_DEFINITION);
  }

  @Test
  public void testGetDefinitionDetailsById() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .ownerId(Long.valueOf(REALM_ID))
            .build();

    Assert.assertNotNull(
        definitionServiceHelper.getDefinitionDetailsById(
            Collections.singletonList(definitionDetails), definitionId));
  }

  @Test
  public void testDeployToCamunda() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedProcess = new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition deployedDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    deployedDefinition.setId("123");
    deployedProcess.put("123", deployedDefinition);

    deployDefinitionResponse.setDeployedProcessDefinitions(deployedProcess);

    WASHttpResponse<Object> result =
        bpmnEngineDefinitionServiceRest.deployDefinition(deployDefinition);
    Assert.assertTrue(result.isSuccess2xx());
    Assert.assertNotNull(result.getResponse());
    Assert.assertNull(result.getError());
  }

  @Test
  public void testPopulateMeta() {
    Metadata result = definitionServiceHelper.getMetadata(authorization,
        definitionInstance.getDefinitionDetails());
    Assert.assertEquals("12345", result.getCreatedBy().getId().getLocalId());
    Assert.assertEquals("54321", result.getUpdatedBy().getId().getLocalId());
  }

  @Test
  public void testFailedDBUpdateCallRollBackWithAuthDetailsTask() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(AsyncTaskConstants.OFFLINE_TICKET_KEY, "ticket");
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    doThrow(new RuntimeException())
        .when(definitionDetailsRepository)
        .setWorkflowId(anyString(), anyString());
    try {
      definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
      Assert.fail(exceptionExpected);
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getMessage(), WorkflowError.DEFINITION_UPDATE_ERROR.getErrorMessage());
    }
  }

  @Test
  public void testSaveUpdateDefinitionDetails_withoutDMN() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(mock(DefinitionDetails.class)));
    Definition updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetails(
            deployDefinitionResponse, definitionInstance, authorization, false);
    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testSaveUpdateDefinitionDetails_withNullPlaceholder() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    definitionInstance.setPlaceholderValue(null);
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    Mockito.when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));
    Definition updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetails(
            deployDefinitionResponse, definitionInstance, authorization, false);
    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesSavewithDMN() {
    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    TemplateDetails templateDetails = new TemplateDetails();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);

    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testSaveUpdateDefinitionDetailsFromTemplateSavewithoutDMN() {
    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    TemplateDetails templateDetails = new TemplateDetails();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @SneakyThrows
  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesWithTagSavewithDMN() {
    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    BpmnModelInstance bpmnModelInstance = readBPMNFile(CUSTOM_REMINDER_WITH_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    // Mock fetching old template details
    SystemTags tags = new SystemTags();
    tags.addSystemTag(SYSTEM_TAG, "1.2.3-zeta");
    String tagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);
    TemplateDetails oldTemplateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(1)
            .tag(tagString)
            .build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());
    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));

    SystemTags newTags = new SystemTags();
    newTags.addSystemTag(SYSTEM_TAG, "1.2.3-meta");
    String newTagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);
    TemplateDetails templateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(2)
            .tag(newTagString)
            .build();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @SneakyThrows
  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesWithTagSavewithoutVersionDMN() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    BpmnModelInstance bpmnModelInstance = readBPMNFile(CUSTOM_REMINDER_WITH_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    // Mock fetching old template details
    SystemTags tags = new SystemTags();
    tags.addSystemTag(SYSTEM_TAG, "1.2.3-zeta");
    String tagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);
    TemplateDetails oldTemplateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(1)
            .tag(tagString)
            .build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());
    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));
    TemplateDetails templateDetails =
        new TemplateDetails().builder().templateName("templateName").version(2).build();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @SneakyThrows
  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesWithoutTagUpdateWithVersionDMN() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    BpmnModelInstance bpmnModelInstance = readBPMNFile(CUSTOM_REMINDER_WITH_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    TemplateDetails oldTemplateDetails =
        new TemplateDetails().builder().templateName("templateName").version(1).build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());

    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));
    // Mock fetching old template details
    SystemTags tags = new SystemTags();
    tags.addSystemTag(SYSTEM_TAG, "1.2.3-zeta");
    String tagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);
    TemplateDetails templateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(2)
            .tag(tagString)
            .build();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @SneakyThrows
  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesWithTagUpdateWithoutVersionDMN() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    BpmnModelInstance bpmnModelInstance = readBPMNFile(CUSTOM_REMINDER_WITH_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);
    SystemTags tags = new SystemTags();
    tags.addSystemTag(SYSTEM_TAG, null);
    String tagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);
    TemplateDetails oldTemplateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(1)
            .tag(tagString)
            .build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());

    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));
    // Mock fetching old template details
    SystemTags updatedTags = new SystemTags();
    updatedTags.addSystemTag(SYSTEM_TAG, null);
    String updatedTagString = new ObjectMapper().writeValueAsString(tags.tagsMapInstance);

    TemplateDetails templateDetails =
        new TemplateDetails()
            .builder()
            .templateName("templateName")
            .version(2)
            .tag(updatedTagString)
            .build();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testSaveUpdateDefinitionDetailFromTemplatesUpdatewithDMN() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });

    deployDefinitionResponse.setDeployedDecisionDefinitions(
        new HashMap<>() {
          {
            put("124", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    DmnModelInstance dmnModelInstance = readDMNFile(INVOICE_APPROVAL_DMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    dmnModelInstanceList.add(dmnModelInstance);

    // Mock fetching old template details
    TemplateDetails oldTemplateDetails =
        new TemplateDetails().builder().templateName("templateName").version(1).build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());

    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));

    TemplateDetails templateDetails =
        new TemplateDetails().builder().templateName("templateName").version(2).build();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testUpdateDefinitionDetailsFromTemplateUpdatewithoutDMN() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenReturn(buildDefinitionDetailsList());

    // Mock fetching old template details
    TemplateDetails oldTemplateDetails =
        new TemplateDetails().builder().templateName("templateName").version(1).build();
    Mockito.when(
            templateDetailsRepository.findByTemplateNameAndVersion(Mockito.any(), Mockito.anyInt()))
        .thenReturn(Optional.of(oldTemplateDetails));

    // Mock find By OwnerId & TemplateDetails
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(new DefinitionDetails().builder().definitionId("id").build());

    Mockito.when(
            definitionDetailsRepository.findByTemplateDetailsAndInternalStatus(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));

    TemplateDetails templateDetails =
        new TemplateDetails().builder().templateName("templateName").version(2).build();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);

    Assert.assertNotNull(updatedDefinition);
  }

  @Test
  public void testProcessMarkedDefinitionsAndGetEnabledDefinitions() {

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails enabledDefinitionDetails =
        DefinitionDetails.builder().definitionId("id1").status(Status.ENABLED).build();

    DefinitionDetails enabledDefinitionDetails1 =
        DefinitionDetails.builder()
            .definitionId("id2")
            .status(Status.ENABLED)
            .internalStatus(InternalStatus.STALE_DEFINITION)
            .build();

    DefinitionDetails disabledDefinitionDetails =
        DefinitionDetails.builder().definitionId("id13").status(Status.DISABLED).build();

    DefinitionDetails disabledDefinitionDetails1 =
        DefinitionDetails.builder()
            .definitionId("id4")
            .status(Status.DISABLED)
            .internalStatus(InternalStatus.STALE_DEFINITION)
            .build();

    definitionDetailsList.add(enabledDefinitionDetails);
    definitionDetailsList.add(enabledDefinitionDetails1);
    definitionDetailsList.add(disabledDefinitionDetails);
    definitionDetailsList.add(disabledDefinitionDetails1);

    List<DefinitionDetails> definitionDetails =
        definitionServiceHelper.processMarkedDefinitionsAndGetEnabledDefinitions(
            definitionDetailsList, REALM_ID);

    Assert.assertFalse(definitionDetails.contains(disabledDefinitionDetails));
    Assert.assertFalse(definitionDetails.contains(disabledDefinitionDetails1));

    Assert.assertTrue(definitionDetails.contains(enabledDefinitionDetails));
    Assert.assertTrue(definitionDetails.contains(enabledDefinitionDetails1));
  }

  @Test
  public void testGetBPMNXMLDefinition_dataFromDB() {

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getDefinitionData()).thenReturn(new byte[5]);
    Mockito.when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    BpmnResponse bpmnResponse = definitionServiceHelper.getBPMNXMLDefinition(definitionDetails);
    Assert.assertNotNull(bpmnResponse);
    Assert.assertEquals("def-id", bpmnResponse.getId());
    Assert.assertNotNull(bpmnResponse.getBpmn20Xml());
    Mockito.verifyNoInteractions(bpmnEngineDefinitionServiceRest);
  }

  @Test
  public void testGetBPMNXMLDefinition_dataFromCamunda() {

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    Mockito.when(bpmnEngineDefinitionServiceRest.getBPMNXMLDefinition(eq("def-id")))
        .thenReturn(
            WASHttpResponse.<BpmnResponse>builder()
                .isSuccess2xx(true)
                .response(new BpmnResponse("def-id", ""))
                .build());
    BpmnResponse bpmnResponse = definitionServiceHelper.getBPMNXMLDefinition(definitionDetails);
    Assert.assertNotNull(bpmnResponse);
    Mockito.verify(bpmnEngineDefinitionServiceRest).getBPMNXMLDefinition(eq("def-id"));
  }

  @Test
  public void testGetDMNXMLDefinition_dataFromDB() {

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getDefinitionData()).thenReturn(new byte[5]);
    Mockito.when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    List<DmnResponse> dmnResponses =
        definitionServiceHelper.getDMNXMLDefinition(Collections.singletonList(definitionDetails));
    Assert.assertEquals(1, dmnResponses.size());
    Assert.assertEquals("def-id", dmnResponses.get(0).getId());
    Assert.assertNotNull(dmnResponses.get(0).getDmnXml());
    Mockito.verifyNoInteractions(bpmnEngineDefinitionServiceRest);
  }

  @Test
  public void testGetDMNXMLDefinition_dataFromCamunda() {

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    Mockito.when(definitionDetails.getDefinitionId()).thenReturn("def-id");
    Mockito.when(bpmnEngineDefinitionServiceRest.getDMNXMLDefinition(eq("def-id")))
        .thenReturn(
            WASHttpResponse.<DmnResponse>builder()
                .isSuccess2xx(true)
                .response(new DmnResponse("def-id", ""))
                .build());
    List<DmnResponse> dmnResponses =
        definitionServiceHelper.getDMNXMLDefinition(Collections.singletonList(definitionDetails));
    Assert.assertNotNull(dmnResponses);
    Mockito.verify(bpmnEngineDefinitionServiceRest).getDMNXMLDefinition(eq("def-id"));
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSaveUpdateDefinitionDetailsException() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put(KEY, new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    definitionServiceHelper.saveUpdateDefinitionDetails(
        deployDefinitionResponse, definitionInstance, authorization, false);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSaveUpdateDefinitionDetailsFromTemplateDataAccessException() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put(KEY, new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenThrow(new DataAccessException("") {});

    TemplateDetails templateDetails = new TemplateDetails();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
        deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSaveUpdateDefinitionDetailsFromTemplateRedisException() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
            new HashMap<>() {
              {
                put(KEY, new DeployDefinitionResponse.DeployedDefinition());
              }
            });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
            .thenThrow(new CacheNonRetriableException(WorkflowError.REDIS_CACHE_ERROR, new Exception()) {});

    TemplateDetails templateDetails = new TemplateDetails();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

    TemplateModelInstance model =
            new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSaveUpdateDefinitionDetailsFromTemplateException() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put(KEY, new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.saveAll(any()))
        .thenThrow(new WorkflowGeneralException(WorkflowError.TRIGGER_SIGNAL_PROCESS_ERROR));

    TemplateDetails templateDetails = new TemplateDetails();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();

    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, false);
    definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
        deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);
  }

  @Test
  public void testUnwrapDefinitionsWithId() {
    definitionServiceHelper.unWrapDefinitionWithIds(
        definitionInstance, WorkflowConstants.SYSTEM_OWNER_ID);
    Assert.assertNotNull(definitionInstance);
  }

  @Test
  public void testUpdateInternalStatus() {
    Mockito.doReturn(1)
        .when(definitionDetailsRepository)
        .updateInternalStatus(
            InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId));

    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
        buildDefinitionDetailsList(), InternalStatus.MARKED_FOR_DELETE);

    Assert.assertEquals(
        ASSERT_SIZE,
        definitionDetailsRepository.updateInternalStatus(
            InternalStatus.STALE_DEFINITION, Collections.singletonList(definitionId)));
  }

  @Test
  public void testUpdateInternalStatusAndPublishDomainEvent() {
    List<DefinitionDetails> defDetails = buildDefinitionDetailsList();
    InternalStatus markedForDelete = InternalStatus.MARKED_FOR_DELETE;

    Mockito.when(definitionDetailsRepository.updateInternalStatus(markedForDelete, List.of("dId")))
        .thenReturn(1);
    Mockito.when(
            definitionDetailsRepository.findAllByDefinitionsInParentId(
                List.of("dId"), List.of("dId")))
        .thenReturn(Optional.of(defDetails));

    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .definitionId("dId")
            .definitionName("dName")
            .ownerId(1234L)
            .modelType(ModelType.BPMN)
            .recordType(RecordType.INVOICE)
            .templateDetails(TemplateDetails.builder().offeringId("oId").build())
            .build();

    List<DomainEntityRequest<DefinitionDetails>> domainEntityRequestList = new ArrayList<>();
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(null)
            .entityChangeAction(EntityChangeAction.DELETE)
            .request(definitionDetails)
            .build();

    domainEntityRequestList.add(domainEntityRequest);

    List<DomainEntityRequest<DefinitionDetails>> domainEvents = new ArrayList<>();
    domainEvents.add(domainEntityRequest);

    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(defDetails, markedForDelete);

    Mockito.verify(definitionDetailsRepository)
        .updateInternalStatus(markedForDelete, List.of("dId"));
    Mockito.verify(definitionDetailsRepository)
        .findAllByDefinitionsInParentId(List.of("dId"), List.of("dId"));
    Mockito.verify(definitionDomainEventHandler, never()).publishAll(domainEntityRequestList);
  }

  @Test
  public void updateDefinitionInternalStatusForBulkDelete_Success() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.ENABLED);
    definitionDetails.setModelType(ModelType.BPMN);
    Mockito.when(definitionDetailsRepository.updateInternalStatus(Mockito.any(), Mockito.any()))
        .thenReturn(1);

    Mockito.when(
            definitionDetailsRepository.findAllByDefinitionsInParentId(
                Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(Arrays.asList(definitionDetails)));

    List<DomainEntityRequest<DefinitionDetails>> domainEntityRequestList = new ArrayList<>();
    DomainEntityRequest domainEntityRequest =
        DomainEntityRequest.builder()
            .eventHeaderEntity(null)
            .entityChangeAction(EntityChangeAction.DELETE)
            .request(definitionDetails)
            .build();

    domainEntityRequestList.add(domainEntityRequest);

    List<DomainEntityRequest<DefinitionDetails>> domainEvents = new ArrayList<>();
    domainEvents.add(domainEntityRequest);

    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
        Arrays.asList(definitionDetails), InternalStatus.MARKED_FOR_DELETE);

    Mockito.verify(definitionDetailsRepository, Mockito.times(1))
        .updateInternalStatus(Mockito.any(), Mockito.any());

    Mockito.verify(definitionDetailsRepository, times(1))
        .findAllByDefinitionsInParentId(Mockito.any(), Mockito.any());
    Mockito.verify(definitionDomainEventHandler, never()).publishAll(domainEntityRequestList);

    Mockito.verify(enabledDefinitionCacheService, times(1)).updateCacheWithDefinitionDetails(Mockito.any());
  }

  @Test
  public void updateDefinitionStatusForBulkDelete_ZeroDefinitions() {
    List<DefinitionDetails> definitionDetailsList = Collections.emptyList();
    definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
        definitionDetailsList, InternalStatus.MARKED_FOR_DELETE);
    Mockito.verify(definitionDetailsRepository, Mockito.never()).updateInternalStatus(any(), any());
  }

  @Test
  public void updateDefinitionStatusForBulkDelete_Failure() {
    definition.setId(TestHelper.getGlobalId(DEF_ID));
    DefinitionDetails definitionDetails =
        TestHelper.mockDefinitionDetails(definition, bpmnTemplateDetail, authorization);
    definitionDetails.setStatus(Status.ENABLED);
    Mockito.when(definitionDetailsRepository.updateInternalStatus(Mockito.any(), Mockito.any()))
        .thenThrow(WorkflowGeneralException.class);
    try {
      definitionServiceHelper.updateInternalStatusAndPublishDomainEvent(
          Arrays.asList(definitionDetails), InternalStatus.MARKED_FOR_DELETE);
      Assert.fail();
    } catch (Exception e) {
      Mockito.verify(definitionDetailsRepository, Mockito.times(1))
          .updateInternalStatus(Mockito.any(), Mockito.any());
    }
  }

  @Test
  public void testDefinitionDetails() {
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(mock(DefinitionDetails.class)));
    DefinitionDetails definitionDetails =
        definitionServiceHelper.getDefinitionDetails(definitionId);
    Assert.assertNotNull(definitionDetails);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testUpdateDefinitionDetailsTemplateNotFoundExceotion() {

    // Mock save definition
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    TemplateDetails templateDetails = new TemplateDetails().builder().build();
    BpmnModelInstance bpmnModelInstance = readBPMNFile(INVOICE_APPROVAL_BPMN);
    List<DmnModelInstance> dmnModelInstanceList = new ArrayList<>();
    TemplateModelInstance model =
        new TemplateModelInstance(bpmnModelInstance, dmnModelInstanceList, true);
    List<DefinitionDetails> updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetailsFromTemplate(
            deployDefinitionResponse, templateDetails, model, WorkflowConstants.SYSTEM_OWNER_ID);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testGetEnabledCustomDefinitionDetailsCase() {
    List<DefinitionDetails> definitionDetailsList =
        (Collections.singletonList(mock(DefinitionDetails.class)));
    when(definitionDetailsRepository
            .findEnabledDefinitionForOwnerIdTemplateIdRecordTypeAndModelType(
                Mockito.anyLong(), Mockito.anyString(), Mockito.any(), Mockito.any()))
        .thenReturn(Optional.of(definitionDetailsList));
    TemplateDetails templateDetails = new TemplateDetails().builder().build();
    templateDetails.setTemplateName("customApproval");
    templateDetails.setId("1");
    definitionServiceHelper.checkIfExistsActiveCustomDefinition("12345", templateDetails, "bill");
  }

  @Test
  public void testFindByOwnerIdAndDefinitionKeyAndInternalStatusIsNull() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .definitionKey(definitionKey)
            .ownerId(Long.valueOf(REALM_ID))
            .build();
    Mockito.when(
            definitionDetailsRepository.findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
                Long.valueOf(REALM_ID), definitionKey))
        .thenReturn(definitionDetails);
    DefinitionDetails definitionDetailsResponse =
        definitionServiceHelper.findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(
            Long.valueOf(REALM_ID), definitionKey);
    Assert.assertEquals(definitionKey, definitionDetailsResponse.getDefinitionKey());
    Assert.assertEquals(Long.valueOf(REALM_ID), definitionDetailsResponse.getOwnerId());
  }

  @Test
  public void findEnabledDefinitionForPrecannedTemplateAndRecordTypeTest() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .definitionKey(definitionKey)
            .ownerId(Long.valueOf(REALM_ID))
            .build();
    List<DefinitionDetails> definitionDetailsList1 = new ArrayList<>();
    definitionDetailsList1.add(definitionDetails);
    String templateName = "customApproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "invoiceapproval",
                    Status.ENABLED,
                    ModelType.BPMN))
        .thenReturn(Optional.of(definitionDetailsList1));
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findEnabledDefinitionForPrecannedTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertFalse(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void findNoEnabledDefinitionForPrecannedTemplateAndRecordTypeTest() {
    String templateName = "customApproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "invoiceapproval",
                    Status.ENABLED,
                    ModelType.BPMN))
        .thenReturn(Optional.empty());
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findEnabledDefinitionForPrecannedTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertTrue(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void findStaleDefinitionForPrecannedTemplateAndRecordTypeTest() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .definitionKey(definitionKey)
                .originalSetupUser(originalSetupUser)
            .ownerId(Long.valueOf(REALM_ID))
            .build();
    List<DefinitionDetails> definitionDetailsList1 = new ArrayList<>();
    definitionDetailsList1.add(definitionDetails);
    String templateName = "customApproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatus(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "invoiceapproval",
                    Status.ENABLED,
                    ModelType.BPMN,
                    InternalStatus.STALE_DEFINITION))
        .thenReturn(Optional.of(definitionDetailsList1));
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findStaleDefinitionForPrecannedTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertFalse(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void findNoStaleDefinitionForPrecannedTemplateAndRecordTypeTest() {
    String templateName = "customApproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatus(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "invoiceapproval",
                    Status.ENABLED,
                    ModelType.BPMN,
                    InternalStatus.STALE_DEFINITION))
        .thenReturn(Optional.empty());
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findStaleDefinitionForPrecannedTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertTrue(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void findEnabledDefinitionForCustomTemplateAndRecordTypeTest() {
    DefinitionDetails definitionDetails =
        DefinitionDetails.builder()
            .workflowId(workflowId)
            .definitionId(definitionId)
            .definitionKey(definitionKey)
            .ownerId(Long.valueOf(REALM_ID))
            .build();
    List<DefinitionDetails> definitionDetailsList1 = new ArrayList<>();
    definitionDetailsList1.add(definitionDetails);
    String templateName = "invoiceapproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "customApproval",
                    Status.ENABLED,
                    ModelType.BPMN))
        .thenReturn(Optional.of(definitionDetailsList1));
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findEnabledDefinitionForCustomTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertFalse(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void findNoEnabledDefinitionForCustomTemplateAndRecordTypeTest() {
    String templateName = "invoiceapproval";
    Mockito.when(
            definitionDetailsRepository
                .findByOwnerIdAndRecordTypeAndTemplateDetails_TemplateNameAndStatusAndModelTypeAndInternalStatusIsNull(
                    Long.parseLong(REALM_ID),
                    RecordType.INVOICE,
                    "customApproval",
                    Status.ENABLED,
                    ModelType.BPMN))
        .thenReturn(Optional.empty());
    List<DefinitionDetails> definitionDetailsList =
        definitionServiceHelper.findEnabledDefinitionForCustomTemplateAndRecordType(
            REALM_ID, templateName, RecordType.INVOICE);
    Assert.assertTrue(CollectionUtils.isEmpty(definitionDetailsList));
  }

  @Test
  public void testSaveUpdateDefinitionDetails_Disabled() {
    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    deployDefinitionResponse.setDeployedProcessDefinitions(
        new HashMap<>() {
          {
            put("123", new DeployDefinitionResponse.DeployedDefinition());
          }
        });
    Mockito.when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(mock(DefinitionDetails.class)));
    Definition updatedDefinition =
        definitionServiceHelper.saveUpdateDefinitionDetails(
            deployDefinitionResponse, definitionInstanceDisabled, authorization, false);
    Assert.assertNotNull(updatedDefinition);

    Mockito.verify(enabledDefinitionCacheService, Mockito.times(1))
            .updateCacheWithDefinitionDetails(Mockito.any());
  }

  @Test
  public void testFindByParentId_Success() {
    DefinitionDetails definitionDetailsBpmn =
        TestHelper.mockDefinitionDetailsWithId(
            bpmnTemplateDetail, authorization, "etet2-2434j2-3232fl-33ff");
    List<DefinitionDetails> definitionDetailsDmn =
        Arrays.asList(
            TestHelper.mockDefinitionDetailsWithParentId(
                bpmnTemplateDetail,
                authorization,
                "h342j3-n13m30-i12kjf-3k0p",
                "etet2-2434j2-3232fl-33ff"));
    Mockito.when(
            definitionDetailsRepository.findByParentId(definitionDetailsBpmn.getDefinitionId()))
        .thenReturn(Optional.ofNullable(definitionDetailsDmn));
    Optional<List<DefinitionDetails>> optionalDmnDefinitionDetails =
        definitionServiceHelper.findByParentId(definitionDetailsBpmn.getDefinitionId());
      Assert.assertFalse(optionalDmnDefinitionDetails.get().isEmpty());
  }

  @Test
  public void testFindByDefinitionId_Failure() {
    try {
      definitionServiceHelper.findByDefinitionId("wrong-id");
      Assert.fail("Above Method should fail");
    } catch (Exception e) {
      Assert.assertEquals("Exception in getting definition details for id %s", e.getMessage());
    }
  }

  @Test
  public void testEventScheduleTaskInExecutionChain() {

    // mock template data
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getDefinition()).thenReturn(mock(Definition.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customReminder");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(definitionDetails));

    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);

    EventScheduleService eventScheduleService = mock(EventScheduleService.class);
    when(eventScheduleHelper.prepareScheduleCreateTasks(any(), any()))
        .thenReturn(
            List.of(
                new SaveEventScheduleWorkflowTask(eventScheduleService, eventScheduleConfig),
                new SaveScheduleDetailsInDataStoreTask(schedulerDetailsRepository)));
    when(eventScheduleService.createSchedules(any(), any())).thenReturn(eventScheduleResponses);
    // mock
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(
            List.of(
                new EventScheduleWorkflowActionModel(
                    "customReminder_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));

    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE));
    Map<String, String> responseMap = state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE);
    Assert.assertEquals(responseMap.get("customReminder_customStart"), "sched-123");
    WASContext.clear();
  }

  @Test
  public void testEventScheduleTaskInExecutionChainForScheduledActions() {

    // mock template data
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getDefinition()).thenReturn(mock(Definition.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customScheduledActions");
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setId(GlobalId.builder().setLocalId("id").build());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    when(definitionDetailsRepository.findByDefinitionId(any()))
            .thenReturn(Optional.of(definitionDetails));
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(),any())).thenReturn(new SchedulingSvcRequest());

    // event scheduler response
    List<SchedulingSvcResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    eventScheduleResponses.add(new SchedulingSvcResponse());
    SchedulingService schedulingService1 = mock(SchedulingService.class);
    when(eventScheduleHelper.prepareSchedulingCreateTask(any(), any()))
            .thenReturn(new SaveEventSchedulingTask(schedulingService1, actionModelToScheduleRequestMapper));
    Mockito.when(schedulingService.isEnabled((Definition) any(), any())).thenReturn(true);
    // mock
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(
            AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
            Optional.of(
                    List.of(
                            new EventScheduleWorkflowActionModel(
                                    "customScheduledActions_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));
    SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.ACTIVE)
            .definitionKey("key")
            .recurrenceRule(new RecurrenceRule())
            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);

    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
    when(schedulingService1.createSchedules(any(), any())).thenReturn(eventScheduleResponses);
    definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Mockito.verify(eventScheduleHelper, times(1))
            .prepareSchedulingCreateTask(Mockito.any(), Mockito.any());
    Mockito.verify(schedulingService1, times(1))
            .createSchedules(Mockito.any(), Mockito.any());
    WASContext.clear();
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testEventScheduleTaskInExecutionChain_Failure() {

    // mock template data
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getDefinition()).thenReturn(mock(Definition.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customReminder");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(mock(DefinitionDetails.class)));

    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);

    EventScheduleService eventScheduleService = mock(EventScheduleService.class);
    when(eventScheduleHelper.prepareScheduleCreateTasks(any(), any()))
        .thenReturn(
            List.of(
                new SaveEventScheduleWorkflowTask(eventScheduleService, eventScheduleConfig),
                new SaveScheduleDetailsInDataStoreTask(schedulerDetailsRepository)));
    when(eventScheduleService.createSchedules(any(), any()))
        .thenThrow(new WorkflowGeneralException("Event schedule service failure"));
    // mock
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(
            List.of(
                new EventScheduleWorkflowActionModel(
                    "customReminder_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));

    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));

    definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE));
    Assert.assertTrue(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_TASK_FAILURE));
    WASContext.clear();
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testSchedulerDetailsTaskInExecutionChain_Failure() {
    // mock template data
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getDefinition()).thenReturn(mock(Definition.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customReminder");
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionDetailsRepository.findByDefinitionId(any()))
        .thenReturn(Optional.of(mock(DefinitionDetails.class)));

    // event scheduler response
    List<EventScheduleResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    EventScheduleResponse eventScheduleResponse = new EventScheduleResponse();
    List<EventScheduleData> eventScheduleData = new ArrayList<>();
    EventScheduleData data = new EventScheduleData();
    data.setId("sched-123");
    eventScheduleData.add(data);
    eventScheduleResponse.setData(eventScheduleData);
    eventScheduleResponses.add(eventScheduleResponse);

    EventScheduleService eventScheduleService = mock(EventScheduleService.class);
    when(eventScheduleHelper.prepareScheduleCreateTasks(any(), any()))
        .thenReturn(
            List.of(
                new SaveEventScheduleWorkflowTask(eventScheduleService, eventScheduleConfig),
                new SaveScheduleDetailsInDataStoreTask(schedulerDetailsRepository)));
    when(eventScheduleService.createSchedules(any(), any())).thenReturn(eventScheduleResponses);
    // mock
    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(
        AsyncTaskConstants.EVENT_SCHEDULE_REQUEST,
        Optional.of(
            List.of(
                new EventScheduleWorkflowActionModel(
                    "customReminder_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));

    WASContext.setMigrationContext(true);
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(INVOICE_APPROVAL_BPMN));
  Mockito.when(schedulerDetailsRepository.saveAll(Mockito.any())).thenThrow(new RuntimeException("Test Failure"));

    definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(state.getValue(AsyncTaskConstants.EVENT_SCHEDULE_RESPONSE));
    Assert.assertTrue(state.getValue(AsyncTaskConstants.SAVE_SCHEDULE_DETAILS_TASK_FAILURE));
    WASContext.clear();
  }
  @Test
  public void testUpdateSchedulerDetailsInExecutionChain() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-124");
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("sche123"));
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);
    Mockito.when(
            eventScheduleHelper.prepareScheduleUpdateTasks(Mockito.any(), Mockito.any()))
        .thenReturn(
            List.of(
                new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig),
                new UpdateSchedulerDetailsInDataStoreTask(schedulerDetailsRepository)));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
    Mockito.verify(schedulerDetailsRepository, times(1))
        .updateDefinitionIdForSchedulers(Mockito.any(), Mockito.any());
  }
  @Test
  public void testUpdateSchedulerDetailsInExecutionChainForScheduledActions() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customScheduledActions");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setId(GlobalId.builder().setLocalId("id").build());
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setDefinitionKey("key");
    definition.setRecurrence(new RecurrenceRule());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-124");
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("sche123"));
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);
    state.addValue(AsyncTaskConstants.SAVE_DEFINITION_RESPONSE_KEY, definition);
    SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.ACTIVE)
            .definitionKey("key")
            .recurrenceRule(new RecurrenceRule())
            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(List.of(new EventScheduleWorkflowActionModel("customScheduledActions_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));
//    when(featureFlagManager.getBoolean(anyString(), anyString())).thenReturn(true);
    Mockito.when(schedulingService.isEnabled((Definition) any(), any())).thenReturn(true);
    List<SchedulingSvcResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    eventScheduleResponses.add(new SchedulingSvcResponse());
    when(schedulingService.updateSchedules(any(), any())).thenReturn(eventScheduleResponses);
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
    Mockito.when(
                    eventScheduleHelper.prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(),Mockito.any(boolean.class)))
            .thenReturn(new UpdateEventSchedulingTask(schedulingService, actionModelToScheduleRequestMapper));
    try (MockedStatic<RecurrenceUtil> mockedStatic = Mockito.mockStatic(RecurrenceUtil.class)) {
      RecurrenceRule recurrenceRule = new RecurrenceRule();
      mockedStatic.when(() -> RecurrenceUtil.prepareRecurrence(any(byte[].class))).thenReturn(recurrenceRule);
      definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
      Mockito.verify(eventScheduleHelper, times(1))
              .prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class));
    }
    Mockito.verify(eventScheduleHelper, times(1))
            .prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class));
  }

  @Test
  public void testUpdateSchedulerDetailsInExecutionChainForScheduledActionsForMigration() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customScheduledActions");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    TemplateDetails templateDetails = TemplateDetails.builder().templateName("customScheduledActions").build();
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(definitionDetails.getDefinitionKey()).thenReturn("key");
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setId(GlobalId.builder().setLocalId("id").build());
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setDefinitionKey("key");
    definition.setRecurrence(new RecurrenceRule());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-124");
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("sche123"));
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);
    state.addValue(AsyncTaskConstants.SAVE_DEFINITION_RESPONSE_KEY, definition);
    SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.ACTIVE)
            .definitionKey("key")
            .recurrenceRule(new RecurrenceRule())
            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(List.of(new EventScheduleWorkflowActionModel("customScheduledActions_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));
    Mockito.when(schedulingService.isEnabled((Definition) any(), any())).thenReturn(false);
    Mockito.when(schedulingService.isMigrated(any())).thenReturn(true);
    List<SchedulingSvcResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    eventScheduleResponses.add(new SchedulingSvcResponse());
    when(schedulingService.updateSchedules(any(), any())).thenReturn(eventScheduleResponses);
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
    Mockito.when(
                    eventScheduleHelper.prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class)))
            .thenReturn(new UpdateEventSchedulingTask(schedulingService, actionModelToScheduleRequestMapper));
    Mockito.when(
                    eventScheduleHelper.prepareScheduleUpdateTasks(Mockito.any(), Mockito.any()))
            .thenReturn(
                    List.of(
                            new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig),
                            new UpdateSchedulerDetailsInDataStoreTask(schedulerDetailsRepository)));
    try (MockedStatic<RecurrenceUtil> mockedStatic = Mockito.mockStatic(RecurrenceUtil.class)) {
      RecurrenceRule recurrenceRule = new RecurrenceRule();
      mockedStatic.when(() -> RecurrenceUtil.prepareRecurrence(any(byte[].class))).thenReturn(recurrenceRule);
      definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
      Mockito.verify(eventScheduleHelper, times(1))
              .prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class));
      Mockito.verify(eventScheduleHelper, times(1))
              .prepareScheduleUpdateTasks(Mockito.any(), Mockito.any());
      Mockito.verify(schedulingService, times(1)).updateSchedules(any(), any());
      Mockito.verify(eventScheduleService, times(1)).updateSchedules(any(), any());
      Mockito.verify(schedulerDetailsRepository, times(1))
              .updateDefinitionIdForSchedulers(Mockito.any(), Mockito.any());
    }
  }


  @Test
  public void testCreateWithRollbackForScheduledActionsFailed() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("customScheduledActions");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    Definition definition = TestHelper.mockDefinitionEntity();
    definition.setId(GlobalId.builder().setLocalId("id").build());
    definition.setStatus(WorkflowStatusEnum.ENABLED);
    definition.setDefinitionKey("key");
    definition.setRecurrence(new RecurrenceRule());
    when(definitionInstance.getDefinition()).thenReturn(definition);
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-124");
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("sche123"));
    state.addValue(
            AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);
    state.addValue(AsyncTaskConstants.SAVE_DEFINITION_RESPONSE_KEY, definition);
    SchedulingMetaData schedulingMetaData = SchedulingMetaData.builder()
            .status(com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.enums.Status.ACTIVE)
            .definitionKey("key")
            .recurrenceRule(new RecurrenceRule())
            .workflowName(CUSTOM_SCHEDULED_ACTIONS.getName()).build();
    state.addValue(AsyncTaskConstants.SCHEDULING_META_DATA, schedulingMetaData);
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_REQUEST, Optional.of(List.of(new EventScheduleWorkflowActionModel("customScheduledActions_customStart", new LocalDate(),new LocalDate(), new RecurrenceRule()))));
    Mockito.when(schedulingService.isEnabled((Definition) any(), any())).thenReturn(true);
    List<SchedulingSvcResponse> eventScheduleResponses = new ArrayList<>();
    // response - 1
    eventScheduleResponses.add(new SchedulingSvcResponse());
    when(schedulingService.updateSchedules(any(), any())).thenThrow(new WorkflowGeneralException("error"));
    when(actionModelToScheduleRequestMapper.convertToScheduleRequest(any(), any())).thenReturn(new SchedulingSvcRequest());
    Mockito.when(
                    eventScheduleHelper.prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class)))
            .thenReturn(new UpdateEventSchedulingTask(schedulingService, actionModelToScheduleRequestMapper));
    try{
      try (MockedStatic<RecurrenceUtil> mockedStatic = Mockito.mockStatic(RecurrenceUtil.class)) {
        RecurrenceRule recurrenceRule = new RecurrenceRule();
        mockedStatic.when(() -> RecurrenceUtil.prepareRecurrence(any(byte[].class))).thenReturn(recurrenceRule);
        definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
        Assert.fail();
      }
    }catch (Exception e){
      Mockito.verify(eventScheduleHelper, times(1))
              .prepareSchedulingUpdateTask(Mockito.any(), Mockito.any(), Mockito.any(boolean.class));
      Mockito.verify(appConnectService, times(0)).createWorkflow(any(), any(), any(), any());
    }finally {
      WASContext.clear();
    }
  }

  @Test
  public void testUpdateSchedulerDetailsInExecutionChainThrowException() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    Definition definition = TestHelper.mockDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    when(definitionDetails.getDefinitionId()).thenReturn("id");
    when(definitionDetails.getTemplateDetails()).thenReturn(mock(TemplateDetails.class));
    when(definitionDetails.getTemplateDetails().getTemplateName()).thenReturn("test");
    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    State state = new State();
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "def-124");
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_IDS, List.of("sche123"));
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);
    Mockito.when(
            eventScheduleHelper.prepareScheduleUpdateTasks(Mockito.any(), Mockito.any()))
        .thenReturn(
            List.of(
                new UpdateEventScheduleTask(eventScheduleService, eventScheduleConfig),
                new UpdateSchedulerDetailsInDataStoreTask(schedulerDetailsRepository)));
    Mockito.when(
            schedulerDetailsRepository.updateDefinitionIdForSchedulers(
                Mockito.any(), Mockito.any()))
        .thenThrow(new RuntimeException("DB failed"));
    definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);
    Mockito.verify(schedulerDetailsRepository, times(1))
        .updateDefinitionIdForSchedulers(Mockito.any(), Mockito.any());
    Assert.assertTrue(state.getValue(AsyncTaskConstants.UPDATE_SCHEDULE_DETAILS_TASK_FAILURE));
  }

  private List<DefinitionDetails> buildDefinitionDetailsList() {

    List<DefinitionDetails> list = new ArrayList<>();
    DefinitionDetails details =
        DefinitionDetails.builder()
            .definitionId("dId")
            .definitionName("dName")
            .ownerId(1234L)
            .modelType(ModelType.BPMN)
            .recordType(RecordType.INVOICE)
                .originalSetupUser(originalSetupUser)
            .templateDetails(TemplateDetails.builder().offeringId("oId").build())
            .build();

    list.add(details);
    return list;
  }

  @Test
  public void testGetChildBpmnModelInstanceFromParent_InvalidBpmn() {
    try {
      definitionServiceHelper.getChildBpmnModelInstanceFromParent(
          readBPMNFile(INVOICE_APPROVAL_BPMN), "reminder");
      Assert.fail("Method should throw exception");
    } catch (WorkflowGeneralException ex) {
      Assert.assertEquals(WorkflowError.INVALID_BPMN_MODEL_INSTANCE, ex.getWorkflowError());
    }
  }

  @Test
  public void testGetChildBpmnModelInstanceFromParent_ActionKeyMismatch() {
    try {
      definitionServiceHelper.getChildBpmnModelInstanceFromParent(
          readBPMNFile(CUSTOM_APPROVAL_MULTI_CONDITION_BPMN), "reminder");
      Assert.fail("Method should throw exception");
    } catch (WorkflowGeneralException ex) {
      Assert.assertEquals(WorkflowError.CALLED_ELEMENT_ACTION_KEY_MISMATCH, ex.getWorkflowError());
    }
  }

  @Test
  public void testGetChildBpmnModelInstanceFromParent_InvalidTemplateDetails() {
    try{
      definitionServiceHelper.getChildBpmnModelInstanceFromParent(
          readBPMNFile(CUSTOM_APPROVAL_MULTI_CONDITION_BPMN), "approval");
      Assert.fail("Method should throw exception");
    } catch (WorkflowGeneralException ex) {
      Assert.assertEquals(WorkflowError.INVALID_TEMPLATE_DETAILS, ex.getWorkflowError());
    }
  }

  @Test
  public void testGetChildBpmnModelInstanceFromParent_success() {

    TemplateDetails childTemplateDetails = new TemplateDetails();
    childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
    childTemplateDetails.setId("childTemplateTestId");

    when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(),any()))
        .thenReturn(Optional.of(childTemplateDetails));

    Assert.assertNotNull(definitionServiceHelper.getChildBpmnModelInstanceFromParent(
        readBPMNFile(CUSTOM_APPROVAL_MULTI_CONDITION_BPMN), "approval"));
  }

  @Test
  public void testCreateRollbackForMultiConditionRecurringReminderDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);
    when(definitionInstance.getTemplateDetails().getTemplateName()).thenReturn("test");
    Definition definition = TestHelper.mockSingleStepRecurringReminderDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);
    WASContext.setMigrationContext(false);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(CUSTOM_REMINDER_MULTI_CONDITION_BPMN));

    TemplateDetails childTemplateDetails = new TemplateDetails();
    childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_REMINDER_CHILD_BPMN_XML).getBytes());

    when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(),any()))
        .thenReturn(Optional.of(childTemplateDetails));

    State state1 =
        definitionServiceHelper.createWithRollBack(false, state, definitionInstance, authorization);
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.WORKFLOW_ID_KEY));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.IS_CREATE_SUBSCRIPTION_REQD));
    Assert.assertNull(state1.getValue(AsyncTaskConstants.OFFLINE_TICKET_KEY));
    Assert.assertNotNull(state1.getValue(AsyncTaskConstants.DEFINITION_ID_KEY));
    verifyNoInteractions(bpmnEngineDefinitionServiceRest);
    verify(definitionDetailsRepository, never()).setWorkflowId(anyString(), anyString());
    WASContext.clear();
  }

  @Test
  public void testCreateRollbackForUpdateMultiConditionReminderDefinition() {
    DefinitionInstance definitionInstance = mock(DefinitionInstance.class);
    TemplateDetails templateDetails = mock(TemplateDetails.class);
    templateDetails.setTemplateName("test");

    when(definitionInstance.getTemplateDetails()).thenReturn(templateDetails);
    when(templateDetails.getDefinitionType()).thenReturn(DefinitionType.SINGLE);

    Definition definition = TestHelper.mockSingleStepRecurringReminderDefinitionEntity();
    when(definitionInstance.getDefinition()).thenReturn(definition);

    DefinitionDetails definitionDetails = mock(DefinitionDetails.class);
    definitionDetails.setDefinitionId("id");

    when(definitionInstance.getDefinitionDetails()).thenReturn(definitionDetails);
    when(definitionDetails.getTemplateDetails()).thenReturn(templateDetails);

    WASContext.setMigrationContext(false);
    Mockito.when(ixpManager.getBoolean(Mockito.anyString(),
        anyBoolean())).thenReturn(true);

    State state = new State();
    state.addValue(AsyncTaskConstants.REALM_ID_KEY, authorization.getRealm());
    state.addValue(AsyncTaskConstants.DEFINITION_ID_KEY, "defId");
    state.addValue(AsyncTaskConstants.SUBSCRIPTION_ID_KEY, "subId");
    state.addValue(
        AsyncTaskConstants.BPMN_ENGINE_DEPLOY_REQUEST_KEY, mock(DeployDefinition.class));
    when(definitionInstance.getBpmnModelInstance()).thenReturn(readBPMNFile(CUSTOM_REMINDER_MULTI_CONDITION_BPMN));
    state.addValue(AsyncTaskConstants.EVENT_SCHEDULE_STATUS, ScheduleStatus.ACTIVE);

    TemplateDetails childTemplateDetails = new TemplateDetails();
    childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_REMINDER_CHILD_BPMN_XML).getBytes());

    when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(),any()))
        .thenReturn(Optional.of(childTemplateDetails));


    State state1 =
        definitionServiceHelper.createWithRollBack(true, state, definitionInstance, authorization);

    verifyNoInteractions(appConnectService);
    WASContext.clear();
  }

  @Test
  public void testIsActivityDetailsPresent(){
    Assert.assertFalse(definitionServiceHelper.isActivityDetailsPresent("test-1"));
  }

  @Test
  public void testDeleteAllByParentIdIn_WithValidIds() {
    List<String> validIds = Arrays.asList("id1", "id2", "id3");
    definitionServiceHelper.deleteAllByParentIdIn(validIds);
    Mockito.verify(definitionDetailsRepository, Mockito.times(1)).deleteAllByParentIdIn(validIds);
  }

  @Test
  public void testDeleteAllByParentIdIn_WithNullInput() {
    definitionServiceHelper.deleteAllByParentIdIn(null);
    Mockito.verify(definitionDetailsRepository, Mockito.times(1)).deleteAllByParentIdIn(null);
  }

}

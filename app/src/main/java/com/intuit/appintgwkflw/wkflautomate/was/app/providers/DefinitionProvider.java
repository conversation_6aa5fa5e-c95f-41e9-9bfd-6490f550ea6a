package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WasUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.CrudOperation;
import com.intuit.v4.GlobalId;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.Provides;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.DefinitionOperations;
import com.intuit.v4.workflows.definitions.WorkflowStatusEnum;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Provides(Definition.class)
@Component
@AllArgsConstructor
public class DefinitionProvider extends WASBaseProvider<Definition>
    implements DefinitionOperations<RequestContext> {

  private DefinitionService definitionService;

  @Override
  protected SingleResult<Definition> wasReadOne(
      RequestContext context, @SuppressWarnings("rawtypes") GlobalId id, QueryHelper query) {
    verifyRealmId(context.getAuthorization());
    boolean isMultiStep =
        MultiStepUtil.isTemplateDataPassed(query.getPreparedQuery().getSubQueries());
    return SingleResult.of(
        definitionService.getDefinitionReadOne(id.getLocalId(), id, isMultiStep));
  }

  @Override
  protected ListResult<Definition> wasReadList(RequestContext context, QueryHelper query) {
    verifyRealmId(context.getAuthorization());
    return definitionService.getAllDefinitions(context.getAuthorization(), query);
  }

  @Override
  protected SingleResult<Definition> wasWriteOne(RequestContext context, Definition definition) {
    verifyRealmId(context.getAuthorization());
    verifyTestDriveRealmId(context.getAuthorization());
    CrudOperation operation = CrudOperation.CREATE;

    if (definition.isIdSet()) {
      // Unmask the definition id:
      // input if like Invoice-Approval$11$c60ee4e8-38f1-11ea-95fb-26a7576e130e
      // then convert it back to like Invoice-Approval:11:c60ee4e8-38f1-11ea-95fb-26a7576e130e
      String definitionId = WasUtils.unMaskDefinitionId(definition.getId().getLocalId());
      definition.setId(definition.getId().setLocalId(definitionId));
      operation = CrudOperation.UPDATE;
    }

    if (definition.isDeletedSet() && definition.isDeleted().booleanValue()) {
      operation = CrudOperation.DELETE;
    } else if (definition.isStatusSet()
        && WorkflowStatusEnum.ENABLED == definition.getStatus()
        && !definition.isWorkflowStepsSet()) {
      operation = CrudOperation.ENABLED;
    } else if (definition.isStatusSet()
        && WorkflowStatusEnum.DISABLED == definition.getStatus()
        && !definition.isWorkflowStepsSet()) {
      operation = CrudOperation.DISABLED;
    }

    switch (operation) {
      case DELETE:
        return SingleResult.of(
            definitionService.deleteDefinition(definition, context.getAuthorization()));

      case UPDATE:
        return SingleResult.of(
            definitionService.updateDefinition(definition, context.getAuthorization()));

      case ENABLED:
        return SingleResult.of(
            definitionService.enableDefinition(definition, context.getAuthorization()));

      case DISABLED:
        return SingleResult.of(
            definitionService.disableDefinition(definition, context.getAuthorization()));

      default:
      case CREATE:
        return SingleResult.of(
            definitionService.createDefinition(definition, context.getAuthorization()));
    }
  }

  @Override
  public SingleResult<Definition> createDefinitionWithDefaultValues(
      RequestContext context, Definition.TemplateInput templateInput) {
    WorkflowLogger.logInfo(
        "Creating definition with default values for template id=%s", templateInput.getName());
    populateMDCHeaders(context);
    return CommonExecutor.execute(
        () -> {
          verifyRealmId(context.getAuthorization());
          return new SingleResult<>(
              definitionService.createDefinitionWithDefaultValues(
                  templateInput.getName(),
                  BooleanUtils.toBoolean(templateInput.isCreatedAsSystemUser()),
                  context.getAuthorization()));
        },
        "Exception occurred while performing create definiton with default values",
        e -> handleServiceException((WorkflowGeneralException) e),
        e1 -> handleSystemError(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }

  /**
   * Reading Definition with obfuscated/redacted values
   *
   * @param context
   * @param queryHelper
   * @return
   */
  @Override
  public ListResult<Definition> getDefinitionDetails(
      RequestContext context, QueryHelper queryHelper) {
    populateMDCHeaders(context);
    return CommonExecutor.execute(
        () -> {
          verifyRealmId(context.getAuthorization());
          boolean isMultiStep =
              MultiStepUtil.isTemplateDataPassed(queryHelper.getPreparedQuery().getSubQueries());
          return new ListResult<>(
              Collections.singletonList(
                  definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, isMultiStep)));
        },
        "Exception occurred while performing reading obfuscate operation ",
        e -> handleServiceExceptionList((WorkflowGeneralException) e),
        e1 -> handleSystemErrorList(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
  }


}

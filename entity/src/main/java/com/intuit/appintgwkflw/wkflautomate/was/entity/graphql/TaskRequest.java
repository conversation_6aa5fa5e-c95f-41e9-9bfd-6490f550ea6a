package com.intuit.appintgwkflw.wkflautomate.was.entity.graphql;

import com.intuit.v4.workflows.tasks.Task;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR> <br>
 *     <br>
 *     </>
 *     <p>This is the uber level class for Tasks. </> <br>
 *     <br>
 *     </> </> 1. query = Map<?,?> of queryfilters (for instance: workflowId, activityId and
 *     activityInstanceId) in Get Task use case</> <br>
 *     <br>
 *     </> 2. headers = map of all custom headers passed. (For instance: assignee, onBehalfOf
 *     etc)</> <br>
 *     <br>
 *     </> 3. task = caters to task object in Mutation use case</>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskRequest {

  private Map<? extends Object, ? extends Object> query;
  private Map<String, String> headers;
  private Task task;
}

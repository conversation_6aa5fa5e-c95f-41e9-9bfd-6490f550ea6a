package com.intuit.appintgwkflw.wkflautomate.was.common.logger;

import com.intuit.appintgwkflw.wkflautomate.telemetry.logger.SimpleLoggingFormat;
import com.intuit.appintgwkflw.wkflautomate.telemetry.logger.WorkflowLoggingFormat;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 */
public class SimpleLoggingFormatTest {

  private final WorkflowLoggingFormat loggingFormat = new SimpleLoggingFormat();

  @Test
  public void testWithMDC() {

    init();
    final WorkflowLoggerRequest loggerRequest =
        WorkflowLoggerRequest.builder()
            .className(WorkflowLoggerTest.class.getSimpleName())
            .message("error occured")
            .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
            .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER)
            .build();
    final String expectedOutput =
        "className=WorkflowLoggerTest message=error occured downstreamComponentName=APP_CONNECT downstreamServiceName=WORKFLOW_TASK_HANDLER definitionId=DEF_ID_TEST ownerId=OWNER_ID_TEST templateId=TEMPLATE_ID_TEST processInstanceId=PID_TEST recordId=RECORD_ID_TEST offeringId=default";
    final String actualOutput = loggingFormat.format(loggerRequest.getLoggingParams());
    Assert.assertEquals(expectedOutput, actualOutput);
  }

  @Test
  public void test() {

    final WorkflowLoggerRequest loggerRequest =
        WorkflowLoggerRequest.builder()
            .className(WorkflowLoggerTest.class.getSimpleName())
            .stackTrace(new NullPointerException())
            .message("error occured")
            .downstreamComponentName(DownstreamComponentName.APP_CONNECT)
            .downstreamServiceName(DownstreamServiceName.WORKFLOW_TASK_HANDLER)
            .build();
    final String actualOutput = loggingFormat.format(loggerRequest.getLoggingParams());
    Assert.assertTrue(actualOutput.contains("className=WorkflowLoggerTest"));
    Assert.assertTrue(actualOutput.contains("message=error occured"));
    Assert.assertTrue(actualOutput.contains("downstreamComponentName=APP_CONNECT"));
    Assert.assertTrue(actualOutput.contains("downstreamServiceName=WORKFLOW_TASK_HANDLER"));
    Assert.assertTrue(actualOutput.contains("stackTrace=java.lang.NullPointerException"));
  }

  private void init() {

    MDC.put(WASContextEnums.PROCESS_INSTANCE_ID.getValue(), "PID_TEST");
    MDC.put(WASContextEnums.DEFINITION_ID.getValue(), "DEF_ID_TEST");
    MDC.put(WASContextEnums.TEMPLATE_ID.getValue(), "TEMPLATE_ID_TEST");
    MDC.put(WASContextEnums.OWNER_ID.getValue(), "OWNER_ID_TEST");
    MDC.put(WASContextEnums.RECORD_ID.getValue(), "RECORD_ID_TEST");
  }
}

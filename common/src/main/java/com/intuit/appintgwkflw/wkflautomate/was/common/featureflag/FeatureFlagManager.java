package com.intuit.appintgwkflw.wkflautomate.was.common.featureflag;

import java.util.Map;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class FeatureFlagManager {

    private final FeatureFlagFactory featureFlagFactory;

    /**
     * Use this method to fetch featureFlags with Context, Make sure you are in valid context.
     *
     * @param flag
     * @param defaultValue
     * @return
     */
    public boolean getBoolean(String flag, boolean defaultValue) {
        FeatureManager featureManager = featureFlagFactory.getInstance();
        return featureManager.getBoolean(flag, defaultValue);
    }

    /**
     * Use this method to fetch featureFlags with Context and workflowName, Make sure you are in valid context.
     * The workflow names added in ff configuration will only be allowed for single definition creation
     * @param flag
     * @param defaultValue
     * @return
     */
    public boolean getBoolean(String flag, boolean defaultValue, String workflowName) {
        FeatureManager featureManager = featureFlagFactory.getInstance();
        return featureManager.getBoolean(flag, defaultValue,  workflowName);
    }

    /**
     * Use this method to fetch featureFlags with Context and workflowName and OwnerId when the context is not set
     * The workflow names added in ff configuration will only be allowed for single definition creation
     * @param flag
     * @param defaultValue
     * @return
     */
    public boolean getBoolean(String flag, boolean defaultValue, String workflowName, Long ownerId) {
        FeatureManager featureManager = featureFlagFactory.getInstance();
        return featureManager.getBoolean(flag, defaultValue, workflowName, ownerId);
    }

    // TODO : Remove after ID 2.0 migration
    public boolean getBoolean(String flag, String ownerId){
        FeatureManager featureManager = featureFlagFactory.getInstance();
        return featureManager.getBoolean(flag, ownerId);
    }

    /**
     * This method fetch the boolean result for the given feature flag name using context.
     *
     * @param flag
     * @param defaultValue
     * @param contextMap
     * @param ownerId
     * @return
     */
    public boolean getBooleanWithContextMap(String flag, boolean defaultValue, Map<String, Object> contextMap,
        Long ownerId) {
        FeatureManager featureManager = featureFlagFactory.getInstance();
        return featureManager.getBoolean(flag, defaultValue, contextMap, ownerId);
    }

}
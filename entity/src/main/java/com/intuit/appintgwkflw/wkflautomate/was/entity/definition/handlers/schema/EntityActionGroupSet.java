package com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema;
import java.util.LinkedHashSet;
import java.util.Set;
import lombok.Data;

/**
 * This class is used to store the Action Group Set for a particular Entity Type.
 * for example: Invoice has a set of Action Groups which are enabled for it.
 * invoice -> actionGroupSet -> actionGroup -> action
 * */
@Data
public class EntityActionGroupSet {

    private String entityId;
    private String actionGroup;
    private LinkedHashSet<String> enabledActions;
    private LinkedHashSet<String> enabledAttributes;
    private String precannedTemplateId;
    private ActionIdMapper actionIdMapper;

    private Set<String> defaultAttributes;
    private Trigger trigger;

}
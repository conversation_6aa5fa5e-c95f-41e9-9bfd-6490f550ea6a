function fn(featureName) {
  var uuid = '' + java.util.UUID.randomUUID();

  var event = 'Supplying-'+ java.lang.System.currentTimeMillis();
  var out = {
    intuit_tid : uuid,
    entity_type : 'PaymentTransactionEvent',
    entity_namespace : 'com.intuit.idx.event.payment.v1',
    entity_version : 'v1',
    test : 'testValue',
    intuit_corpid : "PaymentEntityMessageValidation",
    provider_id: '1234',
    intuit_offeringid: 'Intuit.platform.qbo.dtx.ui',
    intuit_realmid: '9130357007120406',
    "spring.cloud.stream.sendto.destination":"idx-payment-source"
  };
  return out;
}

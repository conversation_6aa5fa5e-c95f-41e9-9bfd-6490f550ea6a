package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.tasks;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.DeploymentResponse;
import com.intuit.async.execution.request.State;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;

@RunWith(MockitoJUnitRunner.class)
public class CamundaDeleteDeploymentTaskTest {

  private static final String DEFINITION_ID = "definitionId";
  @Mock
  private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
  private State state = new State();
  private CamundaDeleteDeploymentTask camundaDeleteDeploymentTask;


  @Test
  public void testDeleteDeploymentWithEmptyParameters() {
    try {
      camundaDeleteDeploymentTask = new CamundaDeleteDeploymentTask(bpmnEngineDefinitionServiceRest,
          null, null, null);
      camundaDeleteDeploymentTask.execute(state);
    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getError().getMessage(), WorkflowError.INVALID_INPUT.name());
    }
  }

  @Test
  public void testDeleteDeploymentWithInvalidError() {
    try {
      camundaDeleteDeploymentTask = new CamundaDeleteDeploymentTask(bpmnEngineDefinitionServiceRest,
          DEFINITION_ID, true, true);

      Mockito.when(bpmnEngineDefinitionServiceRest.getDeploymentDetails(DEFINITION_ID))
          .thenReturn(WASHttpResponse.<DeploymentResponse>builder().status(
              HttpStatus.INTERNAL_SERVER_ERROR).isSuccess2xx(false).build());
      camundaDeleteDeploymentTask.execute(state);

    } catch (WorkflowGeneralException e) {
      Assert.assertEquals(e.getError().getMessage(), WorkflowError.DELETE_DEFINITION_FAILED.name());
    }
  }

  @Test
  public void testDeleteDeploymentWithAlreadyDeletedDeployment() {
    try {
      camundaDeleteDeploymentTask = new CamundaDeleteDeploymentTask(bpmnEngineDefinitionServiceRest,
          DEFINITION_ID, true, true);

      Mockito.when(bpmnEngineDefinitionServiceRest.getDeploymentDetails(DEFINITION_ID))
          .thenReturn(WASHttpResponse.<DeploymentResponse>builder().status(
                  HttpStatus.INTERNAL_SERVER_ERROR).isSuccess2xx(false)
              .error(String.format(WorkflowConstants.DEPLOYMENT_NOT_FOUND, DEFINITION_ID))
              .build());
      camundaDeleteDeploymentTask.execute(state);
    } catch (WorkflowGeneralException e) {
      Assert.fail();
    }
  }

  @Test
  public void testDeleteDeploymentSuccess() {
    DeploymentResponse deploymentResponse = new DeploymentResponse();
    deploymentResponse.setDeploymentId("d11");
    camundaDeleteDeploymentTask = new CamundaDeleteDeploymentTask(bpmnEngineDefinitionServiceRest,
        DEFINITION_ID, true, true);

    Mockito.when(bpmnEngineDefinitionServiceRest.getDeploymentDetails(DEFINITION_ID))
        .thenReturn(WASHttpResponse.<DeploymentResponse>builder().status(
                HttpStatus.NO_CONTENT).isSuccess2xx(true)
            .response(deploymentResponse)
            .build());

    Mockito.doNothing().when(bpmnEngineDefinitionServiceRest).deleteDeployment(Mockito.any());
    camundaDeleteDeploymentTask.execute(state);
    Assert.assertNotNull(state);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void testDeleteDeploymentThrowsException() {
    DeploymentResponse deploymentResponse = new DeploymentResponse();
    deploymentResponse.setDeploymentId("d11");
    camundaDeleteDeploymentTask = new CamundaDeleteDeploymentTask(bpmnEngineDefinitionServiceRest,
        DEFINITION_ID, true, true);

    Mockito.when(bpmnEngineDefinitionServiceRest.getDeploymentDetails(DEFINITION_ID))
        .thenReturn(WASHttpResponse.<DeploymentResponse>builder().status(
                HttpStatus.NO_CONTENT).isSuccess2xx(true)
            .response(deploymentResponse)
            .build());

    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.DELETE_DEPLOYMENT_FAILED))
        .when(bpmnEngineDefinitionServiceRest).deleteDeployment(Mockito.any());
    camundaDeleteDeploymentTask.execute(state);
    Assert.assertNotNull(state);
  }

}

kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  labels:
    env: prd-usw2-eks
  name: was-event-in-rollout
spec:
  strategy:
    canary:
      canaryMetadata:
        annotations:
          role: canary
        labels:
          role: canary
      stableMetadata:
        annotations:
          role: stable
        labels:
          role: stable
      steps:
      - setWeight: 10
      - pause: {duration: 5m}
      - setWeight: 30
      - pause: {duration: 5m}
      - setWeight: 50
      - pause: {duration: 5m}
      - setWeight: 80
      - pause: {duration: 5m}
  # Set replicas to null when enabling HPA, sample: 'replicas: null'
  replicas: null
  template:
    metadata:
      labels:
        env: prd-usw2-eks
        splunk-index: messagemesh
      annotations:
        iam.amazonaws.com/role: k8s-appintgwkflw-wkflautomate-waseventin-usw2-prd
    spec:
      containers:
      - name: app
        env:
        - name: APP_ENV
          value: prd
        - name: APP_CONFIG_NAME
          value: waseventin
        - name: RUNTIME_ID
          value: was-event-in-runtime-estimate
        - name: MESSAGE_MESH_SRC_TOPICS
          value: was-event-in-replay
        - name: EVENT_BUS_IDPS_POLICY_ID
          value: p-cyxnnvv56awh
        - name: MESSAGE_MESH_SRC_CONSUMER_GROUP
          value: was-event-in-processor-group-prd-estimate
        - name: MESSAGE_MESH_PIPELINE_FILENAME
          value: sample-pipeline.yml
        - name: SPRING_CONFIG_IDPS_POLICY_ID
          value: p-c6dcpy6m6xm2
        - name: HOSTED_CONFIG_BRANCH
          value: prd
        resources:
          limits:
            cpu: 4500m
            memory: 6Gi
          requests:
            cpu: 3000m
            memory: 4Gi
      initContainers:
      - name: segment-app-init
        env:
        - name: APP_ENV
          value: prd
        - name: SEGMENT_CLUSTER_ROLE_ARN
          value: arn:aws:iam::536910636111:role/shared.sbg-qbo-prod-usw2-k8s
        - name: SEGMENT_IDPS_APPLIANCE
          value: MSaaSBU02-PRODUCTION-RJPH0E.pd.idps.a.intuit.com
        - name: SEGMENT_IDPS_POLICY_ID
          value: p-vdpuprtfpj5i
        - name: AWS_SDK_LOAD_CONFIG
          value: "1"
        - name: AWS_ROLE_SESSION_NAME
          value: kiam-kiam

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.intuit.paved-road.appintgwkflw-wkflautomate.test</groupId>
    <artifactId>was-event-in-app-integration</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>

    <url>https://github.intuit.com/appintgwkflwwkflautomate/Intuit.git</url>
    <organization>
        <name>Intuit, Inc. :: Your BU or group :: was-event-in-app</name>
        <url>https://github.intuit.com/appintgwkflwwkflautomate</url>
    </organization>

    <name>was-event-in - Integration Test</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot-maven-plugin.version>3.1.9</spring-boot-maven-plugin.version>
        <java.version>11</java.version>
        <junit-version>5.8.1</junit-version>
        <surefireArgLine />
        <jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>
        <maven-cucumber-reporting.version>5.7.1</maven-cucumber-reporting.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-release-plugin.version>2.5.3</maven-release-plugin.version>
        <maven-deploy-plugin.version>2.8.2</maven-deploy-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-surefire-report-plugin.version>3.2.2</maven-surefire-report-plugin.version>
        <project.testresult.directory>${project.build.directory}/test-results</project.testresult.directory>
        <central.repo>https://artifact.intuit.com/artifactory/maven-proxy</central.repo>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>net.masterthought</groupId>
            <artifactId>maven-cucumber-reporting</artifactId>
            <version>${maven-cucumber-reporting.version}</version>
            <!-- Note that maven-cucumber-reporting 3.8.0 (from June 2017), via cucumber-reporting, inappropriately
                 brings in log4j-core as a transitive dependency. This appears to be fixed at least as of 5.2.6.
                 The version of log4j-core _should be_ managed by jsk-bom. -->
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/Test*.java</include>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                        <include>**/*TestCase.java</include>
                    </includes>
                    <useFile>true</useFile>
                    <!-- March 2021 - during transition period, enable legacy mode for Spring
                        Boot 2.4 See https://github.com/spring-projects/spring-boot/wiki/Spring-Boot-Config-Data-Migration-Guide#legacy-mode
                        and https://wiki.intuit.com/display/CTODevSP/Spring+Boot+2.4+Migration+Notes
                        . And we also need to set the system property spring.cloud.bootstrap.enabled
                        (or equivalent environment variable) to have Spring Boot 2.4 (Spring Cloud
                        2020) process bootstrap files! See above and https://docs.spring.io/spring-cloud-config/docs/3.0.0/reference/html/#config-data-import
                        and http://maven.apache.org/surefire/maven-surefire-plugin/faq.html#late-property-evaluation -->
                    <argLine>@{argLine} @{surefireArgLine}</argLine>
                    <forkCount>0</forkCount>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>central-repo</id>
            <url>${central.repo}</url>
            <name>Intuit Maven Cache</name>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central-plugin-repo</id>
            <url>${central.repo}</url>
            <name>Intuit Maven Cache (plugins)</name>
        </pluginRepository>
    </pluginRepositories>
</project>

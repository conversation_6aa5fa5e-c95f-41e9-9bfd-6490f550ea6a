package com.intuit.appintgwkflw.wkflautomate.was.event.gc.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for GC.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "gc")
public class GCConfig {
    private boolean serviceEnabled;
    private int maxRetries;
    private String cron;
    private int unmodifiedSinceSeconds;
    private int maxRecordsToBeFetched;
    private String consumerGroup;
}

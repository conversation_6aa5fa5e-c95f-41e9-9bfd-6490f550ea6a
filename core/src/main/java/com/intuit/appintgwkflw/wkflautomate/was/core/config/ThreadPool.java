package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public final class ThreadPool {

  // The max blocking queue size for all executors
  private Integer maxQueueSize;
  // The minimum threads to be used for the shared executor
  private Integer sharedMinThreads;
  // The maximum threads to be used for the shared executor
  private Integer sharedMaxThreads;
  // The minimum threads to be used for the reserved executors
  private Integer individualMinThreads;
  // The maximum threads to be used for the reserved executors
  private Integer individualMaxThreads;
  // When the number of threads is greater than the core, this is the maximum time
  // that excess idle threads will wait for new tasks before terminating.
  private Integer idleThreadTimeoutSec;
  // The time given to the threads for shutting down gracefully
  private Integer allowedGracefulShutDownTimeSec;
}

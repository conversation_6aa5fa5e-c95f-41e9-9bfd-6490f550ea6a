package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.converter;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import javax.persistence.AttributeConverter;

public class SchedulerActionConverter implements AttributeConverter<SchedulerAction, String> {
  @Override
  public String convertToDatabaseColumn(SchedulerAction attribute) {
    return attribute != null ? attribute.getAction() : null;
  }

  @Override
  public SchedulerAction convertToEntityAttribute(String dbData) {
    return dbData != null ? SchedulerAction.fromAction(dbData) : null;
  }
}

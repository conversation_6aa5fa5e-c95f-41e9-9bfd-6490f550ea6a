package com.intuit.appintgwkflw.wkflautomate.was.worker;

import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.ExtensionAttributesConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.threadPool.ThreadPoolExecutorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamServiceName;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.Worker;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.ExecuteActionAsync;
import com.intuit.appintgwkflw.wkflautomate.was.worker.executor.WorkerExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.camunda.bpm.client.ExternalTaskClient;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.camunda.bpm.client.topic.TopicSubscription;
import org.camunda.bpm.client.topic.TopicSubscriptionBuilder;

@RequiredArgsConstructor
public class ExternalTaskWorker {

  private final ExternalTaskClient externalTaskClient;

  @Getter private final Worker worker;

  private final WorkerExecutor workerExecutor;

  private final ThreadPoolExecutor sharedExecutor;

  private final ThreadPoolExecutor reservedExecutor;

  private final WASContextHandler contextHandler;

  private final ExtensionAttributesConfig extensionAttributesConfig;

  private TopicSubscriptionBuilder topicSubscriptionBuilder;

  private TopicSubscription topicSubscription;

  /** Start the worker and subscribe to the topic. */
  public void initialize(String businessKey) {
    topicSubscriptionBuilder =
        externalTaskClient
            .subscribe(worker.getTopicName())
            .businessKey(businessKey)
            .lockDuration(worker.getLockDuration())
            .includeExtensionProperties(extensionAttributesConfig.isEnabled())
            .handler(this::submit);
    externalTaskClient.stop();
  }

  public void stop() {
    boolean isActive = externalTaskClient.isActive();
    WorkflowLogger.logInfo("For worker=%s isActive=%s", worker.getTopicName(), isActive);
    if (isActive) {
      WorkflowLogger.logInfo("RegionConfig:: Stopping worker=%s", worker.getTopicName());
      externalTaskClient.stop();
      if (topicSubscription != null) {
        topicSubscription.close();
      }
    }
  }

  public void start() {
    WorkflowVerfiy.verifyNull(topicSubscriptionBuilder, WorkflowError.WORKER_NOT_INITIALIZE);
    boolean isActive = externalTaskClient.isActive();
    WorkflowLogger.logInfo("For worker=%s isActive=%s", worker.getTopicName(), isActive);
    if (!isActive) {
      WorkflowLogger.logInfo("RegionConfig:: Starting worker=%s", worker.getTopicName());
      topicSubscription = topicSubscriptionBuilder.open();
      externalTaskClient.start();
    }
  }

  private void submit(final ExternalTask externalTask,
      final ExternalTaskService externalTaskService) {

    WorkflowLogger.info(() -> WorkflowLoggerRequest.builder()
        .className(this.getClass().getSimpleName())
        .downstreamComponentName(DownstreamComponentName.WAS)
        .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
        .message("Submitting task for topic=%s processInstanceId=%s activityId=%s taskId=%s",
            externalTask.getTopicName(),
            externalTask.getProcessInstanceId(),
            externalTask.getActivityId(),
            externalTask.getId()));

    WorkflowLogger.info(() ->
            WorkflowLoggerRequest.builder()
                    .className(this.getClass().getName())
                    .message("Worker Thread pool stats: sharedPoolRemainingCapacity=%s, sharedPoolActiveCount=%s; reservedPoolRemainingCapacity=%s, reservedPoolActiveCount=%s for the topic=%s",
                            sharedExecutor.getQueue().remainingCapacity(),
                            sharedExecutor.getActiveCount(),
                            reservedExecutor.getQueue().remainingCapacity(),
                            reservedExecutor.getActiveCount(),
                            externalTask.getTopicName())
                    .downstreamComponentName(DownstreamComponentName.WAS)
                    .downstreamServiceName(DownstreamServiceName.CAMUNDA_EXTERNAL_TASK)
    );


    ThreadPoolExecutorFactory.getTargetExecutor(sharedExecutor, reservedExecutor).submit(
        new ExecuteActionAsync(externalTask, externalTaskService, workerExecutor, contextHandler,
            worker));
  }
}

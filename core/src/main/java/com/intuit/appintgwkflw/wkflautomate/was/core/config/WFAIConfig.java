package com.intuit.appintgwkflw.wkflautomate.was.core.config;

import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.objects.AdminDetails;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.*;

/**
 * <AUTHOR>
 * @apiNote Configuration Class to fetch Company Admin User
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wfaiconfig")
public class WFAIConfig {
    private Set<String> templates;
    private Map<String, List<AdminDetails>> companyAdminMap;
}

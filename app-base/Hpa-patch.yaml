apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: was-event-in
  name: rollout-hpa
spec:
  # Minimum replicas, check the current CPU utilization and make sure the minimum replicas can sustain under current resources
  minReplicas: 3
  # Maximum replicas, please make sure the maximum replicas are higher than what you have in deployment.yaml
  maxReplicas: 10
  metrics:
  - object:
      metric:
        # The following metrics are aggregated in Prometheus, so that HPA doesn't have to query for each pod
        # It reduces the query frequency on metrics server and Prometheus
        # Average CPU usage on echo pod, 35 will be good target value for cpu utilization
        name: namespace_app_pod_cpu_utilization
      target:
        type: Value
        # Target Value
        value: 50
      describedObject:
        # Please keep the following apiVersion and kind, don't replace them with kind: Rollout
        apiVersion: apps/v1
        kind: Deployment
        # This point to the "app" label on the pod
        name: was-event-in
    type: Object
  - object:
      metric:
        # This metric only picks the avg CPU utilization from pods in ready state only
        # This metric helps the pods which has very long terminating time
        name: namespace_app_container_app_ready_only_avg_cpu_utilization
      target:
        type: Value
        # Target Value
        value: 50
      describedObject:
        # Please keep the following apiVersion and kind, don't replace them with "kind: Rollout"
        apiVersion: apps/v1
        kind: Deployment
        # This point to the "app" label on the pod
        name: was-event-in
    type: Object
  ## Metric for istio-proxy sidecar scale, if the istio container becomes hot
  #  - object:
  #      metric:
  #        name: namespace_app_container_istioproxy_ready_only_avg_cpu_utilization
  #      target:
  #        type: Value
  #        value: 40
  #      describedObject:
  #        apiVersion: apps/v1
  #        kind: Deployment
  #        # Use metadata.labels.app, since the metric is aggregated by app
  #        name: PAWS_BASEARTIFACT_ID
  #
  # Support for configurable scaling behavior: Starting from v1.18 the v2beta2 API allows scaling behavior to be configured through the HPA behavior field
  # Ref: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/#support-for-configurable-scaling-behavior
  # There can be 2 behaviors for scaling - scaleUp and scaleDown
  # Both behaviors has the below 3 parameters - stabilizationWindowSeconds, policies, selectPolicy
  # Examples of behaviors: https://wiki.intuit.com/display/SBSEGAX/HPA+-+Configurable+scaling+behavior
  behavior:
    scaleUp:
      # stabilizationWindowSeconds: The stabilization window is used to restrict the flapping of replicas when the metrics used for scaling keep fluctuating
      stabilizationWindowSeconds: 0
      # policies: One or more scaling policies can be specified in the behavior section of the spec. When multiple policies are specified the policy which allows the highest amount of change is the policy which is selected by default
      policies:
      - # Percent: the percent of current pod count you want to scale up
        type: Percent # type: can be Percent or Pods
        value: 40
        periodSeconds: 15
      - type: Pods # the number of pods you want to scale up
        value: 2
        periodSeconds: 15
      # selectPolicy: By setting the value to Min which would select the policy which allows the smallest change in the replica count. Setting the value to Disabled completely disables scaling in that direction.
      selectPolicy: Max
    scaleDown:
      # stabilizationWindowSeconds: The stabilization window is used to restrict the flapping of replicas when the metrics used for scaling keep fluctuating
      stabilizationWindowSeconds: 300
      # policies: One or more scaling policies can be specified in the behavior section of the spec. When multiple policies are specified the policy which allows the highest amount of change is the policy which is selected by default
      policies:
      - # Percent: the percent of current pod count you want to scaleDown
        type: Percent # type: can be Percent or Pods
        value: 10
        periodSeconds: 60
      # Default select policy is minimum for scaleDown
      selectPolicy: Min
  scaleTargetRef:
    apiVersion: argoproj.io/v1alpha1
    kind: Rollout
    # It should same as the rollout metadata.name in Rollout Spec
    name: was-event-in-appd-rollout

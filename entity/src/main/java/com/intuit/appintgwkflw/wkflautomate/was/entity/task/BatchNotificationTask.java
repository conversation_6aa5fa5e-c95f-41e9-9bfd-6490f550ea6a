package com.intuit.appintgwkflw.wkflautomate.was.entity.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * Modeling attributes for Batch OINP notification.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BatchNotificationTask extends Task {

  private static final ObjectMapper objectMapper = new ObjectMapper();

  /** List of notification events.Notification will sent via Batch OINP API. */
  @JsonDeserialize(using = NotificationTaskListDeserializer.class)
  @NonNull private List<NotificationTask> notificationTaskList;

  @Override
  public TaskType getType() {
    return TaskType.BATCH_NOTIFICATION_TASK;
  }

  public static class NotificationTaskListDeserializer extends JsonDeserializer<List<NotificationTask>> {

    @Override
    public List<NotificationTask> deserialize(JsonParser jp, DeserializationContext ctxt)
        throws IOException {
      JsonNode node = jp.getCodec().readTree(jp);

      if(node.isTextual()) {
        return objectMapper.readValue(node.asText(), new TypeReference<List<NotificationTask>>() {});
      }
      List<NotificationTask> notificationTasks = new ArrayList<>();
      for (JsonNode jsonNode : node){
        if (jsonNode.isObject()){
          Optional.ofNullable(objectMapper.convertValue(jsonNode, NotificationTask.class))
              .ifPresent(notificationTasks::add);
        }
        else {
          Optional.ofNullable(objectMapper.readValue(jsonNode.asText(), NotificationTask.class))
              .ifPresent(notificationTasks::add);
        }
      }
      return notificationTasks;
    }
  }
}

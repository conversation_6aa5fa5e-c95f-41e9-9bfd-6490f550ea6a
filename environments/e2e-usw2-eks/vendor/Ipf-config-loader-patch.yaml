apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: was-event-in-rollout
spec:
  template:
    spec:
      initContainers:
      - name: ipf-config-loader
        env:
          - name: "CONFIG_ENDPOINT"
            value: "https://config-e2e.api.intuit.com/v2/waseventin/default/e2e-workflow-automation/"
          - name: RUNTIME_ID
            value: "was-event-in-runtime-vendor"
          - name: "IDPS_POLICY_ID"
            value: "p-vkvgmu5wnx0r"
          - name: REGISTRY_ENVIRONMENT
            value: "e2e"
          - name: CONFIG_FILE_NAMES
            value: core-config.yml, pipeline-definition-vendor.yml, processor-config.yml
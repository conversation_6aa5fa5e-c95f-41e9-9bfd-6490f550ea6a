package com.intuit.appintgwkflw.wkflautomate.was.dmn.parser;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.WHITESPACE_REGEX;
import static org.apache.commons.lang3.ArrayUtils.getLength;
import static org.apache.commons.lang3.StringUtils.isBlank;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DaysOperator;
import java.text.MessageFormat;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.dmn.engine.impl.spi.type.DmnDataTypeTransformer;
import org.camunda.bpm.engine.variable.Variables;
import org.camunda.bpm.engine.variable.value.TypedValue;
import org.springframework.stereotype.Component;

/** <AUTHOR> */
@Component
@AllArgsConstructor
public class DaysTransformer implements DMNDataTypeTransformer, DmnDataTypeTransformer {

  private static final String MINUS = "-";
  private static final String EQUALS = "==";
  private static Pattern numberPattern = Pattern.compile("^-?[0-9]+$");

  /** Expected value: BF 3 or AF 5 or ON 0 */
  @Override
  public String transformToDmnFriendlyExpression(String userFriendlyExpr, String parameterName,
      String parameterType, boolean useFeelExpr) {
    String[] inputRule = userFriendlyExpr.split(WorkflowConstants.SPACE);
    verify(inputRule);
    String value = inputRule[1].trim();

    /**
     * For FEEL expressions
     * If BF (BEFORE) is the token setting, -value is returned
     * else value is returned
     *
     * <pre>
     *  e.g.
     *  For inputRule = ["BF", -5], -(-5) = 5 is returned
     *  For inputRule = ["AF", 5], 5 is returned
     * </pre>
     */
    if (useFeelExpr) {
      if (DaysOperator.BEFORE.getSymbol().equals(inputRule[0])) {
        return MINUS + value;
      }
      return value;
    }
    if (DaysOperator.BEFORE.getSymbol().equals(inputRule[0])) {
      return MessageFormat.format("{0} {1} {2}{3}", parameterName.replaceAll(WHITESPACE_REGEX, ""), EQUALS, MINUS, value);
    }

    return MessageFormat.format("{0} {1} {2}", parameterName.replaceAll(WHITESPACE_REGEX, ""), EQUALS, value);
  }

  @Override
  public DMNSupportedOperator getName() {
    return DMNSupportedOperator.DAYS;
  }

  @Override
  public String transformToUserFriendlyExpression(String dmnFriendlyExpr, String parameterName) {
    if (isBlank(dmnFriendlyExpr)) {
      return StringUtils.EMPTY;
    }

    int value = getValueFromRule(dmnFriendlyExpr);
    return getUIExpressionForValue(value, parameterName);
  }

  @Override
  public String defaultRule(String parameterName, String defaultValue) {
    int value = 0;
    if (StringUtils.isNotBlank(defaultValue)) {
      value = convertToInteger(defaultValue);
    }
    return getUIExpressionForValue(value, parameterName);
  }

  @Override
  public String getDataType() {
    return Integer.class.getSimpleName();
  }

  /**
   * if value is negative it returns BF {val}
   *
   * <p>if value is positive it returns AF {val}
   *
   * <p>if value is 0 it returns ON {val}
   *
   * <p>if parameterName is Everyday it returns ALL {val}
   *
   * @param value input value
   * @return formatted DMN friendly value.
   */
  private String getUIExpressionForValue(int value, String parameterName) {

    // if its everyday reminder we will return all with given value
    if(WorkflowConstants.EVERY_DAY.equalsIgnoreCase(parameterName)){
      return MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, DaysOperator.EVERY_DAY.getSymbol(), value);
    }
    // if negative return BF value
    else if (value < 0) {
      return MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, DaysOperator.BEFORE.getSymbol(), Math.abs(value));
    }
    // if negative return ON 0
    else if (value == 0) {
      return MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, DaysOperator.ON.getSymbol(), value);
    }
    // if positive return AF value
    return MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_SPACE, DaysOperator.AFTER.getSymbol(), value);
  }

  // The dmn expression can be "DueDate == -3"/"DueDate == 3" in case of Juel or just "-3"/"3" in case of Feel.
  // We need to get the absolute value in both cases (-3/3).
  private int getValueFromRule(String value) {
    String[] dmnRule = value.split(EQUALS);
    if(dmnRule.length == 1 && numberPattern.matcher(dmnRule[0]).matches()) {
      return convertToInteger(dmnRule[0].trim());
    }
    verify(dmnRule);
    return convertToInteger(dmnRule[1].trim());
  }

  /**
   * verifies if length is 2 else throws {@link WorkflowGeneralException}
   *
   * @param expression input expression
   */
  private void verify(String[] expression) {
    WorkflowVerfiy.verify(
        getLength(expression) != 2,
        WorkflowError.INPUT_INVALID,
        DaysTransformer.class.getSimpleName());
  }
  /**
   * throw exception in case not a number
   *
   * @param value input value to be parsed
   * @return integer value
   */
  private int convertToInteger(String value) {
    try {
      return Integer.parseInt(value);
    } catch (NumberFormatException e) {
      WorkflowVerfiy.verify(
          true, WorkflowError.INPUT_INVALID, DaysTransformer.class.getSimpleName());
    }
    // it will never come here
    return -1;
  }

  //Used by engine to transform variable values
  @Override
  public TypedValue transform(Object value) throws IllegalArgumentException {
    if (value instanceof String) {
      if (StringUtils.isEmpty((String) value)) {
        return Variables.untypedNullValue();
      }
      return Variables.integerValue(Integer.parseInt((String) value));
    } else if (value instanceof Number) {
      return Variables.integerValue(((Number) value).intValue());
    }
    WorkflowLogger.logWarn("Untyped_Value value="+value);
    return Variables.untypedValue(value);
  }
}

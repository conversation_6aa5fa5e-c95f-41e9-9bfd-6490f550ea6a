package com.intuit.appintgwkflw.wkflautomate.was.common.retrystrategy;

import com.intuit.appintgwkflw.wkflautomate.was.common.retry.WorkerRetryHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.google.common.primitives.Ints.constrainToRange;

/**
 * Implementation of Retry with Linear Mod BackOff
 * <AUTHOR>
 */
@Component
public class LinearModBackOffRetryImpl implements RetryStrategy {

    @Autowired WorkerRetryHelper workerRetryHelper;

    private final int MIN_MULTIPLIER = 1;
    private final int MAX_MULTIPLIER = 10;

    @Override
    public RetryStrategyName getName() {
        return RetryStrategyName.LINEAR_MOD_BACKOFF;
    }

    @Override
    public long computeRetryTimer(int retryLimit, int retryCount, Long backOffStepSize) {

        // The time is calculated by taking the linear difference of the amount of retries left,
        // ie, (retryLimit - retryCount)
        // In case the amount of retries, or the multiplier, is greater than or less than the allowed
        // range, then the multiplier is clipped to that range.
        // For example, in case of retry count = 1, retry limit = 3 & backOffStepSize = 10 secs, the
        // retry time would = (3-1+1)*30 = 30 seconds.

        int multiplier = constrainToRange(retryLimit - retryCount + 1, MIN_MULTIPLIER, MAX_MULTIPLIER);
        return Math.min(backOffStepSize * multiplier, workerRetryHelper.getMaximumBackOff());
    }
}

package com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.CompositeStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.NonCompositeStepBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadCompositeStepHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.v4.workflows.RuleLine;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * This factory returns whether a composite/non composite step should be built
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class CompositeStepBuilderFactory {

  private final CompositeStepBuilder compositeStepBuilder;
  private final NonCompositeStepBuilder nonCompositeStepBuilder;

  /**
   * This function returns the handler based on the ruleLine type
   *
   * @param ruleLine
   * @return {@link ReadCompositeStepHandler}
   */
  public ReadCompositeStepHandler getHandler(RuleLine ruleLine) {
    //  Will always have a single rule since it is the last condition before action for composite type
    // RuleLine will not be null  and will have atleast 1 rule when it reaches here since there is a check
    RuleLine.Rule rule = ruleLine.getRules().stream().findFirst().orElse(null);
    return MultiStepUtil.isRuleTypeComposite(rule) ? compositeStepBuilder : nonCompositeStepBuilder;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.localisation.service;

import com.intuit.appintgwkflw.wkflautomate.was.localisation.config.LocalisationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.enums.TemplateToFileMapping;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationError;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.exception.LocalisationVerify;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.util.LocalisationUtility;
import org.springframework.util.StringUtils;

import java.util.Locale;
import java.util.MissingResourceException;
import java.util.Optional;
import java.util.ResourceBundle;

import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.GLOBAL_PARAMETERS.FILE_SEPARATOR;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.BASE_PATH;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.DEFAULT_LOCALE;
import static com.intuit.appintgwkflw.wkflautomate.was.localisation.util.Constants.RESOURCE_BUNDLE.SUFFIX_PATH;

/** This class is responsible for getting localised data. */
public class AppLocalisationResourceBundle {

  private final LocalizedResourceBundle localizedResourceBundle;
  private LocalisationConfig localisationConfig;

  public AppLocalisationResourceBundle(
      final LocalizedResourceBundle localizedResourceBundle,
      final LocalisationConfig localisationConfig) {
    this.localizedResourceBundle = localizedResourceBundle;
    this.localisationConfig = localisationConfig;
  }

  public AppLocalisationResourceBundle(final LocalizedResourceBundle localizedResourceBundle) {
    this.localizedResourceBundle = localizedResourceBundle;
  }

  /**
   * This method return the localised string w.r.t given bundle path, key and locale.
   *
   * <pre>
   *   1. Validate the input parameters key, bundle path and locale.
   *   2. Get the locale bundle using <code>WASResBundleControl</code> instance.
   *   3. Get locale string using bundle if key does not exists then checks for the default locale as well.
   *   4. If key is not present in default locale as well then it throws an exception <code>WorkflowGeneralException</code>
   * </pre>
   *
   * @param bundle full path for the resource bundle
   * @param key key for localised string
   * @param locale locale
   * @return Localised string
   */
  public String getString(final String bundle, final String key, final Locale locale) {
    LocalisationVerify.verify(key, LocalisationError.INPUT_INVALID, "key");
    LocalisationVerify.verify(bundle, LocalisationError.INPUT_INVALID, "bundle path");
    LocalisationVerify.verify(locale == null, LocalisationError.INPUT_INVALID, "locale");
    return this.getStringFromLocale(bundle, key, locale);
  }

  /**
   * This method return the localised string w.r.t given bundle path, key and locale.
   *
   * @param bundle full path for the resource bundle
   * @param key key for localised string
   * @param locale locale
   * @return Localised string
   */
  private String getStringFromLocale(final String bundle, final String key, final Locale locale) {
    final ResourceBundle resBundle = this.getBundle(bundle, locale);
    LocalisationVerify.verify(
        resBundle == null, LocalisationError.RESOURCE_BUNDLE_NOT_FOUND, bundle, locale, key);
    try {
      return resBundle.getString(key);
    } catch (final MissingResourceException missingResourceException) {
      final Locale defaultLocale = this.getLocale(null);
      // Throwing exception in case of default or other than en_US region
      LocalisationVerify.verify(
          locale.equals(defaultLocale) || !locale.equals(this.getLocale("en_US")),
          LocalisationError.LOCALISED_KEY_NOT_PRESENT,
          key,
          bundle,
          locale);
      return this.getStringFromLocale(bundle, key, defaultLocale);
    }
  }
  /**
   * This method return the localised string w.r.t given template name, filename, key and locale.
   * e.g.
   * <pre>
   *   - filename = "WasworkflowActions"
   *   - key = "invoice_number"
   *   - template_name = "invoice approval"
   *   - locale = "en"
   *
   *  This method create a bundle path with following format :
   *  <code> wasLocalisationConfigModel.getBaseName()</code>/{templateName}/{filename}
   *  <pre/>
   *
   * @param templateName templateName used as folder in bundle path
   * @param fileName name of file in which we are looking data
   * @param key key for localised string
   * @param localeStr locale
   * @return Localised string
   */
  public String getString(
      final String key, final String templateName, final String fileName, final String localeStr) {
    LocalisationVerify.verify(key, LocalisationError.INPUT_INVALID, "Key");
    LocalisationVerify.verify(fileName, LocalisationError.INPUT_INVALID, "fileName");
    LocalisationVerify.verify(templateName, LocalisationError.INPUT_INVALID, "templateName");
    final Locale locale = this.getLocale(localeStr);
    final StringBuilder bundle =
        new StringBuilder(
            Optional.ofNullable(localisationConfig)
                .map(LocalisationConfig::getBasePath)
                .orElse(BASE_PATH));
    bundle.append(templateName).append(FILE_SEPARATOR);
    if (StringUtils.hasLength(SUFFIX_PATH)) {
      bundle.append(SUFFIX_PATH);
    }
    bundle.append(fileName);
    return this.getString(bundle.toString(), key, locale);
  }

  /**
   * This method returns localised string for <code>localeStr</code>. It pass the file name w.r.t to
   * template name.
   *
   * @param key key for localised string
   * @param templateName templateName used as folder in bundle path
   * @param localeStr locale
   * @return Localised string
   */
  public String getString(final String key, final String templateName, final String localeStr) {

    LocalisationVerify.verify(key, LocalisationError.INPUT_INVALID, "Key");
    LocalisationVerify.verify(templateName, LocalisationError.INPUT_INVALID, "templateName");
    return this.getString(
        key,
        templateName,
        Optional.ofNullable(this.localisationConfig)
            .map(LocalisationConfig::getFileName)
            .orElse(TemplateToFileMapping.getFileNameFromTemplateName(templateName)),
        localeStr);
  }

  /**
   * This method returns localised string for <code>localeStr</code>. It pass the default <code>
   * Template Name</code> to the method<code>getString( key, defaultTemplateName,
   * localeStr)</code>
   *
   * @param key key for localised string
   * @param localeStr locale
   * @return Localised string
   */
  public String getString(final String key, final String localeStr) {
    LocalisationVerify.verify(key, LocalisationError.INPUT_INVALID, "Key");
    return this.getString(
        key,
        Optional.ofNullable(this.localisationConfig)
            .map(LocalisationConfig::getFolderName)
            .orElse(TemplateToFileMapping.DEFAULT.getTemplateName()),
        localeStr);
  }

  /**
   * This method returns localised string for default locale defined in <code>DEFAULT_LOCALE</code>
   * constants. It calls <code>getString(key,DEFAULT_LOCALE)</code>
   *
   * @param key key for localised string
   * @return
   */
  public String getString(final String key) {
    return this.getString(key, DEFAULT_LOCALE);
  }

  /**
   * This method return the Locale. If localeStr is null then default locale will be returned.
   *
   * @param localeStr locale Default value is <code>Default Locale</code>
   * @return
   */
  private Locale getLocale(String localeStr) {
    if (StringUtils.isEmpty(localeStr)) {
      // get default string here
      localeStr = DEFAULT_LOCALE;
    }
    return LocalisationUtility.getLocale(localeStr);
  }

  /**
   * This method return the ResourceBundle using Custom ResourceBundle.Control.
   *
   * @param bundle bundle path
   * @param locale locale
   * @return instance of ResourceBundle or null if <code>MissingResourceException</code> occurs
   */
  public ResourceBundle getBundle(final String bundle, final Locale locale) {
    try {
      return ResourceBundle.getBundle(bundle, locale, this.localizedResourceBundle);
    } catch (final MissingResourceException missingResourceException) {
      return null;
    }
  }
}

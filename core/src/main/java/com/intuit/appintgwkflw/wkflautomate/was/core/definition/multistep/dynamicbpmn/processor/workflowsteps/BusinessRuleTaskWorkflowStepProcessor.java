package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.workflowsteps;

import com.amazonaws.util.CollectionUtils;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.DynamicBpmnUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity.DynamicBpmnWorkflowStepProcessorMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import org.camunda.bpm.model.bpmn.impl.instance.BusinessRuleTaskImpl;
import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.springframework.stereotype.Component;

/**
 * This class is responsible for creating a BusinessRuleTask element in the Dynamic Bpmn Model and
 * adding to the AbstractFlowNodeBuilder.
 *
 * <AUTHOR>
 */
@Component
@NoArgsConstructor
public class BusinessRuleTaskWorkflowStepProcessor implements DynamicBpmnWorkflowStepProcessor {

  private static final String elementType = "elementType";
  private static final String to = "_to_";

  @Override
  public StepTypeEnum getStepType() {
    return StepTypeEnum.CONDITION;
  }

  @Override
  public void processDynamicBpmnStep(
      DynamicBpmnWorkflowStepProcessorMetaData dynamicBpmnWorkflowStepProcessorMetaData) {

    WorkflowStep workflowStep = dynamicBpmnWorkflowStepProcessorMetaData.getWorkflowStep();
    String workflowStepId = workflowStep.getId().getLocalId();

    WorkflowStep parentWorkflowStep =
        dynamicBpmnWorkflowStepProcessorMetaData.getParentWorkflowStep();

    // if parent workflow step is ConditionStep then do not create new node in BPMN, do nothing
    if (!isNewBusinessRuleTaskElementNeeded(parentWorkflowStep)) {
      return;
    }

    String effectiveParentId =
        dynamicBpmnWorkflowStepProcessorMetaData.getEffectiveParentIdMap().get(workflowStepId);

    Map<String, String> dynamicActivityIdMap = dynamicBpmnWorkflowStepProcessorMetaData.getDynamicActivityIdMap();
    String dynamicWorkflowStepId = dynamicActivityIdMap.get(workflowStepId);

    String sequenceFlowId = new StringBuilder(BpmnComponentType.SEQUENCE_FLOW.toString()).append(to)
        .append(dynamicWorkflowStepId).toString();

    BusinessRuleTaskImpl baseTemplateBusinessRuleTaskImpl =
        dynamicBpmnWorkflowStepProcessorMetaData
            .getBaseTemplateBpmnModelInstance()
            .getModelElementById(dynamicWorkflowStepId);

    // if we cannot find a match by id then fetch element using its type
    if (Objects.isNull(baseTemplateBusinessRuleTaskImpl)) {
      Collection<BusinessRuleTask> businessRuleTasks = dynamicBpmnWorkflowStepProcessorMetaData
              .getBaseTemplateBpmnModelInstance().getModelElementsByType(BusinessRuleTask.class);

      if (!CollectionUtils.isNullOrEmpty(businessRuleTasks)){
        baseTemplateBusinessRuleTaskImpl = (BusinessRuleTaskImpl) businessRuleTasks.stream().findFirst().get();
      }
    }

    Map<String, String> extensionElementCamundaPropertyMap =
        BpmnProcessorUtil.getMapOfCamundaProperties(
            baseTemplateBusinessRuleTaskImpl.getExtensionElements());

    // return if the element type is not explicit
    if (!DynamicBpmnUtil.isElementExplicit(extensionElementCamundaPropertyMap.get(elementType))) {
      return;
    }

    dynamicBpmnWorkflowStepProcessorMetaData
        .getFlowNodeBuilder()
        .moveToNode(dynamicActivityIdMap.get(effectiveParentId))
        .sequenceFlowId(sequenceFlowId)
        .businessRuleTask(dynamicWorkflowStepId)
        .name(baseTemplateBusinessRuleTaskImpl.getName())
        .camundaType(baseTemplateBusinessRuleTaskImpl.getCamundaType())
        .camundaTopic(baseTemplateBusinessRuleTaskImpl.getCamundaTopic());
  }

  /**
   * This method returns the dynamic activity id of the workflow step for BusinessRuleTask elements
   * present in the map. The map contains the workflowStepId as key and dynamic activity id as
   * value.
   *
   * @param dynamicActivityIdMap        - Map<String, String>
   * @param effectiveParentWorkflowStep - WorkflowStep
   */
  @Override
  public String getDynamicActivityId(Map<String, String> dynamicActivityIdMap,
      WorkflowStep effectiveParentWorkflowStep) {
    String prefix = BpmnComponentType.BUSINESS_RULE_TASK.getName();

    int businessRuleTaskCounter =
        dynamicActivityIdMap.values().stream()
            .filter(value -> value.startsWith(prefix)).collect(Collectors.toSet()).size();

    if (String.valueOf(businessRuleTaskCounter).equals(WorkflowConstants.ZERO)
        || isNewBusinessRuleTaskElementNeeded(effectiveParentWorkflowStep)) {
      businessRuleTaskCounter++;
    }

    return new StringBuilder(prefix).append(WorkflowConstants.HYPHEN)
        .append(businessRuleTaskCounter).toString();
  }

  private boolean isNewBusinessRuleTaskElementNeeded(WorkflowStep effectiveParentWorkflowStep) {
    // if parent workflow step is null or is an action block then create a new Business Rule task element
    if (Objects.isNull(effectiveParentWorkflowStep) || MultiStepUtil.isActionStep(
        effectiveParentWorkflowStep)) {
      return true;
    }
    return false;
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.event.gc;

import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.GCRetryableEventsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.event.gc.config.GCConfig;
import com.intuit.guranteedconsumer.commons.core.Enum.Status;
import com.intuit.guranteedconsumer.commons.core.model.GCInbox;
import com.intuit.guranteedconsumer.commons.domain.api.GCService;
import com.intuit.guranteedconsumer.commons.domain.model.ErroredEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.MessageHeaders;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

@ExtendWith(MockitoExtension.class)
public class GCAdapterTest {

    @Mock
    private GCService gcService;

    @Mock
    private GCConfig gcConfig;

    @InjectMocks
    private GCAdapter gcAdapter;

    @Mock
    private GCRetryableEventsRepository gcRetryableEventsRepository;


    private MessageHeaders messageHeaders;
    private HashMap<String, Object> meta;
    private String payload;
    private UUID eventId;

    @BeforeEach
    public void setUp() {
        messageHeaders = new MessageHeaders(new HashMap<>());
        meta = new HashMap<>();
        payload = "testPayload";
        eventId = UUID.randomUUID();
    }

    @Test
    public void testRecordEvent() {
        when(gcService.recordInboxEntry(messageHeaders, payload, meta, Status.ERROR, null)).thenReturn(eventId);

        UUID result = gcAdapter.recordEvent(messageHeaders, meta, payload, Status.ERROR, null);

        assertEquals(eventId, result);
        verify(gcService, times(1)).recordInboxEntry(messageHeaders, payload, meta, Status.ERROR, null);
    }

    @Test
    public void testUpdateEvent() {
        gcAdapter.updateEvent(eventId, Status.SUCCESS, null);

        verify(gcService, times(1)).updateEvent(eventId, Status.SUCCESS, null);
    }

    @Test
    public void testRecordFailedEvent() {
        when(gcService.recordFailedEventEntry(eventId, messageHeaders, payload, meta, null)).thenReturn(eventId);

        UUID result = gcAdapter.recordFailedEvent(eventId, messageHeaders, meta, payload, null);

        assertEquals(eventId, result);
        verify(gcService, times(1)).recordFailedEventEntry(eventId, messageHeaders, payload, meta, null);
    }

    @Test
    public void testUpdateInboxRecordAndCreateFailedEvent() {
        when(gcService.recordFailedEventEntry(eventId, messageHeaders, payload, meta, null)).thenReturn(eventId);

        UUID result = gcAdapter.updateInboxRecordAndCreateFailedEvent(eventId, messageHeaders, meta, payload, null);

        assertEquals(eventId, result);
        verify(gcService, times(1)).recordFailedEventEntry(eventId, messageHeaders, payload, meta, null);
        verify(gcService, times(1)).updateEvent(any(), any(), any());
    }

    @Test
    public void testGetErroredEvents() {
        List<ErroredEvent> erroredEvents = List.of(new ErroredEvent());
        when(gcService.getErroredEvents(anyInt(), anyInt())).thenReturn(erroredEvents);
        when(gcConfig.getUnmodifiedSinceSeconds()).thenReturn(3600);
        when(gcConfig.getMaxRecordsToBeFetched()).thenReturn(10);

        List<ErroredEvent> result = gcAdapter.getErroredEvents();

        assertEquals(erroredEvents, result);
        verify(gcService, times(1)).getErroredEvents(3600, 10);
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrame() {
        List<UUID> recordIds = Arrays.asList(UUID.randomUUID(), UUID.randomUUID());
        int updatedRecords = 2;
        when(gcRetryableEventsRepository.updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name())).thenReturn(updatedRecords);

        int result = gcAdapter.retryFailedEventsWithinTimeFrame(recordIds);
        assertEquals(updatedRecords, result);
        verify(gcRetryableEventsRepository, times(1)).updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name());
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameEmptyList() {
        List<UUID> recordIds = Arrays.asList();
        int updatedRecords = 0;
        when(gcRetryableEventsRepository.updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name())).thenReturn(updatedRecords);

        int result = gcAdapter.retryFailedEventsWithinTimeFrame(recordIds);
        assertEquals(updatedRecords, result);
        verify(gcRetryableEventsRepository, times(1)).updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name());
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameException() {
        List<UUID> recordIds = Arrays.asList(UUID.randomUUID(), UUID.randomUUID());
        when(gcRetryableEventsRepository.updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name())).thenThrow(new RuntimeException("Database error"));

        try {
            gcAdapter.retryFailedEventsWithinTimeFrame(recordIds);
        } catch (RuntimeException e) {
            assertEquals("Database error", e.getMessage());
        }
        verify(gcRetryableEventsRepository, times(1)).updateRetryCountAndStatus(recordIds, 0, Status.ERROR.name());
    }

    @Test
    public void testFetchEvent() {
        UUID recordId = UUID.randomUUID();
        GCInbox event = new GCInbox();
        when(gcRetryableEventsRepository.findById(recordId)).thenReturn(Optional.of(event));

        GCInbox result = gcAdapter.fetchEvent(recordId);
        assertEquals(event, result);
        verify(gcRetryableEventsRepository, times(1)).findById(recordId);
    }

    @Test
    public void testFetchEventNotFound() {
        UUID recordId = UUID.randomUUID();
        when(gcRetryableEventsRepository.findById(recordId)).thenReturn(Optional.empty());

        GCInbox result = gcAdapter.fetchEvent(recordId);
        assertNull(result);
        verify(gcRetryableEventsRepository, times(1)).findById(recordId);
    }

    @Test
    public void testFetchEventException() {
        UUID recordId = UUID.randomUUID();
        when(gcRetryableEventsRepository.findById(recordId)).thenThrow(new RuntimeException("Database error"));

        try {
            gcAdapter.fetchEvent(recordId);
        } catch (RuntimeException e) {
            assertEquals("Database error", e.getMessage());
        }
        verify(gcRetryableEventsRepository, times(1)).findById(recordId);
    }

    @Test
    public void testGetFailedRecordIdsByEntityTypeAndTimeFrame() {
        String entityType = "SCHEDULING";
        LocalDateTime startTime = LocalDateTime.of(2023, 10, 1, 12, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 10, 1, 12, 0, 0);
        List<UUID> expectedRecordIds = Arrays.asList(UUID.randomUUID(), UUID.randomUUID());

        when(gcConfig.getMaxRetries()).thenReturn(3);
        when(gcRetryableEventsRepository.findRecordIdsByEntityTypeAndTimeFrame(
                eq(entityType), eq(4), eq(startTime), eq(endTime)))
                .thenReturn(expectedRecordIds);

        List<UUID> result = gcAdapter.getFailedRecordIdsByEntityTypeAndTimeFrame(entityType, startTime, endTime);

        assertEquals(expectedRecordIds, result);
        verify(gcRetryableEventsRepository, times(1))
                .findRecordIdsByEntityTypeAndTimeFrame(eq(entityType), eq(4), eq(startTime), eq(endTime));
    }

    @Test
    public void testGetFailedRecordIdsByEntityTypeAndTimeFrameEmpty() {
        String entityType = "TRIGGER";
        LocalDateTime startTime = LocalDateTime.of(2023, 10, 1, 12, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 10, 1, 12, 0, 0);
        List<UUID> expectedRecordIds = Arrays.asList();

        when(gcConfig.getMaxRetries()).thenReturn(3);
        when(gcRetryableEventsRepository.findRecordIdsByEntityTypeAndTimeFrame(
                eq(entityType), eq(4), eq(startTime), eq(endTime)))
                .thenReturn(expectedRecordIds);

        List<UUID> result = gcAdapter.getFailedRecordIdsByEntityTypeAndTimeFrame(entityType, startTime, endTime);

        assertEquals(expectedRecordIds, result);
        verify(gcRetryableEventsRepository, times(1))
                .findRecordIdsByEntityTypeAndTimeFrame(eq(entityType), eq(4), eq(startTime), eq(endTime));
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameWithNullList() {
        List<UUID> recordIds = null;

        try {
            gcAdapter.retryFailedEventsWithinTimeFrame(recordIds);
        } catch (NullPointerException e) {
            assertEquals("recordIds is marked non-null but is null", e.getMessage());
        }
        verify(gcRetryableEventsRepository, times(0)).updateRetryCountAndStatus(anyList(), anyInt(), anyString());
    }

    @Test
    public void testFetchEventWithNullId() {
        UUID recordId = null;

        assertNull(gcAdapter.fetchEvent(recordId));

        verify(gcRetryableEventsRepository, times(1)).findById(any());
    }

    @Test
    public void testGetFailedRecordIdsByEntityTypeAndTimeFrameWithNullEntityType() {
        String entityType = null;
        LocalDateTime startTime = LocalDateTime.of(2023, 10, 1, 12, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 10, 1, 12, 0, 0);

        try {
            gcAdapter.getFailedRecordIdsByEntityTypeAndTimeFrame(entityType, startTime, endTime);
        } catch (NullPointerException e) {
            assertEquals("entityType is marked non-null but is null", e.getMessage());
        }
        verify(gcRetryableEventsRepository, times(0))
                .findRecordIdsByEntityTypeAndTimeFrame(anyString(), anyInt(), any(), any());
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameWithEntityType() {
        String entityType = "testEntity";
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        LocalDateTime endTime = LocalDateTime.now();
        List<UUID> expectedIds = Arrays.asList(UUID.randomUUID(), UUID.randomUUID());

        when(gcRetryableEventsRepository.updateRetryCountAndStatus(entityType, 0, startTime, endTime))
                .thenReturn(expectedIds);

        List<UUID> result = gcAdapter.retryFailedEventsWithinTimeFrame(entityType, startTime, endTime);

        assertEquals(expectedIds, result);
        verify(gcRetryableEventsRepository, times(1))
                .updateRetryCountAndStatus(entityType, 0, startTime, endTime);
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameEmptyListWithEntityType() {
        String entityType = "testEntity";
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        LocalDateTime endTime = LocalDateTime.now();
        List<UUID> expectedIds = Arrays.asList();

        when(gcRetryableEventsRepository.updateRetryCountAndStatus(entityType, 0, startTime, endTime))
                .thenReturn(expectedIds);

        List<UUID> result = gcAdapter.retryFailedEventsWithinTimeFrame(entityType, startTime, endTime);

        assertEquals(expectedIds, result);
        verify(gcRetryableEventsRepository, times(1))
                .updateRetryCountAndStatus(entityType, 0, startTime, endTime);
    }

    @Test
    public void testRetryFailedEventsWithinTimeFrameExceptionWithEntityType() {
        String entityType = "testEntity";
        LocalDateTime startTime = LocalDateTime.now().minusDays(1);
        LocalDateTime endTime = LocalDateTime.now();

        when(gcRetryableEventsRepository.updateRetryCountAndStatus(entityType, 0, startTime, endTime))
                .thenThrow(new RuntimeException("Database error"));

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            gcAdapter.retryFailedEventsWithinTimeFrame(entityType, startTime, endTime);
        });

        assertEquals("Database error", exception.getMessage());
        verify(gcRetryableEventsRepository, times(1))
                .updateRetryCountAndStatus(entityType, 0, startTime, endTime);
    }
}

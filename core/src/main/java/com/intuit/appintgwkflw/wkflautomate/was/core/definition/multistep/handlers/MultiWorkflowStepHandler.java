package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * This interface contains methods to process the workflow step using create/update definition
 * payload
 *
 * <AUTHOR>
 */
public interface MultiWorkflowStepHandler {

  /**
   * This function is used to process the workflow step
   *
   * @param bpmnElementId        the activity id of the bpmn element that is getting processed
   * @param workflowStep         current workflow step of create/update definition payload that is
   *                             getting processed
   * @param definitionInstance
   * @param activityIds          the list of outgoing activityIds of all the actions at which the
   *                             dmn
   * @param visitedWorkflowSteps
   * @return
   */
  Map<String, String> processWorkflowStep(
      String bpmnElementId,
      WorkflowStep workflowStep,
      DefinitionInstance definitionInstance,
      List<String> activityIds,
      Set<String> visitedWorkflowSteps);
}

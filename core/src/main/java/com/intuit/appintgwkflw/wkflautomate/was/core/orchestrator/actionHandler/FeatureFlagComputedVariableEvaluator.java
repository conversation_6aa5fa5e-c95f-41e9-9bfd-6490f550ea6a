package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ComputedVariableConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ComputedVariableType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Contains the implementation of the feature flag computed variable evaluator
 */
@Component
@AllArgsConstructor
public class FeatureFlagComputedVariableEvaluator extends ComputedVariableEvaluator {

  private FeatureFlagManager featureFlagManager;
  private WASContextHandler contextHandler;

  @Override
  public ComputedVariableType getComputedVariableType() {
    return ComputedVariableType.FEATURE_FLAG;
  }

  @Override
  public String evaluateValue(
      WorkerActionRequest workerActionRequest,
      Map.Entry<String, HandlerDetails.ParameterDetails> parameterDetailsEntry) {

    if (Objects.isNull(parameterDetailsEntry.getValue())
        || Objects.isNull(parameterDetailsEntry.getValue().getComputedVariableDetails())
        || Objects.isNull(
            parameterDetailsEntry
                .getValue()
                .getComputedVariableDetails()
                .get(ComputedVariableConstants.FEATURE_FLAG_NAME))) {
      return BooleanUtils.FALSE;
    }

    return Boolean.toString(
        featureFlagManager.getBoolean(
            parameterDetailsEntry
                .getValue()
                .getComputedVariableDetails()
                .get(ComputedVariableConstants.FEATURE_FLAG_NAME),
            false,
            workerActionRequest.getDefinitionKey(),
            Long.valueOf(contextHandler.get(WASContextEnums.OWNER_ID))));
  }
}

package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Getter
@Setter
@RefreshScope
@Configuration
@ConfigurationProperties("circuitbreaker")
public class WASCircuitBreakerConfiguration {
    private boolean enabled;
    private Map<CircuitBreakerActionType, WASCircuitBreakerRegistryConfig> instances;
}


package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.DefinitionTestConstants.DEF_ID;
import static com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper.getGlobalId;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.MultiStepConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomConfigV2;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfigFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.MigratedConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.OldCustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.definitions.InputParameter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import lombok.SneakyThrows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.util.Assert;

@RunWith(MockitoJUnitRunner.class)
public class MultiStepDefinitionTransformerTest {

  private MultiStepDefinitionTransformer multiStepDefinitionTransformer;

  Definition definition;

  DefinitionDetails definitionDetails;

  @Mock
  TemplateDetailsRepository templateDetailsRepository;
  public static String DICTIONARY_PATH = "schema/testData/dictionary.yaml";
  public static String YAML_KEY = "templateConfig";

  @Mock
  MultiStepConfig multiStepConfig;

  private CustomWorkflowConfig customWorkflowConfig;

  TemplateDetails templateDetails;

  @Mock
  DefinitionDetailsRepository definitionDetailsRepository;

  Map<String, WorkflowTemplate> workflowTemplateMap = new HashMap<>();
  ;

  @Before
  @SneakyThrows
  public void init() {
    String ownerId = "1234";
    OldCustomWorkflowConfig oldCustomWorkflowConfig =
        new ObjectMapper(new YAMLFactory())
            .readValue(
                TestHelper.readResourceAsString(DICTIONARY_PATH),
                new TypeReference<Map<String, OldCustomWorkflowConfig>>() {})
            .get(YAML_KEY);
    oldCustomWorkflowConfig.afterPropertiesSet();
    CustomConfigV2 customConfigV2 = new CustomConfigV2();
    CustomWorkflowConfig customWorkflowConfig = new CustomWorkflowConfig();
    customWorkflowConfig.setCustomWorkflowConfigFactory(
        new CustomWorkflowConfigFactory(
            oldCustomWorkflowConfig, customConfigV2, new MigratedConfig(Set.of(), Set.of())));
    customWorkflowConfig.setOldConfig(oldCustomWorkflowConfig);
    customWorkflowConfig.getCustomWorkflowConfigFactory().afterPropertiesSet();
    this.customWorkflowConfig = customWorkflowConfig;
    multiStepDefinitionTransformer = new MultiStepDefinitionTransformer(customWorkflowConfig,
        multiStepConfig, templateDetailsRepository, definitionDetailsRepository);
    definition = TestHelper.mockCustomWorkflowDefinition(RecordType.INVOICE.getRecordType(),
        CustomWorkflowType.APPROVAL.getActionKey());
    Action createTaskAction =
        new Action()
            .id(getGlobalId("createTask"))
            .parameter(new InputParameter().parameterName("Assignee").fieldValue("1234"));
    definition.getWorkflowSteps(0).setActions(1, new ActionMapper().action(createTaskAction));
    definition.setId(getGlobalId(DEF_ID));
    templateDetails = TestHelper.mockTemplateDetailsObject();
    templateDetails.setVersion(3);
    definitionDetails = TestHelper.mockDefinitionDetails(definition, templateDetails,
        TestHelper.mockAuthorization(ownerId));
    WorkflowTemplate workflowTemplate = new WorkflowTemplate();
    workflowTemplate.setSingleStepVersion(1);
    workflowTemplateMap.put("customApproval", workflowTemplate);
    Mockito.when(multiStepConfig.getWorkflowTemplates()).thenReturn(workflowTemplateMap);
  }

  @Test
  public void testCustomToMultiConditionMigration() {
    Mockito.when(templateDetailsRepository.findByTemplateId(any())).thenReturn(templateDetails);
    multiStepDefinitionTransformer.transformPayload(definition, definitionDetails,
        templateDetails.getId());
    Assert.isTrue(MultiStepUtil.isMultiCondition(definition));
  }

  @Test
  public void testMultiConditionToCustomRollback() {
    templateDetails.setVersion(1);
    Mockito.when(templateDetailsRepository.findByTemplateId(any())).thenReturn(templateDetails);
    Mockito.when(definitionDetailsRepository
            .findByOwnerIdAndDefinitionKeyAndInternalStatusIsNull(anyLong(), anyString()))
        .thenReturn(definitionDetails);
    multiStepDefinitionTransformer.transformPayload(definition, definitionDetails,
        templateDetails.getId());
    Assert.isTrue(!MultiStepUtil.isMultiCondition(definition));
  }

}

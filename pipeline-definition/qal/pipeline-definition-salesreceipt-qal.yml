ipf:
  runtime:
    id: was-event-in-runtime-salesreceipt
    batch:
      groupingMessageHeaders:
    basePackages: > #TODO: Update based on runtime
      com.intuit.dataexchange,
      com.intuit.fdp.enrichment,
      com.intuit.pavedroad,
      com.intuit.v4.fdpenrichment.ipf.runtime,
      com.intuit.platform.fdp.connectivity.mailchimp.processors,
      com.intuit.platform.fdp.connectivity.idxconnectivitytools.rawhost.processors,
      com.intuit.platform.fdp.enrichment.ckidentityresolution.processors,
      com.intuit.fdp.docmgmt.des.processor,
      com.intuit.fdp.docmgmt.doc.processor
  source:
    kafka:
      ratelimit:
        concurrency: 50
        durationInMillis: 50
      commit:
        autoCommit: false
        maxDeferredCommits: 50000
        commitInterval: 70
  replay:
    topic: was-event-in-replay #TODO: create separate replay topic here
    enabled: true
    max: 3
  shutdown:
    wait-time-ms: 30000

  # Kafka configuration
  kafka:
    clusterProperties:
      - name: default
        consumerproperties:
          bootstrap-servers: eventbus-kafka.pl-data-lake-e2e.a.intuit.com:19701,eventbus-kafka.pl-data-lake-e2e.a.intuit.com:19801,eventbus-kafka.pl-data-lake-e2e.a.intuit.com:19901
          '[group.id]': was-event-in-processor-group-qal-salesreceipt
          '[auto.offset.reset]': latest
          '[auto.commit.interval]': 1000
          '[heartbeat.interval]': 1000
          '[security.protocol]': SSL
          '[ssl.truststore.location]': /app/resources/kafkatruststore.jks
          '[ssl.truststore.password]': '{secret}idps:/ipf/eventbus/default/secret'
          '[key.deserializer]': org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
          '[value.deserializer]': org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
          '[spring.deserializer.key.delegate.class]': org.apache.kafka.common.serialization.StringDeserializer
          '[spring.deserializer.value.delegate.class]': com.intuit.dataexchange.enrichment.pipelineframework.connectors.source.kafka.deserialization.KafkaDeserializer
      - name: qboawscluster
        consumerproperties:
          bootstrap-servers: b-2.eventbusmsk-sbseg-e2e.hz7ee1.c3.kafka.us-west-2.amazonaws.com:9094,b-3.eventbusmsk-sbseg-e2e.hz7ee1.c3.kafka.us-west-2.amazonaws.com:9094,b-5.eventbusmsk-sbseg-e2e.hz7ee1.c3.kafka.us-west-2.amazonaws.com:9094
          '[group.id]': was-event-in-processor-group-qal-salesreceipt
          '[auto.offset.reset]': latest
          '[auto.commit.interval]': 1000
          '[heartbeat.interval.ms]': 1000
          '[security.protocol]': SSL
          '[ssl.path]': '{secret}idps:/ipf/eventbus/qbocluster/e2e-usw2/certificate.jks'
          '[ssl.keystore.password]': '{secret}idps:/ipf/eventbus/qbocluster/e2e-usw2/certificate.password'
          '[ssl.keystore.location]': /app/resources/kafkaWestCertificate.jks
          '[key.deserializer]': org.apache.kafka.common.serialization.StringDeserializer
          '[value.deserializer]': org.apache.kafka.common.serialization.StringDeserializer
      - name: qboawsclusterEast
        consumerproperties:
          bootstrap-servers: b-3.eventbusmsksbsege2e.n0pbv7.c7.kafka.us-east-2.amazonaws.com:9094,b-4.eventbusmsksbsege2e.n0pbv7.c7.kafka.us-east-2.amazonaws.com:9094,b-6.eventbusmsksbsege2e.n0pbv7.c7.kafka.us-east-2.amazonaws.com:9094
          '[group.id]': was-event-in-processor-group-qal-salesreceipt
          '[auto.offset.reset]': latest
          '[auto.commit.interval]': 1000
          '[heartbeat.interval.ms]': 1000
          '[security.protocol]': SSL
          '[ssl.path]': '{secret}idps:/ipf/eventbus/qbocluster/e2e-use2/kafkaEastCertificate.jks'
          '[ssl.keystore.password]': '{secret}idps:/ipf/eventbus/qbocluster/e2e-use2/certificate.password'
          '[ssl.keystore.location]': /app/resources/kafkaEastCertificate.jks
          '[key.deserializer]': org.apache.kafka.common.serialization.StringDeserializer
          '[value.deserializer]': org.apache.kafka.common.serialization.StringDeserializer

# 1. Pipeline definitions
pipelines:
  definitions:
    - id: 1
      name: SalesReceiptEventPipeline
      version: 1.0.0
      eventName: SalesReceiptEvent
      eventNamespace: com.intuit.dataexchange.bespokeintegrations.workflowautomation.v1.events
      eventVersion: v1
      source:
        kafkaSource:
          - topic: e2e.commerce.invoicingandsales.invoicingworkflows.salesreceipt.v1 #iedm topic
            clusterName: qboawscluster
            replay: false
            transformConfig:
              enabled: true
              islScriptPath: isl/e2e/salesreceipt-to-idx-schema.isl
      sink:
        disabled: true
      processorLinks: pipeline-graph/qal/was-pipeline-graph-salesreceipt.json
      entityModels:
        - groupId: com.intuit.dataexchange.bespokeintegrations.workflowautomation.v1
          artifactId: entities
          version: 1.0.23
        - groupId: com.intuit.dataexchange.bespokeintegrations.workflowautomation.v1
          artifactId: events
          version: 1.0.26

      runtimes:
        - id: was-event-in-runtime-salesreceipt
          environment: qal
      errorHandler:
        defaultErrorHandlingBehavior: skip

processors:
  definitions:
    - artifactId: event-validator
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.event.validator.EventValidationProcessor
      stopProcessingOnError: true
    - artifactId: event-filter
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.event.filter.EventFilterProcessor
      stopProcessingOnError: true
    - artifactId: workflow-fanout
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.event.fanout.EventFanoutProcessor
      stopProcessingOnError: true
    - artifactId: enrichment-segregation
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.enrichment.segregation.EnrichmentSegregationProcessor
      stopProcessingOnError: true
    - artifactId: event-enrichment
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.event.enrichment.EventEnrichmentProcessor
      stopProcessingOnError: true
    - artifactId: dedupe-persist
      groupId: com.intuit.appintgwkflw-wkflautomate.event-processor
      version: 1.0.11
      classpath: com.intuit.dataexchange.enrichment.appintgwkflw.dedupe.persist.DedupePersistProcessor
      stopProcessingOnError: true

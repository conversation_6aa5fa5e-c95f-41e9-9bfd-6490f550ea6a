package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class CorrelateAllMessageAsync {

  private String messageName;
  private ProcessInstanceQuery processInstanceQuery;

  @Getter
  @Builder
  public static class ProcessInstanceQuery {
    private String businessKey;
    private List<ProcessVariable> variables;
  }

  @Getter
  @Builder
  public static class ProcessVariable {
    private String name;
    private final String operator = WorkflowConstants.EQ.toLowerCase();
    private String value;
  }

}

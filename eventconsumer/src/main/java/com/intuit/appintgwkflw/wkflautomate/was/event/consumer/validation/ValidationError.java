package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.validation;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum's for validation error messages
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ValidationError {
  MISSING_INTUIT_TID_HEADER("Event headers do not have intuit tid for entityId=%s"),
  MISSING_IDEMPOTENCY_KEY_HEADER("Event headers do not have idempotency key"),
  MISSING_OFFERING_ID("Event headers do not have offering id");

  private final String errorMessage;

}
package com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config;

import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.constants.DataAccessConstants;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Injects the multi offering data source for all the offering that will be onboarded for a
 * particular Swimlane.
 */
@Configuration
@EnableTransactionManagement
@EnableConfigurationProperties({JpaProperties.class})
@EnableJpaRepositories(
    basePackages = DataAccessConstants.READER_PACKAGE_SCAN,
    entityManagerFactoryRef = DataAccessConstants.OFFERING_AWARE_EM_READER,
    transactionManagerRef = DataAccessConstants.OFFERING_AWARE_TM_READER)
public class ReaderDataSourceConfiguration extends BaseDataSourceConfiguration{

  @Autowired private JpaProperties jpaProperties;
  @Autowired OfferingConfig offeringConfig;

  @Autowired DataSourceProperties defaultDataSourceProperties;
  @Autowired ReaderDataSourceConfig readerDataSourceConfig;

  /**
   * Iterates through the offerings listed in the application config file and creates
   * the @HikariDataSource
   *
   * @return Map of offering data sources that are read from the application config file
   */
  @Bean(name = DataAccessConstants.DATA_SOURCES_READER)
  public Map<Object, Object> getDataSources() {

    WorkflowLogger.logInfo("Creating datasource map for reader");
    return offeringConfig
            .getDownstreamServices()
            .stream()
            .collect(
                    Collectors.toMap(
                            downstream -> downstream.getOfferingId(),
                            downstream -> {
                              DataSource dataSource =
                                      downstream.getDataSource() == null
                                              ? new DataSource()
                                              : downstream.getDataSource();

                              return this.getHikariDataSource(dataSource);
                            }));
  }

  /**
   * Creates new Hikari data source for the given tenant config. This method uses the tenant
   * specific URL/User/PWD for the data source if given. Else, it will pick the data source
   * properties from spring.datasource property
   *
   * @return Datasource
   */
  public HikariDataSource getHikariDataSource(DataSource dataSourceProperty) {
    HikariDataSource dataSource = new HikariDataSource();
    getHikariConfigReader().copyStateTo(dataSource);

    dataSource.setJdbcUrl(getReaderDataSourceUrl(dataSourceProperty));
    dataSource.setUsername(
            resolveProperty(
                    dataSourceProperty.getUsername(), defaultDataSourceProperties.determineUsername()));
    dataSource.setPassword(
            resolveProperty(
                    dataSourceProperty.getPassword(), defaultDataSourceProperties.determinePassword()));
    return dataSource;
  }

  /**
   * Choose the writer/reader datasource url based on the config
   * @param dataSource
   * @return datasource url
   */
  private String getReaderDataSourceUrl(DataSource dataSource){
    String url = resolveProperty(dataSource.getUrl(), defaultDataSourceProperties.getUrl());
    if (readerDataSourceConfig.isEnabled()){
      url = resolveProperty(dataSource.getReaderUrl(), readerDataSourceConfig.getUrl());
    }
    return Optional.ofNullable(url).orElseThrow();

  }

  /**
   * Configures all the data source with hikari pool properties
   *
   * @return HikariConfig
   */
  @ConfigurationProperties(prefix = "spring.datasource.hikari")
  @Bean(name = "hikariConfigReader")
  public HikariConfig getHikariConfigReader() {
    return new HikariConfig();
  }

  /**
   * Injects Multi offering data source which wraps the map of data sources for different offerings
   *
   * @param dataSources Map of datasources for different offerings that are defined in the
   *     application config.
   * @return Wrapper Routing data source
   */
  @Bean(name = DataAccessConstants.ROUTING_DATASOURCE_READER)
  @DependsOn(DataAccessConstants.DATA_SOURCES_READER)
  public javax.sql.DataSource dataSource(
          @Qualifier(DataAccessConstants.DATA_SOURCES_READER) Map<Object, Object> dataSources) {
    AbstractRoutingDataSource tenantRoutingDataSource = new OfferingAwareReaderDataSource();
    tenantRoutingDataSource.setTargetDataSources(dataSources);
    Object defaultDataSource = null;
    if (offeringConfig.getDefaultOffering() != null
            && !dataSources.isEmpty()
            && dataSources.get(offeringConfig.getDefaultOffering()) != null) {
      defaultDataSource = dataSources.get(offeringConfig.getDefaultOffering());
    } else {
      /*
       An empty data source config will create the default data source from the spring data
       source property
      */
      defaultDataSource = getHikariDataSource(new DataSource());
    }
    tenantRoutingDataSource.setDefaultTargetDataSource(defaultDataSource);
    tenantRoutingDataSource.afterPropertiesSet();
    return tenantRoutingDataSource;
  }

  /**
   * Entity manager is defined to make hibernate DAOs identifiable.
   * Entity manages sets scan on package
   *
   * @param tenantRoutingDataSource
   * @return
   */
  @Bean(name = DataAccessConstants.OFFERING_AWARE_EM_READER)
  public LocalContainerEntityManagerFactoryBean multiEntityManagerReader(
          @Qualifier(DataAccessConstants.ROUTING_DATASOURCE_READER)
          javax.sql.DataSource tenantRoutingDataSource) {
    LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
    em.setDataSource(tenantRoutingDataSource);
    em.setPackagesToScan(DataAccessConstants.PACKAGE_SCAN_ENTITY);
    em.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
    em.setJpaPropertyMap(jpaProperties(jpaProperties));
    /*
     * When there are more than 1 entityManager beans,
     * it is good practise to provide the desired entity manager name in @Persistentcontext annotation
     * So we are setting persistence unit name below
     */
    em.setPersistenceUnitName(DataAccessConstants.OFFERING_AWARE_EM_READER);
    return em;
  }

  /**
   * Every transaction manager is linked to en entity manager.
   * It is responsible for managing all transactions.
   * @param entityManagerFactoryBean - entity manager to which transaction manager should be linked
   */

  @Bean(name = DataAccessConstants.OFFERING_AWARE_TM_READER)
  public PlatformTransactionManager multiTransactionManagerReader(
          @Qualifier(DataAccessConstants.OFFERING_AWARE_EM_READER)
          LocalContainerEntityManagerFactoryBean entityManagerFactoryBean) {
    return new JpaTransactionManager(entityManagerFactoryBean.getObject());
  }

  @Data
  @AllArgsConstructor
  private class OfferingIdDataSource {
    private Object tenantId;
    private HikariDataSource dataSource;
  }
}

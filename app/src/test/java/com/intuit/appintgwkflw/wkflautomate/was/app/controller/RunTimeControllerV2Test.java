package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

import java.util.HashMap;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.RunTimeService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.history.TriggerNowRequest;

@RunWith(SpringRunner.class)
@Import(RunTimeControllerV2.class)
public class RunTimeControllerV2Test {

  @Autowired
  private RunTimeControllerV2 runTimeControllerV2;

  @MockBean
  private RunTimeService runTimeService;
  
  @Test
  public void whenTrigger_thenSuccess() {

    final WorkflowGenericResponse expectedSaveResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedSaveResponse).when(runTimeService).processTriggerMessageV2(any());

    final WorkflowGenericResponse triggerResponse = runTimeControllerV2
        .trigger(new HashMap<>());
    Assert.assertEquals(expectedSaveResponse, triggerResponse);
  }

  @Test
  public void whenEvaluateAndTrigger_andEvaluationTrue_thenSuccess() {

    final WorkflowGenericResponse expectedSaveResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedSaveResponse).when(runTimeService).processEvaluateAndTriggerMessage(any());

    final WorkflowGenericResponse triggerResponse = runTimeControllerV2
        .evaluateAndTrigger(new HashMap<>());
    Assert.assertEquals(expectedSaveResponse, triggerResponse);
  }
  
  @Test
  public void whenTriggerNow_thenSuccess() {

    final WorkflowGenericResponse expectedSaveResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    TriggerNowRequest triggerNowRequest = new TriggerNowRequest("defId");
    doReturn(expectedSaveResponse).when(runTimeService).runNow(triggerNowRequest);

    final WorkflowGenericResponse triggerResponse = runTimeControllerV2
        .runNow(triggerNowRequest);
    
    Assert.assertEquals(expectedSaveResponse, triggerResponse);
  }

  @Test
  public void whenEvaluateAndTrigger_andEvaluationFalse_thenSuccess() {

    final WorkflowGenericResponse expectedSaveResponse = WorkflowGenericResponse.builder().status(
        ResponseStatus.SUCCESS).build();
    doReturn(expectedSaveResponse).when(runTimeService).processEvaluateAndTriggerMessage(any());

    final WorkflowGenericResponse triggerResponse = runTimeControllerV2
        .evaluateAndTrigger(new HashMap<>());
    Assert.assertEquals(expectedSaveResponse, triggerResponse);
  }
}
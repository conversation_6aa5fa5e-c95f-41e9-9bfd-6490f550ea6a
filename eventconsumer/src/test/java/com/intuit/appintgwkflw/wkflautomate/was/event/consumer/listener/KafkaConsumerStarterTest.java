package com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener;

import java.util.ArrayList;
import java.util.Collection;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.config.KafkaListenerEndpointRegistry;
import org.springframework.kafka.listener.MessageListenerContainer;

import static org.mockito.Mockito.*;

public class KafkaConsumerStarterTest {

  @Mock private KafkaListenerEndpointRegistry registry;

  @InjectMocks private KafkaConsumerStarter starter;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void testStartAll() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Collection<MessageListenerContainer> containers = new ArrayList<>();
    containers.add(mockedContainer);
    Mockito.when(registry.getListenerContainers()).thenReturn(containers);
    starter.startAllConsumers();
    Mockito.verify(mockedContainer).start();
  }

  @Test
  public void testStartAllRunningConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(mockedContainer.isRunning()).thenReturn(true);
    Collection<MessageListenerContainer> containers = new ArrayList<>();
    containers.add(mockedContainer);
    Mockito.when(registry.getListenerContainers()).thenReturn(containers);
    starter.startAllConsumers();
    Mockito.verify(mockedContainer, times(0)).start();
  }

  @Test
  public void testStopAll() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(mockedContainer.isRunning()).thenReturn(true);
    Collection<MessageListenerContainer> containers = new ArrayList<>();
    containers.add(mockedContainer);
    Mockito.when(registry.getListenerContainers()).thenReturn(containers);
    starter.stopAllConsumers();
    Mockito.verify(mockedContainer).stop();
  }

  @Test
  public void testStopAllStoppedContainer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(mockedContainer.isRunning()).thenReturn(false);
    Collection<MessageListenerContainer> containers = new ArrayList<>();
    containers.add(mockedContainer);
    Mockito.when(registry.getListenerContainers()).thenReturn(containers);
    starter.stopAllConsumers();
    Mockito.verify(mockedContainer, times(0)).stop();
  }

  @Test
  public void testStartAllNullContainer() {
    KafkaConsumerStarter consumerStarter = Mockito.mock(KafkaConsumerStarter.class);
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    doCallRealMethod().when(consumerStarter).startAllConsumers();
    consumerStarter.startAllConsumers();
    Mockito.verify(mockedContainer, times(0)).start();
  }

  @Test
  public void testStopAllNullContainer() {
    KafkaConsumerStarter consumerStarter = Mockito.mock(KafkaConsumerStarter.class);
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    doCallRealMethod().when(consumerStarter).stopAllConsumers();

    consumerStarter.stopAllConsumers();
    Mockito.verify(mockedContainer, times(0)).stop();
  }

  @Test
  public void testPausingNonExistentConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(null);
    starter.pauseConsumer("test");
    Mockito.verify(mockedContainer, times(0)).isRunning();
  }

  @Test
  public void testPausingNonRunningExistentConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(mockedContainer);
    Mockito.when(mockedContainer.isRunning()).thenReturn(false);
    starter.pauseConsumer("test");
    Mockito.verify(mockedContainer, times(0)).pause();
  }

  @Test
  public void testPausingConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(mockedContainer);
    Mockito.when(mockedContainer.isRunning()).thenReturn(true);
    starter.pauseConsumer("test");
    Mockito.verify(mockedContainer, times(1)).isRunning();
    Mockito.verify(mockedContainer).pause();
  }

  @Test
  public void testResumingNonExistentConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(null);
    starter.resumeConsumer("test");
    Mockito.verify(mockedContainer, times(0)).isRunning();
  }

  @Test
  public void testResumingNonRunningExistentConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(mockedContainer);
    Mockito.when(mockedContainer.isRunning()).thenReturn(false);
    starter.resumeConsumer("test");
    Mockito.verify(mockedContainer, times(0)).resume();
  }

  @Test
  public void testResumingConsumer() {
    MessageListenerContainer mockedContainer = Mockito.mock(MessageListenerContainer.class);
    Mockito.when(registry.getListenerContainer(Mockito.anyString())).thenReturn(mockedContainer);
    Mockito.when(mockedContainer.isRunning()).thenReturn(true);
    starter.resumeConsumer("test");
    Mockito.verify(mockedContainer).resume();
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>workflow-automation-service-aggregator</artifactId>
    <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
    <version>1.1.20</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>was-provideradapter</artifactId>
  <packaging>jar</packaging>

  <dependencies>
  <dependency>
    <groupId>org.projectlombok</groupId>
    <artifactId>lombok</artifactId>
  </dependency>
    <dependency>
      <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
      <artifactId>was-entity</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.intuit.appintgwkflw.wkflautomate</groupId>
      <artifactId>was-common</artifactId>
      <version>${project.version}</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>



</project>

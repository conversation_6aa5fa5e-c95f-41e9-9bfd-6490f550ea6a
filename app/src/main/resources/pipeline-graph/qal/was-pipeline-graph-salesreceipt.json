{"nodes": [{"id": "1", "groupId": "com.intuit.appintgwkflw-wkflautomate.event-processor", "artifactId": "event-validator", "version": "1.0.11", "classpath": "com.intuit.dataexchange.enrichment.appintgwkflw.event.validator.EventValidationProcessor", "type": "PROCESSOR", "runtimeId": "was-event-in-runtime-salesreceipt"}, {"id": "2", "groupId": "com.intuit.appintgwkflw-wkflautomate.event-processor", "artifactId": "event-filter", "version": "1.0.11", "classpath": "com.intuit.dataexchange.enrichment.appintgwkflw.event.filter.EventFilterProcessor", "type": "PROCESSOR", "runtimeId": "was-event-in-runtime-salesreceipt"}, {"id": "3", "groupId": "com.intuit.appintgwkflw-wkflautomate.event-processor", "artifactId": "workflow-fanout", "version": "1.0.11", "classpath": "com.intuit.dataexchange.enrichment.appintgwkflw.event.fanout.EventFanoutProcessor", "type": "PROCESSOR", "runtimeId": "was-event-in-runtime-salesreceipt"}], "edges": [{"source": "1", "target": "2"}, {"source": "2", "target": "3"}]}
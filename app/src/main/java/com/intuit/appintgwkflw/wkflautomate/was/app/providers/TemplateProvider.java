package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.CommonExecutor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.providers.Provides;
import com.intuit.v4.providers.SingleResult;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.TemplateOperations;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Provides(Template.class)
@Component
@AllArgsConstructor
public class TemplateProvider extends WASBaseProvider<Template>
    implements TemplateOperations<RequestContext> {

  private TemplateService templateService;

  @Override
  protected SingleResult<Template> wasReadOne(
      RequestContext context, @SuppressWarnings("rawtypes") GlobalId id, QueryHelper query) {
    verifyRealmId(context.getAuthorization());
    try {
      List<Query.PreparedQuery> subQueries = query.getPreparedQuery().getSubQueries();
      boolean isMultiStepPayload = MultiStepUtil.isTemplateDataPassed(subQueries);
      WorkflowLogger.logInfo("step=wasReadOnePrecannedTemplate isMultiStepPayload=%s", isMultiStepPayload);
      return new SingleResult<>(templateService.fetchTemplate(id.getLocalId(), id, isMultiStepPayload));
    } catch (IOException e) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Exception occurred for Template Id=%s", id.getLocalId())
                  .stackTrace(e)
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(this.getClass().getSimpleName()));
      return handleServiceException(
          new WorkflowGeneralException(WorkflowError.PROVIDER_PARSING_EXCEPTION));
    }
  }

  @Override
  protected ListResult<Template> wasReadList(RequestContext context, QueryHelper query) {
    verifyRealmId(context.getAuthorization());
    try {
      return templateService.readAllTemplates(context.getAuthorization(), query);

    } catch (IOException ex) {
      WorkflowLogger.error(
          () ->
              WorkflowLoggerRequest.builder()
                  .message("Exception occurred in Read All Template Request")
                  .downstreamComponentName(DownstreamComponentName.WAS)
                  .className(this.getClass().getSimpleName()));
      return handleServiceExceptionList(
          new WorkflowGeneralException(WorkflowError.PROVIDER_PARSING_EXCEPTION));
    }
  }

  @Override
  protected SingleResult<Template> wasWriteOne(RequestContext context, Template entity) {
    return handleUnSupportedOperation(WorkflowError.PROVIDER_WRITE_UNSUPPORTED.getErrorMessage());
  }

  /**
   * Read template metadata for a source and record type. For do it yourself usecase (Build your own
   * workflow), all conditions and actions for a record type will be returned to the caller.
   *
   * @param context
   * @param queryHelper
   * @return
   */
  @Override
  public ListResult<Template> readTemplateMetadata(
      RequestContext context, QueryHelper queryHelper) {
    populateMDCHeaders(context);
    Optional<Object> recordMetadataInput = queryHelper.getArg(WorkflowConstants.BY);
    if (recordMetadataInput.isPresent()) {
      HashMap<String, String> metaDataInput = (HashMap<String, String>) recordMetadataInput.get();
      String source = metaDataInput.get(WorkflowConstants.SOURCE);
      String recordType = metaDataInput.get(WorkflowConstants.RECORD_TYPE);
      // if templateData is received as part of GraphQl query only then we will return the templateData object
      List<Query.PreparedQuery> subQueries = queryHelper.getPreparedQuery().getSubQueries();
      boolean isMultiStepPayload = MultiStepUtil.isTemplateDataPassed(subQueries);
      WorkflowLogger.logInfo("step=wasReadOneCustomTemplate isMultiStepPayload=%s", isMultiStepPayload);
      // Added actionKey support. Now record metadata conditions can be fetched on the basis of
      // RecordType + ActionKey Combination.
      String actionKey = metaDataInput.get(WorkflowConstants.ACTION_KEY);
      if (StringUtils.isNotBlank(source) && StringUtils.isNotBlank(recordType)) {
        return CommonExecutor.execute(
            () -> {
              verifyRealmId(context.getAuthorization());
              return ListResult.of(
                  Arrays.asList(
                      templateService.getTemplateMetadataForRecordType(
                          source, recordType, actionKey, isMultiStepPayload)));
            },
            "Exception occurred while performing read template metadata. Source="
                + source
                + " RecordType="
                + recordType,
            e -> handleServiceExceptionList((WorkflowGeneralException) e),
            e1 -> handleSystemErrorList(WorkflowError.INTERNAL_EXCEPTION.getErrorMessage()));
      }
    }
    return ListResult.of(new ArrayList<Template>());
  }
}

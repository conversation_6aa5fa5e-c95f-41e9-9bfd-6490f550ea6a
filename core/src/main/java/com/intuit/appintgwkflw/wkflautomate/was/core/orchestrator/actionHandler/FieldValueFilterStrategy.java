package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsFetchStrategy.FIND_FIRST;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsFetchStrategy.MULTI_LEVEL;

import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.ParameterDetailsFetchStrategy;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * Can be used to extract values from field value array dependant on multiple
 * strategies, like HIERARCHICAL &PARALLEL
 *
 * <AUTHOR>
 */
@Component
public class FieldValueFilterStrategy {

  /**
   * Used to extract values from field value array dependant on multiple strategies
   * @param workerActionRequest
   * @param parameterDetailsEntry
   * @return
   */
  public String fetchFieldValueFromParameterDetail(
      WorkerActionRequest workerActionRequest,
      Map.Entry<String, ParameterDetails> parameterDetailsEntry
  ) {
    ParameterDetailsFetchStrategy parameterDetailsFetchStrategy =
        getFetchStrategy(workerActionRequest, parameterDetailsEntry.getValue());
    switch (parameterDetailsFetchStrategy) {

      case MULTI_LEVEL:
        // In this strategy, it checks if the fieldValues in parameters details are more than one which means this
        // is the case of multi level for eg. multi level approval createTask will have more than one assignee id
        // however task has to be created for a particular single assignee as per the variable provided in process scope.
        // therefore if the variable is present in process takes higher preference else fall back to FIND_FIRST.
        if (parameterDetailsEntry.getValue().getFieldValue().size() > 1 &&
                workerActionRequest.getInputVariables().containsKey(parameterDetailsEntry.getKey())) {
          return workerActionRequest.getInputVariables().get(parameterDetailsEntry.getKey());
        }
      case FIND_FIRST:
        return String.join(",", parameterDetailsEntry.getValue().getFieldValue());
      default:
        return StringUtils.EMPTY;
    }
  }


  /**
   * Used to find field strategy to adapt to fetch the parameter value.
   * @param workerActionRequest
   * @param parameterDetails
   * @return
   */
  private ParameterDetailsFetchStrategy getFetchStrategy(
      WorkerActionRequest workerActionRequest,
      ParameterDetails parameterDetails
  ) {

    Map<String, String> extensionAttributes = Optional.ofNullable(workerActionRequest.getExtensionProperties()).orElse(Collections.emptyMap());

    // Checks if Multi Level property is set to true on the activity level Extension Attributes
    return Boolean.valueOf(
            Optional.ofNullable(extensionAttributes.get(WorkFlowVariables.MULTI_LEVEL.getName()))
                    .orElse(Boolean.FALSE.toString())) ? MULTI_LEVEL : FIND_FIRST;
  }
}

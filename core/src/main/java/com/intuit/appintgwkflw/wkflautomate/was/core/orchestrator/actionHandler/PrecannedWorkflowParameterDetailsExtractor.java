package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.actionHandler;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SingleDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.worker.WorkerActionRequest;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PrecannedWorkflowParameterDetailsExtractor
    implements AppConnectParameterDetailExtractor {

  private final ProcessDetailsRepoService processDetailsRepoService;

  /**
   * As part of single definition, parameter details are filled using the user placeholder values.
   * In case of precanned-workflows, all parameter-details coming from the template are filled using
   * the user placeholder values.
   *
   * @param workerActionRequest
   * @return ParameterDetailsMap
   */
  @Override
  @Metric(name = MetricName.PRECANNED_WORKFLOW_PARAMETER_DETAILS_EXTRACTOR, type = Type.APPLICATION_METRIC)
  public Optional<Map<String, HandlerDetails.ParameterDetails>> getParameterDetails(
      WorkerActionRequest workerActionRequest) {

//  In case of larger Input/Output parameter details, they will be stored in Extension elements.
//  Camunda throws error when Input/Output variable size is GT 4000 characters.
    Map<String, String> parameterDetailsInputMap = workerActionRequest.getInputVariables().get(
        WorkFlowVariables.PARAMETERS_KEY.getName()) == null ? workerActionRequest.getExtensionProperties()
        : workerActionRequest.getInputVariables();

    Optional<Map<String, HandlerDetails.ParameterDetails>> parameterDetailsMap =
            SchemaDecoder.getParametersForHandler(parameterDetailsInputMap);
    //In case of notification being sent through was, there won't be ParameterDetails. So in that case we are returning Optional.empty()
    if (parameterDetailsMap == null || !parameterDetailsMap.isPresent()){
      return Optional.empty();
    }
    Optional<DefinitionDetails> definitionDetails =
        processDetailsRepoService.findByProcessIdWithoutDefinitionData(
            workerActionRequest.getProcessInstanceId());

    WorkflowVerfiy.verify(!definitionDetails.isPresent(), WorkflowError.DEFINITION_NOT_FOUND);

    String actionId = workerActionRequest.getActivityId();
    JSONObject userVariablesActionData =
        SingleDefinitionUtil.getActionPlaceholderValuePreCanned(definitionDetails.get(), actionId);

//  If User Variables doesn't contain any value, then no placeholder substitution is needed.
//  This also takes care of Non User Defined variables.
    if (Objects.nonNull(userVariablesActionData)) {
      SingleDefinitionUtil.fillParameterDetails(parameterDetailsMap.get(), userVariablesActionData);
    }

    return parameterDetailsMap;
  }
}

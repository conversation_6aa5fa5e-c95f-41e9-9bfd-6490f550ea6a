package com.intuit.appintgwkflw.wkflautomate.was.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * Voc jira config
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "jira")
public class JiraConfig {

  private String slackToken;
  private String jiraEndPoint;
  private String username;
  private String password;

}

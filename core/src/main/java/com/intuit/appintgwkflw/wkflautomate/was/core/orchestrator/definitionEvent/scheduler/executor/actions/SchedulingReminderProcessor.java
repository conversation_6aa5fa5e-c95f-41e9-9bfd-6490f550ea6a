package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.executor.actions;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.IXPManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.AuthDetailsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.DuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool.RecordDuzzitPaginationConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher.RecordListFetcher;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.publisher.TriggerEventPublisher;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.*;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.AuthDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.SchedulerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkFlowVariables;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CustomWorkflowType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.SchedulerAction;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.WorkflowNameEnum;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.EntityChangeIdentifier;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.MetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.trigger.Trigger;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.listner.EventScheduleMessageData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.foundation.workflow.scheduling.Execution;
import com.intuit.v4.Authorization;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaProperty;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.*;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ID_KEY;

/**
 * The `SchedulingReminderScheduleActionProcessor` class is responsible for processing scheduled actions
 * related to reminders. It implements the `WorkflowScheduleActionProcessor` interface and provides
 * methods to handle the processing of scheduling events, fetching records, and publishing trigger events.
 * This class also manages context handling and request parameter preparation for different workflows.
 *
 * It supports both single-step and multi-condition workflows, and integrates with various services
 * such as `AuthDetailsService`, `EventScheduleHelper`, and `TriggerEventPublisher`.
 *
 * @author: ssayyaparaj
 */
@Component
@AllArgsConstructor
public class SchedulingReminderProcessor implements WorkflowScheduleActionProcessor{
    private final WASContextHandler wasContextHandler;
    private final AuthDetailsService authDetailsService;
    private final EventScheduleHelper eventScheduleHelper;
    private final DefinitionActivityDetailsRepository definitionActivityDetailsRepository;
    private final DuzzitPaginationConfig paginationConfig;
    private final IXPManager ixpManager;
    private static final String REMINDER_250_NEW_DUZZIT_FF = "qbo-adv-reminder-250-limit";
    private static final String INPUT_PARAMETER_REQUEST_FLOW = "requestFlow";
    private final RecordListFetcher recordListFetcher;
    private final TriggerEventPublisher triggerEventPublisher;

    /**
     * Retrieves the workflow name associated with this processor.
     *
     * @return `WorkflowNameEnum.CUSTOM_SCHEDULED_ACTIONS` indicating the workflow name.
     */
    @Override
    public WorkflowNameEnum getWorkflowName() {
        return WorkflowNameEnum.REMINDER;
    }

    /**
     * Processes the scheduled actions based on the provided scheduler details and event schedule
     * message data. Currently returns an empty map.
     *
     * @param schedulerDetails the details of the scheduler.
     * @param eventScheduleMessageData the data related to the event schedule message.
     * @return an empty map.
     */
    @Override
    public Map<String, String> process(SchedulerDetails schedulerDetails, EventScheduleMessageData eventScheduleMessageData) {
        return new HashMap<String, String>();
    }

    /**
     * Processes the scheduled actions based on the provided definition details and scheduling event.
     * It prepares the transaction entity, triggers the process, and logs the response.
     *
     * @param definitionDetails the details of the definition.
     * @param schedulingEvent the scheduling event data.
     * @return a map containing the entity ID and change type if successful, otherwise an empty map.
     * @throws Exception if an error occurs during processing.
     */
    @Override
    public Map<String, String> process(
            final DefinitionDetails definitionDetails, final Execution schedulingEvent) {
        if (!eventScheduleHelper.isEventSchedulingEnabledForWorkflow(
                definitionDetails.getTemplateDetails().getTemplateName(),
                String.valueOf(definitionDetails.getOwnerId()))) {
            EventingLoggerUtil.logInfo(
                    "Processing CustomReminder Action. step=skip_schedule_event, definitionId=%s, referenceId=%s",
                    this.getClass().getSimpleName(),
                    definitionDetails.getDefinitionId(),
                    schedulingEvent.getReferenceId());
            return Collections.emptyMap();
        }
        List<String> definitionKeyAndAction = List.of(schedulingEvent.getReferenceId().split(COLON));
        SchedulerAction action = SchedulerAction.fromAction(definitionKeyAndAction.get(1));

        // add all the keys to the context for calling downstream services
        addKeysToContextHandler(definitionDetails, schedulingEvent);

        // get connectorId for the given schedulerAction and recordType
        String connectorId = getConnectorId(
                action,
                definitionDetails.getRecordType());

        // process differently for multiCondition workflows
        if (MultiStepUtil.isMultiConditionWorkflow(definitionDetails)) {
            return processForMultiConditionWorkflows(definitionDetails, schedulingEvent, action);
        }

        // resume the flow for single step custom workflows
        Map<String, String> requestParameters = getRequestParameters(definitionDetails, null);
        EventingLoggerUtil.logInfo(
                "Processing CustomReminder Action. step=start_record_fetch, definitionId=%s, action=%s",
                this.getClass().getSimpleName(),
                definitionDetails.getDefinitionId(),
                action);

        return fetchRecordsAndPublishTriggerEvent(definitionDetails, connectorId, requestParameters,
                schedulingEvent, null, action);

    }

    /**
     * This method processes the multiCondition workflows
     *
     * @param definitionDetails
     * @param schedulingEvent
     * @param action
     * @return
     */
    private Map<String, String> processForMultiConditionWorkflows(
            DefinitionDetails definitionDetails,
            Execution schedulingEvent,
            SchedulerAction action) {
        // get all the callActivities for the given definitionId
        Optional<List<DefinitionActivityDetail>> optionalDefinitionActivityDetailList =
                definitionActivityDetailsRepository.findAllByDefinitionDetails_DefinitionId(
                        definitionDetails.getDefinitionId());
        if (optionalDefinitionActivityDetailList.isEmpty()) {
            WorkflowLogger.logError("No call activities found for definitionId=%s action=%s",
                    this.getClass().getSimpleName(),
                    definitionDetails.getDefinitionId(),
                    action);
            return Collections.emptyMap();
        }

        List<DefinitionActivityDetail> calledActivitiesList = optionalDefinitionActivityDetailList.get();
        Map<String,String> txnTriggeredDetails = new HashMap<>();

        // enrich conditions for all call activities and fetch the records from appConnect

        calledActivitiesList.stream()
                .filter(callActivity -> Objects.nonNull(callActivity.getUserAttributes()))
                .forEach(
                        callActivity -> {

                            // get the request parameters for the given activity
                            Map<String, String> requestParameters = getRequestParameters(definitionDetails,
                                    callActivity.getUserAttributes());

                            // fetch records from customStart and customWait duzzits
                            Map<String,String> triggerDetails = fetchRecordsAndPublishTriggerEvent(
                                    definitionDetails,
                                    getConnectorId(action,
                                            definitionDetails.getRecordType()),
                                    requestParameters,
                                    schedulingEvent,
                                    callActivity.getActivityId(),
                                    action);

                            txnTriggeredDetails.putAll(triggerDetails);

                            // if the action is customReminder_customWait and recurrence is applicable, then
                            // fetch records from CustomRecur duzzit
                            if (SchedulerAction.CUSTOM_REMINDER_CUSTOM_WAIT.equals(
                                    action) &&
                                    requestParameters.containsKey(WorkflowConstants.IS_RECURRING_ENABLED) &&
                                    Boolean.TRUE.toString()
                                            .equals(requestParameters.get(WorkflowConstants.IS_RECURRING_ENABLED))) {

                                /**
                                 * Recur action is associated with Wait and is instantiated On Demand.
                                 * Making a copy of schedulerDetails for recur from Wait Action.
                                 **/
                                Map<String,String> recurrenceTriggerDetails = fetchRecordsAndPublishTriggerEvent(
                                        definitionDetails,
                                        getConnectorId(SchedulerAction.CUSTOM_REMINDER_CUSTOM_RECUR,
                                                definitionDetails.getRecordType()),
                                        requestParameters,
                                        schedulingEvent,
                                        callActivity.getActivityId(),
                                        SchedulerAction.CUSTOM_REMINDER_CUSTOM_RECUR);
                                txnTriggeredDetails.putAll(recurrenceTriggerDetails);
                            }
                        });
        return txnTriggeredDetails;
    }

    /**
     * Method to fetch eligible records from AppConnect and publish trigger events to kafka
     * @param definitionDetails
     * @param connectorId
     * @param requestParameters
     * @return
     * @return
     */
    private Map<String, String> fetchRecordsAndPublishTriggerEvent(
            DefinitionDetails definitionDetails,
            String connectorId,
            Map<String, String> requestParameters,
            Execution schedulingEvent,
            String activityId,
            SchedulerAction action) {

        StringBuilder idempotencyKey = new StringBuilder(schedulingEvent.getExecutionId());

        // Making one unique call to AC per event per activityId per scheduleAction in case of Multi-Condition
        if (StringUtils.isNotBlank(activityId)) {
            idempotencyKey.append(UNDERSCORE)
                    .append(activityId)
                    .append(UNDERSCORE)
                    .append(action);
        }

        wasContextHandler.addKey(WASContextEnums.IDEMPOTENCY_KEY, idempotencyKey.toString());

        RecordQueryConnectorResponse recordQueryConnectorResponse =
                recordListFetcher.fetchRecords(
                        new RecordQueryConnectorRequest(connectorId, requestParameters));
        if (CollectionUtils.isEmpty(recordQueryConnectorResponse.getRecordList())) {
            EventingLoggerUtil.logInfo(
                    "Records not found for definitionId=%s, action=%s",
                    this.getClass().getSimpleName(),
                    definitionDetails.getDefinitionId(),
                    action);
            return Collections.emptyMap();
        }
        EventingLoggerUtil.logInfo(
                "Processing CustomReminder Action. step=complete_record_fetch , definitionId=%s, action=%s, recordCount=%s",
                this.getClass().getSimpleName(),
                definitionDetails.getDefinitionId(),
                action,
                recordQueryConnectorResponse.getRecordList().size());

        Map<String,String> responseMap = new HashMap<>();

        recordQueryConnectorResponse
                .getRecordList()
                .forEach(
                        record -> {
                            addTriggerKeyToContextHandler(schedulingEvent, record.get(ID_KEY), activityId);
                            triggerEventPublisher.publishTriggerEvent(
                                    prepareTriggerEvent(definitionDetails, record, action));
                            responseMap.put(record.get(ID_KEY), action.getEntityChangeType());
                        });

        return responseMap;
    }


    /**
     * This method adds all keys to the context that the downstream services require.
     *
     * @param definitionDetails the details of the definition.
     * @param schedulingEvent the scheduling event data.
     */
    private void addKeysToContextHandler(
            DefinitionDetails definitionDetails, Execution schedulingEvent) {
        wasContextHandler.addKey(
                WASContextEnums.OWNER_ID, String.valueOf(definitionDetails.getOwnerId()));
        wasContextHandler.addKey(
                WASContextEnums.OFFERING_ID,
                definitionDetails.getTemplateDetails().getOfferingId());
        /**
         * setting intuit_userid using LastModifiedByUserId from definition. this will be used for
         * populating intuit_userid in process variables. Previously the value is taken from the
         * authorization but new processes will be triggered using events and authorization will not
         * have the required values
         */
        wasContextHandler.addKey(
                WASContextEnums.INTUIT_USERID,
                String.valueOf(definitionDetails.getModifiedByUserId()));

        AuthDetails authDetails =
                authDetailsService.getAuthDetailsFromRealmId(
                        wasContextHandler.get(WASContextEnums.OWNER_ID));

        Authorization authorization = new Authorization();
        authorization.set(authDetailsService.renewOfflineTicketAndUpdateDB(authDetails));
        wasContextHandler.addKey(WASContextEnums.AUTHORIZATION_HEADER, authorization.toString());
    }

    /**
     * This method returns the connectorId for the given scheduleActions and recordType
     *
     * @param schedulerAction
     * @param recordType
     * @return
     */
    private String getConnectorId(SchedulerAction schedulerAction, RecordType recordType) {
        // ##TODO read from config
        return schedulerAction.getConnectorId(recordType);
    }

    /**
     * This method prepares all the request parameters required for fetching the records.
     *
     * @param definitionDetails the details of the definition.
     * @param callActivityAttributes the attributes of the call activity.
     * @return
     */
    private Map<String, String> getRequestParameters(DefinitionDetails definitionDetails, String callActivityAttributes) {

        Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap =
                MultiStepUtil.isMultiConditionWorkflow(definitionDetails) ?
                        getParameterDetailsMapForMultiConditionWorkflows(callActivityAttributes) :
                        getParameterDetailsMap(definitionDetails);

        Map<String, String> inputs = new HashMap<>();
        addInputParameter(WorkflowConstants.FILTER_RECORD_TYPE, inputs, parameterDetailsMap);
        addInputParameter(WorkflowConstants.FILTER_CONDITION, inputs, parameterDetailsMap);
        addInputParameter(WorkflowConstants.FILTER_CLOSE_TASK_CONDITIONS, inputs, parameterDetailsMap);
        // To stop appconnect scheduler, we are passing headers source so that only ess call serve by duzzits.
        inputs.put(WorkflowConstants.DUZZIT_SOURCE_KEY, WorkflowConstants.DUZZIT_SOURCE_VALUE);
        inputs.put(WorkflowConstants.WORKFLOW_NAME, definitionDetails.getTemplateDetails().getTemplateName());

        inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_PAGE_SIZE,
                String.valueOf(paginationConfig.getRecordsPerPage()));
        inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_MAXIMUM_RESULTS,
                String.valueOf(paginationConfig.getMaxResult()));
        inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_TOTAL_PAGES,
                String.valueOf(paginationConfig.getTotalPages()));
        /**
         * This helps to fetch invoices for companies which get filtered out due to PageLimit = 2, MaxRecords from QBO=250.
         * Override default config with recordBased config.
         */
        if(isOverrideRecordBasedConfig(definitionDetails)){
            RecordDuzzitPaginationConfig recordDuzzitPaginationConfig = paginationConfig.getRecordConfig()
                    .get(definitionDetails.getRecordType());
            inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_PAGE_SIZE,
                    String.valueOf(recordDuzzitPaginationConfig.getRecordsPerPage()));
            inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_MAXIMUM_RESULTS,
                    String.valueOf(recordDuzzitPaginationConfig.getMaxResult()));
            inputs.put(WorkflowConstants.CUSTOM_REMINDER_START_TOTAL_PAGES,
                    String.valueOf(recordDuzzitPaginationConfig.getTotalPages()));
        }
        /**
         * IXP Based input parameter for pagination flow.
         */
        if(ixpManager.getBoolean(REMINDER_250_NEW_DUZZIT_FF,
                String.valueOf(definitionDetails.getOwnerId()))){
            inputs.put(INPUT_PARAMETER_REQUEST_FLOW, REMINDER_250_NEW_DUZZIT_FF);
        }

        // input fields for custom-recur duzzit
        addInputParameter(WorkflowConstants.IS_RECURRING_ENABLED, inputs, parameterDetailsMap);
        addInputParameter(WorkflowConstants.RECUR_FREQUENCY, inputs, parameterDetailsMap);
        addInputParameter(WorkflowConstants.MAX_SCHEDULE_COUNT, inputs, parameterDetailsMap);
        return inputs;
    }

    /**
     * Adds input parameter to MultiValueMap
     *
     * @param inputParameterKey
     * @param inputs
     * @param parameterDetailsMap
     */
    private void addInputParameter(
            String inputParameterKey,
            Map<String, String> inputs,
            Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap) {
        Optional.ofNullable(parameterDetailsMap.get(inputParameterKey))
                .ifPresent(
                        parameterDetails -> {
                            if (CollectionUtils.isNotEmpty(parameterDetails.getFieldValue())) {
                                inputs.put(inputParameterKey, parameterDetails.getFieldValue().get(0));
                            }
                        });
    }

    /**
     * This method retrieves all parameters details defined as startEventElement properties in BPMN.
     * These parameter details contain the conditions that the user specified when creating a
     * definition.
     *
     * @param definitionDetails
     * @return
     */
    private Map<String, HandlerDetails.ParameterDetails> getParameterDetailsMap(
            DefinitionDetails definitionDetails) {
        // get the start element from the definition BPMN.
        // #TODO will start reading from placeholder value once all the customReminder filter values
        // added to placeholder values
        FlowElement startEventElement =
                CustomWorkflowUtil.findStartEventElement(
                        BpmnProcessorUtil.readBPMN(definitionDetails.getDefinitionData()));
        Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();
        // This map contains all the parameterDetails parameter key value in the extension variables for
        // startElement of BPMN.
        Optional<CamundaProperty> propertyMap =
                BpmnProcessorUtil.getCamundaProperty(
                        startEventElement, WorkFlowVariables.PARAMETERS_KEY.getName());
        if (propertyMap.isPresent()) {
            parameterDetailsMap.putAll(
                    ObjectConverter.fromJson(
                            propertyMap.get().getCamundaValue(),
                            new TypeReference<Map<String, HandlerDetails.ParameterDetails>>() {}));
        }
        return parameterDetailsMap;
    }


    /**
     * This method extracts all parameters details from the userAttributes field of the callActivity
     * @param userAttributes String
     * @return
     */
    private Map<String, HandlerDetails.ParameterDetails> getParameterDetailsMapForMultiConditionWorkflows(
            String userAttributes) {

        Map<String, HandlerDetails.ParameterDetails> parameterDetailsMap = new HashMap<>();

        Map<String, Object> userAttributesMap = ObjectConverter.fromJson(userAttributes,
                new TypeReference<Map<String, Object>>() {});

        if (MapUtils.isEmpty(userAttributesMap) || !userAttributesMap.containsKey(WorkflowConstants.PARAMETERS)) {
            return parameterDetailsMap;
        }

        Object parametersMap = userAttributesMap.get(WorkflowConstants.PARAMETERS);

        parameterDetailsMap = ObjectConverter.fromJson(ObjectConverter.toJson(parametersMap),
                new TypeReference<Map<String, HandlerDetails.ParameterDetails>>() {});

        return parameterDetailsMap;
    }

    /**
     * Checks if Record Based Config Provided and IXP is enabled.
     * @param definitionDetails
     * @return
     */
    private boolean isOverrideRecordBasedConfig(DefinitionDetails definitionDetails) {
        return MapUtils.isNotEmpty(paginationConfig.getRecordConfig()) &&
                paginationConfig.getRecordConfig()
                        .containsKey(definitionDetails.getRecordType());
    }

    /**
     * This method add all the keys required for publishing an event to trigger topic
     *
     * @param schedulingEvent
     * @param recordId
     * @param activityId
     */
    private void addTriggerKeyToContextHandler(
            Execution schedulingEvent, String recordId, String activityId) {
        String entityId =
                getEntityId(
                        schedulingEvent.getReferenceId(),
                        recordId);
        wasContextHandler.addKey(WASContextEnums.ENTITY_ID, entityId);

        // Making one unique trigger call per entity per activityId in case of Multi-Condition
        String idempotencyKey =
                StringUtils.isEmpty(activityId) ? entityId : entityId + UNDERSCORE + activityId;
        wasContextHandler.addKey(WASContextEnums.IDEMPOTENCY_KEY, idempotencyKey);
    }

    /**
     * This method prepares the workflowTrigger Event
     *
     * @param definitionDetails
     * @param data
     * @param action
     * @return {@link Trigger}
     */
    private Trigger prepareTriggerEvent(DefinitionDetails definitionDetails, Map<String, String> data, SchedulerAction action) {
        MetaData metaData =
                MetaData.builder()
                        .workflow(CustomWorkflowType.getActionKey(definitionDetails.getTemplateDetails().getTemplateName()))
                        .entityType(definitionDetails.getRecordType().getRecordType())
                        .entityChangeIdentifier(
                                new EntityChangeIdentifier(
                                        action.getEntityChangeType()))
                        .entityId(data.get(ID_KEY))
                        .providerWorkflowId(definitionDetails.getWorkflowId())
                        .blockProcessOnSignalFailure(false)
                        .targetApi(action.getTriggerTargetAPI())
                        .definitionKey(definitionDetails.getDefinitionKey())
                        .build();
        return new Trigger(
                metaData, null, Map.of(definitionDetails.getRecordType().getRecordType(), data));
    }

    /**
     * This method returns the entityId as a combination of messageId, schedulerId, recordId
     *
     * @param referenceId
     * @param recordId
     * @return
     */
    private String getEntityId(String referenceId, String recordId) {
        // this would be helpful in debugging the recordId for a messageId of schedulerId when trigger
        // event consumed by trigger
        // consumer.
        return new StringBuilder()
                .append(referenceId)
                .append(WorkflowConstants.COLON)
                .append(recordId)
                .toString();
    }

}

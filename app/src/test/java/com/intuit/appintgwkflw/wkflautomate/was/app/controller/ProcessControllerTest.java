package com.intuit.appintgwkflw.wkflautomate.was.app.controller;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.impl.ProcessDetailsRepoService;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowProcessesResponse;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;

@RunWith(SpringRunner.class)
@Import(ProcessController.class)
public class ProcessControllerTest {

  @Autowired private ProcessController processController;

  @MockBean private ProcessDetailsRepoService processDetailsRepoService;

  @Test
  public void whenGetProcessDetailsForDefinitionAndOwner_thenSuccess() {
    List<WorkflowProcessResponse> processResponses = new ArrayList<>();
    WorkflowProcessesResponse workflowProcessesResponse =
        new WorkflowProcessesResponse(processResponses);
    doReturn(workflowProcessesResponse)
        .when(processDetailsRepoService)
        .getAllProcessesForDefinition(any());
    final WorkflowGenericResponse fetchProcessesResponse =
        processController.fetchProcessListForDefinition(UUID.randomUUID().toString());
    Assert.assertNotNull(fetchProcessesResponse);
  }

  @Test
  public void whenGetProcessDetailsByProcessId_thenSuccess() {
    WorkflowProcessResponse processResponse = new WorkflowProcessResponse();
    ProcessDetails processDetails = ProcessDetails.builder().processId("pId").build();

    Mockito.when(processDetailsRepoService.getProcessDetails("pId")).thenReturn(processDetails);
    processResponse.setProcessId("pId");
    final WorkflowProcessResponse fetchProcessesResponse =
        processController.getProcessDetails("pId");
    Assert.assertNotNull(fetchProcessesResponse);
  }

  @Test(expected = WorkflowGeneralException.class)
  public void whenGetProcessDetailsByProcessId_thenProcessNotFound() {
    WorkflowProcessResponse processResponse = new WorkflowProcessResponse();
    Mockito.when(processDetailsRepoService.getProcessDetails("pId")).thenReturn(null);
    final WorkflowProcessResponse fetchProcessesResponse =
        processController.getProcessDetails("pId2");
    Assert.assertNotNull(fetchProcessesResponse);
  }
}

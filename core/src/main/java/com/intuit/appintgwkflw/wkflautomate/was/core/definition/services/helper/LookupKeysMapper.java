package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper;

import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.v4.workflows.definitions.LookupKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Lookup are stored as a jsonB string in definitionDetails, as a Map of key and value in DefinitionInstance and as a List of lookupkey in Definition schema
 * This class assists conversion betwen different formats of lookupKeys
 */
@Service
public class LookupKeysMapper{

  /**
   * Converts the json string of lookupKeys to a list of lookupKeys
   * Used for the conversion from DefinitionDetails to Definition
   * "{'envelopeId':'1234','customerId':'1234}" -> [{key:"envelopeId", value:"1234"}, {key:"customerId", value:"1234"}]
   * @param lookupKeys
   * @return List of LookupKeys
   */
  public static Optional<List<LookupKey>> getListOfLookupKeysFromString(String lookupKeys){
    if(StringUtils.isEmpty(lookupKeys))
      return Optional.empty();
    Map<String, String> lookupKeyMap = ObjectConverter.fromJson(lookupKeys, Map.class);
    if(CollectionUtils.isEmpty(lookupKeyMap))
      return Optional.empty();
    List<LookupKey> lookupList = lookupKeyMap.entrySet().stream().map(e -> createLookupKeyInstance(e.getKey(), e.getValue()))
                  .collect(Collectors.toCollection(ArrayList::new));
    return Optional.ofNullable(lookupList);
  }

  /**
   * Converts the list of lookupKey to a Map of key and values
   * Used for conversion from definition to DefinitionInstance
   * [{key:"envelopeId", value:"1234"}, {key:"customerId", value:"1234"} --> {'envelopeId':'1234','customerId':'1234}
   * @param lookupKeyList
   * @return Map of key and value of lookupKeys
   */
  public static Optional<Map<String, String>> getMapOfLookupKeysFromList(List<LookupKey> lookupKeyList){
    if(CollectionUtils.isEmpty(lookupKeyList))
      return Optional.empty();
    Map<String, String> lookupKeyValueMap = lookupKeyList.stream()
        .collect(Collectors.toMap(LookupKey::getKey, LookupKey::getValue));
    return Optional.ofNullable(lookupKeyValueMap);
  }

  /**
   * pseudo constructor for LookupKey which is a part of v4 schema
   */
  private static LookupKey createLookupKeyInstance(String key, String value){
    LookupKey lookupKey = new LookupKey();
    lookupKey.setKey(key);
    lookupKey.setValue(value);
    return lookupKey;
  }
}

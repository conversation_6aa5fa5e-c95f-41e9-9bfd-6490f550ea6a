package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.entity;

import lombok.Builder;
import lombok.Getter;

import java.util.List;

/**
 * This class represents the parent level object in the json string created for comparing dynamic Bpmns.
 * <AUTHOR>
 */
@Getter
@Builder
public class DynamicBpmnAdjacencyListData {

    private String actionKey;
    private List<DynamicBpmnNode> bpmnNodeList;
}

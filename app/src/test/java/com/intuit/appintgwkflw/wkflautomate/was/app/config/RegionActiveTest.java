package com.intuit.appintgwkflw.wkflautomate.was.app.config;

import static org.mockito.Mockito.times;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.listener.KafkaConsumerStarter;
import com.intuit.appintgwkflw.wkflautomate.was.worker.config.WorkerGenerator;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

public class RegionActiveTest {

  @Mock private WorkerGenerator generator;
  @Mock private KafkaConsumerStarter consumerStarter;

  @InjectMocks private RegionConfig regionActive;

  @Before
  public void init() {
    MockitoAnnotations.initMocks(this);
  }

  @Test
  public void regionActiveTrue() {
    regionActive.setActive(true);
    regionActive.onRefreshScopeRefreshed(null);
    Mockito.verify(generator).startWorkers();
  }

  @Test
  public void regionActiveFalse() {
    regionActive.setActive(false);
    regionActive.onRefreshScopeRefreshed(null);
    Mockito.verify(generator).stopWorkers();
  }

  @Test
  public void regionEventConsumptionActiveTrue() {
    regionActive.setConsumerActive(true);
    regionActive.onRefreshScopeRefreshed(null);
    Mockito.verify(consumerStarter).startAllConsumers();
  }

  @Test
  public void regionEventConsumptionActiveFalse() {
    regionActive.setConsumerActive(false);
    regionActive.onRefreshScopeRefreshed(null);
    Mockito.verify(consumerStarter).stopAllConsumers();
  }

  @Test
  public void regionEventConsumptionActiveOnAppStartedTrue() {
    regionActive.setConsumerActive(true);
    regionActive.onAppStarted(null);
    Mockito.verify(consumerStarter).startAllConsumers();
  }

  @Test
  public void regionEventConsumptionActiveOnAppStartedFalse() {
    regionActive.setConsumerActive(false);
    regionActive.onAppStarted(null);
    Mockito.verify(consumerStarter, times(0)).startAllConsumers();
  }

  @Test
  public void regionActiveOnAppStartedTrue() {
    regionActive.setActive(true);
    regionActive.onAppStarted(null);
    Mockito.verify(generator).startWorkers();
  }

  @Test
  public void regionActiveOnAppStartedFalse() {
    regionActive.setActive(false);
    regionActive.onAppStarted(null);
    Mockito.verify(generator, times(0)).startWorkers();
  }
  @Test
  public void stopPollingEventTrue() {
    regionActive.setActive(false);
    regionActive.stopPolling(null);
    Mockito.verify(generator).stopWorkers();
  }
}

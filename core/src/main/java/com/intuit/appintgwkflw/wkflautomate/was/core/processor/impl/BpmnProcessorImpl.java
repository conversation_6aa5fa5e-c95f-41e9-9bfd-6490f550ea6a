package com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl;

import com.google.common.annotations.VisibleForTesting;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLoggerRequest;
import com.intuit.appintgwkflw.wkflautomate.was.core.camunda.template.schema.SchemaDecoder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessingHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.BpmnProcessorTask;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.CamundaElements;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.ConfigurationDefinitionUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.dmn.parser.DMNDataTypeTransformers;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowTemplateConstant;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.BpmnComponentType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DMNSupportedOperator;
import com.intuit.appintgwkflw.wkflautomate.was.entity.logger.DownstreamComponentName;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.CurrentStepDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.ParameterDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.schema.HandlerDetails.TaskDetails;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.async.execution.Task;
import com.intuit.async.execution.impl.RxExecutionChain;
import com.intuit.async.execution.request.State;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.RuleLine;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.Trigger;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStepCondition;
import com.intuit.v4.workflows.definitions.ConditionalParameter;
import com.intuit.v4.workflows.definitions.FieldTypeEnum;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.v4.workflows.definitions.LogicalOperatorsEnum;
import com.intuit.v4.workflows.definitions.NextTypeEnum;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.impl.instance.BusinessRuleTaskImpl;
import org.camunda.bpm.model.bpmn.impl.instance.Incoming;
import org.camunda.bpm.model.bpmn.impl.instance.Outgoing;
import org.camunda.bpm.model.bpmn.impl.instance.SendTaskImpl;
import org.camunda.bpm.model.bpmn.impl.instance.ServiceTaskImpl;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.SubProcess;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.Decision;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.DmnElement;
import org.camunda.bpm.model.dmn.instance.Input;
import org.camunda.bpm.model.dmn.instance.InputEntry;
import org.camunda.bpm.model.dmn.instance.InputExpression;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.camunda.bpm.model.xml.ModelInstance;
import org.camunda.bpm.model.xml.impl.instance.ModelElementInstanceImpl;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import static java.util.Objects.nonNull;

/** <AUTHOR> */
@Service(WorkflowBeansConstants.BPMN_PROCESSOR_IMPL)
@NoArgsConstructor
public class BpmnProcessorImpl<T> implements BpmnProcessor<T> {
  @Autowired private WorkflowGlobalConfiguration workflowGlobalConfiguration;
  @Autowired private ReadCustomDefinitionHandler readCustomDefinitionHandler;
  @Autowired private WASContextHandler wasContextHandler;
  @Autowired private TranslationService translationService;
  @Autowired private TemplateBuilder templateBuilder;
  @Autowired private FeatureFlagManager featureFlagManager;

    /**
     * This function generates the template object with populated workflowsSteps
     * by traversing the bpmn model instance
     *
     * @param definitionInstance definition instance object
     * @param templateId         template id
     * @param isDefinitionRead   flag to determine whether read one flow
     * @return generic type cast as Template
     * @throws IOException
     */
    @Override
    public T processBpmn(
            DefinitionInstance definitionInstance,
            GlobalId templateId,
            boolean isDefinitionRead) throws IOException {
        TemplateDetails templateDetails = definitionInstance.getTemplateDetails();
        BpmnModelInstance bpmnModelInstance = definitionInstance.getBpmnModelInstance();
        List<DmnModelInstance> dmnModelInstanceList = definitionInstance.getDmnModelInstanceList();
        Template template = templateBuilder.buildTemplateDetails(templateId, templateDetails);
        return preProcessingTraversal(bpmnModelInstance, dmnModelInstanceList, template, templateId, isDefinitionRead);
    }

    /**
   * @param map : Map of Bpmn and Related DMN List from Template Details table.
   * @return
   */
  @Override
  public List<Template> processBpmn(Map<TemplateDetails, List<TemplateDetails>> map) {
    State state = new State();
    Map<String, Pair<TemplateDetails, List<TemplateDetails>>> requestMap = new HashMap<>();
    List<String> responseKey = new ArrayList<>();

    List<Task> tasks = new ArrayList<>();
    map.forEach(
        (key, value) -> {
          requestMap.put(key.getTemplateName(), Pair.of(key, value));
          responseKey.add(
              MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_UNDERSCORE, WorkflowConstants.RESPONSE_IDENTIFIER, key.getId()));
          tasks.add(
              new BpmnProcessorTask(
                  key.getTemplateName(),
                  MessageFormat.format(
                      WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_UNDERSCORE, WorkflowConstants.RESPONSE_IDENTIFIER, key.getId()), this));
        });

    state.addValue(WorkflowConstants.TEMPLATE_REQ, requestMap);
    State resp = new RxExecutionChain(state, tasks.toArray(new Task[tasks.size()])).execute();

    Map<String, Object> response = resp.getAll();

    return responseKey.stream()
        .filter(responseKeys -> !ObjectUtils.isEmpty(response.get(responseKeys)))
        .map(templates -> (Template) response.get(templates))
        .collect(Collectors.toList());
  }

  /**
   * @param modelInstance : Object of BPMN Template XML
   * @param dmnModelInstances : List of Objects of associated DMN with a BPMN Template
   * @param template : Template Entity
   * @param globalId : Global ID of the template
   * @return Returns object of type Template.
   */
  private T preProcessingTraversal(
      BpmnModelInstance modelInstance,
      List<DmnModelInstance> dmnModelInstances,
      Template template,
      GlobalId globalId,
      Boolean isDefinitionRead) {
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    // Map of Ids and Camunda Elements (FlowElements) which encapsulates all the properties required
    // for future processing of Action/Events/Triggers
    Map<String, CamundaElements> elementsMap = new HashMap<>();
    AtomicReference<String> startEventId = new AtomicReference<>();

    // There will always be a single process in the bpmn XML containing the startEvent for that
    // process
    processes.forEach(
        process ->
            process
                .getFlowElements()
                .forEach(
                    element -> {
                      if (!(BpmnComponentType.SEQUENCE_FLOW
                          .getName()
                          .equalsIgnoreCase(element.getElementType().getTypeName()))) {
                        // Add the flow element in the map if not sequenceFlow type

                        // If elements inside the sub-process are part of the stepDetails object
                        // then the individual elements needs to be parsed so that they can be
                        // converted to an equivalent v4 representation later on  in the code.
                        if (BpmnComponentType.SUB_PROCESS
                            .getName()
                            .equalsIgnoreCase(element.getElementType().getTypeName())) {
                          prepareSubProcessMetaData(modelInstance, element.getId(), elementsMap);
                        }
                        camundaElementsMapBuilder(element, elementsMap);

                        if (BpmnComponentType.START_EVENT
                            .getName()
                            .equalsIgnoreCase(element.getElementType().getTypeName())) {
                          startEventId.set(element.getId());
                        }
                      }
                    }));

    return (T)
        prepareResponse(
            modelInstance,
            dmnModelInstances,
            elementsMap,
            startEventId.get(),
            template,
            globalId,
            isDefinitionRead);
  }

  /**
   * This method prepares data for sub-process' individual elements.
   *
   * @param modelInstance
   * @param subProcessId
   * @param elementsMap
   */
  private void prepareSubProcessMetaData(
      ModelInstance modelInstance, String subProcessId, Map<String, CamundaElements> elementsMap) {

    SubProcess subProcesses = modelInstance.getModelElementById(subProcessId);
    subProcesses
        .getFlowElements()
        .forEach(
            element -> {
              if (!(BpmnComponentType.SEQUENCE_FLOW
                  .getName()
                  .equalsIgnoreCase(element.getElementType().getTypeName()))) {
                camundaElementsMapBuilder(element, elementsMap);
              }
            });
  }

  /**
   * @param modelInstance : Object of BPMN Template XML
   * @param dmnModelInstances : List of Objects of associated DMN with a BPMN Template
   * @param elementsMap : Map of Ids and Camunda Elements (FlowElements) which encapsulates all the
   *     properties required for future processing of Action/Events/Triggers.
   * @param startEventId : Id of the Start Event. This contains all the stepDetails (i.e.
   *     Information about WorkFlow Steps(Logical Unit of Trigger, Condition and Actions)). This is
   *     required to read the configuration provided by the developer and kick-start the processing
   *     of Workflow Entities.
   * @param template
   * @param globalId
   * @param isDefinitionRead - Whether the flow is exercised during read definition
   * @return : Returns object of type Template.
   */
  private Template prepareResponse(
      BpmnModelInstance modelInstance,
      List<DmnModelInstance> dmnModelInstances,
      Map<String, CamundaElements> elementsMap,
      String startEventId,
      Template template,
      GlobalId globalId,
      Boolean isDefinitionRead) {
    // Get step details from start event element
    Map<String, Set<String>> stepDetails =
        getStepDetails(
            ConfigurationDefinitionUtil.getConfigurationsAsMap(
                elementsMap.get(startEventId).getFlowElement()));

    /** This list will contain the list of WorkFlow Steps for a particular template. */
    List<WorkflowStep> workflowSteps = new ArrayList<>();
    /** For Maintaining Sequence count inside Java 8 lambda */
    AtomicInteger stepCount = new AtomicInteger(1);

    /**
     * id : Start State of WorkFlowStep (Basically Trigger of some sort) value : Value set of
     * [Trigger Condition Action] next elements in the Workflow Step need not be in the correct
     * order
     */
    stepDetails.forEach(
        (id, value) -> {
          WorkflowLogger.logInfo("Processing for WorkflowStepId=%s", id);
          WorkflowStep workflowStep = new WorkflowStep();
          Trigger trigger = new Trigger();
          WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
          /** Setting Global Id for Each Workflow Step */
          List<WorkflowStep.ActionMapper> actionMappers = new ArrayList<>();

          /**
           * This queue has been used to process elements in the order as they appear in the bpmn
           * xml indicated by nexts. However, the element will be added to this processing queue
           * only when it occurs as a step in the respective workflow step for which processing is
           * happening. The step details containing set of states ensures that the next element to
           * the current element(in processing state) is added to the queue if and only it exists in
           * the value set for that workflow step.
           *
           * <p>Once the element is processed, it is dequeued from the queue.
           */
          Queue<String> unprocessedElements = new LinkedList<>();
          unprocessedElements.add(id);

          /**
           * * This parameter governs the visibility of WorkflowStep in the start Element of
           * WorkflowStep
           */
          CurrentStepDetails currentStepDetails =
              getCurrentStepDetails(
                  ConfigurationDefinitionUtil.getConfigurationsAsMap(
                      elementsMap.get(id).getFlowElement()));

          while (!unprocessedElements.isEmpty()) {
            String currId = unprocessedElements.remove();

            /** Type of element if it is Action, Trigger or just a condition. */
            String elementType =
                BpmnProcessorUtil.getElementType(
                    elementsMap.get(currId).getFlowElement().getElementType().getTypeName());

            if ((WorkflowTemplateConstant.CONDITION.getName()).equalsIgnoreCase(elementType)) {
              workflowStepCondition =
                  prepareWorkflowStepConditions(
                      dmnModelInstances,
                      elementsMap.get(currId),
                      currId,
                      globalId,
                      isDefinitionRead);
              workflowStep.setWorkflowStepCondition(workflowStepCondition);
            } else if (WorkflowTemplateConstant.TRIGGER.getName().equalsIgnoreCase(elementType)) {
              trigger = prepareTriggers(elementsMap.get(currId), currId, globalId);
              workflowStep.setTrigger(trigger);
            } else if (WorkflowTemplateConstant.ACTION.getName().equalsIgnoreCase(elementType)) {
              WorkflowStep.ActionMapper actionMapper =
                  prepareActions(
                      modelInstance,
                      elementsMap,
                      currId,
                      value,
                      globalId,
                      stepCount.get(),
                      template,
                      isDefinitionRead);
              actionMappers.add(actionMapper);
            }
            /**
             * Prepare the queue for iteration. Takes in list of nexts attached to an element and
             * verifies if the nexts attached to it are part of the workflow step. If the next
             * element is part of the workflow step then it is added to the queue otherwise ignored.
             */
            prepareQueueForNextElements(
                outgoingNextsBuilder(modelInstance, elementsMap.get(currId)),
                unprocessedElements,
                value);
          }
          workflowStep.setTrigger(trigger);
          workflowStep.setName(id);
          workflowStep.setActions(actionMappers);
          // Setting WorkflowStep Id equal to Trigger ID
          workflowStep.setId(
              GlobalId.builder()
                  .setTypeId(workflowStep.getTypeId())
                  .setRealmId(globalId.getRealmId())
                  .setLocalId(trigger.getId().getLocalId())
                  .build());
          workflowStep.setSequence(String.valueOf(stepCount.getAndIncrement()));

          // Custom workflow use case: update workflow step with actions and conditions from
          // config
          if (isDefinitionRead && CustomWorkflowUtil.isCustomWorkflow(template)) {
            readCustomDefinitionHandler.updateWorkflowStep(
                template.getName(), template.getRecordType(), workflowStep);
          }
          /**
           * Setting visibility from CurrentStepDetails parameter in the workflow. This variable
           * indicates if this workflow step will be visible at the front-end.
           */
          if (!ObjectUtils.isEmpty(currentStepDetails)) {
            workflowStep.setRequired(currentStepDetails.getRequired());
          }
          if (BooleanUtils.isTrue(workflowStep.isRequired())) {
            // Adding the step to the response if required is true.
            workflowSteps.add(workflowStep);
          }
        });
    template.setWorkflowSteps(workflowSteps);
    template.setId(globalId);
    return template;
  }

  /**
   * @param nextReferencedElements : List of ids of elements which are connected to the current
   *     element (Outgoing edges.)
   * @param unprocessedElements : Main Queue which regulates the iteration of current workflow step,
   * @param stepParticipants : Ids of all the elements which are part of a Workflow Step.
   *     <p></>
   * @usage : Prepare the queue for iteration. Takes in list of nexts attached to an element and
   *     verifies if the nexts attached to it are part of the workflow step. If the next element is
   *     part of the workflow step then it is added to the queue otherwise ignored.
   */
  private void prepareQueueForNextElements(
      List<String> nextReferencedElements,
      Queue<String> unprocessedElements,
      Set<String> stepParticipants) {
    nextReferencedElements.forEach(
        nextReferencedElement -> {
          if (stepParticipants.contains(nextReferencedElement)) {
            unprocessedElements.add(nextReferencedElement);
          }
        });
  }

  /**
   * @param modelInstance : Object of BPMN Template XML
   * @param camundaElementsMap : Camunda Element is a POJO that contains id, type, FlowElement and
   *     all relevant properties needed to prepare the Action object of v4 Entity.
   * @param id : Id of the element/action which is getting processed.
   * @param valueSet : IDs of All Elements in a workflow step
   * @param globalId
   * @param stepCount : Count of the current Step which is required to prepare Local ID
   * @return : ActionMapper object
   */
  private WorkflowStep.ActionMapper prepareActions(
      BpmnModelInstance modelInstance,
      Map<String, CamundaElements> camundaElementsMap,
      String id,
      Set<String> valueSet,
      GlobalId globalId,
      int stepCount,
      Template template,
      boolean isDefinitionRead) {
    WorkflowLogger.logInfo("Inside prepareActions method for id=%s", id);
    WorkflowStep.ActionMapper actionMapper = new WorkflowStep.ActionMapper();
    Action action = new Action();
    /** Setting Global ID for an Action */
    action.setId(
        GlobalId.builder()
            .setRealmId(globalId.getRealmId())
            .setTypeId(action.getTypeId())
            .setLocalId(id)
            .build());
    // TODO : this key needs to be there in the configuration
    action.setName(camundaElementsMap.get(id).getFlowElement().getName());

    // Preparing Action's Parameter List
    Map<String, String> configurationMap =
        ConfigurationDefinitionUtil.getConfigurationsAsMap(
            camundaElementsMap.get(id).getFlowElement());
    List<InputParameter> inputParameters = prepareInputParameters(configurationMap);
    action.setParameters(inputParameters);

    String required =
        inputParameters.stream()
            .filter(
                inputParameter ->
                    WorkflowTemplateConstant.REQUIRED
                        .getName()
                        .equalsIgnoreCase(inputParameter.getParameterName()))
            .map(t -> t.getFieldValues(0))
            .collect(Collectors.joining());

    action.setRequired(Boolean.valueOf(required));

    // Setting isSelected attribute for Action here

    action.selected(
        checkIsSelected(
            modelInstance,
            camundaElementsMap.get(id).getFlowElement().getChildElementsByType(Incoming.class)));

    // custom workflow use case - set action selected based on conditional flow element's expression
    if (isDefinitionRead && CustomWorkflowUtil.isCustomWorkflow(template)) {
      FlowElement flowElement = camundaElementsMap.get(id).getFlowElement();
      action.setSelected(CustomWorkflowUtil.getActionSelected(flowElement, modelInstance));
    }
    // Setting next Action types
    List<Action.Next> nextAction = new ArrayList<>();
    List<String> nextReferencedStateIds =
        outgoingNextsBuilder(modelInstance,camundaElementsMap.get(id));

    nextReferencedStateIds.forEach(
        nextElementId -> {
          if (valueSet.contains(nextElementId)) {
            // part of the current workflow step and it has to be of type action
            Action.Next actionNext = new Action.Next();
            actionNext.setNextType(NextTypeEnum.ACTION);
            actionNext.setNextWorkflowStep(null);
            Action actionNew = new Action();
            actionNew.setId(
                GlobalId.builder()
                    .setRealmId(globalId.getRealmId())
                    .setTypeId(action.getTypeId())
                    .setLocalId(nextElementId)
                    .build());
            actionNew.setName(nextElementId);
            actionNext.setNextAction(actionNew);
            nextAction.add(actionNext);
          } else {
            Action.Next actionNext = new Action.Next();
            actionNext.setNextAction(null);
            actionNext.setNextType(NextTypeEnum.WORFKLOWSTEP);
            WorkflowStep workflowStep = new WorkflowStep();
            workflowStep.setName(nextElementId);
            workflowStep.setId(
                GlobalId.builder()
                    .setTypeId(workflowStep.getTypeId())
                    .setRealmId(globalId.getRealmId())
                    .setLocalId(MessageFormat.format("{0}", stepCount))
                    .build());
            actionNext.setNextWorkflowStep(workflowStep);
            nextAction.add(actionNext);
          }
        });

    action.setNexts(nextAction);

    String mappedActionKey = getMappedActionKey(camundaElementsMap.get(id).getFlowElement());
    actionMapper.setActionKey(mappedActionKey);
    actionMapper.setAction(action);
    return actionMapper;
  }

  /**
   * @param flowElement : FlowElement containing all the properties/attributes of the current
   *     processing element
   * @return : Action Key -> name which will tell if an action is linked to a particular condition .
   *     Returns null if action succeeds directly from trigger.
   */
  private String getMappedActionKey(FlowElement flowElement) {
    String actionKeyName = null;
    boolean terminateLoop = false;

    if (BpmnComponentType.SEND_TASK
        .getName()
        .equalsIgnoreCase(flowElement.getElementType().getTypeName())) {
      SendTaskImpl task = (SendTaskImpl) flowElement;
      actionKeyName = getActionKeyNameInSendTask(task);

    } else if (BpmnComponentType.SERVICE_TASK
        .getName()
        .equalsIgnoreCase(flowElement.getElementType().getTypeName())) {
      ServiceTaskImpl task = (ServiceTaskImpl) flowElement;
      actionKeyName = getActionKeyNameInServiceTask(terminateLoop, task, actionKeyName);
    }

    /**
     * Reading mapped Action key from sequence flows leading to that particular action. There can be
     * cases : <br>
     * 1. Condition is leading to action and sequence flow might have any conditional expression.
     * <br>
     * 2. Trigger to Action so conditional expression will be null <br>
     * 3. Post definition creation, variables are replaced by true and false values so they have to
     * be skipped. Such are the cases, per say and few more.
     */

    /*if expression comes as ${true} and ${false} then mapped Action key will be set as null in
    case of ReadOne Definition as Camunda reads true and false as String and not as boolean. This
    check will verify the same in Read one definition flow as during the read one template flow,
    it has keys as sendReminder and autoApproved which are correctly set as mappedActionKey attribute.*/
    if (StringUtils.isEmpty(actionKeyName)
        || ("null").equalsIgnoreCase(actionKeyName)
        || "true".equalsIgnoreCase(actionKeyName)
        || "false".equalsIgnoreCase(actionKeyName)
        || "${true}".equalsIgnoreCase(actionKeyName)
        || "${false}".equalsIgnoreCase(actionKeyName)) {
      actionKeyName = null;
    } else {
      /* Enhancing the logic to accomodate SDEF usecase when conditional expression is like : ${sendReminder == true}
         With in house DMN evaluator, we don't need 'decision.' in front of the expression
         Custom workflows are not impacted as their action key is populated from the config
       */
      try {
        int actionKeyStartIndex = (actionKeyName.indexOf('.') == -1) ? actionKeyName.indexOf('{') : actionKeyName.indexOf('.');

        actionKeyName =
            actionKeyName
                .substring(actionKeyStartIndex + 1, actionKeyName.indexOf("=="))
                .trim();
      }catch(Exception ex){
        WorkflowLogger.logError("Error occurred while reading mapped action key from sequence flows", ex);
      }
    }
    return actionKeyName;
  }

  private String getActionKeyNameInServiceTask(
      boolean terminateLoop, ServiceTaskImpl task, String actionKeyName) {
    for (SequenceFlow sequenceFlow : task.getIncoming()) {
      {
        if (BpmnComponentType.BOUNDARY_EVENT
            .getName()
            .equalsIgnoreCase(sequenceFlow.getSource().getElementType().getTypeName())) {
          terminateLoop = true;
          break;
        }
      }
    }
    if (!terminateLoop) {
      actionKeyName =
          task.getIncoming().stream()
              .map(
                  sequenceFlow ->
                      ObjectUtils.isEmpty(sequenceFlow.getConditionExpression())
                          ? null
                          : sequenceFlow.getConditionExpression().getRawTextContent())
              .filter(Objects::nonNull)
              .collect(Collectors.joining(","));
    }
    return actionKeyName;
  }

  private String getActionKeyNameInSendTask(SendTaskImpl task) {

    return task.getIncoming().stream()
        .map(
            sequenceFlow ->
                ObjectUtils.isEmpty(sequenceFlow.getConditionExpression())
                    ? null
                    : sequenceFlow.getConditionExpression().getRawTextContent())
        .collect(Collectors.joining(","));
  }

  /**
   * @param camundaElements : Camunda Element is a POJO that contains id, type, FlowElement and all
   *     * relevant properties needed to prepare the Action object of v4 Entity.
   * @param id : Id of the element/action which is getting processed.
   * @param globalId
   * @return : Trigger Object
   */
  private Trigger prepareTriggers(CamundaElements camundaElements, String id, GlobalId globalId) {
    WorkflowLogger.logInfo("Inside prepareTriggers method for id=%s", id);
    Trigger trigger = new Trigger();
    /** Setting Global ID for Triggers */
    trigger.setId(
        GlobalId.builder()
            .setLocalId(id)
            .setTypeId(trigger.getTypeId())
            .setRealmId(globalId.getRealmId())
            .build());
    trigger.setName(camundaElements.getFlowElement().getName());
    trigger.setDeleted(false);
    Map<String, String> configurationMap =
        ConfigurationDefinitionUtil.getConfigurationsAsMap(camundaElements.getFlowElement());
    if (!ObjectUtils.isEmpty(configurationMap)) {
      List<InputParameter> inputParameters = new ArrayList<>();
      prepareParameterDetailsForTriggerAndConditions(configurationMap, inputParameters);
      trigger.setParameters(inputParameters);
      trigger.setRequired(isStepVisibleForUI(configurationMap));
    }
    return trigger;
  }

  /**
   * @param dmnModelInstances : List of Associated DMNs related to the Business Rule Task in BPMN
   *     XML
   * @param camundaElements : Camunda Element is a POJO that contains id, type, FlowElement and all
   *     relevant properties needed to prepare the Action object of v4 Entity.
   * @param flowElementId : Id of the element/action which is getting processed.
   * @param globalId
   * @param isDefinitionRead
   * @return : Condition Object
   */
  private WorkflowStepCondition prepareWorkflowStepConditions(
      List<DmnModelInstance> dmnModelInstances,
      CamundaElements camundaElements,
      String flowElementId,
      GlobalId globalId,
      Boolean isDefinitionRead) {
    WorkflowLogger.debug(
        () ->
            WorkflowLoggerRequest.builder()
                .message("Inside prepareWorkflowStepConditions for ID=%s", flowElementId)
                .downstreamComponentName(DownstreamComponentName.WAS)
                .className(this.getClass().getSimpleName()));
    Map<String, String> configurationMap =
        ConfigurationDefinitionUtil.getConfigurationsAsMap(camundaElements.getFlowElement());
    WorkflowStepCondition workflowStepCondition = new WorkflowStepCondition();
    workflowStepCondition.setName(camundaElements.getFlowElement().getName());
    /** Setting Global ID for Workflow Condition */
    workflowStepCondition.setId(
        GlobalId.builder()
            .setRealmId(globalId.getRealmId())
            .setTypeId(workflowStepCondition.getTypeId())
            .setLocalId(flowElementId)
            .build());

    workflowStepCondition.setDescription("Description : ".concat(flowElementId));
    workflowStepCondition.setRequired(isStepVisibleForUI(configurationMap));
    workflowStepCondition.setDeleted(false);

    // Get list of conditional parameters
    List<ConditionalParameter> conditionalParameters = new ArrayList<>();
    if (camundaElements
        .getFlowElement()
        .getElementType()
        .getTypeName()
        .equalsIgnoreCase(BpmnComponentType.BUSINESS_RULE_TASK.getName())) {
      BusinessRuleTaskImpl task = (BusinessRuleTaskImpl) camundaElements.getFlowElement();
      DmnModelInstance dmnModelInstance = getDMNModelInstance(dmnModelInstances, task, globalId.getRealmId());
      Collection<DecisionTable> decisionTables =
          dmnModelInstance.getModelElementsByType(DecisionTable.class);

      /**
       * As of now there exists a single decision table in a DMN. Need to add wrapper over this
       * layer for supporting multiple decision tables in future.
       */
      if (isDefinitionRead) {
        List<RuleLine> ruleLines = prepareRuleLines(decisionTables);
        workflowStepCondition.setRuleLines(ruleLines);
      }

      // This Code if For Preparing Conditional Input Parameters
      for (DecisionTable decisionTable : decisionTables) {
        decisionTable
            .getInputs()
            .forEach(
                input -> {
                  String parameterType = input.getInputExpression().getTypeRef();
                  ConditionalParameter conditionalParameter = new ConditionalParameter();
                  InputParameter inputParameter = new InputParameter();
                  inputParameter.setParameterName(input.getLabel());
                  inputParameter.setParameterType(
                      FieldTypeEnum.valueOf(parameterType.toUpperCase()));

                  inputParameter.setRequired(Boolean.TRUE);
                  inputParameter.setConfigurable(Boolean.FALSE);

                  inputParameter.setMultiSelect(getMultiSelectConfiguration(parameterType));
                  inputParameter.setGetOptionsForFieldValue(
                      buildFieldValueOptions(parameterType, input.getLabel().toUpperCase()));
                  conditionalParameter.setInputParameter(inputParameter);

                  /*
                   * Preparing Property Map for List of Supported Operators.
                   * */
                  conditionalParameter.setSupportedOperators(
                      BpmnProcessorUtil.prepareOperatorList(
                          BpmnProcessorUtil.getSupportedOperator(
                              conditionalParameter
                                  .getInputParameter()
                                  .getParameterType()
                                  .value())));

                  conditionalParameters.add(conditionalParameter);
                });
      }

      // Preparing Default Rule here to indicate UI which attribute needs to be set as default
      if (!isDefinitionRead) {
        DecisionTable decisionTableInstance = decisionTables.stream().findFirst().get();
        String parameter = getDefaultConditionName(decisionTableInstance);
        // Annotation doesn't contain Default Column name then pick it up from the first column
        // label.
        if (StringUtils.isEmpty(parameter)) {
          parameter = decisionTableInstance.getInputs().stream().findFirst().get().getLabel();
        }
        // prepare Default Rule Line
        workflowStepCondition.setRuleLines(prepareDefaultRule(parameter, decisionTableInstance));
      }

    } else if (BpmnComponentType.EXCLUSIVE_GATEWAY
        .getName()
        .equalsIgnoreCase(camundaElements.getFlowElement().getElementType().getTypeName())) {

      List<InputParameter> inputParameters = new ArrayList<>();
      prepareParameterDetailsForTriggerAndConditions(configurationMap, inputParameters);

      inputParameters.forEach(
          inputParameter -> {
            ConditionalParameter conditionalParameter = new ConditionalParameter();
            conditionalParameter.setInputParameter(inputParameter);

            /*
             * Preparing Property Map for List of Supported Operators.
             * */
            conditionalParameter.setSupportedOperators(
                BpmnProcessorUtil.prepareOperatorList(
                    BpmnProcessorUtil.getSupportedOperator(
                        inputParameter.getParameterType().value())));
            conditionalParameters.add(conditionalParameter);
          });
    }
    // here we are localising the condition description.
    conditionalParameters.forEach(
        conditionalParameter ->
            conditionalParameter.getSupportedOperators().stream()
                .forEach(
                    operator ->
                        operator.setDescription(getFormattedString(operator.getDescription()))));
    workflowStepCondition.setConditionalInputParameters(conditionalParameters);
    workflowStepCondition.setRequired(isStepVisibleForUI(configurationMap));
    return workflowStepCondition;
  }

  private DmnModelInstance getDMNModelInstance(
      List<DmnModelInstance> dmnModelInstances, BusinessRuleTaskImpl task, String realmId) {
    DmnModelInstance dmnModelInstance = null;
    for (DmnModelInstance dmn : dmnModelInstances) {
      Collection<Decision> decisions = dmn.getModelElementsByType(Decision.class);
      if (null == dmnModelInstance) {
        for (Decision decision : decisions) {
          // Check whether decisionId is equal to CamundaDecisionRef or extracted one in case of Single Definition
          if (decision.getId().equalsIgnoreCase(task.getCamundaDecisionRef()) || getExtractedDecisionId(realmId,
                  decision.getId()).equalsIgnoreCase(task.getId())) {
            dmnModelInstance = dmn;
            break;
          }
        }
      }
    }
    return dmnModelInstance;
  }

  /**
   * For Single Definition, the template data is read from TemplateDetails table which is not
   * appended with _<<realmId>>_<<uuid>>, hence this method is used to extract it out.
   * @param realmId
   * @param decisionId
   * @return extracted decisionId for e.g. Will return "decisionElement" from "decisionElement_realmId_uuid"
   */
  private String getExtractedDecisionId(String realmId, String decisionId) {
    //TODO refactor this method to decide on the realmId usage from Authorization instead of GlobalId.
    if (decisionId.contains(realmId)) {
      //Will return "decisionElement" for instance, from "decisionElement_realmId_uuid"
      decisionId = decisionId.substring(0, decisionId.indexOf(realmId) - 1);
    }
    return decisionId;
  }

  /**
   * * Setting GET_<ParameterName> for String Data-types and null for Number Types. This is to
   * indicate to the UI that from where this field has to be populated.
   *
   * @param parameterType : Type of DMN Column Name
   * @param parameterName : Name of the DMN parameter
   * @return
   */
  private String buildFieldValueOptions(String parameterType, String parameterName) {
    return WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(parameterType)
        ? MessageFormat.format(WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_UNDERSCORE, WorkflowConstants.GET_KEYWORD, parameterName)
        : null;
  }

  /**
   * Setting Multi-select property to indicate to the UI that there is a dropdown. All String types
   * will have a drop-down and all the number types will not have one. This is specific to DMN
   * Columns
   *
   * @param parameterType : Type of Parameter
   * @return
   */
  private Boolean getMultiSelectConfiguration(String parameterType) {
    return Boolean.valueOf(WorkflowConstants.STRING_MODIFIER.equalsIgnoreCase(parameterType));
  }

  /**
   * @param parameter : Name of the parameter [Comma separated values or a single value in case of
   *     Invoice Approval]
   * @param decisionTableInstance : DMN ModelInstance Object
   * @return : Default Rule
   */
  @VisibleForTesting
  List<RuleLine> prepareDefaultRule(String parameter, DecisionTable decisionTableInstance) {
    List<RuleLine> ruleLines = new ArrayList<>();
    RuleLine ruleLine = new RuleLine();
    List<RuleLine.Rule> ruleList = new ArrayList<>();
    List<String> listOfParameters = Arrays.asList(parameter.split(WorkflowConstants.COMMA));

    listOfParameters.forEach(
        parameters -> {
          RuleLine.Rule rule = new RuleLine.Rule();
          String parameterType;
          String defaultValue = null;
          String parameterName;
          if (parameters.contains(WorkflowConstants.COLON)) {
            // combination of column name and default value. For ex : Amount:300
            String[] dmnColumnNameAndDefaultValue = parameters.split(WorkflowConstants.COLON);
            defaultValue = dmnColumnNameAndDefaultValue[1].trim();
            parameterName = dmnColumnNameAndDefaultValue[0].trim();
          } else {
            // No Default value is there and hence parameterName will be the same as that in the
            // list. Trim to avoid any trailing spaces if any.
            parameterName = parameters.trim();
          }
          parameterType =
              decisionTableInstance.getInputs().stream()
                  .filter(input -> parameterName.equalsIgnoreCase(input.getLabel()))
                  .map(input -> input.getInputExpression().getTypeRef())
                  .collect(Collectors.joining());
          rule.setParameterName(parameterName);

          // get the transformer based on the data type
          DMNDataTypeTransformer dataTypeTransformer =
              DMNDataTypeTransformers.getTransformer(DMNSupportedOperator.value(parameterType));

          // populate default conditional expression
          if (nonNull(dataTypeTransformer)) {
            rule.setConditionalExpression(dataTypeTransformer.defaultRule(parameterName, defaultValue));
          }
          ruleList.add(rule);
        });
    ruleLine.setRules(ruleList);
    ruleLine.setMappedActionKeys(
        decisionTableInstance.getOutputs().stream()
            .map(DmnElement::getLabel)
            .collect(Collectors.toList()));
    ruleLines.add(ruleLine);

    return ruleLines;
  }

  /**
   * @param decisionTable : DecisionTable Object
   * @return Comma separated representation of all Parameters
   */
  private String getDefaultConditionName(DecisionTable decisionTable) {
    Collection<Rule> rules = decisionTable.getRules();
    return rules.stream()
        .map(
            rule ->
                ObjectUtils.isEmpty(rule.getDescription())
                    ? null
                    : rule.getDescription().getRawTextContent())
        .collect(Collectors.joining(WorkflowConstants.COMMA));
  }

  /**
   * Create rule lines from DMN
   *
   * @param decisionTables : Collection of Decision Tables
   * @return : Object of type RuleLines
   */
  private List<RuleLine> prepareRuleLines(Collection<DecisionTable> decisionTables) {
    List<RuleLine> ruleLines = new ArrayList<>();

    for (DecisionTable decisionTable : decisionTables) {
      List<String> inputLabelList =
          decisionTable.getInputs().stream().map(DmnElement::getLabel).collect(Collectors.toList());

      List<String> inputTypeRefList =
          decisionTable.getInputs().stream()
              .map(Input::getInputExpression)
              .map(InputExpression::getTypeRef)
              .collect(Collectors.toList());

      decisionTable
          .getRules()
          .forEach(
              rule -> {
                int loopIncrement = 0;
                RuleLine ruleLine = new RuleLine();
                List<RuleLine.Rule> rules = new ArrayList<>();
                // Check which parameters have rule expression as select all. Rule description could
                // be like Customer:SELECT_ALL,Department:SELECT_ALL
                Set<String> paramsWithSelectAllRule = BpmnProcessingHelper.getSelectAllParametersForRule(rule);

                for (InputEntry inputEntry : rule.getInputEntries()) {
                  RuleLine.Rule dmnRule = new RuleLine.Rule();
                  dmnRule.setRuleId(
                      MessageFormat.format(
                          WorkflowConstants.MESSAGE_PLACE_HOLDER_WITH_UNDERSCORE,
                          "Rule",
                          loopIncrement));
                  dmnRule.setParameterName(inputLabelList.get(loopIncrement));
                  dmnRule.selectedlogicalGroupingOperator(LogicalOperatorsEnum.AND);
                  String ruleExpression = BpmnProcessingHelper.getUIFriendlyRuleExpression(
                          inputLabelList.get(loopIncrement),
                          inputTypeRefList.get(loopIncrement),
                          paramsWithSelectAllRule,
                          inputEntry.getText().getTextContent());

                  /*Adding the parameter type to be a part of dmn rule*/
                  if (!StringUtils.isEmpty(ruleExpression)) {
                    dmnRule.setConditionalExpression(ruleExpression);
                    dmnRule.setParameterType(
                        FieldTypeEnum.valueOf(inputTypeRefList.get(loopIncrement).toUpperCase()));
                    rules.add(dmnRule);
                  }
                  // else do not add rule as conditional expression is null and it is not Select All
                  // Type
                  loopIncrement++;
                }
                ruleLine.setRules(rules);
                // preparing mapped action keys here
                ruleLine.setMappedActionKeys(
                    decisionTable.getOutputs().stream()
                        .map(DmnElement::getLabel)
                        .collect(Collectors.toList()));
                ruleLines.add(ruleLine);
              });
    }
    // removing the default row from response
    ruleLines.remove(ruleLines.size() - 1);
    return ruleLines;
  }

  /**
   * @param configurationMap : Configuration Map which is basically map of <String,String></> where
   *     key = value of Camunda Property/ Camunda Input Parameter or both and value is the resulting
   *     configuration provided.
   * @usage : This method looks for all the configuration provided for a particular element in the
   *     bpmn XML. With the help of Schema Decoder, this method process the provided information and
   *     create parameters which may or may not be displayed on the UI or parameters/configuration
   *     to be passed to the AppConnect.
   */
  private List<InputParameter> prepareInputParameters(Map<String, String> configurationMap) {
    List<InputParameter> parameters = new ArrayList<>();
    Optional<HandlerDetails> handlerDetailsOptional =
        SchemaDecoder.getHandlerDetails(configurationMap);
    handlerDetailsOptional.ifPresent(
        handlerDetails -> handlerDetailsBuilder(handlerDetails, parameters));

    Optional<TaskDetails> taskDetailsOptional = SchemaDecoder.getTaskDetails(configurationMap);
    taskDetailsOptional.ifPresent(taskDetails -> taskDetailsBuilder(taskDetails, parameters));

    Optional<Map<String, ParameterDetails>> parametersForUIOptional =
        SchemaDecoder.getParametersForUI(configurationMap);
    parametersForUIOptional.ifPresent(
        stringParameterDetailsMap -> parametersForUIBuilder(stringParameterDetailsMap, parameters));
    parameters.stream()
        .forEach(inputParameter -> localiseInputParameterFieldValues(inputParameter));
    return parameters;
  }

  /**
   * @param stringParameterDetailsMap : Map of <String,ParameterDetails> returned by Schema
   *     Decoder</>
   * @param parameters : List Of Input Parameters which is a V4 Entity
   */
  private void parametersForUIBuilder(
      Map<String, ParameterDetails> stringParameterDetailsMap, List<InputParameter> parameters) {
    stringParameterDetailsMap.forEach(
        (key, value) -> {
          InputParameter parameter = new InputParameter();
          parameter.setParameterName(key);
          parameter.setParameterType(FieldTypeEnum.fromValue(value.getFieldType().toUpperCase()));
          parameter.setRequired(value.isRequiredByUI());
          if (null != value.getActionByUI())
            parameter.setGetOptionsForFieldValue(value.getActionByUI().name());
          parameter.setMultiSelect(value.isMultiSelect());
          parameter.setConfigurable(value.isConfigurable());
          parameter.setFieldValues(value.getFieldValue());
          parameter.setHelpVariables(value.getHelpVariables());
          parameter.setPossibleFieldValues(value.getPossibleFieldValues());
          parameters.add(parameter);
        });
  }

  /**
   * @param stringParameterDetailsMap : Map of <String,ParameterDetails> returned by Schema
   *     Decoder</>
   * @param parameters : List Of Input Parameters which is a V4 Entity
   */
  private void parametersForHandlerBuilder(
      Map<String, ParameterDetails> stringParameterDetailsMap, List<InputParameter> parameters) {
    stringParameterDetailsMap.forEach(
        (key, value) -> {
          InputParameter parameter = new InputParameter();
          parameter.setParameterName(key);
          if(Objects.nonNull(value.getFieldType())) {
              parameter.setParameterType(FieldTypeEnum.fromValue(value.getFieldType().toUpperCase()));
          }
          parameter.setRequired(value.isRequiredByHandler());
          if (null != value.getActionByUI())
            parameter.setGetOptionsForFieldValue(value.getActionByUI().name());
          parameter.setMultiSelect(value.isMultiSelect());
          parameter.setConfigurable(value.isConfigurable());
          parameter.setFieldValues(value.getFieldValue());
          parameter.setHelpVariables(value.getHelpVariables());
          parameter.setPossibleFieldValues(value.getPossibleFieldValues());
          parameters.add(parameter);
        });
  }

  /**
   * @usage : A Util method to extract the configuration about visibility of a WorkFlow Step on UI
   * @param taskDetails : TaskDetail object containing information about visibility of a WorkFlow
   *     Step on UI.
   * @param parameters : List Of Input Parameters which is a V4 Entity
   */
  private void taskDetailsBuilder(TaskDetails taskDetails, List<InputParameter> parameters) {
    if (!ObjectUtils.isEmpty(taskDetails.getRequired())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowTemplateConstant.REQUIRED.getName());
      parameter.setParameterType(FieldTypeEnum.BOOLEAN);
      parameter.setFieldValues(Collections.singletonList(taskDetails.getRequired().toString()));
      parameters.add(parameter);
    }
  }

  /**
   * @param handlerDetails : Details of the Handler(In our case, the one who will invoke Actions.
   *     Can be AppConnect, Camunda or WAS(WorkFlow Automation Service))
   * @param parameters : List Of Input Parameters which is a V4 Entity
   */
  private void handlerDetailsBuilder(
      HandlerDetails handlerDetails, List<InputParameter> parameters) {
    if (!ObjectUtils.isEmpty(handlerDetails.getActionName())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowConstants.ACTION_NAME);
      parameter.setParameterType(FieldTypeEnum.STRING);
      parameter.setFieldValues(Collections.singletonList(handlerDetails.getActionName()));
      parameters.add(parameter);
    }

    if (!ObjectUtils.isEmpty(handlerDetails.getTaskHandler())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowConstants.TASK_HANDLER);
      parameter.setParameterType(FieldTypeEnum.STRING);
      parameter.setFieldValues(Collections.singletonList(handlerDetails.getTaskHandler()));
      parameters.add(parameter);
    }

    if (!ObjectUtils.isEmpty(handlerDetails.getHandlerId())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowConstants.HANDLER_ID);
      parameter.setParameterType(FieldTypeEnum.STRING);
      parameter.setFieldValues(Collections.singletonList(handlerDetails.getHandlerId()));
      parameters.add(parameter);
    }

    if (!ObjectUtils.isEmpty(handlerDetails.getRecordType())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowConstants.RECORD_TYPE);
      parameter.setParameterType(FieldTypeEnum.STRING);
      parameter.setFieldValues(Collections.singletonList(handlerDetails.getRecordType().name()));
      parameters.add(parameter);
    }

    if (!ObjectUtils.isEmpty(handlerDetails.getHandlerScope())) {
      InputParameter parameter = new InputParameter();
      parameter.setParameterName(WorkflowConstants.HANDLER_SCOPE);
      parameter.setParameterType(FieldTypeEnum.STRING);
      parameter.setFieldValues(Collections.singletonList(handlerDetails.getHandlerScope()));
      parameters.add(parameter);
    }
  }

  /**
   * Sets the visibility of Actions Triggers and Conditions based on TaskDetails
   *
   * @param configurationMap : Map<String,String> </>
   * @return : the value of required field which is Boolean
   */
  private boolean isStepVisibleForUI(Map<String, String> configurationMap) {
    Optional<TaskDetails> taskDetailsOptional = SchemaDecoder.getTaskDetails(configurationMap);
    if (taskDetailsOptional.isPresent()) {
      return taskDetailsOptional.get().getRequired();
    }
    /**
     * In case user missed the required Parameter in the configuration. Ideally there should be
     * required parameter for every element.
     */
    return false;
  }

  /**
   * This method populates Parameter Details from Input Elements/ Camunda Properties for Trigger and
   * Conditional Objects
   *
   * @param configurationMap : Map of <String,ParameterDetails> returned by Schema * Decoder</>
   * @param parameters : List Of Conditional Parameters which is a V4 Entity to encapsulate
   *     conditions.
   */
  private void prepareParameterDetailsForTriggerAndConditions(
      Map<String, String> configurationMap, List<InputParameter> parameters) {
    Optional<Map<String, ParameterDetails>> optionalParameters =
        SchemaDecoder.getParametersForHandler(configurationMap);
    optionalParameters.ifPresent(
        stringParameterDetailsMap ->
            parametersForHandlerBuilder(stringParameterDetailsMap, parameters));
    optionalParameters = SchemaDecoder.getParametersForUI(configurationMap);
    optionalParameters.ifPresent(
        stringParameterDetailsMap -> parametersForUIBuilder(stringParameterDetailsMap, parameters));
  }

  /**
   * @usage : For pre-processing all the elements in the Process Object. This map is inevitable to
   *     get the object by IDs for quick processing at later stages (O(1) Access)
   * @param flowElement : Contains all the properties of element.
   * @param map : Stores the configuration of id and respective camunda element.
   * @return : Map<String, CamundaElements></>
   */
  private Map<String, CamundaElements> camundaElementsMapBuilder(
      FlowElement flowElement, Map<String, CamundaElements> map) {
    map.put(
        flowElement.getId(),
        CamundaElements.builder()
            .flowElement(flowElement)
            .outgoing(
                !ObjectUtils.isEmpty(flowElement.getChildElementsByType(Outgoing.class))
                    ? flowElement.getChildElementsByType(Outgoing.class)
                    : null)
            .build());
    return map;
  }

  /**
   * @param modelInstance : Object of BPMN Template XML
   * @param camundaElements : {@link CamundaElements} object
   * @return : List<String> which is basically the IDs of the next element that is connected to the
   *     current element</>
   */
  private List<String> outgoingNextsBuilder(
      ModelInstance modelInstance, CamundaElements camundaElements) {
    List<String> nextTargetState = new ArrayList<>();

    // In case next sequence element is pointing to a sub-process and sub-process element is part of
    // the step-details then we will
    // iterate over the flowElements of sub-process and return the start event as the first object
    // that needs to be processed next, in a similar way that we do for the parent process. Once
    // this is done, our bpmn execution will resume as expected.
    /**
     * For Example in below Sample XML, in an XML like this, sendTask -> subProcess -> endEvent(main
     * process). Inorder, to map inner contents of sub-process (which is part of the step-Details
     * object, we add next Processing step as startEvent from the sub-process so that all the
     * elements within the sub-process can be iterated based on the outgoing sequence flows in the
     * graph.) <br>
     * <br>
     * <process> <startEvent next="sendTask"></startEvent> <sendTask next="subProcess"></sendTask>
     * <subProcess next="endEvent"> <startEvent></startEvent> <sendTask></sendTask>
     * <endEvent></endEvent> </subProcess> <endEvent></endEvent> </process>
     */
    if (BpmnComponentType.SUB_PROCESS
        .getName()
        .equalsIgnoreCase(camundaElements.getFlowElement().getElementType().getTypeName())) {

      // Getting the desired subProcess element by Id
      SubProcess subProcess =
          modelInstance.getModelElementById(camundaElements.getFlowElement().getId());

      return subProcess.getFlowElements().stream()
          .filter(
              flowElement ->
                  BpmnComponentType.START_EVENT
                      .getName()
                      .equalsIgnoreCase(flowElement.getElementType().getTypeName()))
          .map(FlowElement::getId)
          .collect(Collectors.toList());
    }
    List<String> nextReferencedStateIds =
        Arrays.asList(
            camundaElements.getOutgoing().stream()
                .map(ModelElementInstanceImpl::getTextContent)
                .collect(Collectors.joining(WorkflowConstants.COMMA))
                .split(WorkflowConstants.COMMA));

    nextReferencedStateIds.forEach(
        t -> {
          SequenceFlow nextStates = modelInstance.getModelElementById(t);
          nextTargetState.add(nextStates.getTarget().getId());
        });
    return nextTargetState;
  }

  /**
   * This method returns isSelected Field for the Actions. The action which is going directly from
   * trigger or business rule task is true and the one's going from Gateway elements is set to
   * false.
   *
   * @param modelInstance : ModelInstance of the BPMN
   * @param incomings : List<> of Incoming nodes</>
   * @return
   */
  private Boolean checkIsSelected(ModelInstance modelInstance, Collection<Incoming> incomings) {
    List<String> parentState =
        Arrays.asList(
            incomings.stream()
                .map(ModelElementInstanceImpl::getTextContent)
                .collect(Collectors.joining(WorkflowConstants.COMMA))
                .split(WorkflowConstants.COMMA));
    AtomicReference<String> previousTargetState = new AtomicReference<>();
    parentState.forEach(
        t -> {
          SequenceFlow nextStates = modelInstance.getModelElementById(t);
          previousTargetState.set(nextStates.getSource().getId());
        });
    ModelElementInstance element = modelInstance.getModelElementById(previousTargetState.get());
    String type = element.getElementType().getTypeName();
    return !BpmnComponentType.EXCLUSIVE_GATEWAY.getName().equalsIgnoreCase(type)
        && !BpmnComponentType.PARALLEL_GATEWAY.getName().equalsIgnoreCase(type);
  }

  /**
   * @param configurationMap : Map<String, String> : contains information of StepDetails </>
   * @return map of Step Details, required to start processing of BPMN
   */
  private Map<String, Set<String>> getStepDetails(Map<String, String> configurationMap) {
    Optional<Map<String, Set<String>>> stepDetails = SchemaDecoder.getStepDetails(configurationMap);
    WorkflowVerfiy.verify(!stepDetails.isPresent(), WorkflowError.STEP_DETAILS_NOT_FOUND);
    return stepDetails.get();
  }
  /**
   * @param configurationMap : Map<String, String> : contains information of currentStepDetails </>
   * @return object of CurrentStepDetails which is required to inspect the visibility of
   *     WorkflowStep.
   */
  private CurrentStepDetails getCurrentStepDetails(Map<String, String> configurationMap) {
    Optional<HandlerDetails.CurrentStepDetails> currentStepDetails =
        SchemaDecoder.getCurrentStepDetails(configurationMap);
    return currentStepDetails.orElse(null);
  }

  /**
   * This method localised the field values of InputParameter.
   *
   * @param inputParameter {@link InputParameter}
   */
  private void localiseInputParameterFieldValues(InputParameter inputParameter) {
    if (CollectionUtils.isEmpty(inputParameter.getFieldValues())) {
      return;
    }

    List<String> localizedFieldValues = new ArrayList<>();
    for (String fieldValue : inputParameter.getFieldValues()) {
      localizedFieldValues.add(getFormattedString(fieldValue));
    }
    inputParameter.setFieldValues(localizedFieldValues);
  }

  private String getLocale() {
    return wasContextHandler.get(WASContextEnums.INTUIT_WAS_LOCALE);
  }

  private String getFormattedString(String key) {
    return translationService.getFormattedString(key, getLocale());
  }
}

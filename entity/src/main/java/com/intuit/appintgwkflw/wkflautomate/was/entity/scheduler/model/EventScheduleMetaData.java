package com.intuit.appintgwkflw.wkflautomate.was.entity.scheduler.model;

import com.intuit.v4.payments.schedule.ScheduleStatus;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;


/** <AUTHOR> */
@Getter
@Setter
@Builder
public class EventScheduleMetaData {

  /** Definition Id of scheduler */
  private String definitionId;

  private ScheduleStatus scheduleStatus;

  /** Workflow name for checking scheduler is enable or not. */
  private String workflowName;
}

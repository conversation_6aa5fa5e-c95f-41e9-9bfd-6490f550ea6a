package com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders;

import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowVerfiy;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.RuleLineConvertorTypeFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ActivityInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepWorkflowEntity;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers.ReadMultiStepWorkflowHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineParser;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.processors.MultiConditionRuleLineProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.DmnHeader;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.v4.workflows.Template;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.camunda.bpm.model.dmn.instance.DecisionTable;
import org.camunda.bpm.model.dmn.instance.Rule;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * This class is responsible for building workflowStepCondition in workflowStep
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepDefinitionConditionBuilder implements ReadMultiStepWorkflowHandler {

  private final MultiConditionRuleLineParser multiConditionProcessor;
  private final CustomWorkflowConfig customWorkflowConfig;
  private final MultiConditionRuleLineProcessor multiConditionRuleLineProcessor;
  private final RuleLineConvertorTypeFactory ruleLineConvertorTypeFactory;

  /**
   * This function reads the dmn, gets a map of all the dmn rules and prepares workflowStep with
   * workflowStepCondition
   *
   * @param multiStepWorkflowEntity multiStepWorkflowEntity
   */
  @Override
  public void buildWorkflowStep(MultiStepWorkflowEntity multiStepWorkflowEntity) {
    ActivityInstance activityInstance = multiStepWorkflowEntity.getActivityInstance();
    WorkflowLogger.logInfo("step=processWorkflowStep stepType=condition");
    DmnModelInstance dmnModelInstance = activityInstance.getDmnModelInstance();
    Collection<DecisionTable> decisionTables = dmnModelInstance.getModelElementsByType(
        DecisionTable.class);
    WorkflowVerfiy.verify(CollectionUtils.isEmpty(decisionTables),
        WorkflowError.DECISION_TABLE_MISSING);
    DecisionTable decisionTable = decisionTables.stream().findFirst()
        .orElseThrow(() -> new WorkflowGeneralException(WorkflowError.DECISION_TABLE_MISSING));
    Map<String, Map<String, DmnHeader>> attributeToHeaderMap = multiConditionRuleLineProcessor.buildDmnHeadersMap(
        decisionTable);
    Map<String, Map<String, List<List<Rule>>>> indexToYesAndNoDmnRulesMap = multiConditionProcessor.parseDecisionTable(
        decisionTable, attributeToHeaderMap);
    Template template = multiStepWorkflowEntity.getTemplate();
    Record record = customWorkflowConfig.getRecordObjForType(template.getRecordType());
    RuleLineBuilder ruleLineBuilder = ruleLineConvertorTypeFactory.getHandler(indexToYesAndNoDmnRulesMap);
    ruleLineBuilder.convertRulesToWorkflowStepCondition(
        record,
        multiStepWorkflowEntity,
        indexToYesAndNoDmnRulesMap,
        attributeToHeaderMap);
  }
}
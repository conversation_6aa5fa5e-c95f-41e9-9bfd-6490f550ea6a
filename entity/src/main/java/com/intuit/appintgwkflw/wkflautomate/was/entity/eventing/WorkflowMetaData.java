package com.intuit.appintgwkflw.wkflautomate.was.entity.eventing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> stripathy1 WorkflowMetaData will be part of all publishers who are publishing to
 *     Event bus. Currently this POJO is written manually, later after IEDM onboarding the schema
 *     will be moved to IEDM. These POJO will be automatially gennerated and can be deleted from
 *     this folder
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowMetaData {
  private String workflowOwnerId;
  private String processInstanceId;
  private String processDefinitionId;
  private String workflowName;
  private Integer workflowVersion;
}

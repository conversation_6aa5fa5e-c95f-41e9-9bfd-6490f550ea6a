apiVersion: v1
kind: Service
metadata:
  name: was-event-in-desired-service
spec:
  ports:
    - name: https
      port: 443
      targetPort: 8443
  selector:
    app: was-event-in
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: was-event-in-root-service
spec:
  ports:
    - name: https
      port: 443
      targetPort: 8443
  selector:
    app: was-event-in
  type: NodePort

---
apiVersion: v1
kind: Service
metadata:
  name: was-event-in-stable-service
spec:
  ports:
    - name: https
      port: 443
      targetPort: 8443
  selector:
    app: was-event-in
  type: NodePort
---
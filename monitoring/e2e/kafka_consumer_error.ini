[IPF - was-event-in - E2E - Kafka Consumer Error]
alert_metric_name = rawsum(align(60s, sum, ratediff(ts("iks.intuit.dataexchange.pipelineframework.kafka.consume.entry.count.total" and status="FAILURE" and  namespace=appintgwkflw-wkflautomate-waseventin-usw2-e2e))))
severity = SEVERE
threshold = 0
operator = >
alert_target_id = <EMAIL>
wait_before_alert = 1
additional_info =
custom_tags = idx.pipeline.framework



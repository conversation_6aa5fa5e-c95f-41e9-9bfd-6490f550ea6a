apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:

  - ../../../app-base-pipeline

patchesStrategicMerge:

  - Ingress-patch.yaml
  - Hpa-patch.yaml
  - Rollout-patch.yaml
  - Ipf-processor-loader-patch.yaml
  - Ipf-config-loader-patch.yaml

commonLabels:
  app: was-event-in-purchaseorder

namePrefix: "purchaseorder-"

replacements:
  - source:
      kind: Rollout
      name: was-event-in-rollout
      fieldPath: spec.selector.matchLabels.app
    targets:
      - select:
          name: was-event-in-rollout-hpa
          kind: HorizontalPodAutoscaler
        fieldPaths:
          - spec.metrics.*.object.describedObject.name
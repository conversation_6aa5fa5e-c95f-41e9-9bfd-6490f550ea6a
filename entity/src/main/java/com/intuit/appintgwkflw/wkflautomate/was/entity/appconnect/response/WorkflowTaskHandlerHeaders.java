package com.intuit.appintgwkflw.wkflautomate.was.entity.appconnect.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Getter;
import lombok.Setter;

/** <AUTHOR> */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowTaskHandlerHeaders {
  private String name;

  private String status;

  private String message;
}

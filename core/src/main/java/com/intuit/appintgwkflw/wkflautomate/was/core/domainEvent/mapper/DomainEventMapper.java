package com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.ENTITY_TYPE;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.PlaceholderUserVariableExtractorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.mapper.helper.PlaceholderUserVariableExtractors;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.SubstitutePlaceholderUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.AdditionalDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEntityRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.DomainEventErrorDetails;
import com.intuit.foundation.workflow.workflowautomation.ActivityRuntime;
import com.intuit.foundation.workflow.workflowautomation.Definition;
import com.intuit.foundation.workflow.workflowautomation.Process;
import com.intuit.foundation.workflow.workflowautomation.Template;
import com.intuit.foundation.workflow.workflowautomation.types.Attribute;
import com.intuit.foundation.workflow.workflowautomation.types.Audit;
import com.intuit.foundation.workflow.workflowautomation.types.DefinitionDetail;
import com.intuit.foundation.workflow.workflowautomation.types.ErrorDetail;
import com.intuit.foundation.workflow.workflowautomation.types.LookUpKey;
import com.intuit.foundation.workflow.workflowautomation.types.ProcessDetail;
import com.intuit.foundation.workflow.workflowautomation.types.SimpleAudit;
import com.intuit.foundation.workflow.workflowautomation.types.TemplateDetail;
import com.intuit.foundation.workflow.workflowautomation.types.WorkflowExternalEntity;
import com.intuit.system.types.EntityRef;
import com.intuit.system.types.Metadata;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *     <p>IEDM Entity Instance Mapper. Enrichment of event to be pushed to outbox table happens
 *     here.
 */
@UtilityClass
public class DomainEventMapper {

  private static final ZoneId UTC_ZONE = ZoneId.of("UTC");
  private static final String EXTERNAL_ACCESS_TYPE = "external";

  /**
   * Mapper class for ProcessDetail DB object to Process Domain Event Object
   *
   * @param domainEntityRequest
   * @return
   */
  public Process mapEntityToProcessDomainEvent(final DomainEntityRequest domainEntityRequest) {
    ProcessDetails processDetails = (ProcessDetails) domainEntityRequest.getRequest();
    // Setting Process Level Metadata
    Process process = new Process();
    process.setId(processDetails.getProcessId());
    process.setDefinitionDetail(buildDefinitionDetail(processDetails.getDefinitionDetails()));
    process.setOwnerId(
        ObjectUtils.isNotEmpty(processDetails.getOwnerId())
            ? processDetails.getOwnerId().toString()
            : null);
    process.setStatus(processDetails.getProcessStatus().getProcessStatus());
    WorkflowExternalEntity triggerEntity = new WorkflowExternalEntity();

    boolean isChildProcess = MultiStepUtil.isChildProcess(processDetails);
    // In case of Call Activity take the recordType from parent

    String entityType = MultiStepUtil.getEntityType(processDetails);

    // In case of on demand approval entityType will be null in definition
    if(Objects.isNull(entityType)){
      entityType =  Optional.ofNullable(domainEntityRequest.getAdditionalDetails())
          .map(AdditionalDetails::getVariables)
          .map(vars -> vars.get(ENTITY_TYPE))
          .map(Object::toString)
          .orElse(null);
      WorkflowLogger.logInfo("Fetching entity type from domainEntityRequest for on demand approval for realmId = %s and entity id = %s and record = %s", processDetails.getOwnerId(), processDetails.getRecordId(), Objects.nonNull(entityType) ? entityType : "unavailable");
    }

    triggerEntity.setEntityType(entityType);

    triggerEntity.setEntityId(processDetails.getRecordId());

    // Set docNumber as entityReferenceID in process Event (this will be present in case of
    // transactional entities)
    // in case null is returned in docNumber, toString() method will throw NPE
    Optional.ofNullable(domainEntityRequest)
        .map(DomainEntityRequest::getAdditionalDetails)
        .map(AdditionalDetails::getVariables)
        .ifPresent(
            variableMap ->
                triggerEntity.setEntityReferenceId(
                    ObjectUtils.isNotEmpty(
                            variableMap.getOrDefault(WorkflowConstants.DOC_NUMBER, ""))
                        ? variableMap.getOrDefault(WorkflowConstants.DOC_NUMBER, "").toString()
                        : ""));

    process.setTriggerEntity(triggerEntity);
    // Set Audit
    SimpleAudit simpleAudit = new SimpleAudit();
    // Setting Start time
    simpleAudit.setCreatedDate(
        Objects.nonNull(processDetails.getCreatedDate())
            ? processDetails.getCreatedDate().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));
    simpleAudit.setLastModifiedDate(ZonedDateTime.now(UTC_ZONE));
    process.setAudit(simpleAudit);
    // If Error Details are present then set
    if (Objects.nonNull(domainEntityRequest.getDomainEventErrorDetails())) {
      ErrorDetail errorDetail = new ErrorDetail();
      errorDetail.setErrorMessage(domainEntityRequest.getDomainEventErrorDetails().getErrorMessage());
      errorDetail.setActivityId(domainEntityRequest.getDomainEventErrorDetails().getActivityId());
      process.setErrorDetail(errorDetail);
    }
    // Setting Meta Data
    Metadata metadata = new Metadata();
    metadata.setCreated(simpleAudit.getCreatedDate());
    metadata.setUpdated(simpleAudit.getLastModifiedDate());
    // Setting owner id from process details table
    EntityRef ref = new EntityRef();
    ref.setId(String.valueOf(processDetails.getOwnerId()));
    metadata.setCreatedByUser(ref);
    metadata.setUpdatedByUser(ref);
    process.setMeta(metadata);

    process.setParentId(processDetails.getParentId());

    /* Refer the doc for more details
     https://docs.google.com/document/d/1v69nEG3YGusOIxJEQnb1M1BWOLx2SpgBR02B4DYBP6M/edit#heading=h.psw6w91zl3jg
     */
    process.setCorrelationId(isChildProcess ? processDetails.getParentId()
        : processDetails.getDefinitionDetails().getDefinitionKey());
    process.setAccessType(EXTERNAL_ACCESS_TYPE);
    process.setGroup(false);
    return process;
  }

  public Definition mapEntityToDefinitionDomainEvent(final DefinitionDetails definitionDetails) {
    // Setting Definition Level Metadata
    Definition definition = new Definition();
    definition.setId(definitionDetails.getDefinitionId());

    definition.setOwnerId(
        ObjectUtils.isNotEmpty(definitionDetails.getOwnerId())
            ? definitionDetails.getOwnerId().toString()
            : null);

    definition.setStatus(definitionDetails.getStatus().getStatus());
    definition.setModelType(definitionDetails.getModelType().toString());

    // Call Activity can have recordType as null
    definition.setEntityType(Objects.nonNull(definitionDetails.getRecordType()) ?
        definitionDetails.getRecordType().getDisplayValue() : null);

    // this is corresponding to WAS entity definitionDetails version
    definition.setVersion(String.valueOf(definitionDetails.getVersion()));

    definition.setName(definitionDetails.getDefinitionName());
    definition.setParentId(definitionDetails.getParentId());
    definition.setShortName(definitionDetails.getDefinitionKey());

    Optional<List<LookUpKey>> listOfLookupKeysFromString =
        getListOfLookupKeysFromString(definitionDetails.getLookupKeys());

    listOfLookupKeysFromString.ifPresentOrElse(definition::setLookUpKeys, Collections::emptyList);

    definition.setInternalStatus(
        ObjectUtils.isNotEmpty(definitionDetails.getInternalStatus())
            ? definitionDetails.getInternalStatus().toString()
            : null);

    definition.setDescription(definitionDetails.getDescription());

    definition.setEntityType(
        ObjectUtils.isNotEmpty(definitionDetails.getRecordType())
            ? definitionDetails.getRecordType().getRecordType()
            : null);

    // setting AppConnect id
    definition.setExternalWorkflowRefId(definitionDetails.getWorkflowId());

    definition.setOfferingId(
        ObjectUtils.isNotEmpty(definitionDetails.getOfferingId())
            ? definitionDetails.getOfferingId().toString()
            : null);

    TemplateDetail templateDetail = new TemplateDetail();
    templateDetail.setTemplateId(definitionDetails.getTemplateDetails().getId());
    templateDetail.setTemplateName(definitionDetails.getTemplateDetails().getTemplateName());
    definition.setTemplateDetail(templateDetail);

    // Set Audit
    Audit audit = new Audit();
    // Setting Start time
    audit.setCreatedDate(
        Objects.nonNull(definitionDetails.getCreatedDate())
            ? definitionDetails.getCreatedDate().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));

    audit.setLastModifiedDate(ZonedDateTime.now(UTC_ZONE));

    definition.setAudit(audit);

    // Setting Meta Data
    Metadata metadata = new Metadata();
    metadata.setCreated(audit.getCreatedDate());
    metadata.setUpdated(audit.getLastModifiedDate());
    // Setting owner id from process details table
    EntityRef ref = new EntityRef();
    ref.setId(String.valueOf(definitionDetails.getOwnerId()));
    metadata.setCreatedByUser(ref);
    metadata.setUpdatedByUser(ref);
    definition.setMeta(metadata);
    // Setting Template id  as correlation id
    definition.setCorrelationId(definitionDetails.getTemplateDetails().getId());
    definition.setAccessType(EXTERNAL_ACCESS_TYPE);
    definition.setGroup(false);
    return definition;
  }

  public Template mapEntityToTemplateDomainEvent(final TemplateDetails templateDetails) {
    // Setting template Level Metadata
    Template template = new Template();
    template.setId(templateDetails.getId());
    template.setOwnerId(templateDetails.getOwnerId().toString());
    template.setStatus(templateDetails.getStatus().getStatus());
    template.setCreatorType(templateDetails.getCreatorType().getCreatorType());
    template.setModelType(templateDetails.getModelType().getModelType());

    template.setDefinitionType(
        ObjectUtils.isNotEmpty(templateDetails.getDefinitionType())
            ? templateDetails.getDefinitionType().toString()
            : null);

    template.setVersion(String.valueOf(templateDetails.getVersion()));
    template.setParentId(templateDetails.getParentId());
    template.setOfferingId(templateDetails.getOfferingId());
    template.setDeployedDefinitionId(templateDetails.getDeployedDefinitionId());
    template.setName(templateDetails.getTemplateName());
    template.setDescription(templateDetails.getDescription());
    template.setDisplayName(templateDetails.getDisplayName());

    template.setEntityType(
        ObjectUtils.isNotEmpty(templateDetails.getRecordType())
            ? templateDetails.getRecordType().getRecordType()
            : null);

    template.setMultipleDefinitionsAllowed(
        Objects.nonNull(templateDetails.getAllowMultipleDefinitions()));
    template.setCategory(templateDetails.getTemplateCategory());

    // Set Audit
    Audit audit = new Audit();
    // Setting Start time
    audit.setCreatedDate(
        Objects.nonNull(templateDetails.getCreatedDate())
            ? templateDetails.getCreatedDate().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));

    audit.setLastModifiedDate(ZonedDateTime.now(UTC_ZONE));

    template.setAudit(audit);
    // Setting Meta Data
    Metadata metadata = new Metadata();
    metadata.setCreated(audit.getCreatedDate());
    metadata.setUpdated(audit.getLastModifiedDate());
    // Setting owner id from template details table
    EntityRef ref = new EntityRef();
    ref.setId(String.valueOf(templateDetails.getOwnerId()));
    metadata.setCreatedByUser(ref);
    metadata.setUpdatedByUser(ref);
    template.setMeta(metadata);
    return template;
  }

  public ActivityRuntime mapEntityToActivityRuntimeDomainEvent(
      final ActivityProgressDetails activityProgressDetails,
      final DomainEventErrorDetails domainEventErrorDetails) {
    ActivityRuntime activityRuntime = new ActivityRuntime();
    ActivityDetail activityDetails = activityProgressDetails.getActivityDefinitionDetail();
    if (ObjectUtils.isNotEmpty(activityDetails)) {
      com.intuit.foundation.workflow.workflowautomation.types.ActivityDetail activityDetail =
          new com.intuit.foundation.workflow.workflowautomation.types.ActivityDetail();
      // Setting user defined Id field here
      activityDetail.setActivityId(String.valueOf(activityDetails.getActivityId()));
      activityDetail.setActivityType(activityDetails.getActivityType());
      activityDetail.setName(activityDetails.getActivityName());
      // Setting activity id field here
      activityDetail.setUserDefinedActivityId(activityDetails.getActivityId());
      activityRuntime.setActivityDetail(activityDetail);
    }

    SimpleAudit simpleAudit = new SimpleAudit();
    // Setting Start time
    simpleAudit.setCreatedDate(
        Objects.nonNull(activityProgressDetails.getStartTime())
            ? activityProgressDetails.getStartTime().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));
    simpleAudit.setLastModifiedDate(
        Objects.nonNull(activityProgressDetails.getUpdatedTime())
            ? activityProgressDetails.getUpdatedTime().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));

    List<com.intuit.foundation.workflow.workflowautomation.types.Attribute> attribute =
        prepareAttributes(activityProgressDetails);

    // Setting Process Details
    ProcessDetail processDetail = new ProcessDetail();
    DefinitionDetail definitionDetail = new DefinitionDetail();
    definitionDetail.setDefinitionName(
        activityProgressDetails.getProcessDetails().getDefinitionDetails().getDefinitionName());
    definitionDetail.setDefinitionId(
        activityProgressDetails.getProcessDetails().getDefinitionDetails().getDefinitionId());

    processDetail.setProcessId(activityProgressDetails.getProcessDetails().getProcessId());
    // TODO: What to set in Process Attributes?
    processDetail.setDefinitionDetail(definitionDetail);
    processDetail.setOwnerId(
        String.valueOf(activityProgressDetails.getProcessDetails().getOwnerId()));

    // Setting Activity Runtime Level Metadata
    activityRuntime.setId(activityProgressDetails.getId());
    activityRuntime.setActivityStatus(activityProgressDetails.getStatus());
    activityRuntime.setId(activityProgressDetails.getId());
    // setting audit object
    activityRuntime.setAudit(simpleAudit);
    activityRuntime.setProcessDetail(processDetail);
    activityRuntime.setAttributes(attribute);

    // Setting Meta Data
    Metadata metadata = new Metadata();
    metadata.setCreated(simpleAudit.getCreatedDate());
    metadata.setUpdated(simpleAudit.getLastModifiedDate());
    // Setting owner id from template details table
    EntityRef ref = new EntityRef();
    ref.setId(String.valueOf(activityProgressDetails.getProcessDetails().getOwnerId()));
    metadata.setCreatedByUser(ref);
    metadata.setUpdatedByUser(ref);
    activityRuntime.setMeta(metadata);

    // Setting Process id  as correlation id
    activityRuntime.setCorrelationId(
        MultiStepUtil.fetchParentProcessDetails(activityProgressDetails.getProcessDetails())
            .getProcessId());
    activityRuntime.setAccessType(EXTERNAL_ACCESS_TYPE);
    activityRuntime.setGroup(false);

    // Setting End Time. An activity can support both start and end events or either of the events
    // exclusively. Hence, setting the last updated time here
    activityRuntime.setEndTime(
        Objects.nonNull(activityProgressDetails.getUpdatedTime())
            ? activityProgressDetails.getUpdatedTime().toInstant().atZone(UTC_ZONE)
            : ZonedDateTime.now(UTC_ZONE));
    return activityRuntime;
  }

  private static DefinitionDetail buildDefinitionDetail(DefinitionDetails definitionDetails) {
    DefinitionDetail definitionDetail = new DefinitionDetail();
    definitionDetail.setDefinitionId(definitionDetails.getDefinitionId());
    definitionDetail.setDefinitionName(definitionDetails.getDefinitionName());
    return definitionDetail;
  }


  /**
   * Converts the json string of lookupKeys to a list of lookupKeys
   * Used for the conversion from DefinitionDetails to Definition
   * "{'envelopeId':'1234','customerId':'1234}" -> [{key:"envelopeId", value:"1234"}, {key:"customerId", value:"1234"}]
   * @param lookupKeys
   * @return List of LookupKeys
   */
  public static Optional<List<LookUpKey>> getListOfLookupKeysFromString(String lookupKeys){
    if(StringUtils.isEmpty(lookupKeys))
      return Optional.empty();
    Map<String, String> lookupKeyMap = ObjectConverter.fromJson(lookupKeys, Map.class);
    if(CollectionUtils.isEmpty(lookupKeyMap))
      return Optional.empty();
    List<LookUpKey> lookupList = lookupKeyMap.entrySet().stream().map(e -> createLookupKeyInstance(e.getKey(), e.getValue()))
        .collect(Collectors.toList());
    return Optional.of(lookupList);
  }

  /**
   * pseudo constructor for LookupKey which is a part of Data map schema
   */
  private static LookUpKey createLookupKeyInstance(String key, Object value) {
    LookUpKey lookupKey = new LookUpKey();
    lookupKey.setKey(key);
    lookupKey.setValue(value.toString());
    return lookupKey;
  }

  /**
   * This method will get runTimeAttributes and modelAttributes from activity progress details and create a list of attributes from them
   * @param activityProgressDetails
   * @return
   */
  private static List<Attribute> prepareAttributes(ActivityProgressDetails activityProgressDetails) {

    String attributes = activityProgressDetails.getAttributes();
    if (StringUtils.isEmpty(attributes)) return Collections.emptyList();
    Map<String, Object> activityAttributes =
        ObjectConverter.fromJson(attributes, new TypeReference<Map<String, Object>>() {});

    addUserVariablesInRunTimeAttributes(activityProgressDetails, activityAttributes);

    return createAttributeListFromMap(activityAttributes);
  }


  /**
   * This method will get the map of user variables stored in placeholders associated with the given activity
   * @param activityProgressDetails
   * @return
   */
  private Map<String, String> getUserPlaceholderAttributes(ActivityProgressDetails activityProgressDetails, Map<String, String> runTimeAttributes){
    return PlaceholderUserVariableExtractors.getHandler(PlaceholderUserVariableExtractorUtil.getPlaceholderExtractor(runTimeAttributes)).getUserVariablesForActivity(activityProgressDetails, runTimeAttributes);
  }

  /**
   * This method will add user placeholder values in runtime attributes
   * @param activityProgressDetails
   * @param activityAttributes
   */
  private void addUserVariablesInRunTimeAttributes(ActivityProgressDetails activityProgressDetails, Map<String, Object> activityAttributes){

    Map<String, Object> attributeMap = ObjectConverter.convertObject(activityAttributes.get(WorkflowConstants.RUNTIME_ATTRIBUTES), new TypeReference<Map<String, Object>>() {});

    if(MapUtils.isEmpty(attributeMap)){
      WorkflowLogger.logInfo("No Runtime attributes found for activityId=%s processId=%s", activityProgressDetails.getId(), activityProgressDetails.getProcessDetails().getProcessId());
      return;
    }

    //Process variables can be of any type (Map or List) (Used in multi-level), explicitly convert them to json string for value resolution logic
    Map<String, String> runTimeAttributes = attributeMap.entrySet().stream()
            .collect(
                    Collectors.toMap(entry -> entry.getKey(),
                            entry -> ((entry.getValue() instanceof String) ?
                                    entry.getValue().toString() :
                                    Optional.ofNullable(ObjectConverter.toJson(entry.getValue())).orElse(""))));

    Map<String,String> userPlaceholderAttributes = getUserPlaceholderAttributes(activityProgressDetails, runTimeAttributes);

    if(MapUtils.isNotEmpty(userPlaceholderAttributes)){
      userPlaceholderAttributes.entrySet().stream()
          .filter(entry -> !(runTimeAttributes.containsKey(entry.getKey())))
          .forEach(entry -> runTimeAttributes.put(entry.getKey(), entry.getValue()));
      //remove null values from runtime attributes
      runTimeAttributes.values().removeAll(Collections.singleton(null));
      //resolve user placeholder values with runTimeAttributes
      // e.g. taskName = Task for Invoice [[DocNumber]], replace this value with actual DocNumber present in runTimeAttributes
      Map<String, String> substitutedRuntimeAttributes = SubstitutePlaceholderUtil.substitutePlaceholder(runTimeAttributes, false);
      //overwrite runTimeAttributes with substituted values
      runTimeAttributes.putAll(substitutedRuntimeAttributes);
      activityAttributes.put(WorkflowConstants.RUNTIME_ATTRIBUTES, runTimeAttributes);
    }
  }

  private List<Attribute> createAttributeListFromMap(Map<String, Object> attributeMap){
    if(CollectionUtils.isEmpty(attributeMap))
      return Collections.emptyList();
    return attributeMap.entrySet().stream()
        .map(
            entry -> {
              Attribute attribute = new Attribute();
              attribute.setName(entry.getKey());
              attribute.setValues(ObjectConverter.toJson(entry.getValue()));
              return attribute;
            })
        .collect(Collectors.toList());
  }

}

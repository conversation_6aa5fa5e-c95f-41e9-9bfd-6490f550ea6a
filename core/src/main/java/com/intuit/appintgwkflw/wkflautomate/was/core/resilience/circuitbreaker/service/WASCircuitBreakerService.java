package com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.service;

import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WASCircuitBreakerRegistryConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.handler.CircuitBreakerActionHandlers;
import com.intuit.appintgwkflw.wkflautomate.was.core.resilience.circuitbreaker.helpers.WASCircuitBreakerHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OfferingConfig;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CircuitBreakerActionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ResiliencyConstants;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class WASCircuitBreakerService {
    private final WASCircuitBreakerConfiguration wasCircuitBreakerConfiguration;
    private final WASCircuitBreakerHelper wasCircuitBreakerHelper;
    private final OfferingConfig offeringConfig;
    private Map<CircuitBreaker, CircuitBreakerActionType> circuitBreakerToActionMap = new HashMap<>();

    /**
     * Runs only once to create circuit breakers for each instance in config
     */
    public void createCircuitBreakers() {
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Creating circuit breakers");
        Optional.ofNullable(wasCircuitBreakerConfiguration.getInstances())
                .ifPresent(instances -> instances.forEach(this::createCircuitBreaker));
    }

    /**
     * Create circuit breaker/circuit breakers for each instance, depending on whether the instance is offering-specific.
     *
     * @param cbAction the circuit breaker instance in config file - e.g. ExternalTaskCompleteEvent
     * @param wasCircuitBreakerRegistryConfig circuit breaker configurations of the instance
     */
    private void createCircuitBreaker(CircuitBreakerActionType cbAction, WASCircuitBreakerRegistryConfig wasCircuitBreakerRegistryConfig) {
        if(!wasCircuitBreakerRegistryConfig.isEnabled()) return;
        Optional<CircuitBreakerActionHandler> cbHandler = CircuitBreakerActionHandlers.getHandler(cbAction);
        if(wasCircuitBreakerRegistryConfig.isOfferingSpecific()) {
            offeringConfig
                    .getDownstreamServices()
                    .stream()
                    .forEach((offering) -> {
                        createCircuitBreakerAndRegisterEventHandler(
                                // Create circuit breaker for each offering with name '<offering><action>'
                                offering.getOfferingId().concat(cbAction.toString()),
                                wasCircuitBreakerRegistryConfig,
                                cbAction,
                                cbHandler
                        );
                    });
        }
        else {
            createCircuitBreakerAndRegisterEventHandler(
                    // Create only one circuit breaker with name '<action>', which is used for all offerings
                    cbAction.toString(), wasCircuitBreakerRegistryConfig, cbAction, cbHandler
            );
        }
    }

    /**
     * Creates circuit breaker and for each circuit breaker, registers event handler associated with the circuit breaker (if present)
     * for circuit breaker events such as circuit breaker state transition events
     *
     * @param cbName
     * @param config
     * @param cbAction
     * @param cbHandler
     */
    private void createCircuitBreakerAndRegisterEventHandler(String cbName, WASCircuitBreakerRegistryConfig config,
                                                             CircuitBreakerActionType cbAction,
                                                             Optional<CircuitBreakerActionHandler> cbHandler) {
        CircuitBreaker cb = wasCircuitBreakerHelper.createCircuitBreaker(cbName, config);
        cbHandler.ifPresent((handler) -> wasCircuitBreakerHelper.addCircuitBreakerEventHandler(cb, handler));
        circuitBreakerToActionMap.put(cb, cbAction);
    }

    /**
     * Transition to closed state and executes corresponding state transition handler
     */
    public void enableAllCircuitBreakers() {
        // Circuit breakers created for first time if not already created
        if(circuitBreakerToActionMap.isEmpty()){
            createCircuitBreakers();
        }
        // To enable the circuit breaker if it was disabled, we transition the circuit breaker to closed state as
        // the circuit breaker doesn't have an explicit ENABLED state. Transitioning from disabled to closed state
        // enables the circuit breaker.
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Closing all circuit breakers");
        wasCircuitBreakerHelper.closeCircuitBreakers(circuitBreakerToActionMap.keySet());
    }

    /**
     * Transitions to disabled state and executes corresponding state transition handler
     */
    public void disableAllCircuitBreakers() {
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Disabling all circuit breakers");
        wasCircuitBreakerHelper.disableCircuitBreakers(circuitBreakerToActionMap.keySet());
    }

    /**
     * Checks if all circuit breakers for the passed action type (e.g. ExternalTaskCompleteEvent) are closed
     *
     * @param actionType
     * @return
     */
    public boolean areAllCircuitBreakersOfActionTypeClosed(CircuitBreakerActionType actionType) {
        // Can remove these log statements once testing done
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Checking if all circuit breakers closed for actionType=%s", actionType);
        Set<CircuitBreaker> cbSet = circuitBreakerToActionMap.entrySet()
                .stream()
                .filter(cb -> cb.getValue().equals(actionType))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        return wasCircuitBreakerHelper.areAllCircuitBreakersClosed(cbSet);
    }

    /**
     * Closes all circuit breakers for the passed action type
     *
     * @param actionType
     */
    public void closeAllCircuitBreakersOfActionType(CircuitBreakerActionType actionType) {
        // Can remove these log statements once testing done
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Closing all circuit breakers of actionType=%s", actionType);
        Set<CircuitBreaker> cbSet = circuitBreakerToActionMap.entrySet()
                .stream()
                .filter(cb -> cb.getValue().equals(actionType))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        wasCircuitBreakerHelper.closeCircuitBreakers(cbSet);
    }

    /**
     * Create circuit breaker if not already present and if it is specified in config
     *
     * @param cbName
     * @return
     */
    public Optional<CircuitBreaker> getCircuitBreaker(String cbName) {
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Fetching circuit breaker.");
        return circuitBreakerToActionMap.keySet()
                .stream()
                .filter(cb -> cb.getName().equals(cbName))
                .findFirst();
    }

    /**
     * Used to get Circuit breakers that are isolated for different offerings
     * Fetch circuit breaker for the offering if present in config. Else return nothing
     *
     * @param action identifies what the method the circuit breaker is being used for
     *               is doing. E.g. ExternalTaskCompleteEvent (circuitBreakerName = ttLiveExternalTaskCompleteEvent)
     * @return
     */
    public Optional<CircuitBreaker> getCircuitBreakerForOffering(CircuitBreakerActionType action) {
        if(action == null) return Optional.empty();
        WorkflowLogger.logInfo(ResiliencyConstants.CIRCUIT_BREAKER_PREFIX + "Fetching circuit breaker for offering.");
        Optional<String> offering = WASContext.getOfferingId();
        return offering
                .map(x -> getCircuitBreaker(x.concat(action.toString())))
                .orElse(Optional.empty());
    }

}

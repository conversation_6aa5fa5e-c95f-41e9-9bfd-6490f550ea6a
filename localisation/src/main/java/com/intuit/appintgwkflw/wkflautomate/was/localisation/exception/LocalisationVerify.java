package com.intuit.appintgwkflw.wkflautomate.was.localisation.exception;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 *     <p>This class is responsible for evaluating input expression and
 *     throwing @LocalisationGeneralException with the @LocalisationError if condition satisfies.
 *     <p>These methods should be used instead of if else blocks for verifying conditions.
 */
public class LocalisationVerify {
  private LocalisationVerify(){}

  /**
   * evaluates the given input string by calling {@link StringUtils.isBlank(input)} and if that
   * returns true throws @LocalisationGeneralException with given @LocalisationError. Replaces %s
   * arguments in the input error message of LocalisationError and replaces with the
   * localisationErrorMessageArgs.
   *
   * @param input given input string.
   * @param localisationError workflow error to be thrown
   * @throws LocalisationGeneralException if expression evaluates to true.
   */
  public static void verify(
      String input, LocalisationError localisationError, Object... localisationErrorMessageArgs) {
    verifyNull(localisationError, null);
    if (StringUtils.isBlank(input)) {
      throw new LocalisationGeneralException(localisationError, localisationErrorMessageArgs);
    }
  }

  /**
   * evaluates the expression and if true throws @LocalisationGeneralException with
   * given @LocalisationError. Replaces %s arguments in the input error message of LocalisationError
   * and replaces with the localisationErrorMessageArgs.
   *
   * @param expression input expression evaluation flag
   * @param localisationError workflow error to be thrown
   * @throws LocalisationGeneralException if expression evaluates to true.
   */
  public static void verify(
      boolean expression,
      LocalisationError localisationError,
      Object... localisationErrorMessageArgs) {
    verifyNull(localisationError, null);
    if (expression) {
      throw new LocalisationGeneralException(localisationError, localisationErrorMessageArgs);
    }
  }

  /**
   * checks if given input reference is null and throws @LocalisationGeneralException with
   * INVALID_INPUT if input @LocalisationError is null else returns @LocalisationGeneralException
   * with the given input error.
   *
   * @throws LocalisationGeneralException if reference is null
   * @param reference input reference object
   * @param localisationError workflow error to be thrown
   */
  public static <T> void verifyNull(
      T reference, LocalisationError localisationError, Object... localisationErrorMessageArgs) {
    if (null == reference) {
      LocalisationError error = fetchLocalisationError(localisationError);
      throw new LocalisationGeneralException(error, localisationErrorMessageArgs);
    }
  }

  /**
   * @param localisationError localisationError error to be thrown
   * @return {@link LocalisationError}
   */
  private static LocalisationError fetchLocalisationError(final LocalisationError localisationError) {
    return null == localisationError
        ? LocalisationError.INVALID_LOCALISATION_ERROR_INPUT
        : localisationError;
  }
}

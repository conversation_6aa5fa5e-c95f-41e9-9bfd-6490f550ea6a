apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: was-event-in-rollout
spec:
  template:
    spec:
      initContainers:
      - name: ipf-config-loader
        env:
          - name: IDPS_ENDPOINT
            value: "vkm.ps.idps.a.intuit.com"
          - name: "CONFIG_ENDPOINT"
            value: "https://config.api.intuit.com/v2/waseventin/default/prd-workflow-automation/"
          - name: RUNTIME_ID
            value: "was-event-in-runtime-customer"
          - name: "IDPS_POLICY_ID"
            value: "p-c6dcpy6m6xm2"
          - name: REGISTRY_ENVIRONMENT
            value: "prd"
          - name: CONFIG_FILE_NAMES
            value: core-config.yml, pipeline-definition-customer.yml, processor-config.yml
package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.fetcher;

import static java.lang.Boolean.TRUE;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.mocks.MockHelper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.fetcher.RecordQueryConnectorResponse;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

public class AppconnectRecordListFetcherMock implements RecordListFetcher {

  private String data;
  private Random r = new Random();

  public AppconnectRecordListFetcherMock() {
    data = MockHelper.readResourceAsString("duzzitOutputs/appconnectCRResponse.json");
  }

  @Override
  public RecordQueryConnectorResponse fetchRecords(
      RecordQueryConnectorRequest recordQueryConnectorRequest) {
    WorkflowLogger.logInfo("Fetching data from mocked fetchRcordList");
    try {
      // sleeps between 500-600 ms
      Thread.sleep(r.nextInt(1500) + 3000);
    } catch (InterruptedException e) {
    }
    return fetchTransactionsResponse();
  }

  public RecordQueryConnectorResponse fetchTransactionsResponse() {
    RecordQueryConnectorResponse appConnectFetchTransactionsResponse =
        ObjectConverter.fromJson(data, RecordQueryConnectorResponse.class);
    appConnectFetchTransactionsResponse.setSuccess(TRUE.toString());
    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    Date date = new Date();
    for (Map<String, String> data : appConnectFetchTransactionsResponse.getRecordList()) {
      data.put("TxnDate", dateFormat.format(date));
      data.put("TxnDueDays", "-30"); // BF 30
      data.put("TxnAmount", "1111.00");
      data.put("Id", UUID.randomUUID().toString());
    }

    return appConnectFetchTransactionsResponse;
  }
}

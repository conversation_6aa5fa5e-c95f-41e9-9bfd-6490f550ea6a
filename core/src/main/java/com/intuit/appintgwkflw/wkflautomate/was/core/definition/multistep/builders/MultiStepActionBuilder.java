package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateActionBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.v4.GlobalId;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * Builds workflow steps with actions for multi-condition multi-step workflows
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MultiStepActionBuilder implements MultiWorkflowStepBuilder {

  private final WASContextHandler wasContextHandler;
  private final TemplateActionBuilder actionBuilder;

  /**
   * This function converts the config step to a workflowStep if the config step includes an action.
   * The constructed workflowStep will include actionGroup object with the parent-action and
   * sub-action structure This structure will be constructed from the call activity action id
   * defined in the config step
   *
   * @param record              record
   * @param actionKey           action key
   * @param isPreCannedTemplate whether template is custom or precanned
   * @param currentConfigStep   current config step which will be used to construct the
   *                            workflowStep
   * @param path                next path object if previous workflowStep
   * @param configStepIdMap     map of stepId and step
   */
  @Override
  public WorkflowStep processWorkflowStep(
      Record record,
      String actionKey,
      Boolean isPreCannedTemplate,
      Steps currentConfigStep,
      WorkflowStep.StepNext path,
      Map<Integer, Steps> configStepIdMap) {
    WorkflowStep actionStep = new WorkflowStep();
    WorkflowLogger.logInfo("step=buildActionStep step=%s, stepType=%s",
        currentConfigStep.getStepId(), currentConfigStep.getStepType());

    // filter out the action group whose id machtes the actionkey and it should
    // also have an actionIdMapper object defined i.e call activity
    ActionGroup recordActionGroup = record.getActionGroups().stream().filter(actionGroup ->
            Objects.nonNull(actionKey) && actionGroup.getId().equals(actionKey)
                && Objects.nonNull(actionGroup.getActionIdMapper())
                && Objects.nonNull(actionGroup.getActionIdMapper().getActionId())).findFirst()
        .orElse(null);

    // if we don't find any actionGroup with a call activity defined then we won't
    // have anything to populate in the workflowStep, so throw an exception here
    if (Objects.isNull(recordActionGroup)) {
      WorkflowLogger.logWarn(
          "actionIdMapper not found for actionkey=%s in config record=%s isPrecanned=%s actionGroup=%s",
          actionKey, record, isPreCannedTemplate, recordActionGroup);
      throw new WorkflowGeneralException(
          WorkflowError.ACTION_ID_MAPPER_NOT_FOUND_IN_TEMPLATE_CONFIG);
    }
    com.intuit.v4.workflows.Action action = actionBuilder.buildTemplateStepAction(
        record,
        recordActionGroup,
        recordActionGroup.getActionIdMapper().getActionId());

    com.intuit.v4.workflows.ActionGroup stepActionGroup = new com.intuit.v4.workflows.ActionGroup();
    stepActionGroup.setActionKey(recordActionGroup.getId());
    stepActionGroup.setAction(action);
    actionStep.setStepType(StepTypeEnum.ACTION);
    actionStep.setActionGroup(stepActionGroup);
    // workflowStepId for action steps will include the call activity's parent action id
    // ex: sendForApproval is the call activity, and it will also be used as is to construct
    // the workflowSteps globalId
    actionStep.setId(
        GlobalId.builder()
            .setRealmId(wasContextHandler.get(WASContextEnums.OWNER_ID))
            .setTypeId(actionStep.getTypeId())
            .setLocalId(recordActionGroup.getActionIdMapper().getActionId()
                .concat(WorkflowConstants.HYPHEN)
                .concat(Integer.toString(currentConfigStep.getStepId())))
            .build());
    // if path is passed then set the current constructed stepId as the
    // path's setNextWorkflowStepId. Previous step's next id's should match
    // the current workflowSteps id.
    if (Objects.nonNull(path)) {
      path.setWorkflowStepId(actionStep.getId().toString());
    }
    actionStep.setRequired(true);
    return actionStep;
  }
}

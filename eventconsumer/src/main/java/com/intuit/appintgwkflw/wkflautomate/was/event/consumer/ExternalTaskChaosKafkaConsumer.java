package com.intuit.appintgwkflw.wkflautomate.was.event.consumer;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.EventListener;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.EventingLoggerUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.eventHandler.ExternalTaskEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.consumer.processor.EntityHandler;
import com.intuit.appintgwkflw.wkflautomate.was.event.util.EventUtil;
import com.intuit.eventbus.exceptions.FormatException;
import com.intuit.eventbus.utils.Result;
import lombok.AllArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * Used in FMEA tests to test circuit breaker for ExternalTaskEventHandler using unique Kafka topic for chaos pod
 */
@Component
@AllArgsConstructor
@ConditionalOnExpression(
    "${event.consumer.enabled:false} && '${spring.profiles.active}'.contains('chaos-monkey') && ${circuitbreaker.enabled:false} && ${circuitbreaker.instances.ExternalTaskCompleteEvent.enabled: false}")
public class ExternalTaskChaosKafkaConsumer {
  private final ExternalTaskEventHandler externalTaskEventHandler;

  @PostConstruct
  public void init() {
    EventingLoggerUtil.logInfo(
            "Initializing Chaos Kafka Consumer for external task", this.getClass().getSimpleName());
  }

  /**
   * Receive an event and manually commit i.e. acknowledge Kafka offset.
   *
   * @param consumerRecord consumed record
   * @param messageHeaders message headers
   * @param acknowledgment manual acknowledge-er
   */
  @KafkaListener(
          topics = "#{eventConsumerUtil.getFilteredTopicList('externalTaskChaos',false)}",
          groupId = "${event.consumer.groupId}",
          containerFactory = "kafkaListenerFactory")
  @EventListener(name = MetricName.EVENT_EXTERNAL_TASK, type = Type.EVENT_METRIC)
  public void receive(
          ConsumerRecord<String, Result<FormatException, String>> consumerRecord,
          @Headers MessageHeaders messageHeaders,
          Acknowledgment acknowledgment) {
    EventingLoggerUtil.logDebug(
            "Reading message in ExternalTaskChaosKafkaConsumer", this.getClass().getSimpleName());
    Map<String, String> headers = EventUtil.transformHeader(messageHeaders);

    externalTaskEventHandler.transformAndExecute(consumerRecord.value().get(), headers);
  }
}
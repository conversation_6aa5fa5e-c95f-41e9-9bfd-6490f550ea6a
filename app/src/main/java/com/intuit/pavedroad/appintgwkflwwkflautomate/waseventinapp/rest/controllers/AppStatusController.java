package com.intuit.pavedroad.appintgwkflwwkflautomate.waseventinapp.rest.controllers;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class AppStatusController {

  @GetMapping("/health/full")
  public ResponseEntity<String> healthCheck() {
    return new ResponseEntity<>("Health Check Ok", HttpStatus.OK);
  }
}
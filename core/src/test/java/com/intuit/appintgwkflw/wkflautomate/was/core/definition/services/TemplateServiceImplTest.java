package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.AppConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DomainEventConfig;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.WorkflowGlobalConfiguration;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.tags.SystemTags;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.bpmnengineadapter.BPMNEngineDefinitionServiceRest;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.ClientAccessConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.config.TemplateSaveUpdateConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.MultiStepTemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.builders.TemplateBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.TemplateLabelsService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.TemplateServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.SingleDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.SystemDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.TemplateHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.processor.UserDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator.TemplateValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.domainEvent.impl.TemplateDomainEventHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateFilters;
import com.intuit.appintgwkflw.wkflautomate.was.core.filters.template.TemplateNameFilter;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.config.CustomWorkflowConfig;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.CustomWorkflowUtil;
import com.intuit.appintgwkflw.wkflautomate.was.core.workflowAI.services.WFAIService;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.WorkflowTaskConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityDetail;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TriggerDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.ActivityDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TriggerDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionGroup;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ActionIdMapper;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Attribute;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.ConfigTemplate;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Next;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Record;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.handlers.schema.Steps;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.CreatorType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.TemplateCategory;
import com.intuit.appintgwkflw.wkflautomate.was.entity.http.WASHttpResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowGenericResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.WorkflowTemplateResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.template.TemplateMetadata;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.appintgwkflw.wkflautomate.was.workflowvariability.VariabilityEngineService;
import com.intuit.async.execution.request.State;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.RequestContext;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.query.FilterExpression;
import com.intuit.v4.workflows.NextLabelEnum;
import com.intuit.v4.workflows.StepTypeEnum;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.definitions.NextTypeEnum;
import java.nio.charset.Charset;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.dmn.Dmn;
import org.camunda.bpm.model.dmn.DmnModelInstance;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.SYSTEM_TAG;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.response.ResponseStatus.SUCCESS;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@Import(TemplateServiceImpl.class)
@RunWith(SpringRunner.class)
public class TemplateServiceImplTest {

    private static final String INVOICE_APPROVAL_BPMN = "src/test/resources/bpmn/invoiceapproval.bpmn";
    private static final String TTLIVE_BPMN = "src/test/resources/bpmn/ttlive.bpmn";
    private static final String INVOICE_APPROVAL_ONLY_BPMN = "src/test/resources/bpmn/InvoiceApproval_onlyBpmn.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_FAIL = "src/test/resources/bpmn/InvoiceApproval_Failed.bpmn";
    private static final String INVOICE_APPROVAL_DMN = "src/test/resources/dmn/decision_invoiceapprovalv1.3.dmn";
    private static final String INVOICE_APPROVAL_DMN_EXTRA = "src/test/resources/dmn/invoiceSendDecisionExtra.dmn";
    private static final String INVOICE_APPROVAL_DMN_DEFAULTRULE = "src/test/resources/dmn/decision_invoiceapproval_defaultRule.dmn";
    private static final String CONTENT_TYPE = "application/octet-stream";
    private static final String INVOICE_APPROVAL_BPMN_INVALID = "src/test/resources/bpmn/invoiceapproval.bpmnL";
    private static final String INVOICE_APPROVAL_DMN_INVALID = "src/test/resources/bpmn/invoiceapproval.bpmnL";
    private static final String INVOICE_APPROVAL_TAG_BPMN = "src/test/resources/bpmn/invoiceApprovalWithSystemTag.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_INVALID_NO_STEP_INFO = "src/test/resources/bpmn/invoiceapprovalStepDetailsMissing.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_NONDUPLICATE_TRIGGER_NAMES = "src/test/resources/bpmn/invoiceapproval_nonduplicate_trigger_names.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_V1 = "bpmn/invoiceapproval.bpmn";
    private static final String INVOICE_APPROVAL_BPMN_UPDATED_DECISION_REF = "bpmn/invoiceApprovalUpdatedDecisionRef.bpmn";
    private static final String INVOICE_APPROVAL_DMN_V1 = "dmn/decision_invoiceapproval.dmn";
    private static final String INVOICE_APPROVAL_DMN_UPDATED_ID = "dmn/decision_invoiceapproval_realmId.dmn";
    private static final String OFFER_ID = "offerId";
    private static final String OWNER_ID = "1234";
    private static final String CREATED_BY = "1234";
    private static final String REALM_ID = "realmId";
    private static final String LOCAL_ID = "localId";
    private static final String WORKITEM_BPMN = "src/test/resources/bpmn/WorkItem.bpmn";
    private static final String MULTI_CONDITION_WORKFLOW_BPMN_XML = "bpmn/customApproval_multiCondition.bpmn";
    private static final String SEND_FOR_APPROVAL_CHILD_BPMN_XML = "bpmn/sendForApproval.bpmn";
    private static final String QBLiveLiteCleanupOffboarding_bpmn_xml = "bpmn/QBLiveLiteCleanupOffboarding.bpmn";
    private static final String MULTI_CONDITION_WORKFLOW_INVALID_CALL_ACTIVITY_BINDING = "bpmn/customApproval_multiCondition_invalidCallActivityBinding.bpmn";
    private static final String MULTI_CONDITION_WORKFLOW_MISSING_INPUT_OUTPUT_IN_CALL_ACTIVITY = "bpmn/customApproval_multiCondition_missingInputOutputInCallActivity.bpmn";
    private static final String MULTI_CONDITION_WORKFLOW_WITH_MISSING_ROOT_PROCESS_INSTANCE_ID = "bpmn/customApproval_multiCondition_missingRootProcessInstanceId.bpmn";
    private static final String MULTI_CONDITION_WORKFLOW_WITH_DUPLICATE_PARENT_CHILD_TRIGGER_NAMES = "bpmn/customApproval_multiCondition_duplicateParentChildTriggerNames.bpmn";
    private static final String ASYNC_BEFORE_NOT_SET = "src/test/resources/bpmn/asyncBeforeNotSet.bpmn";

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();
    @Autowired
    private TemplateServiceImpl templateService;
    @MockBean
    private TemplateDetailsRepository templateDetailsRepository;
    @MockBean
    private TriggerDetailsRepository triggerDetailsRepository;
    @MockBean
    private WorkflowGlobalConfiguration workflowGlobalConfiguration;
    @MockBean
    private ReadCustomDefinitionHandler readCustomDefinitionHandler;
    @MockBean
    private WASContextHandler contextHandler;
    @MockBean
    private AuthDetailsServiceHelper authDetailsServiceHelper;
    @MockBean
    private ProviderHelper providerHelper;
    @MockBean
    private BPMNEngineDefinitionServiceRest bpmnEngineDefinitionServiceRest;
    @MockBean
    private SingleDefinitionProcessor singleDefinitionProcessor;
    @MockBean
    private SystemDefinitionProcessor systemDefinitionProcessor;
    @MockBean
    private UserDefinitionProcessor userDefinitionProcessor;
    @MockBean(name = WorkflowBeansConstants.TEMPLATE_BUILDER)
    private TemplateBuilder templateBuilder;
    @MockBean
    private TemplateValidator templateValidator;
    @MockBean
    private TemplateDomainEventHandler templateDomainEventHandler;
    @Spy
    private TemplateNameFilter templateNameFilter;
    @MockBean
    private WorkflowTaskConfig workflowTaskConfig;
    @MockBean
    private DomainEventConfig domainEventTopiConfig;
    @MockBean
    private FeatureFlagManager featureFlagManager;
    @MockBean
    private ClientAccessConfig clientAccessConfig;
    @MockBean
    private WFAIService wfaiService;
    @MockBean
    private AppConfig appConfig;
    @Autowired
    private TranslationService translationService;
    @MockBean
    private TemplateLabelsService templateLabelsService;
    @MockBean
    @Qualifier(WorkflowBeansConstants.MULTI_STEP_TEMPLATE_BUILDER)
    private MultiStepTemplateBuilder multiStepTemplateBuilder;
    @MockBean
    private CustomWorkflowConfig customWorkflowConfig;
    private String sampleAuth =
            "Intuit_APIKey intuit_token_type=\"IAM-Ticket\",intuit_userid=\"9130347714346276\",intuit_apkey_version=\"1.0\"";
    private String intuitTid = "intuit_tid";
    private File bpmnFile = new File(INVOICE_APPROVAL_BPMN);

    private FileInputStream fisBpmn;
    @Autowired
    private BpmnProcessorImpl bpmnProcessor;
    @MockBean
    private VariabilityEngineService variabilityEngineService;

    @MockBean
    private ActivityDetailsRepository activityDetailsRepository;

    /**
     * @param fileName
     * @return : BPMN Model Instance
     */
    private static BpmnModelInstance readBPMNFile(String fileName) {
        return Bpmn.readModelFromStream(
                TemplateServiceImplTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    /**
     * @param fileName
     * @return : DMN Model Instance
     */
    private static DmnModelInstance readDMNFile(String fileName) {
        return Dmn.readModelFromStream(
                TemplateServiceImplTest.class.getClassLoader().getResourceAsStream(fileName));
    }

    @Before
    public void setUp() throws FileNotFoundException {
        populateTemplateHandler();
        TemplateFilters.add(templateNameFilter);
        Authorization authContext = new Authorization();
        authContext.put(WorkflowConstants.APP_ID, "test");
        WASContext.setAuthContext(authContext);
        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
        fisBpmn = new FileInputStream(bpmnFile);
    }

    @After
    public void UnsetAuthContext() {
        WASContext.clear();
    }

    @Test
    public void testSaveTemplateSuccess_ForUser() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };

        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(domainEventTopiConfig.isEnabled()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verifyNoInteractions(bpmnEngineDefinitionServiceRest);
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateSuccess_ForSingle() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SINGLE, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(singleDefinitionProcessor.executeAction(Mockito.any(), Mockito.any()))
                .thenReturn(new State());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(singleDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateSuccess_ForSystem() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();

        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateSuccess_with_HumanAndSystemTask() throws Exception {
        File bpmnFile = new File(WORKITEM_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.USER, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .definitionType(DefinitionType.USER)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(userDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceWorkTaskA");
    }

    @Test
    public void shouldThrowExceptionAsynBeforeNotSetStartEventUpdate() throws IOException {
        File bpmnFile = new File(ASYNC_BEFORE_NOT_SET);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);

        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        try {
            templateService.updateTemplate(templates, mockTemplateMetadata);
            Assert.fail();
        } catch (WorkflowGeneralException workflowGeneralException) {
            Assert.assertEquals(
                    WorkflowError.ASYNC_BEFORE_NOT_SET, workflowGeneralException.getWorkflowError());
        }
    }

    @Test
    public void testSaveTemplateSuccess_BoundaryEvent() throws Exception {
        File bpmnFile = new File(TTLIVE_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.HIDDEN,
                false, false,  false ,null, false);

        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
        };
        TemplateDetails templateDetails =
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);

        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap = new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition = new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "engagementvepfstest1");
    }

    @Test
    public void testSaveTemplateSuccess_ForSystemUpdate() throws Exception {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.SYSTEM,
                false, false,  false ,null, false);

        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(systemDefinitionProcessor.executeAction(Mockito.any(), Mockito.any()))
                .thenReturn(new State());

        TemplateDetails templateDetail =
                TemplateDetails
                        .builder()
                        .id("id")
                        .createdByUserId(Long.valueOf(WorkflowConstants.SYSTEM_OWNER_ID))
                        .definitionType(DefinitionType.SYSTEM)
                        .build();
        when(templateDetailsRepository.findTopByTemplateNameOrderByVersionDesc(Mockito.any()))
                .thenReturn(Optional.of(templateDetail));
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateValidationException() throws IOException {
        File bpmnFileFailed = new File(INVOICE_APPROVAL_BPMN_FAIL);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFileFailed);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFileFailed.getName(),
                        bpmnFileFailed.getName(),
                        CONTENT_TYPE,
                        IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        exceptionRule.expect(Exception.class);
        templateService.saveTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testSaveTemplateOnlyBpmnSuccess() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateNoDmn() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.MISSING_DMN.getErrorMessage());
        templateService.saveTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testSaveTemplateNoBPMN() throws IOException {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.NO_BPMN_FILE.getErrorMessage());
        templateService.saveTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testSaveTemplateExtraDMN() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        File extraDmnFile = new File(INVOICE_APPROVAL_DMN_EXTRA);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        FileInputStream fisDmnExtra = new FileInputStream(extraDmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn)),
                new MockMultipartFile(
                        extraDmnFile.getName(),
                        extraDmnFile.getName(),
                        CONTENT_TYPE,
                        IOUtils.toByteArray(fisDmnExtra))
        };
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.EXTRA_DMN.getErrorMessage());
        templateService.saveTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testSaveExistingTemplate() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.TEMPLATE_ALREADY_EXISTS.getErrorMessage());
        templateService.saveTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testUpdateTemplateSuccess() throws Exception {
        populateTemplateHandler();
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).saveAndFlush(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse updateResponse =
                templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, updateResponse.getStatus());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testUpdateTemplateFailure_DuplicateTriggerNames() throws Exception {
        populateTemplateHandler();
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).saveAndFlush(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.updateTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testUpdateTemplateSuccess_NonDuplicateTriggerNames() throws Exception {
        populateTemplateHandler();
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN_NONDUPLICATE_TRIGGER_NAMES);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();

        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).saveAndFlush(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse updateResponse =
                templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, updateResponse.getStatus());
    }

    @Test
    public void testUpdateTemplateFailure_DefinitionTypeChange() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.HIDDEN,
                false, false, false ,null, false);
        MultipartFile[] templates = {
                new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.DEFINITION_TYPE_UPDATE_ERROR.getErrorMessage());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.updateTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testUpdateTemplate_DefinitionTypeChangeFromUserToSingle() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata( CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.USER, TemplateCategory.HIDDEN, false, false, false, null, false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails =
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .definitionType(DefinitionType.SINGLE)
                        .build();

        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn("1111").when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        WorkflowGenericResponse workflowGenericResponse = templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(workflowGenericResponse.getStatus(), SUCCESS);
    }

    @Test
    public void testUpdateTemplate_DefinitionTypeChangeFromSingleToUser() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SINGLE, TemplateCategory.HIDDEN, false, false, false, null, false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails =
                TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .definitionType(DefinitionType.USER)
                        .build();

        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn("1111").when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        WorkflowGenericResponse workflowGenericResponse = templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(workflowGenericResponse.getStatus(), SUCCESS);
    }

    @Test
    public void testUpdateTemplateWithNonExistingTemplate() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.TEMPLATE_DOES_NOT_EXIST.getErrorMessage());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.updateTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testFetchTemplateSuccess() throws Exception {
        String bpmnFileName = "invoiceapproval.bpmn";
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName("invoiceapproval")
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(Optional.empty()).when(templateDetailsRepository).findByParentId(any());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.fetchTemplates(bpmnFileName, "archive.zip");
        File file = new File("archive.zip");
        file.deleteOnExit();
        Assert.assertTrue(file.exists());
    }

    @Test
    public void testFetchTemplateInvalidFileType() {
        String invalidFileName = "InvoiceApproval.dmn";
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        templateService.fetchTemplates(invalidFileName, "");
    }

    @Test
    public void testFetchTemplateNoBPMNFileExist() {
        String bpmnFileName = "InvoiceApproval.bpmn";
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(bpmnFileName);
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.BPMN_DOESNT_EXIST.getErrorMessage());
        templateService.fetchTemplates(bpmnFileName, "");
    }

    @Test
    public void testFetchTemplateByVersionSuccess() throws Exception {
        String bpmnFileName = "invoiceapproval.bpmn";
        int templateVersion = 1;
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName("invoiceapproval")
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .deployedDefinitionId("123")
                        .build();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findByTemplateNameAndVersion(any(), anyInt());
        BpmnResponse bpmnResponse = BpmnResponse.builder()
                .bpmn20Xml("bpmnxml")
                .build();
        WASHttpResponse<BpmnResponse> httpResponse = WASHttpResponse.<BpmnResponse>builder()
                .isSuccess2xx(true)
                .response(bpmnResponse)
                .build();
        Mockito.doReturn(httpResponse)
                .when(bpmnEngineDefinitionServiceRest)
                .getBPMNXMLDefinition(any());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse response = templateService.fetchTemplateByVersion(bpmnFileName, templateVersion);
        Assert.assertEquals("bpmnxml", ((WorkflowTemplateResponse) response.getResponse()).getResponse());
    }

    @Test
    public void testFetchTemplateByVersionCamundaErrorResponse() throws Exception {
        String bpmnFileName = "invoiceapproval.bpmn";
        int templateVersion = 1;
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName("invoiceapproval")
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .deployedDefinitionId("123")
                        .build();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findByTemplateNameAndVersion(any(), anyInt());
        WASHttpResponse<BpmnResponse> httpResponse = WASHttpResponse.<BpmnResponse>builder()
                .isSuccess2xx(false)
                .build();
        Mockito.doReturn(httpResponse)
                .when(bpmnEngineDefinitionServiceRest)
                .getBPMNXMLDefinition(any());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.TEMPLATE_READ_EXCEPTION.getErrorMessage());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.fetchTemplateByVersion(bpmnFileName, templateVersion);
    }

    @Test
    public void testFetchTemplateByVersionCamundaEmptyResponse() throws Exception {
        String bpmnFileName = "invoiceapproval.bpmn";
        int templateVersion = 1;
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName("invoiceapproval")
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .deployedDefinitionId("123")
                        .build();
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findByTemplateNameAndVersion(any(), anyInt());
        WASHttpResponse<BpmnResponse> httpResponse = WASHttpResponse.<BpmnResponse>builder()
                .isSuccess2xx(true)
                .response(null)
                .build();
        Mockito.doReturn(httpResponse)
                .when(bpmnEngineDefinitionServiceRest)
                .getBPMNXMLDefinition(any());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.EMPTY_BPMN_EXCEPTION.getErrorMessage());
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        templateService.fetchTemplateByVersion(bpmnFileName, templateVersion);
    }

    @Test
    public void testFetchTemplateByVersionBlankFileName() {
        String invalidFileName = "";
        int templateVersion = 1;
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        templateService.fetchTemplateByVersion(invalidFileName, templateVersion);
    }

    @Test
    public void testFetchTemplateByVersionInvalidFileType() {
        String invalidFileName = "InvoiceApproval.dmn";
        int templateVersion = 1;
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        templateService.fetchTemplateByVersion(invalidFileName, templateVersion);
    }

    @Test
    public void testFetchTemplateByVersionInvalidTemplateVersion() {
        String bpmnFileName = "InvoiceApproval.bpmn";
        int invalidTemplateVersion = -1;
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findByTemplateNameAndVersion(bpmnFileName, invalidTemplateVersion);
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        templateService.fetchTemplateByVersion(bpmnFileName, invalidTemplateVersion);
    }

    @Test
    public void testFetchTemplateByVersionNotSystemDefinition() {
        String bpmnFileName = "InvoiceApproval.bpmn";
        int templateVersion = 2;
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findByTemplateNameAndVersion(bpmnFileName, templateVersion);
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.ONLY_SYSTEM_DEFINITION_IS_SUPPORTED.getErrorMessage());
        templateService.fetchTemplateByVersion(bpmnFileName, templateVersion);
    }

    @Test
    public void fetchTemplateSuccess() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        List<TemplateDetails> dmnDetails = Collections.singletonList(
                        TemplateDetails.builder()
                                .id(UUID.randomUUID().toString())
                                .recordType(RecordType.INVOICE)
                                .templateName(bpmnFile.getName())
                                .modelType(ModelType.BPMN)
                                .createdByUserId(Long.parseLong(CREATED_BY))
                                .creatorType(CreatorType.SYSTEM)
                                .offeringId(OFFER_ID)
                                .recordType(RecordType.INVOICE)
                                .ownerId(Long.parseLong(OWNER_ID))
                                .version(1)
                                .parentId(null)
                                .templateData(IOUtils.toByteArray(fisBpmn))
                                .build());

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(any());
        Mockito.doReturn(Optional.of(dmnDetails))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(any(), Mockito.anyInt());

        BpmnModelInstance bpmnModelInstance =
                TemplateServiceImplTest.readBPMNFile(INVOICE_APPROVAL_BPMN_V1);
        Assert.assertNotNull(bpmnModelInstance);
        DmnModelInstance dmnModelInstance =
                TemplateServiceImplTest.readDMNFile(INVOICE_APPROVAL_DMN_V1);
        Assert.assertNotNull(dmnModelInstance);
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);
        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
        Template template =
                (Template) bpmnProcessor.processBpmn(definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID), true);

        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
    }

    @Test
    public void fetchTemplateSuccessWithUpdatedId() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .recordType(RecordType.INVOICE)
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        List<TemplateDetails> dmnDetails = Collections.singletonList(buildTemplateDetails());

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(any());
        Mockito.doReturn(Optional.of(dmnDetails))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(any(), Mockito.anyInt());

        BpmnModelInstance bpmnModelInstance =
                TemplateServiceImplTest.readBPMNFile(INVOICE_APPROVAL_BPMN_V1);
        Assert.assertNotNull(bpmnModelInstance);
        DmnModelInstance dmnModelInstance =
                TemplateServiceImplTest.readDMNFile(INVOICE_APPROVAL_DMN_UPDATED_ID);
        Assert.assertNotNull(dmnModelInstance);
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);
        Template multiStepTemplate = new Template();
        multiStepTemplate.setName("customApproval");
        multiStepTemplate.setRecordType(RecordType.INVOICE.getRecordType());
        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(multiStepTemplate);
        Template template = (Template) bpmnProcessor.processBpmn(definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID), true);

        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
    }

    @Test(expected = NullPointerException.class)
    public void fetchTemplateSuccessWithMismatchedId() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        List<TemplateDetails> dmnDetails = Collections.singletonList(
                        TemplateDetails.builder()
                                .id(UUID.randomUUID().toString())
                                .templateName(bpmnFile.getName())
                                .modelType(ModelType.BPMN)
                                .createdByUserId(Long.parseLong(CREATED_BY))
                                .creatorType(CreatorType.SYSTEM)
                                .offeringId(OFFER_ID)
                                .recordType(RecordType.INVOICE)
                                .ownerId(Long.parseLong(OWNER_ID))
                                .version(1)
                                .parentId(null)
                                .templateData(IOUtils.toByteArray(fisBpmn))
                                .build());

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(any());
        Mockito.doReturn(Optional.of(dmnDetails))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(any(), Mockito.anyInt());

        BpmnModelInstance bpmnModelInstance =
                TemplateServiceImplTest.readBPMNFile(INVOICE_APPROVAL_BPMN_UPDATED_DECISION_REF);
        Assert.assertNotNull(bpmnModelInstance);
        DmnModelInstance dmnModelInstance =
                TemplateServiceImplTest.readDMNFile(INVOICE_APPROVAL_DMN_V1);
        Assert.assertNotNull(dmnModelInstance);
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);
        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
        Template template = (Template) bpmnProcessor.processBpmn(definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID), true);

        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
    }

    @Test
    public void fetchTemplateWithDefaultRuleSuccess() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        List<TemplateDetails> dmnDetails = Collections.singletonList(buildTemplateDetails());

        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(any());
        Mockito.doReturn(Optional.of(dmnDetails))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(any(), Mockito.anyInt());
        File dmnFile = new File(INVOICE_APPROVAL_DMN_DEFAULTRULE);
        BpmnModelInstance bpmnModelInstance =
                TemplateServiceImplTest.readBPMNFile(INVOICE_APPROVAL_BPMN_V1);
        Assert.assertNotNull(bpmnModelInstance);
        DmnModelInstance dmnModelInstance = Dmn.readModelFromStream(new FileInputStream(dmnFile));
        Assert.assertNotNull(dmnModelInstance);
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);

        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
        Template template = (Template) bpmnProcessor.processBpmn(definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID), true);
        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
        Assert.assertNotNull(
                template.getWorkflowSteps().get(0).getWorkflowStepCondition().getRuleLines());
    }

    @Test
    public void testEnableTemplateSuccess() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        WorkflowGenericResponse enableTemplateResponse =
                templateService.updateTemplateStatus(bpmnFile.getName(), "enabled");
        Assert.assertEquals(SUCCESS, enableTemplateResponse.getStatus());
    }

    @Test
    public void testEnableTemplateNotExistError() {
        String bpmnFileName = "InvoiceApproval.bpmn";
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.BPMN_DOESNT_EXIST.getErrorMessage());
        templateService.updateTemplateStatus(bpmnFileName, "enabled");
    }

    @Test
    public void testDisableTemplateSuccess() throws IOException {
        TemplateDetails bpmnDetails = buildTemplateDetails();
        Mockito.doReturn(Optional.of(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        WorkflowGenericResponse disableTemplateResponse =
                templateService.updateTemplateStatus(bpmnFile.getName(), "disabled");
        Assert.assertEquals(SUCCESS, disableTemplateResponse.getStatus());
    }

    @Test
    public void testDisableTemplateNotExistError() {
        String bpmnFileName = "InvoiceApproval.bpmn";
        Mockito.doReturn(Optional.empty())
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.BPMN_DOESNT_EXIST.getErrorMessage());
        templateService.updateTemplateStatus(bpmnFileName, "disabled");
    }

    @Test
    public void fetchAllTemplateTestWithworkflowFilter() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .recordType(RecordType.INVOICE)
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);

        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Map<Boolean, List<TemplateDetails>> mapOfTemplates =
                templateDetails.stream()
                        .collect(Collectors.partitioningBy(t -> ObjectUtils.isEmpty(t.getParentId())));

        List<TemplateDetails> bpmnFiles = mapOfTemplates.get(true);
        List<TemplateDetails> dmnFiles = mapOfTemplates.get(false);

        Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
        bpmnFiles.forEach(
                bpmnTemplate -> {
                    List<TemplateDetails> dmnTemplateFiles =
                            dmnFiles.stream()
                                    .filter(
                                            dmnTemplate ->
                                                    bpmnTemplate.getId().equalsIgnoreCase(dmnTemplate.getParentId()))
                                    .collect(Collectors.toList());
                    map.put(bpmnTemplate, dmnTemplateFiles);
                });
        BpmnModelInstance bpmnModelInstance =
                TemplateServiceImplTest.readBPMNFile(INVOICE_APPROVAL_BPMN_V1);
        Assert.assertNotNull(bpmnModelInstance);
        DmnModelInstance dmnModelInstance =
                TemplateServiceImplTest.readDMNFile(INVOICE_APPROVAL_DMN_V1);
        Assert.assertNotNull(dmnModelInstance);
        DefinitionInstance definitionInstance = CustomWorkflowUtil.generateDefinitionInstanceForBpmnProcessing(
                bpmnModelInstance,
                Collections.singletonList(dmnModelInstance),
                bpmnDetails);
        Template multiStepTemplate = new Template();
        multiStepTemplate.setName("customApproval");
        multiStepTemplate.setRecordType(RecordType.INVOICE.getRecordType());
        when(templateBuilder.buildTemplateDetails(Mockito.any(), Mockito.any())).thenReturn(new Template());
        Template template = (Template) bpmnProcessor.processBpmn(definitionInstance,
                                GlobalId.create(REALM_ID, LOCAL_ID), true);

        Assert.assertNotNull(template);
        Assert.assertEquals(2, template.getWorkflowSteps().size());
    }

    @Test
    public void fetchAllTemplateTestWithoutFilter() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Assert.assertNotNull(templateDetails);
        Assert.assertNotNull(templateService.fetchAllTemplates(queryHelper));
    }

    @Test
    public void fetchAllTemplateTestWithFilterWithoutWorkflowSteps() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        FilterExpression filterExpression = new FilterExpression();
        filterExpression.setProperty("name");
        filterExpression.addArgs("invoiceapproval.bpmn");
        filterExpression.addArgs("test.bpmn");
        preparedQuery.setWhere(filterExpression);
        QueryHelper queryHelper = new QueryHelper(query);
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Assert.assertNotNull(templateDetails);
        Assert.assertNotNull(templateService.fetchAllTemplates(queryHelper));
    }

    @Test
    public void fetchAllTemplateTestWithWorkflowSteps() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .recordType(RecordType.INVOICE)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .recordType(RecordType.INVOICE)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisDmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Assert.assertNotNull(templateDetails);
        Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
        map.put(bpmnDetails, Collections.singletonList(dmnDetails));
        List<Template> templates = bpmnProcessor.processBpmn(map);
        Assert.assertNotNull(templates);
        Assert.assertNotNull(templates.get(0).getId());
        Assert.assertEquals(templates, templateService.fetchAllTemplatesWithWorkflowSteps(queryHelper));
    }

    @Test
    public void fetchTemplateTest() throws IOException {
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        String templateId = UUID.randomUUID().toString();
        String dmnTemplateId = UUID.randomUUID().toString();
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(templateId)
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(dmnTemplateId)
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .recordType(RecordType.INVOICE)
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisDmn))
                        .build();
        Mockito.doReturn(Optional.ofNullable(bpmnDetails))
                .when(templateDetailsRepository)
                .findTopByIdOrderByVersionDesc(templateId);

        Mockito.doReturn(Optional.ofNullable(Collections.singletonList(dmnDetails)))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(templateId, bpmnDetails.getVersion());
        Template template =
                templateService.fetchTemplate(templateId, TestHelper.getGlobalId(templateId), false);
        Assert.assertNotNull(template);
    }

    @Test
    public void getReferencedChildTemplatesTest() throws IOException {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        String dmnTemplateId = UUID.randomUUID().toString();
        TemplateDetails bpmnDetails = buildTemplateDetails();
        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(dmnTemplateId)
                        .templateName(dmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .build();

        Mockito.doReturn(Optional.of(Collections.singletonList(dmnDetails)))
                .when(templateDetailsRepository)
                .getTemplateDetailsByParentIdAndVersion(bpmnDetails.getId(), bpmnDetails.getVersion());

        Optional<List<TemplateDetails>> templateDetails =
                templateService.getReferencedChildTemplates(bpmnDetails);
        Assert.assertNotNull(templateDetails);
    }

    @Test
    public void getTemplateDetailsTest() {
        String templateId = UUID.randomUUID().toString();
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(templateId)
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .build();

        Mockito.doReturn(Optional.ofNullable(bpmnDetails))
                .when(templateDetailsRepository)
                .findById(templateId);

        Optional<TemplateDetails> finalBpmnDetails = templateService.getTemplateDetails(templateId);
        Assert.assertNotNull(bpmnDetails);
        Assert.assertEquals(bpmnDetails, finalBpmnDetails.get());
    }

    @Test
    public void testValidateTemplateExceptionInvalidFormatSaving() throws IOException {
        {
            File bpmnFile = new File(INVOICE_APPROVAL_BPMN_INVALID);
            File dmnFile = new File(INVOICE_APPROVAL_DMN_INVALID);
            FileInputStream fisBpmn = new FileInputStream(bpmnFile);
            FileInputStream fisDmn = new FileInputStream(dmnFile);
            TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
            MultipartFile[] templates = {
                    new MockMultipartFile(
                            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                    new MockMultipartFile(
                            dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
            };
            when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
            exceptionRule.expect(WorkflowGeneralException.class);
            exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
            templateService.saveTemplate(templates, mockTemplateMetadata);
        }
    }

    @Test
    public void testValidateTemplateExceptionInvalidFormatUpdate() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN_INVALID);
        File dmnFile = new File(INVOICE_APPROVAL_DMN_INVALID);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        templateService.updateTemplate(templates, mockTemplateMetadata);
    }

    @Test
    public void testValidateTemplateExceptionInvalidFormatSaveNoStepDetails() throws IOException {
        {
            File bpmnFile = new File(INVOICE_APPROVAL_BPMN_INVALID_NO_STEP_INFO);
            File dmnFile = new File(INVOICE_APPROVAL_DMN);
            FileInputStream fisBpmn = new FileInputStream(bpmnFile);
            FileInputStream fisDmn = new FileInputStream(dmnFile);
            TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
            MultipartFile[] templates = { new MockMultipartFile(
                            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                    new MockMultipartFile(
                            dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
            };
            exceptionRule.expect(WorkflowGeneralException.class);
            exceptionRule.expectMessage(WorkflowError.TEMPLATE_SAVE_EXCEPTION.getErrorMessage());
            templateService.saveTemplate(templates, mockTemplateMetadata);
        }
    }

    @Test
    public void testfetchAllTemplatesWithWorkflowSteps() throws IOException {
        RequestContext context = Mockito.mock(RequestContext.class);
        when(context.getAuthorization()).thenReturn(TestHelper.mockAuthorization("realm"));
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        when(providerHelper.checkForWorkflowSteps(queryHelper)).thenReturn(true);

        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .recordType(RecordType.INVOICE)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .recordType(RecordType.INVOICE)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisDmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);

        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Assert.assertNotNull(templateDetails);
        Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
        map.put(bpmnDetails, Collections.singletonList(dmnDetails));
        List<Template> templates = bpmnProcessor.processBpmn(map);

        ListResult<Template> response =
                templateService.readAllTemplates(context.getAuthorization(), queryHelper);

        Assert.assertNotNull(response);
        Assert.assertNull(response.getError());
        Assert.assertNotNull(response.getResult());
        Assert.assertEquals(
                response.getResult().stream().findFirst().get(), templates.stream().findFirst().get());
    }

    @Test
    public void testfetchAllTemplatesWithWorkflowStepsWithQueryFilters() throws IOException {
        RequestContext context = Mockito.mock(RequestContext.class);
        when(context.getAuthorization()).thenReturn(TestHelper.mockAuthorization("realm"));
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        FilterExpression filterExpression = new FilterExpression();
        filterExpression.setProperty("name");
        filterExpression.addArgs("invoiceapproval.bpmn");
        preparedQuery.setWhere(filterExpression);
        QueryHelper queryHelper = new QueryHelper(query);
        when(providerHelper.checkForWorkflowSteps(queryHelper)).thenReturn(true);

        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateDetails bpmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .recordType(RecordType.INVOICE)
                        .offeringId(OFFER_ID)
                        .status(Status.ENABLED)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

        TemplateDetails dmnDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.DMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .recordType(RecordType.INVOICE)
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(bpmnDetails.getId())
                        .templateData(IOUtils.toByteArray(fisDmn))
                        .build();
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);

        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();
        Assert.assertNotNull(templateDetails);
        Map<TemplateDetails, List<TemplateDetails>> map = new HashMap<>();
        map.put(bpmnDetails, Collections.singletonList(dmnDetails));
        List<Template> templates = bpmnProcessor.processBpmn(map);

        ListResult<Template> response =
                templateService.readAllTemplates(context.getAuthorization(), queryHelper);

        Assert.assertNotNull(response);
        Assert.assertNull(response.getError());
        Assert.assertNotNull(response.getResult());
        Assert.assertEquals(
                response.getResult().stream().findFirst().get(), templates.stream().findFirst().get());
    }

    @Test
    public void testfetchAllTemplates() throws IOException {
        RequestContext context = Mockito.mock(RequestContext.class);
        when(context.getAuthorization()).thenReturn(TestHelper.mockAuthorization("realm"));
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        when(providerHelper.checkForWorkflowSteps(queryHelper)).thenReturn(false);

        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = prepareBpmnDetails(fisBpmn, Status.ENABLED, bpmnFile.getName());
        TemplateDetails dmnDetails =
                prepareDmnDetails(fisBpmn, bpmnDetails.getId(), Status.ENABLED, bpmnFile.getName());
        List<TemplateDetails> templateDetails = new ArrayList<>();
        templateDetails.add(bpmnDetails);
        templateDetails.add(dmnDetails);

        Mockito.doReturn(templateDetails).when(templateDetailsRepository).getAllRecords();

        ListResult<Template> response =
                templateService.readAllTemplates(context.getAuthorization(), queryHelper);

        Assert.assertNotNull(response);
        Assert.assertNull(response.getError());
        Assert.assertNotNull(response.getResult());
    }

    @Test
    public void testfetchAllTemplatesDisabled() throws IOException {
        RequestContext context = Mockito.mock(RequestContext.class);
        when(context.getAuthorization()).thenReturn(TestHelper.mockAuthorization("realm"));
        QueryHelper query = Mockito.mock(QueryHelper.class);
        when(providerHelper.checkForWorkflowSteps(query)).thenReturn(false);

        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = prepareBpmnDetails(fisBpmn, Status.DISABLED, bpmnFile.getName());

        Mockito.doReturn(Collections.emptyList()).when(templateDetailsRepository).getAllRecords();

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.TEMPLATE_DOES_NOT_EXIST.getErrorMessage());
        templateService.readAllTemplates(context.getAuthorization(), query);
    }

    @Test
    public void testFetchDisabledAndEnabled() throws IOException {
        RequestContext context = Mockito.mock(RequestContext.class);
        when(context.getAuthorization()).thenReturn(TestHelper.mockAuthorization("realm"));
        Query query = new Query();
        Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
        query.setPreparedQuery(preparedQuery);
        QueryHelper queryHelper = new QueryHelper(query);
        when(providerHelper.checkForWorkflowSteps(queryHelper)).thenReturn(false);

        File bpmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateDetails bpmnDetails = prepareBpmnDetails(fisBpmn, Status.DISABLED, bpmnFile.getName());

        TemplateDetails bpmnDetails1 = prepareBpmnDetails(fisBpmn, Status.ENABLED, bpmnFile.getName().concat("test"));

        TemplateDetails dmnDetails1 = prepareDmnDetails(
                        fisBpmn, bpmnDetails.getId(), Status.ENABLED, bpmnFile.getName().concat("test"));

        List<TemplateDetails> resultList = new ArrayList<>();
        resultList.add(bpmnDetails1);
        resultList.add(dmnDetails1);
        Mockito.doReturn(resultList).when(templateDetailsRepository).getAllRecords();
        ListResult<Template> response =
                templateService.readAllTemplates(context.getAuthorization(), queryHelper);

        Assert.assertNotNull(response);
        Assert.assertNull(response.getError());
        Assert.assertEquals(response.getResult().get(0).getName(), bpmnFile.getName().concat("test"));
        Assert.assertNotNull(response.getResult());
    }

    @Test
    public void testValidateSuccess() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test
    public void testValidateMissingDMN() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.MISSING_DMN.getErrorMessage());
        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test
    public void testValidateInvalidFileFormat() throws Exception {
        File bpmnFile = new File(INVOICE_APPROVAL_BPMN_INVALID);
        File dmnFile = new File(INVOICE_APPROVAL_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_FILE_FORMAT.getErrorMessage());
        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test
    public void testValidateInvalidExtraDmn() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        File dmnFile1 = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        FileInputStream fisDmn1 = new FileInputStream(dmnFile1);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn)),
                new MockMultipartFile(
                        dmnFile1.getName(), dmnFile1.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn1))
        };
        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.EXTRA_DMN.getErrorMessage());
        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test
    public void testPopulateTriggerDetails() throws ClassNotFoundException {
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .recordType(RecordType.ENGAGEMENT)
                        .build();
        List<TriggerDetails> triggerDetails =
                templateService.buildTriggerDetails(
                        readBPMNFile("bpmn/engagement-full-service.bpmn"), templateDetails);
        Assert.assertNotNull(triggerDetails);
        Assert.assertEquals(50, triggerDetails.size());
        Assert.assertFalse(
                triggerDetails.stream()
                        .filter(trigger -> Objects.isNull(trigger.getTriggerName()))
                        .findFirst()
                        .isPresent());
    }

    @Test
    public void testFilterConfigTemplatesFromDbTemplates() {
        List<Template> dbTemplates = new ArrayList<>();
        List<Template> preCannedTemplates = new ArrayList<>();
        prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);

        List<Template> templates =
                templateService.filterConfigTemplatesFromDbTemplates(preCannedTemplates, dbTemplates);
        Assert.assertNotNull(templates);
        Assert.assertEquals(3, templates.size());
    }

    @Test
    public void testFilterConfigTemplatesFromDbTemplatesWithCustomIAFFOn() {
        List<Template> dbTemplates = new ArrayList<>();
        List<Template> preCannedTemplates = new ArrayList<>();
        prepareMockDataForReadAllTemplates(dbTemplates, preCannedTemplates);
        String enableCustomIAFlag = "qbo-adv-enable-custom-invoice-approval";
        when(featureFlagManager.getBoolean(enableCustomIAFlag, false)).thenReturn(true);
        List<Template> templates =
                templateService.filterConfigTemplatesFromDbTemplates(preCannedTemplates, dbTemplates);
        Assert.assertNotNull(templates);
        Assert.assertEquals(3, templates.size());
        Assert.assertTrue(templates.stream()
                .anyMatch(x -> x.getName().equals("invoiceapproval") && x.getCategory().equals("CUSTOM")));
    }

    @Test
    public void testGetConfigTemplateByIdInvalid() {
        Template template = templateBuilder.getConfigTemplateById("Invalid Id");
        Assert.assertNull(template);
    }

    @Test
    public void testGetConfigTemplates() {
        List<Template> templates = templateBuilder.buildConfigTemplates();
        Assert.assertNotNull(templates);
        Assert.assertEquals(0, templates.size());
    }

    @Test
    public void testGetTemplateMetadataForRecordType() {
        Record record = new Record();
        record.setId("invoice");

        ActionGroup actionGroup = new ActionGroup();
        actionGroup.setId("reminder");
        record.setActionGroups(Arrays.asList(actionGroup));

        ConfigTemplate configTemplate = new ConfigTemplate();
        configTemplate.setId("invoiceunsentreminder");

        Map<String, ConfigTemplate> configTemplateMap = new HashMap<>();
        configTemplate.setRecord("invoice");
        configTemplate.setActionGroups(Arrays.asList(actionGroup));
        configTemplateMap.put("invoiceunsentreminder", configTemplate);

        when(customWorkflowConfig.getTemplateMap()).thenReturn(configTemplateMap);
        when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(record);

        Template template = new Template();
        template.setId(GlobalId.create(OWNER_ID, "invoiceunsentreminder"));

        when(templateBuilder.build(Mockito.eq("QBO"), Mockito.eq("invoice"), Mockito.eq("reminder"), Mockito.anyBoolean()))
                .thenReturn(template);
        Template val =
                templateService.getTemplateMetadataForRecordType("QBO", "invoice", "reminder", false);
        Assert.assertNotNull(val);
        Assert.assertEquals("invoiceunsentreminder", val.getId().getLocalId());
    }

    @Test
    public void testGetTemplateMetadataForRecordTypeWithConfigSteps() {
        Record record = new Record();
        record.setId("invoice");

        ActionGroup actionGroup = new ActionGroup();
        actionGroup.setId("approval");
        ActionIdMapper actionIdMapper = new ActionIdMapper();
        List<String> subactionIds = new ArrayList<>();
        subactionIds.add("createTask");
        subactionIds.add("sendCompanyEmail");
        actionIdMapper.setActionId("sendForApproval");
        actionIdMapper.setSubActionIds(subactionIds);
        actionGroup.setActionIdMapper(actionIdMapper);
        record.setActionGroups(Arrays.asList(actionGroup));

        when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(record);

        Template template = new Template();
        template.setId(GlobalId.create(OWNER_ID, "invoiceapproval"));

        when(multiStepTemplateBuilder.build(Mockito.eq("QBO"), Mockito.eq("invoice"), Mockito.eq("approval"), Mockito.anyBoolean()))
                .thenReturn(template);
        Template val =
                templateService.getTemplateMetadataForRecordType("QBO", "invoice", "approval", true);
        Assert.assertNotNull(val);
        Assert.assertEquals("invoiceapproval", val.getId().getLocalId());
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testGetTemplateMetadataForRecordTypeWithInvalidRecord() {
        when(customWorkflowConfig.getRecordObjForType("invalid")).thenReturn(null);
        try {
            templateService.getTemplateMetadataForRecordType("QBO", "invalid", null, false);
            Assert.fail("Method should throw exception");
        } catch (WorkflowGeneralException e) {
            Assert.assertEquals(WorkflowError.INVALID_INPUT, e.getWorkflowError());
            throw e;
        }
    }

    @Test
    public void testFetchConfigTemplateSuccess() throws IOException {
        Record record = new Record();
        record.setId("invoice");

        ActionGroup actionGroup = new ActionGroup();
        actionGroup.setId("reminder");
        record.setActionGroups(Arrays.asList(actionGroup));

        ConfigTemplate configTemplate = new ConfigTemplate();
        configTemplate.setId("invoiceunsentreminder");

        Map<String, ConfigTemplate> configTemplateMap = new HashMap<>();
        configTemplate.setRecord("invoice");
        configTemplate.setActionGroups(Arrays.asList(actionGroup));
        configTemplateMap.put("invoiceunsentreminder", configTemplate);

        when(customWorkflowConfig.getTemplateMap()).thenReturn(configTemplateMap);
        when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(record);

        Template template = new Template();
        template.setId(GlobalId.create(OWNER_ID, "invoiceunsentreminder"));
        Mockito.doNothing().when(wfaiService).populateAssignee(Mockito.anyObject());
        when(templateService.getConfigTemplateById("invoiceunsentreminder", false))
                .thenReturn(template);
        Template val =
                templateService.fetchTemplate(
                        "invoiceunsentreminder", GlobalId.create(OWNER_ID, "invoiceunsentreminder"), true);
        Assert.assertNotNull(val);
        Assert.assertEquals("invoiceunsentreminder", val.getId().getLocalId());
    }

    @Test
    public void testFetchConfigTemplateWithSteps() throws IOException {
        Record record = new Record();
        record.setId("invoice");

        ActionGroup actionGroup = new ActionGroup();
        actionGroup.setId("approval");
        ActionIdMapper actionIdMapper = new ActionIdMapper();
        List<String> subactionIds = new ArrayList<>();
        subactionIds.add("createTask");
        subactionIds.add("sendCompanyEmail");
        actionIdMapper.setActionId("sendForApproval");
        actionIdMapper.setSubActionIds(subactionIds);
        actionGroup.setActionIdMapper(actionIdMapper);
        record.setActionGroups(Arrays.asList(actionGroup));

        Steps configStep = new Steps();
        configStep.setStepId(1);
        configStep.setStepType(StepTypeEnum.CONDITION.value());
        Next yesPath = new Next();
        Next noPath = new Next();
        yesPath.setType(NextTypeEnum.ACTION.value());
        yesPath.setStepId("2");
        yesPath.setLabel(NextLabelEnum.YES.value());
        noPath.setType(StepTypeEnum.CONDITION.value());
        noPath.setStepId("3");
        noPath.setLabel(NextLabelEnum.NO.value());
        List<Next> nexts = new ArrayList<>();
        nexts.add(yesPath);
        nexts.add(noPath);
        configStep.setNexts(nexts);
        Attribute txnAttr = new Attribute();
        txnAttr.setName("TxnAmount");
        txnAttr.setId("txnAmount");
        txnAttr.setType("DOUBLE");
        txnAttr.setDefaultValue("500");
        txnAttr.setDefaultOperator("GTE");
        configStep.setAttributes(Arrays.asList(txnAttr));

        ConfigTemplate configTemplate = new ConfigTemplate();
        configTemplate.setId("invoiceapproval-multicondition");
        configTemplate.setSteps(Arrays.asList(configStep));

        Map<String, ConfigTemplate> configTemplateMap = new HashMap<>();
        configTemplate.setRecord("invoice");
        configTemplate.setActionGroups(Arrays.asList(actionGroup));
        configTemplateMap.put("invoiceapproval-multicondition", configTemplate);

        when(customWorkflowConfig.getTemplateMap()).thenReturn(configTemplateMap);
        when(customWorkflowConfig.getRecordObjForType("invoice")).thenReturn(record);

        Template template = new Template();
        template.setId(GlobalId.create(OWNER_ID, "invoiceapproval-multicondition"));

        when(templateService.getConfigTemplateById("invoiceapproval-multicondition", true))
                .thenReturn(template);
        Template val =
                templateService.fetchTemplate(
                        "invoiceapproval-multicondition", GlobalId.create(OWNER_ID, "invoiceapproval-multicondition"), true);
        Assert.assertNotNull(val);
        Assert.assertEquals("invoiceapproval-multicondition", val.getId().getLocalId());
    }

    private TemplateDetails prepareDmnDetails(
            FileInputStream fisBpmn, String parentId, Status status, String name) throws IOException {
        return TemplateDetails.builder()
                .id(UUID.randomUUID().toString())
                .templateName(name)
                .modelType(ModelType.DMN)
                .createdByUserId(Long.parseLong(CREATED_BY))
                .creatorType(CreatorType.SYSTEM)
                .offeringId(OFFER_ID)
                .status(status)
                .ownerId(Long.parseLong(OWNER_ID))
                .version(1)
                .parentId(parentId)
                .templateData(IOUtils.toByteArray(fisBpmn))
                .build();
    }

    private TemplateDetails prepareBpmnDetails(FileInputStream fisBpmn, Status status, String name)
            throws IOException {
        return TemplateDetails.builder()
                .id(UUID.randomUUID().toString())
                .templateName(name)
                .modelType(ModelType.BPMN)
                .createdByUserId(Long.parseLong(CREATED_BY))
                .creatorType(CreatorType.SYSTEM)
                .offeringId(OFFER_ID)
                .status(status)
                .ownerId(Long.parseLong(OWNER_ID))
                .version(1)
                .parentId(null)
                .templateData(IOUtils.toByteArray(fisBpmn))
                .build();
    }

    private void populateTemplateHandler() {
        TemplateHandler.addHandler(DefinitionType.USER, userDefinitionProcessor);
        TemplateHandler.addHandler(DefinitionType.SINGLE, singleDefinitionProcessor);
        TemplateHandler.addHandler(DefinitionType.SYSTEM, systemDefinitionProcessor);
    }

    private void prepareMockDataForReadAllTemplates(
            List<Template> dbTemplates, List<Template> preCannedTemplates) {
        Template template =
                TestHelper.mockDbTemplateEntity("unsentInvoiceReminder", TemplateCategory.HUB);
        dbTemplates.add(template);
        template = TestHelper.mockDbTemplateEntity("invoiceapproval", TemplateCategory.HUB);
        dbTemplates.add(template);

        Template template1 =
                TestHelper.mockConfigTemplateEntity("unsentInvoiceReminder", TemplateCategory.CUSTOM);
        preCannedTemplates.add(template1);
        template1 =
                TestHelper.mockConfigTemplateEntity("invoiceoverduereminder", TemplateCategory.CUSTOM);
        preCannedTemplates.add(template1);
        template1 = TestHelper.mockConfigTemplateEntity("invoiceapproval", TemplateCategory.CUSTOM);
        preCannedTemplates.add(template1);
    }

    /**
     * Task Parsing Disabled - Successful execution.
     *
     * @throws Exception
     */
    @Test
    public void testSaveTemplateSuccess_ForSystem_taskParsingDisabled() throws Exception {
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(workflowTaskConfig.isEnable()).thenReturn(false);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");

    }

    @Test
    public void testAPIBlocked_returnException() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };

        TemplateSaveUpdateConfig templateSaveUpdateConfig = new TemplateSaveUpdateConfig();
        templateSaveUpdateConfig.setBlock(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(templateSaveUpdateConfig);

        Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
            templateService.saveTemplate(templates, mockTemplateMetadata);
        });
        String expectedMessage = WorkflowError.API_ACCESS_BLOCKED.getErrorMessage();
        String actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    public void testAPIBlockedForAppId_returnException() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };

        TemplateSaveUpdateConfig templateSaveUpdateConfig = new TemplateSaveUpdateConfig();
        templateSaveUpdateConfig.setBlock(false);
        templateSaveUpdateConfig.setWhitelistAppId(List.of("Intuit.appintgwkflw.wkflautomate.qbowasapiclient"));
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(templateSaveUpdateConfig);

        Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
            templateService.saveTemplate(templates, mockTemplateMetadata);
        });
        String expectedMessage = WorkflowError.ACCESS_DENIED_FOR_CLIENT_APP_ID.getErrorMessage();
        String actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    public void testNoAppId_returnException() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        WASContext.clear();

        Exception exception = assertThrows(WorkflowGeneralException.class, () -> {
            templateService.saveTemplate(templates, mockTemplateMetadata);
        });
        String expectedMessage = WorkflowError.MISSING_APP_ID.getErrorMessage();
        String actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));
    }

    @Test
    public void testWhitelistEmptySuccess() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);

        TemplateSaveUpdateConfig templateSaveUpdateConfig = new TemplateSaveUpdateConfig();
        templateSaveUpdateConfig.setBlock(false);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(templateSaveUpdateConfig);

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testAPIAccessAvailableForAppIdSuccess() throws IOException {
        File bpmnFile = new File(INVOICE_APPROVAL_ONLY_BPMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };
        TemplateDetails templateDetails = buildTemplateDetails();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);

        TemplateSaveUpdateConfig templateSaveUpdateConfig = new TemplateSaveUpdateConfig();
        templateSaveUpdateConfig.setBlock(false);
        templateSaveUpdateConfig.setWhitelistAppId(List.of("Intuit.appintgwkflw.wkflautomate.qbowasapiclient", "Intuit.appintgwkflw.wkflautomate.vepclient"));
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(templateSaveUpdateConfig);

        Authorization authContext = new Authorization();
        authContext.put(WorkflowConstants.APP_ID, "Intuit.appintgwkflw.wkflautomate.qbowasapiclient");
        WASContext.setAuthContext(authContext);

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        WASContext.clear();

        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testSaveTemplateSuccess_ForSystem_WithSystemTag() throws Exception {

        File bpmnFile = new File(INVOICE_APPROVAL_TAG_BPMN);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.SYSTEM, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        SystemTags tags = new SystemTags();
        tags.addSystemTag(SYSTEM_TAG, "1.2.3-zeta");
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .definitionType(DefinitionType.SYSTEM)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .tag(tags.getTagsMapInstance().get(SYSTEM_TAG) != null
                                ? new ObjectMapper().writeValueAsString(tags.tagsMapInstance)
                                : null)
                        .build();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test(expected = WorkflowGeneralException.class)
    public void testSaveTemplateSuccess_ForUser_WithSystemTag() throws Exception {

        File bpmnFile = new File(INVOICE_APPROVAL_TAG_BPMN);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        TemplateMetadata mockTemplateMetadata = new TemplateMetadata(
                CreatorType.SYSTEM, Status.ENABLED, false,
                DefinitionType.USER, TemplateCategory.HIDDEN,
                false, false, false, null, false);

        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        SystemTags tags = new SystemTags();
        tags.addSystemTag(SYSTEM_TAG, "1.2.3-zeta");
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .definitionType(DefinitionType.USER)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .tag(tags.getTagsMapInstance().get(SYSTEM_TAG) != null
                                ? new ObjectMapper().writeValueAsString(tags.tagsMapInstance)
                                : null)
                        .build();
        Mockito.doReturn(Collections.singletonList(templateDetails))
                .when(templateDetailsRepository)
                .saveAll(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        // assert the deployment task and save task is called
        DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition processDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        processDefinition.setId("id");
        processDefinition.setKey("key");
        deployedDefinitionMap.put("id", processDefinition);

        Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMapDecision =
                new HashMap<>();
        DeployDefinitionResponse.DeployedDefinition decisionDefinition =
                new DeployDefinitionResponse.DeployedDefinition();
        decisionDefinition.setId("id2");
        decisionDefinition.setKey("decision_invoiceapproval");
        deployedDefinitionMapDecision.put("id2", decisionDefinition);
        deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
        deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMapDecision);
        deployDefinitionResponse.setId("123");
        WASHttpResponse<Object> responseEntity =
                WASHttpResponse.builder().response(deployDefinitionResponse).build();
        when(bpmnEngineDefinitionServiceRest.deployDefinition(any()))
                .thenReturn(responseEntity);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse saveResponse =
                templateService.saveTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, saveResponse.getStatus());
        Mockito.verify(systemDefinitionProcessor).execute(Mockito.any(), Mockito.any());
        Mockito.verify(contextHandler, Mockito.times(1))
                .addKey(WASContextEnums.WORKFLOW, "invoiceapproval");
    }

    @Test
    public void testUpdateTemplateWithTagSuccess() throws Exception {
        populateTemplateHandler();
        File bpmnFile = new File(INVOICE_APPROVAL_TAG_BPMN);
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        mockTemplateMetadata.setDefinitionType(DefinitionType.SYSTEM);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        SystemTags tags = new SystemTags();
        tags.addSystemTag(SYSTEM_TAG, "1.2.3-meta");
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .definitionType(DefinitionType.SYSTEM)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .tag(tags.getTagsMapInstance().get(SYSTEM_TAG) != null
                                ? new ObjectMapper().writeValueAsString(tags.tagsMapInstance)
                                : null)
                        .build();

        List<TemplateDetails> templateExceptMetadata = new ArrayList<>();
        templateExceptMetadata.add(templateDetails);
        Mockito.doReturn((Optional.of(templateExceptMetadata)))
                .when(templateDetailsRepository)
                .findTemplateDetailsExceptTemplateData(any(), any(), any());
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).saveAndFlush(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse updateResponse =
                templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, updateResponse.getStatus());
    }

    @Test
    public void testUpdateTemplateWithoutNewTagSuccess() throws Exception {
        populateTemplateHandler();
        File dmnFile = new File(INVOICE_APPROVAL_DMN);
        FileInputStream fisBpmn = new FileInputStream(bpmnFile);
        FileInputStream fisDmn = new FileInputStream(dmnFile);
        TemplateMetadata mockTemplateMetadata = new TemplateMetadata();
        mockTemplateMetadata.setValidateTriggerNames(false);
        MultipartFile[] templates = { new MockMultipartFile(
                        bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
                new MockMultipartFile(
                        dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };
        SystemTags tags = new SystemTags();
        TemplateDetails templateDetails = TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .tag(tags.getTagsMapInstance().get(SYSTEM_TAG) != null
                                ? new ObjectMapper().writeValueAsString(tags.tagsMapInstance)
                                : null)
                        .build();

        List<TemplateDetails> templateExceptMetadata = new ArrayList<TemplateDetails>();
        templateExceptMetadata.add(templateDetails);
        Mockito.doReturn((Optional.of(templateExceptMetadata)))
                .when(templateDetailsRepository)
                .findTemplateDetailsExceptTemplateData(any(), any(), any());
        Mockito.doReturn(Optional.of(templateDetails))
                .when(templateDetailsRepository)
                .findTopByTemplateNameOrderByVersionDesc(any());
        Mockito.doReturn(templateDetails).when(templateDetailsRepository).saveAndFlush(any());
        Mockito.doReturn(sampleAuth).when(contextHandler).get(WASContextEnums.AUTHORIZATION_HEADER);
        Mockito.doReturn(intuitTid).when(contextHandler).get(WASContextEnums.INTUIT_TID);
        when(workflowTaskConfig.isEnable()).thenReturn(true);
        when(clientAccessConfig.getTemplateSaveUpdateConfig()).thenReturn(new TemplateSaveUpdateConfig());

        WorkflowGenericResponse updateResponse =
                templateService.updateTemplate(templates, mockTemplateMetadata);
        Assert.assertEquals(SUCCESS, updateResponse.getStatus());
    }

    private TemplateDetails buildTemplateDetails() throws IOException {

        return TemplateDetails.builder()
                        .id(UUID.randomUUID().toString())
                        .templateName(bpmnFile.getName())
                        .modelType(ModelType.BPMN)
                        .createdByUserId(Long.parseLong(CREATED_BY))
                        .creatorType(CreatorType.SYSTEM)
                        .offeringId(OFFER_ID)
                        .ownerId(Long.parseLong(OWNER_ID))
                        .version(1)
                        .parentId(null)
                        .templateData(IOUtils.toByteArray(fisBpmn))
                        .build();

    }

    @TestConfiguration
    static class BpmnProcessorImplContextConfiguration {

        @Bean
        public BpmnProcessorImpl bpmnProcessor() {
            return new BpmnProcessorImpl();
        }

        @Bean
        TranslationService translationService() {
            return TestHelper.initTranslationService();
        }
    }

    @Test()
    @DisplayName("Happy case for the multi condition invoice approval workflow with six Call Activity block.")
    public void testValidateMultiConditionPayloadWithCallActivity_Success() {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(), any()))
                .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(MULTI_CONDITION_WORKFLOW_BPMN_XML), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertDoesNotThrow(() -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails));
    }

    @Test()
    @DisplayName("Test case to check if an exception is thrown when child template details are not found for call activity in parent bpmn.")
    public void testCallActivityChildTemplateDetailsNotFound() {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc("sendForApproval", Status.ENABLED))
            .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(MULTI_CONDITION_WORKFLOW_BPMN_XML), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertThrows(WorkflowGeneralException.class, () -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails),
            WorkflowError.CALL_ACTIVITY_TEMPLATE_NOT_FOUND.getErrorMessage());
    }

    @Test()
    @DisplayName("Test case to check if an exception is thrown when the binding of the call activity is neither null nor latest")
    public void testInvalidCallActivityBindingWithNonNullValue()  {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(), any()))
            .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(
                MULTI_CONDITION_WORKFLOW_INVALID_CALL_ACTIVITY_BINDING), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertThrows(WorkflowGeneralException.class, () -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails),
            WorkflowError.INVALID_CALLED_ELEMENT_BINDING.getErrorMessage());
    }

    @Test()
    @DisplayName("Test case to check if an exception is thrown when the inputOutput block is missing from the call activity element")
    public void testMissingInputOutputBlockInCallActivityElement()  {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(), any()))
            .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(
                MULTI_CONDITION_WORKFLOW_MISSING_INPUT_OUTPUT_IN_CALL_ACTIVITY), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertThrows(WorkflowGeneralException.class, () -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails),
            WorkflowError.EMPTY_INPUT_OUTPUT_IN_CALL_ACTIVITY.getErrorMessage());
    }

    @Test()
    @DisplayName("Test case to check if an exception is thrown when the rootProcessInstanceId is missing from the call activity element")
    public void testMissingRootProcessInstanceIdInCallActivityElement()  {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(), any()))
            .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(
                MULTI_CONDITION_WORKFLOW_WITH_MISSING_ROOT_PROCESS_INSTANCE_ID), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertThrows(WorkflowGeneralException.class, () -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails),
            WorkflowError.CALL_ACTIVITY_ROOT_PROCESS_INSTANCE_ID_NOT_FOUND.getErrorMessage());
    }

    @Test()
    @DisplayName("Test case to check if an exception is thrown when the parent bpmn contains duplicate trigger signal names.")
    public void testParentBpmnContainsDuplicateTriggerSignalNamesInBoundaryEvents() {

        TemplateDetails childTemplateDetails = new TemplateDetails();
        childTemplateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());
        childTemplateDetails.setId("childTemplateTestId");

        when(templateDetailsRepository.findTopByTemplateNameAndStatusOrderByCreatedDateDesc(any(), any()))
            .thenReturn(Optional.of(childTemplateDetails));

        BpmnModelInstance bpmnModelInstance = Bpmn.readModelFromStream(
            IOUtils.toInputStream(TestHelper.readResourceAsString(MULTI_CONDITION_WORKFLOW_WITH_DUPLICATE_PARENT_CHILD_TRIGGER_NAMES), Charset.defaultCharset()));
        Assert.assertNotNull(bpmnModelInstance);

        TemplateDetails bpmnTemplateDetails = new TemplateDetails();
        bpmnTemplateDetails.setRecordType(RecordType.INVOICE);

        assertThrows(WorkflowGeneralException.class, () -> templateService.validateCallActivityElements(bpmnModelInstance, bpmnTemplateDetails),
            WorkflowError.PARENT_TEMPLATE_AND_CHILD_CALL_ACTIVITY_CONTAINS_DUPLICATE_TRIGGER_NAMES.getErrorMessage());
    }

    /*
    @Test()
    @DisplayName("Test case to check if child template exists for the call activity element in the parent bpmn.")
    public void testIfChildBpmnExists() throws IOException {
        File parentBpmnFile = new File("src/test/resources/" + MULTI_CONDITION_WORKFLOW_BPMN_XML);
        FileInputStream fisBpmn = new FileInputStream(parentBpmnFile);

        MultipartFile[] templates = { new MockMultipartFile(
            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.CALL_ACTIVITY_TEMPLATE_NOT_FOUND.getErrorMessage());

        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test()
    @DisplayName("Test case to check if validation fails if child template does not have attributes in startEvent")
    public void testThrowsErrorIfStateTransitionEventPresentInChildBpmn() throws IOException {
        File parentBpmnFile = new File("src/test/resources/" + MULTI_CONDITION_WORKFLOW_BPMN_XML);
        FileInputStream fisBpmn = new FileInputStream(parentBpmnFile);

        MultipartFile[] templates = { new MockMultipartFile(
            bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn))
        };

        TemplateDetails templateDetails = buildTemplateDetails();
        templateDetails.setTemplateData(TestHelper.readResourceAsString(QBLiveLiteCleanupOffboarding_bpmn_xml).getBytes());

        when(templateDetailsRepository.findTopByTemplateNameInAndModelTypeAndStatusOrderByCreatedDateDesc(any(), any(), any()))
            .thenReturn(Optional.of(Collections.singletonList(templateDetails)));

        when(activityDetailsRepository.findByTemplateIdAndActivityType(any(), any()))
            .thenReturn(Collections.emptyList());

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.INVALID_BPMN_ELEMENT.getErrorMessage());

        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test()
    @DisplayName("Test case to throw error if state transition event is present in startEvent of childBpmn.")
    public void testErrorInCaseStateTransitionEventPresentInChildBpmn() throws IOException {
        File parentBpmnFile = new File("src/test/resources/" + MULTI_CONDITION_WORKFLOW_BPMN_XML);
        FileInputStream fisBpmn = new FileInputStream(parentBpmnFile);

        File dmnFile = new File("src/test/resources/dmn/multiConditionDMN.dmn");
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        MultipartFile[] templates = {
            new MockMultipartFile(
                bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
            new MockMultipartFile(
                dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };

        TemplateDetails templateDetails = buildTemplateDetails();
        templateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());

        when(templateDetailsRepository.findTopByTemplateNameInAndModelTypeAndStatusOrderByCreatedDateDesc(any(), any(), any()))
            .thenReturn(Optional.of(Collections.singletonList(templateDetails)));

        ActivityDetail activityDetail = new ActivityDetail();
        activityDetail.setAttributes("{\"modelAttributes\": {\"events\": \"[\\\"start\\\"]\", \"stepDetails\": \"{\\\"startEvent\\\":[\\\"StartEvent_1\\\"]}\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"recordType\\\": \\\"engagement\\\"}\", \"startableEvents\": \"[\\\"created\\\"]\", \"processVariablesDetails\": \"[   {     \\\"variableName\\\": \\\"engagementId\\\",     \\\"variableType\\\": \\\"String\\\"   },   {     \\\"variableName\\\": \\\"customerAccountId\\\",     \\\"variableType\\\": \\\"String\\\"   }]\"}}");
        when(activityDetailsRepository.findByTemplateIdAndActivityType(any(), any()))
            .thenReturn(Collections.singletonList(activityDetail));

        exceptionRule.expect(WorkflowGeneralException.class);
        exceptionRule.expectMessage(WorkflowError.EVENT_NOT_SUPPORTED.getErrorMessage());

        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }

    @Test()
    @DisplayName("Test case to check if child bpmn does not have any state transition event in startEvent.")
    public void testNoStateTransitionEventInChildBpmn() throws IOException {
        File parentBpmnFile = new File("src/test/resources/" + MULTI_CONDITION_WORKFLOW_BPMN_XML);
        FileInputStream fisBpmn = new FileInputStream(parentBpmnFile);

        File dmnFile = new File("src/test/resources/dmn/multiConditionDMN.dmn");
        FileInputStream fisDmn = new FileInputStream(dmnFile);

        MultipartFile[] templates = {
            new MockMultipartFile(
                bpmnFile.getName(), bpmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisBpmn)),
            new MockMultipartFile(
                dmnFile.getName(), dmnFile.getName(), CONTENT_TYPE, IOUtils.toByteArray(fisDmn))
        };

        TemplateDetails templateDetails = buildTemplateDetails();
        templateDetails.setTemplateData(TestHelper.readResourceAsString(SEND_FOR_APPROVAL_CHILD_BPMN_XML).getBytes());

        when(templateDetailsRepository.findTopByTemplateNameInAndModelTypeAndStatusOrderByCreatedDateDesc(any(), any(), any()))
            .thenReturn(Optional.of(Collections.singletonList(templateDetails)));

        ActivityDetail activityDetail = new ActivityDetail();
        activityDetail.setAttributes("{\"modelAttributes\": {\"stepDetails\": \"{\\\"startEvent\\\":[\\\"StartEvent_1\\\"]}\", \"activityName\": \"startEvent\", \"handlerDetails\": \"{\\\"recordType\\\": \\\"engagement\\\"}\", \"startableEvents\": \"[\\\"created\\\"]\", \"processVariablesDetails\": \"[   {     \\\"variableName\\\": \\\"engagementId\\\",     \\\"variableType\\\": \\\"String\\\"   },   {     \\\"variableName\\\": \\\"customerAccountId\\\",     \\\"variableType\\\": \\\"String\\\"   }]\"}}");

        when(activityDetailsRepository.findByTemplateIdAndActivityType(any(), any()))
            .thenReturn(Collections.singletonList(activityDetail));

        Assert.assertTrue(templateService.validateTemplate(templates, new TemplateMetadata()));
    }
*/
}

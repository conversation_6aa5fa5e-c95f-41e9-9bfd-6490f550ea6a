kind: Rollout
apiVersion: argoproj.io/v1alpha1
metadata:
  name: rollout
spec:
  template:
    metadata:
      labels:
        istio-injected: 'true'
      annotations:
        policyId: p-pil2gp70f4mb
        alpha.istio.io/identity: Intuit.appintgwkflw.wkflautomate.waseventin
        sidecar.istio.io/inject: 'true'
        traffic.sidecar.istio.io/includeInboundPorts: '8090'
        traffic.sidecar.istio.io/excludeOutboundPorts: 3306,3308,61617,443
        traffic.sidecar.istio.io/excludeOutboundIPRanges: ***************/32
        admiral.io/env: prf
    spec:
      containers:
      - name: app
        env:
        - name: MESH_ENABLED
          value: 'true'
        - name: MESH_SIDECAR_PORT
          value: '15000'
        - name: MESH_TRAFFIC_PORT
          value: '8090'

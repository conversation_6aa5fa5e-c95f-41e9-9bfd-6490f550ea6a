package com.intuit.appintgwkflw.wkflautomate.was.core.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.ObjectConverter;
import com.intuit.appintgwkflw.wkflautomate.was.core.task.common.WorkflowTaskUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ActivityProgressDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.ProcessDetails;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.ActivityConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.EventHeaderConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventEntityType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.EventHeaderEntity;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.PublishEventType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.WorkflowMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.ActivityMetaData;
import com.intuit.appintgwkflw.wkflautomate.was.entity.eventing.events.WorkflowStateTransitionEvents;
import com.intuit.appintgwkflw.wkflautomate.was.entity.response.ExternalTaskDetail;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowActivityAttributes;
import com.intuit.appintgwkflw.wkflautomate.was.entity.task.WorkflowTaskRequest;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import lombok.experimental.UtilityClass;


@UtilityClass
public class StateTransitionServiceHelper {


  /**
   * Preparing header map to build EventHeaderEntity.
   *
   * @param executionId - to set entityId in event header.
   * @param headers     - headers map using which EventHeaderEntity will build.
   * @return typed EventHeaderEntity.
   */
  public EventHeaderEntity eventHeaderEntity(String executionId, Map<String, String> headers) {
    Map<String, String> eventHeaders = new HashMap<>();
    eventHeaders.putAll(headers);
    eventHeaders.put(EventHeaderConstants.ENTITY_ID, executionId);
    eventHeaders.put(EventHeaderConstants.IDEMPOTENCY_KEY, executionId);
    eventHeaders.put(EventHeaderConstants.DOMAIN_EVENT, WorkflowConstants.EXTERNAL_TASK);
    return InternalEventsUtil.buildEventHeader(eventHeaders,
        PublishEventType.WORKFLOW_TRANSITION_EVENTS, EventEntityType.WORKFLOW_TRANSITION_EVENTS);
  }

  /**
   * Prepare state transition event payload.
   *
   * @param externalTaskDetail     - External Task details having executionId to fetch camunda
   *                               variables.
   * @param activityProgressDetail - DB record to fill payload.
   * @param camundaVariableMap - variable map of camunda activity(model).
   * @param eventType - type of event being published - CREATED, COMPLETED etc.
   * @param transitionTime - time on which Camunda API was invoked for transition of task.
   * @return WorkflowStateTransitionEvents (payload of event)
   */
  public WorkflowStateTransitionEvents prepareWorkflowStateTransitionEvent(
      ExternalTaskDetail externalTaskDetail,
      ActivityProgressDetails activityProgressDetail,
      Map<String, Object> camundaVariableMap, String eventType,
      Long transitionTime) {
    ProcessDetails processDetails = activityProgressDetail.getProcessDetails();
    String recordType = Optional.ofNullable(processDetails.getDefinitionDetails().getRecordType())
    		.map(RecordType::getRecordType).orElse(null);
	return WorkflowStateTransitionEvents.builder()
    	.businessEntityId(processDetails.getRecordId())
    	.businessEntityType(recordType)
        .workflowMetadata(populateWorkflowMetaData(externalTaskDetail, activityProgressDetail))
        .activityMetadata(populateActivityMetaData(activityProgressDetail,
            camundaVariableMap))
        .activityType(activityProgressDetail.getActivityDefinitionDetail().getActivityType())
        .eventType(eventType)
        .status(activityProgressDetail.getStatus().toLowerCase())
        .txnId(activityProgressDetail.getTxnDetails().getTxnId())
        .timestamp(null != transitionTime? transitionTime : Instant.now().toEpochMilli())
        .build();
  }


  /**
   * WorkflowMetadata of State Transition event payload.
   *
   * @param externalTaskDetail     - External Task details having process details.
   * @param activityProgressDetail - DB record to fill payload.
   * @return typed WorkflowMetaData
   */
  public WorkflowMetaData populateWorkflowMetaData(ExternalTaskDetail externalTaskDetail,
      ActivityProgressDetails activityProgressDetail) {
    ProcessDetails processDetails = activityProgressDetail.getProcessDetails();
    return WorkflowMetaData.builder().processInstanceId(externalTaskDetail.getProcessInstanceId())
        .processDefinitionId(externalTaskDetail.getProcessDefinitionId())
        .workflowOwnerId(Long.toString(processDetails.getOwnerId()))
        .workflowName(processDetails.getDefinitionDetails().getDefinitionName())
        .workflowVersion(processDetails.getDefinitionDetails().getVersion())
        .build();
  }


  /**
   * ActivityMetadata of State Transition event payload.
   *
   * @param activityProgressDetail - DB record to fill payload.
   * @param camundaVariableMap - variable map of camunda activity(model).
   * @return typed ActivityMetaData
   */
  public ActivityMetaData populateActivityMetaData(ActivityProgressDetails activityProgressDetail,
      Map<String, Object> camundaVariableMap) {

    WorkflowActivityAttributes activityAttributes = ObjectConverter
        .fromJson(activityProgressDetail.getActivityDefinitionDetail().getAttributes(),
            new TypeReference<WorkflowActivityAttributes>() {
            });

    return ActivityMetaData.builder()
        .activityName(activityProgressDetail.getActivityDefinitionDetail().getActivityName())
        .scope(ActivityConstants.SCOPE_ACTIVITY)
        .activityId(activityProgressDetail.getActivityDefinitionDetail().getActivityId())
        .variables(camundaVariableMap)
        .properties(activityAttributes.getModelAttributes())
        .externalTaskId(activityProgressDetail.getId()).build();
  }

  /**
   * Checks event is present stateTransitionEvents list.
   * 
   * @param event - event to be checked.
   * @param extensionProperties - map of extension properties
   * @return typed boolean
   */
  public boolean isStateTransitionEventPublishEnabled(final String event,
      Map<String, String> extensionProperties) {
	Set<String> stateTransitionEvents = WorkflowTaskUtil.getTransitionEnabledEvents(extensionProperties);  
    return Optional.ofNullable(stateTransitionEvents).map(stateTransition ->
        stateTransition.stream()
            .anyMatch(stateTransitionEvent -> stateTransitionEvent.toLowerCase().equals(event)))
        .orElse(false);
  }
  
  
  /**
   * @param taskRequest :: Task request from ExternalTaskFramework.
   * @param eventType   :: eventType for which stateTransition event is to publish.
   * @return typed boolean
   */
  public boolean isStateTransitionPublishEnabled(WorkflowTaskRequest taskRequest,
      String eventType) {
    return taskRequest.isPublishWorkflowStateTransitionEvent() &&
        isStateTransitionEventPublishEnabled(eventType,
                taskRequest.getTaskAttributes().getModelAttributes());
  }

}

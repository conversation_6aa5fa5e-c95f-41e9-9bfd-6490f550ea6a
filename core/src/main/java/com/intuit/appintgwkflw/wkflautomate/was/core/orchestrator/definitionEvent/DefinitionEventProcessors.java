package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent;

import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionEventType;
import java.util.EnumMap;
import java.util.Map;

/**
 * Class acts as factory to return Definition event processors
 *
 * <AUTHOR>
 */
public class DefinitionEventProcessors {
  private static final Map<DefinitionEventType, DefinitionEventProcessor> EVENT_PROCESSOR_MAP =
      new EnumMap<>(DefinitionEventType.class);

  /**
   * Adds a processor.
   *
   * @param eventEntity the entity type for the event
   * @param eventProcessor the event handler
   */
  public static void addProcessor(
      DefinitionEventType eventEntity, DefinitionEventProcessor eventProcessor) {
    EVENT_PROCESSOR_MAP.put(eventEntity, eventProcessor);
  }

  /**
   * Gets processor
   *
   * @param eventEntity input name
   * @return definition event processor impl
   */
  public static DefinitionEventProcessor getProcessor(DefinitionEventType eventEntity) {
    return EVENT_PROCESSOR_MAP.get(eventEntity);
  }

  /**
   * Contains boolean.
   *
   * @param eventEntity the entity type name
   * @return true /false if processor is present or not
   */
  public static boolean contains(DefinitionEventType eventEntity) {

    return EVENT_PROCESSOR_MAP.containsKey(eventEntity);
  }
}

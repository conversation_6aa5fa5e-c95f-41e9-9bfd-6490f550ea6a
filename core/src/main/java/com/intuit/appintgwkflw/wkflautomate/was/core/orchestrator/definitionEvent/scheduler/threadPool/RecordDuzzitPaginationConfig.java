package com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.definitionEvent.scheduler.threadPool;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecordDuzzitPaginationConfig {

	 /**
	   * Total No. of Pages to be fetched from QBO.
	   */
	  @Builder.Default
	  private int totalPages = 2;
	  
	  /**
	   * No. of Records within a Page being being fetched from QBO.
	   */
	  @Builder.Default
	  private int recordsPerPage = 125;

	  /**
	   * Total No. of Records to be sent back as response to WAS.
	   */
	  @Builder.Default
	  private int maxResult = 250;
}

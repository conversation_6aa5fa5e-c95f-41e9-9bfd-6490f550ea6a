package com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition;

import lombok.Data;

import java.io.File;
import java.util.List;
import java.util.Map;

@Data
public class DeployDefinition extends RestDefinitionCommon {
    private List<File> dmnDefinitionFileList;
    private File bpmnDefinitionFile;
    private String deploymentName;

    public DeployDefinition(List<File> dmnDefinitionFileList, File bpmnDefinitionFile,
    		Map<String, String> headers, Class responseType, String deploymentName) {
        super(headers, responseType);
        this.dmnDefinitionFileList = dmnDefinitionFileList;
        this.bpmnDefinitionFile = bpmnDefinitionFile;
        this.deploymentName = deploymentName;
    }
}

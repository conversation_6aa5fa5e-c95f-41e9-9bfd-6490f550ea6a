## It Contains implementation that are common for all the WAS modules

 ```
 1. Async Execution chain
 2. Http Client
 3. AOP modules
 4. HeaderHttp client Interceptor
 5. MDC Interceptor
 6. Configs for WAS
 7. workflow logger
 
 ```

## Details on Each module:

# async-execution
Async Execution of code in Java based application.
This framework in written using Rx Java.Can be extended to write in any other choice of async implementation. 

## Use case
In any java application or any backend application there is a need of executing any business logic in async so that main thread can be freed or series of business logic needs to be performed one after the another or all of them can be executed independently parallely.
 So in any of the cases everyone has to write the boilerplate code to handle this use case.
 
##### User can use any of the below approaches to achive this.
 
 ```
 1. @async annotaton of Spring 
 2. using Futures of Java
 3. Completeable future of java
 4. using  rxjava.
 
 ```

If this is to be done at many places same code has to be repeated.This Framework will solve this and developer can directly focus on writing the business logic without worrying about the boilerplate code to handle async execution.

```
Lets take an example:

Suppose We need to place an order for an item that involves checking the inventory,Prepare Shipping Order,Scheduling Delivery,Send order confirmation 

Lets name this actions as Task A,Task B,Task C,Task D
```

### In naive way how user will implement is
```
1. First make a call to execute TaskA
2. Then make call to TaskB
3. make call to Task C
4. make call to Task D
```


after Task A executes then only TaskB and Task C can execute but can execute in parallel then post completion of this TaskD can execute.

Here TaskA --> then TaskB,TaskC ---> TaskD

#### If we need to implement such scenario it will be very simple using this library.

```java
public class TaskA implements Task {
	public State execute(State inputRequest) {
		// checking the inventory
		inputRequest.addValue("TaskAResponse","response");
	}
}

public class TaskB implements Task {
	public State execute(State inputRequest) {
		// Prepare Shipping Order
		inputRequest.addValue("TaskBResponse","response");
	}
}

public class TaskC implements Task {
	public State execute(State inputRequest) {
		// Scheduling Delivery
		inputRequest.addValue("TaskCResponse","response");
	}
}

public class TaskD implements Task {
	public State execute(State inputRequest) {
		// Send order confirmation 
		inputRequest.addValue("TaskDResponse","response");
	}
}
```

```
We need to create a State request that is shared between each Task.

1. we need to  create a request
State request = new State();

req.addValue("TaskARequest", 1);
req.addValue("TaskBRequest", 1);
req.addValue("TaskCRequest", 1);
req.addValue("TaskDRequest", 1);

2. Create the Tasks that we want to execute

 TaskA taskA = new TaskA();
 TaskB taskB = new TaskB();
 TaskC taskC = new TaskC();
 TaskD taskD = new TaskD();

3. execute the tasks

State response = new RxExecutionChain(request, taskA).next(taskB, taskC).next(taskD).execute();

4. fetch the response of any task from the response by calling response.getValue("TaskDResponse");

```

```
in this taskA will first execute in a separate thread then as soon as it completes taskB and taskC will execute parallelly and post its  completion taskD will execute.Main thread will wait for all action to execute.

If we don't want main thread to wait if we want to do fire and forget just call executeAsync() instead on execute.
```

```
Task can be marked as fatal and non fatal as well so in case of fatal chain will break and in case of non fatal tasks chain will continue even if exception is thrown from the tasks.
```

### Sequence flow

![Sequence Flow](docs/sequence_diagram.png "Chain Sequence Flow")

### TODO Features:
```
* Support for Custom executor instead of unbounded cached thread pool.
* Logging of time taken by each task based on a config flag.
```
 
package com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.handlers;


import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.BPMN_PLACEHOLDER_VALUES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.PROCESS_VARIABLES;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_META_DATA;
import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.USER_VARIABLES;

import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.MetricName;
import com.intuit.appintgwkflw.wkflautomate.telemetry.metrics.Type;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Metric;
import com.intuit.appintgwkflw.wkflautomate.was.common.annotations.Throttle;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.logger.WorkflowLogger;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.ConditionalElementFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.MultiStepProcessorFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.factory.OutgoingActivityMapperFactory;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.MultiStepPlaceholderExtractor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.PlaceholderExtractorProvider;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.MultiStepUtil;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowBeansConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ThrottleAttribute;
import com.intuit.appintgwkflw.wkflautomate.was.localisation.service.TranslationService;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.WorkflowStep;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.camunda.bpm.model.bpmn.instance.BusinessRuleTask;
import org.javatuples.Pair;
import org.springframework.stereotype.Component;

/**
 * This class parses the create definition payload and creates the definition for Multi-Step
 * workflow.
 *
 * <AUTHOR>
 */
@Component(WorkflowBeansConstants.CREATE_MULTI_STEP_DEFINITION_HANDLER)
public class CreateMultiStepDefinitionHandler extends CreateDefinitionHandler {

  private final OutgoingActivityMapperFactory outgoingActivityMapperFactory;
  private final MultiStepProcessorFactory multiStepProcessorFactory;
  private final MultiStepPlaceholderExtractor multiStepPlaceholderExtractor;

  public CreateMultiStepDefinitionHandler(
      ConditionalElementFactory conditionalElementFactory,
      PlaceholderExtractorProvider placeholderExtractorProvider,
      TranslationService translationService,
      WASContextHandler wasContextHandler,
      FeatureFlagManager featureFlagManager,
      OutgoingActivityMapperFactory outgoingActivityMapperFactory,
      MultiStepProcessorFactory multiStepProcessorFactory,
      MultiStepPlaceholderExtractor multiStepPlaceholderExtractor) {
    super(conditionalElementFactory, placeholderExtractorProvider, translationService,
        wasContextHandler, featureFlagManager);
    this.multiStepProcessorFactory = multiStepProcessorFactory;
    this.outgoingActivityMapperFactory = outgoingActivityMapperFactory;
    this.multiStepPlaceholderExtractor = multiStepPlaceholderExtractor;
  }

  /**
   * Creates the definition object, and creates the dmn instances
   *
   * @param definitionInstance {@link Definition}
   * @param realmId            Company id
   * @return Updated {@link Definition} object
   */
  // The @Throttle annotation below will throttle both CREATE and UPDATE definition calls.
  // Since currently, this method is also used in the update definition flow.
  @Throttle(attribute = ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_PER_REALM)
  @Throttle(attribute = ThrottleAttribute.DEFINITIONS_PER_WORKFLOW_IN_TIMEFRAME)
  @Metric(name = MetricName.CREATE_MULTI_STEP_WORKFLOW_DEFINITION, type = Type.API_METRIC)
  public DefinitionInstance process(DefinitionInstance definitionInstance, String realmId)
      throws WorkflowGeneralException {

    WorkflowLogger.logInfo(
        "step=CreateMultiStepDefinition status=Started templateCategory=%s",
        definitionInstance.getTemplateDetails().getTemplateCategory());

    definitionInstance.setWorkflowStepMap(prepareWorkflowStepMap(definitionInstance));

    //This visited set is created as to maintain the first workflow step Ids for every dmn to make sure that
    //we don't end up creating multiple dmns for the same set of workflow steps(same subtree)
    Set<String> visitedDmnRootWorkflowStepIds = new HashSet<>();

    // Process workflow steps from the definition
    WorkflowStep firstWorkflowStep = MultiStepUtil.findFirstWorkflowStep(
        definitionInstance.getDefinition()
            .getWorkflowSteps());

    Map<String, Object> placeholders = multiStepPlaceholderExtractor.extractPlaceholderValue(
        definitionInstance);
    definitionInstance.setPlaceholderValue(placeholders);

    String firstConditionElementId =
            definitionInstance.getBpmnModelInstance().getModelElementsByType(BusinessRuleTask.class).stream().findFirst().get().getId();

    processWorkflowSteps(firstConditionElementId, String.valueOf(firstWorkflowStep.getId()),
        definitionInstance, visitedDmnRootWorkflowStepIds);

    // Get the lookupKeys from definition object if present and map appropriately to store in definitionInstance
    definitionInstance.setLookupKeys(LookupKeysMapper.getMapOfLookupKeysFromList(
        definitionInstance.getDefinition().getLookupKeys()).orElse(Collections.emptyMap()));

    WorkflowLogger.logInfo(
        "step=CreateMultiStepDefinition status=Completed templateCategory=%s",
        definitionInstance.getTemplateDetails().getTemplateCategory());

    buildPlaceholderMap(definitionInstance, placeholders);

    return definitionInstance;
  }

  /**
   * Generates a map of workflow step id to workflow step for every workflow step in the payload
   *
   * @param definitionInstance
   * @return
   */
  private Map<String, WorkflowStep> prepareWorkflowStepMap(DefinitionInstance definitionInstance) {
    Map<String, WorkflowStep> workflowStepMap = new HashMap<>();

    definitionInstance
        .getDefinition()
        .getWorkflowSteps()
        .forEach(
            workflowStep ->
                workflowStepMap.put(String.valueOf(workflowStep.getId()), workflowStep));

    return workflowStepMap;
  }

  /**
   * Recursive function to process the payload of the workflow to find all the unique dmns and
   * action steps
   *
   * @param currentBpmnElementActivityId
   * @param workflowStepId
   * @param definitionInstance
   * @param visitedDmnRootWorkflowStepIds
   */
  private void processWorkflowSteps(String currentBpmnElementActivityId,
      String workflowStepId,
      DefinitionInstance definitionInstance,
      Set<String> visitedDmnRootWorkflowStepIds) {

    Map<String, WorkflowStep> workflowStepMap = definitionInstance.getWorkflowStepMap();
    WorkflowStep currentWorkflowStep = workflowStepMap.get(workflowStepId);

    List<Pair<String, String>> activityIdToCalledElements =
        outgoingActivityMapperFactory.fetchOutgoingActivityIds(
            currentBpmnElementActivityId, definitionInstance);

    List<String> outgoingActivityIds = activityIdToCalledElements.stream()
        .map(Pair::getValue0)
        .collect(Collectors.toCollection(LinkedList::new));

    WorkflowLogger.logInfo(
        "step=processWorkflowStepsFromBpmn currentBpmnElementActivityId=%s",
        currentBpmnElementActivityId);

    Map<String, String> stepIdToActivityIdMap = multiStepProcessorFactory
        .processWorkflowStep(currentWorkflowStep, currentBpmnElementActivityId, definitionInstance,
            outgoingActivityIds, visitedDmnRootWorkflowStepIds);

    stepIdToActivityIdMap.forEach((stepId, currentActivityId) ->
        processWorkflowSteps(currentActivityId, stepId, definitionInstance, visitedDmnRootWorkflowStepIds));

  }

  /**
   * BPMN_PLACEHOLDER_VALUES is the exact json value which will be stored in DB
   *
   * @param definitionInstance
   * @param placeholderValues
   */
  private void buildPlaceholderMap(DefinitionInstance definitionInstance,
      Map<String, Object> placeholderValues) {
    Map<String, Object> definitionPlaceholders = new HashMap<>();
    definitionPlaceholders.put(PROCESS_VARIABLES, placeholderValues.get(PROCESS_VARIABLES));
    definitionPlaceholders.put(USER_META_DATA, placeholderValues.get(USER_META_DATA));
    definitionPlaceholders.put(USER_VARIABLES, placeholderValues.get(USER_VARIABLES));
    definitionInstance.getPlaceholderValue().put(BPMN_PLACEHOLDER_VALUES, definitionPlaceholders);
  }

}
